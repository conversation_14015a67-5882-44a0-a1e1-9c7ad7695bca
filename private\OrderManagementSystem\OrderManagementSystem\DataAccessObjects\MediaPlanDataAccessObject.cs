﻿using Microsoft.AdCenter.Shared.MT;
using OrderManagementSystem.Common;
using OrderManagementSystem.Entities;
using OrderManagementSystem.Entities.Internal;
using OrderManagementSystem.Interfaces;
using OrderManagementSystem.Requests;
using System.Data;
using System.Data.Common;
using System.Text;

namespace OrderManagementSystem.DataAccessObjects
{
    public interface IMediaPlanDao : IDataAccessObject
    {
        public Task<Result<MediaPlan>> Post(MediaPlanPostRequest request);
        public Task<Result<MediaPlan[]>> Get(MediaPlanGetAllRequest request);
        public Task<Result<MediaPlan>> Get(MediaPlanGetRequest request);
        public Task<Result<MediaPlanCommitOperation>> Get(MediaPlanCommitGetRequest request);
        public Task<Result<MediaPlan[]>> Get(MediaPlanGetAllForUserRequest request);
        public Task<Result<MediaPlan[]>> Get(MediaPlanGetMultipleRequest request);
        public Task<Result> Put(MediaPlanPutRequest request);
        public Task<Result> Delete(MediaPlanDeleteRequest request);
        public Task<Result> Commit(XandrCommitRequest request);
        public Task<Result<long>> TrackCommit(MediaPlanCommitUpsertRequest request);
        public Task<Result<MediaPlanCommitTask>> GetTask(MediaPlanCommitGetTaskRequest request);
    }

    public class MediaPlanDao : IMediaPlanDao
    {
        private IProcDaoExecutor procDaoExecutor;
        private IProductDao productDao;
        private IMediaPlanCommitHelper mediaPlanCommitHelper;

        public MediaPlanDao(IProcDaoExecutor procDaoExecutor, IProductDao productDao, IMediaPlanCommitHelper mediaPlanCommitHelper)
        {
            this.procDaoExecutor = procDaoExecutor;
            this.productDao = productDao;
            this.mediaPlanCommitHelper = mediaPlanCommitHelper;
        }

        public async Task<Result<MediaPlan>> Post(MediaPlanPostRequest request)
        {
            var result = new Result<MediaPlan>();

            try
            {
                using var reader = await procDaoExecutor.ExecuteDataReaderAsync(SpWrappers.CreatePublicMediaPlanCreateCommand(request.MediaPlan.CustomerId, request.MediaPlan, request.UserId), request.Logger, DBType.OrderManagementSystemCustomerDB);
                result.Entity = request.MediaPlan;

                if (await reader.ReadAsync())
                {
                    //When we support bulk, we will have to dynamically deal with this aspect based on mapping InputId to the appropriate row in the response
                    result.Entity.Id = reader.GetInt64(1);
                }
                else
                {
                    throw new Exception("MediaPlan Id not returned from db");
                }
            }
            catch (Exception ex)
            {
                procDaoExecutor.HandleSqlException(ex, result, request.Logger, request.ToString());
            }
            return result;
        }

        public async Task<Result<MediaPlan[]>> Get(MediaPlanGetAllRequest request)
        {
            var result = new Result<MediaPlan[]>();
            try
            {
                List<MediaPlan> mediaPlans = new List<MediaPlan>();
                using var reader = await procDaoExecutor.ExecuteDataReaderAsync(SpWrappers.CreatePublicMediaPlanGetCommand(request.CustomerId, null, null, null, null), request.Logger, DBType.OrderManagementSystemCustomerDB);
                while (await reader.ReadAsync())
                {
                    mediaPlans.Add(ReadMediaPlanFromDataReader(reader));
                }

                result.Entity = mediaPlans.ToArray();
                result.Count = result.Entity.Length;
            }
            catch (Exception ex)
            {
                procDaoExecutor.HandleSqlException(ex, result, request.Logger, request.ToString());
            }
            return result;
        }

        public async Task<Result<MediaPlan[]>> Get(MediaPlanGetMultipleRequest request)
        {
            var result = new Result<MediaPlan[]>();
            try
            {
                List<MediaPlan> mediaPlans = new List<MediaPlan>();
                using var reader = await procDaoExecutor.ExecuteDataReaderAsync(SpWrappers.CreatePublicMediaPlanGetCommand(request.MediaPlanIds), request.Logger, DBType.OrderManagementSystemCustomerDB);
                while (await reader.ReadAsync())
                {
                    mediaPlans.Add(ReadMediaPlanFromDataReader(reader));
                }

                result.Entity = mediaPlans.ToArray();
                result.Count = result.Entity.Length;
            }
            catch (Exception ex)
            {
                procDaoExecutor.HandleSqlException(ex, result, request.Logger, request.ToString());
            }
            return result;
        }

        public async Task<Result<MediaPlan>> Get(MediaPlanGetRequest request)
        {
            var result = new Result<MediaPlan>();

            try
            {
                using var reader = await procDaoExecutor.ExecuteDataReaderAsync(SpWrappers.CreatePublicMediaPlanGetCommand(request.CustomerId, request.Id, null, null, null), request.Logger, DBType.OrderManagementSystemCustomerDB);
                if (await reader.ReadAsync())
                {
                    result.Entity = ReadMediaPlanFromDataReader(reader);
                }
            }
            catch (Exception ex)
            {
                procDaoExecutor.HandleSqlException(ex, result, request.Logger, request.ToString());
            }

            return result;
        }

        public async Task<Result<MediaPlanCommitOperation>> Get(MediaPlanCommitGetRequest request)
        {
            var result = new Result<MediaPlanCommitOperation>();

            try
            {
                using var reader = await procDaoExecutor.ExecuteDataReaderAsync(SpWrappers.CreatePublicMediaPlanGetCommitCommand(request.MediaPlanId), request.Logger, DBType.OrderManagementSystemCustomerDB);
                if (await reader.ReadAsync())
                {
                    result.Entity = ReadMediaPlanCommitOperationFromDataReader(reader);
                }
            }
            catch (Exception ex)
            {
                procDaoExecutor.HandleSqlException(ex, result, request.Logger, request.ToString());
            }

            return result;
        }

        public async Task<Result<MediaPlan[]>> Get(MediaPlanGetAllForUserRequest request)
        {
            var result = new Result<MediaPlan[]>();

            try
            {
                List<MediaPlan> mediaPlans = new List<MediaPlan>();
                using var reader = await procDaoExecutor.ExecuteDataReaderAsync(SpWrappers.CreatePublicMediaPlanGetCommand(null, null, null, null, null), request.Logger, DBType.OrderManagementSystemCustomerDB);
                while (await reader.ReadAsync())
                {
                    mediaPlans.Add(ReadMediaPlanFromDataReader(reader));
                }

                result.Entity = mediaPlans.ToArray();
                result.Count = result.Entity.Length;
            }
            catch (Exception ex)
            {
                procDaoExecutor.HandleSqlException(ex, result, request.Logger, request.ToString());
            }

            return result;
        }

        public async Task<Result> Put(MediaPlanPutRequest request)
        {
            var result = new Result();

            try
            {
                await procDaoExecutor.ExecuteNonQueryAsync(SpWrappers.CreatePublicMediaPlanUpdateCommand(request.MediaPlan.CustomerId, request.MediaPlan, request.UserId), request.Logger, DBType.OrderManagementSystemCustomerDB);
            }
            catch (Exception ex)
            {
                procDaoExecutor.HandleSqlException(ex, result, request.Logger, request.ToString());
            }

            return result;
        }

        public async Task<Result> Delete(MediaPlanDeleteRequest request)
        {
            var result = new Result();

            try
            {
                await procDaoExecutor.ExecuteNonQueryAsync(SpWrappers.CreatePublicMediaPlanDeleteCommand(request.CustomerId, [request.Id], request.UserId), request.Logger, DBType.OrderManagementSystemCustomerDB);
            }
            catch (Exception ex)
            {
                procDaoExecutor.HandleSqlException(ex, result, request.Logger, request.ToString());
            }

            return result;
        }

        public async Task<Result<long>> TrackCommit(MediaPlanCommitUpsertRequest request)
        {
            var result = new Result<long>();

            try
            {
                using var reader = await procDaoExecutor.ExecuteDataReaderAsync(SpWrappers.CreatePublicMediaPlanCommitUpsertCommand(request, request.UserId), request.Logger, DBType.OrderManagementSystemCustomerDB);
                if (await reader.ReadAsync())
                {
                    result.Entity = reader.GetInt64(0);
                }
            }
            catch (Exception ex)
            {
                procDaoExecutor.HandleSqlException(ex, result, request.Logger, request.ToString());
            }

            return result;
        }

        public async Task<Result<MediaPlanCommitTask>> GetTask(MediaPlanCommitGetTaskRequest request)
        {
            var result = new Result<MediaPlanCommitTask>();

            try
            {
                using var reader = await procDaoExecutor.ExecuteDataReaderAsync(SpWrappers.CreatePublicMediaPlanGetTaskCommand(request.UserId), request.Logger, DBType.OrderManagementSystemCustomerDB);
                if (await reader.ReadAsync())
                {
                    result.Entity = new MediaPlanCommitTask()
                    {
                        CustomerId = reader.GetInt32(SpWrappers.MediaPlanCustomerIdOrdinalName),
                        MediaPlanId = reader.GetInt64(SpWrappers.MediaPlanIdOrdinalName),
                        MediaPlanCommitId = reader.GetInt64(SpWrappers.MediaPlanMediaPlanCommitIdOrdinalName)
                    };
                }
            }
            catch (Exception ex)
            {
                procDaoExecutor.HandleSqlException(ex, result, request.Logger, request.ToString());
            }

            return result;
        }

        public async Task<Result> Commit(XandrCommitRequest request)
        {
            var result = new Result();
            var logger = request.Logger;

            if (request.MediaPlan.Status == MediaPlanStatus.Committed || request.MediaPlan.Status == MediaPlanStatus.Serving)
            {
                //Means no significant updates have occurred on the Media Plan, therefore we can skip the commit entirely
                return result;
            }

            //Step 1: Commit the Media Plan to Monetize
            var ioCommitResult = await mediaPlanCommitHelper.CommitMediaPlan(request, XandrApiHelper.XandrIOCommitBaseUrl);
            result.Merge(ioCommitResult);
            if (result.Failed) { return result; }

            var finalIO = ioCommitResult.Entity!;
            request.MediaPlan.MonetizeInsertionOrderId = finalIO.Id;
            request.MediaPlan.MonetizeBudgetIntervalId = finalIO.BudgetIntervals![0].Id;

            //All subsequent steps occur for each individual Line, so get the lines
            foreach (var line in request.Lines)
            {
                if (line.Status == LineStatus.Synced)
                {
                    //Means no significant/critical changes occurred for this Line since it was last committed to Monetize. This means we do not need to re-upload
                    continue;
                }

                bool isLineCreation = line.MonetizeLineItemId == null;

                //Step 2: If this is a creation, see if it's already been created on Xandr (could still be marked as a creation due to service failure)
                //If so, pull the needed IDs out. We will do an update instead of a creation
                result.Merge(await mediaPlanCommitHelper.CheckForExistingLineItem(request, line, XandrApiHelper.XandrLineItemCommitBaseUrl));
                if (result.Failed) { return result; } //Since Line Items directly lead to customer spend, if this fails we DO want to fail the request right away so we ensure no duplication

                if (!isLineCreation || (isLineCreation && line.MonetizeLineItemId == null))
                {
                    //Step 3: Get the product associated to the Line, as we need the placements
                    // TODO To refactor the NoPublishserRequest outside of the foreach loop
                    var getProductNoPublisherRequest = new ProductGetNoPublisherRequest(logger!, new[] { line.ProductId });
                    var getProductResult = await productDao.GetNoPublisher(getProductNoPublisherRequest);
                    result.Merge(getProductResult);
                    if (getProductResult.Failed) { return result; }

                    var products = getProductResult.Entity;
                    if (products == null || products.Length == 0)
                    {
                        result.AddError(new Error(source: $"{request}", message: ErrorMessage.NotFound, type: ErrorType.UserError),
                            request.Logger, $"[{nameof(Commit)}] No products found for Line Item: {line.Id} in Media Plan: {request.MediaPlan.Id}");
                        return result;
                    }

                    var product = products[0];

                    //Step 4: Create the Profile in Monetize. Only needed if targeting has changed (or we can always update? Not sure we have a mechanism to track if targeting has changed since last commit)
                    var profileIdResponse = await mediaPlanCommitHelper.CreateXandrProfile(request, line, product, XandrApiHelper.XandrProfileCommitBaseUrl);
                    result.Merge(profileIdResponse);
                    if (result.Failed)
                    {
                        logger?.LogError($"Failed to create or retrieve Xandr profile for LineId {line.Id} in MediaPlanId {request.MediaPlan.Id}.");
                        return result;
                    }

                    var profileId = profileIdResponse.Entity!;

                    //Step 5: Commit the Line Item to Monetize
                    var lineItemCommitResponse = await mediaPlanCommitHelper.CommitLine(request, line, product, profileId, product.TimeZoneId, product.Slot, XandrApiHelper.XandrLineItemCommitBaseUrl, request.ScoreCards!.GetValueOrDefault(line.Id));
                    result.Merge(lineItemCommitResponse);
                    if (result.Failed)
                    {
                        logger?.LogError($"Failed to commit line item for LineId {line.Id} in MediaPlanId {request.MediaPlan.Id}.");
                        return result;
                    }

                    var monetizeLineItem = lineItemCommitResponse.Entity!;
                    line.MonetizeLineItemId = monetizeLineItem.Id;
                    line.MonetizeBudgetIntervalId = monetizeLineItem.BudgetIntervals![0].Id; //There is guaranteed to be 1, and only 1, budget interval
                }

                //Step 6: Update Line on the DB (not batching into single DB call to minimize risk of Monetize commit happening but never succeeding in DB call)
                line.Status = LineStatus.Synced;

                try
                {
                    await procDaoExecutor.ExecuteNonQueryAsync(SpWrappers.CreatePublicLineUpdateCommand(line, null, request.UserId), logger!);
                }
                catch (Exception ex)
                {
                    procDaoExecutor.HandleSqlException(ex, result, request.Logger, request.ToString());
                }

                if (result.Failed) { return result; }
            }

            //Step 7: Update Media Plan on the DB
            if (request.MediaPlan.StartDate <= DateTime.UtcNow)
            {
                if (request.MediaPlan.Status == MediaPlanStatus.VerbalCommit)
                {
                    request.MediaPlan.Status = MediaPlanStatus.ServingVerbalOnly;
                }
                else
                {
                    request.MediaPlan.Status = MediaPlanStatus.Serving;
                }
            }
            else
            {
                if (request.MediaPlan.Status == MediaPlanStatus.VerbalCommit)
                {
                    request.MediaPlan.Status = MediaPlanStatus.CommittedVerbalOnly;
                }
                else
                {
                    request.MediaPlan.Status = MediaPlanStatus.Committed;
                }
            }
            var mediaPlanUpdateRequest = new MediaPlanPutRequest(logger, request.MediaPlan, request.UserId);
            var mediaPlanUpdateResult = await Put(mediaPlanUpdateRequest);
            result.Merge(mediaPlanUpdateResult);
            if (mediaPlanUpdateResult.Failed)
            {
                logger?.LogError($"Failed to update Media PlanId {request.MediaPlan.Id}.");
                return result;
            }

            return result;
        }

        private static MediaPlan ReadMediaPlanFromDataReader(DbDataReader reader)
        {
            return new MediaPlan()
            {
                CustomerId = reader.GetInt32(SpWrappers.MediaPlanCustomerIdOrdinalName),
                Id = reader.GetInt64(SpWrappers.MediaPlanIdOrdinalName),
                Name = reader.GetString(SpWrappers.MediaPlanNameOrdinalName),
                Description = reader.IsDBNull(SpWrappers.MediaPlanDescriptionOrdinalName) ? null : reader.GetString(SpWrappers.MediaPlanDescriptionOrdinalName),
                Status = (MediaPlanStatus)reader.GetByte(SpWrappers.MediaPlanStatusIdOrdinalName),
                StartDate = reader.GetDateTime(SpWrappers.MediaPlanStartDateOrdinalName),
                EndDate = reader.GetDateTime(SpWrappers.MediaPlanEndDateOrdinalName),
                TargetSpend = reader.GetDecimal(SpWrappers.MediaPlanTargetSpendOrdinalName),
                CurrencyCode = reader.GetString(SpWrappers.MediaPlanCurrencyCodeOrdinalName),
                Contact = reader.GetString(SpWrappers.MediaPlanContactOrdinalName),
                OpportunityId = reader.IsDBNull(SpWrappers.MediaPlanOpportunityIdStringOrdinalName) ? null : reader.GetString(SpWrappers.MediaPlanOpportunityIdStringOrdinalName),
                MonetizeInsertionOrderId = reader.IsDBNull(SpWrappers.MediaPlanMonetizeIOIdOrdinalName) ? null : (int)reader.GetInt64(SpWrappers.MediaPlanMonetizeIOIdOrdinalName), //BIGINT on DB, but INT from Monetize
                MonetizeBudgetIntervalId = reader.IsDBNull(SpWrappers.MediaPlanMonetizeBudgetIntervalIdOrdinalName) ? null : reader.GetInt32(SpWrappers.MediaPlanMonetizeBudgetIntervalIdOrdinalName),
                CommitStatus = reader.IsDBNull(SpWrappers.MediaPlanCommitStatusIdOrdinalName) ? null : (OfflineOperationStatus)reader.GetByte(SpWrappers.MediaPlanCommitStatusIdOrdinalName),
                CommitFailureCode = reader.IsDBNull(SpWrappers.MediaPlanCommitFailureCodeOrdinalName) ? null : (FailureCode)reader.GetInt32(SpWrappers.MediaPlanCommitFailureCodeOrdinalName),
                CommitModifiedDateTime = reader.IsDBNull(SpWrappers.MediaPlanCommitModifiedDateTimeOrdinalName) ? null : reader.GetDateTime(SpWrappers.MediaPlanCommitModifiedDateTimeOrdinalName),
                CreatedDateTime = reader.GetDateTime(SpWrappers.MediaPlanCreatedDateTimeOrdinalName),
                ModifiedDateTime = reader.GetDateTime(SpWrappers.MediaPlanModifiedDateTimeOrdinalName),
                ModifiedByUserId = reader.GetInt64(SpWrappers.MediaPlanModifiedByUserIdOrdinalName)
            };
        }

        private static MediaPlanCommitOperation ReadMediaPlanCommitOperationFromDataReader(DbDataReader reader)
        {
            return new MediaPlanCommitOperation()
            {
                MediaPlanId = reader.GetInt64(SpWrappers.MediaPlanIdOrdinalName),
                MediaPlanCommitId = reader.GetInt64(SpWrappers.MediaPlanMediaPlanCommitIdOrdinalName),
                CommitStatus = (OfflineOperationStatus)reader.GetByte(SpWrappers.MediaPlanCommitStatusIdOrdinalName),
                CommitFailureCode = reader.IsDBNull(SpWrappers.MediaPlanCommitFailureCodeOrdinalName) ? null : (FailureCode)reader.GetInt32(SpWrappers.MediaPlanCommitFailureCodeOrdinalName),
                LastUpdateTime = reader.GetDateTime(SpWrappers.MediaPlanModifiedDateTimeOrdinalName)
            };
        }
    }
}
