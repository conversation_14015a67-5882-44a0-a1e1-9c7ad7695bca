﻿using System;
using System.ServiceModel;
using CampaignTest.ApiFunctionalTests.Collections;

namespace Microsoft.BingAds.CampaignManagement
{
    public class BulkServiceRest : IBulkService
    {
        private readonly CollectionBase _collectionBase;

        public BulkServiceRest(CollectionBase collectionBase)
        {
            _collectionBase = collectionBase;
        }

        public DownloadCampaignsByAccountIdsResponse DownloadCampaignsByAccountIds(DownloadCampaignsByAccountIdsRequest request)
        {
            return _collectionBase.CallRestApi<DownloadCampaignsByAccountIdsResponse>("DownloadCampaignsByAccountIds", request, (r, t) => { r.TrackingId = t; });
        }

        public Task<DownloadCampaignsByAccountIdsResponse> DownloadCampaignsByAccountIdsAsync(DownloadCampaignsByAccountIdsRequest request)
        {
            throw new NotImplementedException();
        }

        public DownloadCampaignsByCampaignIdsResponse DownloadCampaignsByCampaignIds(DownloadCampaignsByCampaignIdsRequest request)
        {
            return _collectionBase.CallRestApi<DownloadCampaignsByCampaignIdsResponse>("DownloadCampaignsByCampaignIds", request, (r, t) => { r.TrackingId = t; });
        }

        public Task<DownloadCampaignsByCampaignIdsResponse> DownloadCampaignsByCampaignIdsAsync(DownloadCampaignsByCampaignIdsRequest request)
        {
            throw new NotImplementedException();
        }

        public GetBulkDownloadStatusResponse GetBulkDownloadStatus(GetBulkDownloadStatusRequest request)
        {
            return _collectionBase.CallRestApi<GetBulkDownloadStatusResponse>("GetBulkDownloadStatus", request, (r, t) => { r.TrackingId = t; });
        }

        public Task<GetBulkDownloadStatusResponse> GetBulkDownloadStatusAsync(GetBulkDownloadStatusRequest request)
        {
            throw new NotImplementedException();
        }

        public GetBulkUploadUrlResponse GetBulkUploadUrl(GetBulkUploadUrlRequest request)
        {
            return _collectionBase.CallRestApi<GetBulkUploadUrlResponse>("GetBulkUploadUrl", request, (r, t) => { r.TrackingId = t; });
        }

        public Task<GetBulkUploadUrlResponse> GetBulkUploadUrlAsync(GetBulkUploadUrlRequest request)
        {
            throw new NotImplementedException();
        }

        public GetBulkUploadStatusResponse GetBulkUploadStatus(GetBulkUploadStatusRequest request)
        {
            return _collectionBase.CallRestApi<GetBulkUploadStatusResponse>("GetBulkUploadStatus", request, (r, t) => { r.TrackingId = t; });
        }

        public Task<GetBulkUploadStatusResponse> GetBulkUploadStatusAsync(GetBulkUploadStatusRequest request)
        {
            throw new NotImplementedException();
        }

        public UploadEntityRecordsResponse UploadEntityRecords(UploadEntityRecordsRequest request)
        {
            return _collectionBase.CallRestApi<UploadEntityRecordsResponse>("UploadEntityRecords", request, (r, t) => { r.TrackingId = t; });
        }

        public Task<UploadEntityRecordsResponse> UploadEntityRecordsAsync(UploadEntityRecordsRequest request)
        {
            throw new NotImplementedException();
        }
    }
}