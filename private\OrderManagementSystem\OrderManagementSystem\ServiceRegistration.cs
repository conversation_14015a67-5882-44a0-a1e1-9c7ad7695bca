﻿using OrderManagementSystem.Authorization;
using OrderManagementSystem.Authorization.ClientCenter;
using OrderManagementSystem.Common;
using OrderManagementSystem.Common.DRS;
using OrderManagementSystem.Common.Xandr;
using OrderManagementSystem.Configuration;
using OrderManagementSystem.DataAccessObjects;
using OrderManagementSystem.Entities;
using OrderManagementSystem.Entities.Common;
using OrderManagementSystem.Entities.External;
using OrderManagementSystem.Entities.Internal;
using OrderManagementSystem.Interfaces;
using OrderManagementSystem.Processors;
using OrderManagementSystem.Requests;
using OrderManagementSystem.Services;
using OrderManagementSystem.Validators;
using System.ServiceModel;

namespace OrderManagementSystem
{
    public class ServiceRegistration
    {
        private IServiceCollection _services;
        private IConfiguration _configuration;

        public ServiceRegistration(IServiceCollection services, IConfiguration configuration)
        {
            _services = services;
            _configuration = configuration;
        }

        public void RegisterDependencies()
        {
            RegisterCountryDependencies();
            RegisterLocationDependencies();
            RegisterDMADependencies();
            RegisterCurrencyDependencies();
            RegisterSegmentDependencies();
            RegisterTimeZoneDependencies();

            RegisterPublisherDependencies();
            RegisterPlacementDependencies();
            RegisterProductDependencies();

            RegisterCustomerDependencies();
            RegisterMediaPlanDependencies();
            RegisterLineDependencies();
            RegisterPublisherApprovalDependencies();
            RegisterScoreCardDependencies();
            RegisterCalendarDependencies();

            RegisterXandrApiDependencies();
            RegisterClientCenterDependencies();

            RegisterDaoDependencies();
            RegisterEmailPublisherDependencies();
            RegisterBlobDependencies();
            RegisterDRSDependencies();

            RegisterMonitoringDependencies();
            RegisterOfflineOperationDependencies();
            RegisterDynamicConfigDependencies();
            RegisterKeyValueDependencies();
            // extend as needed
        }

        private void RegisterCountryDependencies()
        {
            _services.AddSingleton<ICountryDao, CountryDao>();

            _services.AddScoped<IProcessor<CountryGetAllRequest, XandrCountry[]>, CountryGetAllProcessor>();
            _services.AddScoped<IValidator<CountryGetAllRequest>, CountryGetAllValidator>();
            _services.AddHostedService<CountryCacheService>();

        }

        private void RegisterLocationDependencies()
        {
            _services.AddScoped<ILocationDataAccessObject, LocationDataAccessObject>();
            _services.AddHostedService<LocationCacheService>();

            _services.AddScoped<IProcessor<CountryGetAllLocationRequest, XandrCountry[]>, CountryGetAllLocationProcessor>();
            _services.AddScoped<IProcessor<CountryGetByIdLocationRequest, XandrCountry?>, CountryGetByIdLocationProcessor>();
            _services.AddScoped<IProcessor<RegionGetAllLocationRequest, XandrRegion[]>, RegionGetAllProcessor>();
            _services.AddScoped<IProcessor<RegionGetByIdLocationRequest, XandrRegion?>, RegionGetByIdProcessor>();
            _services.AddScoped<IProcessor<CityGetAllLocationRequest, XandrCity[]>, CityGetAllProcessor>();
            _services.AddScoped<IProcessor<CityGetByIdLocationRequest, XandrCity?>, CityGetByIdProcessor>();
            _services.AddScoped<IProcessor<CityGetByIdsRequest, XandrCity[]>, CityGetByIdsProcessor>();


            _services.AddScoped<IValidator<CountryGetAllLocationRequest>, CountryGetAllLocationValidator>();
            _services.AddScoped<IValidator<CountryGetByIdLocationRequest>, CountryGetByIdLocationValidator>();
            _services.AddScoped<IValidator<RegionGetAllLocationRequest>, RegionGetAllLocationValidator>();
            _services.AddScoped<IValidator<RegionGetByIdLocationRequest>, RegionGetByIdLocationValidator>();
            _services.AddScoped<IValidator<CityGetAllLocationRequest>, CityGetAllLocationValidator>();
            _services.AddScoped<IValidator<CityGetByIdLocationRequest>, CityGetByIdLocationValidator>();
            _services.AddScoped<IValidator<CityGetByIdsRequest>, CityGetByIdsValidator>();

        }

        private void RegisterDMADependencies()
        {
            _services.AddScoped<IDMADataAccessObject, DMADataAccessObject>();
            _services.AddHostedService<DMACacheService>();

            _services.AddScoped<IProcessor<DMAGetAllRequest, XandrDMA[]>, DMAGetAllProcessor>();
            _services.AddScoped<IValidator<DMAGetAllRequest>, DMAGetAllValidator>();

            _services.AddScoped<IProcessor<DMAGetByIdRequest, XandrDMA?>, DMAGetByIdProcessor>();
            _services.AddScoped<IValidator<DMAGetByIdRequest>, DMAGetByIdValidator>();
        }

        private void RegisterCurrencyDependencies()
        {
            _services.AddSingleton<ICurrencyDao, CachedCurrencyDao>();

            _services.AddScoped<IProcessor<CurrencyGetAllRequest, Currency[]>, CurrencyGetAllProcessor>();
            _services.AddScoped<IValidator<CurrencyGetAllRequest>, CurrencyGetAllValidator>();

            _services.AddScoped<IProcessor<CurrencyExchangeRateGetRequest, decimal>, CurrencyExchangeRateGetProcessor>();
            _services.AddScoped<IValidator<CurrencyExchangeRateGetRequest>, CurrencyExchangeRateGetValidator>();

            _services.AddHostedService<CurrencyCacheService>();
        }

        private void RegisterSegmentDependencies()
        {
            _services.AddSingleton<ISegmentDao, CachedSegmentDao>();
            _services.AddHostedService<SegmentCacheService>();

            _services.AddScoped<IProcessor<SegmentGetAllRequest, XandrSegment[]>, SegmentGetAllProcessor>();
            _services.AddScoped<IValidator<SegmentGetAllRequest>, SegmentGetAllValidator>();
        }

        private void RegisterTimeZoneDependencies()
        {
            _services.AddScoped<ITimeZoneDao, TimeZoneDao>();

            _services.AddScoped<IProcessor<TimeZoneGetAllRequest, XandrTimeZone[]>, TimeZoneGetAllProcessor>();
            _services.AddScoped<IValidator<TimeZoneGetAllRequest>, TimeZoneGetAllValidator>();

            _services.AddScoped<IProcessor<TimeZoneGetIanaMappingRequest, Dictionary<string, string>>, TimeZoneGetIanaMappingProcessor>();
            _services.AddScoped<IValidator<TimeZoneGetIanaMappingRequest>, TimeZoneGetIanaMappingValidator>();
        }

        private void RegisterPlacementDependencies()
        {
            _services.AddScoped<IPlacementDao, CachedPlacementDao>();

            _services.AddScoped<IProcessor<PlacementGetAllRequest, Placement[]>, PlacementGetAllProcessor>();
            _services.AddScoped<IValidator<PlacementGetAllRequest>, PlacementGetAllValidator>();
            _services.AddHostedService<PlacementCacheService>();
        }

        private void RegisterProductDependencies()
        {
            _services.AddScoped<IProductDao, CachedProductDao>();
            _services.AddScoped<IProductLinkHelper, ProductLinkHelper>();

            _services.AddScoped<IProcessor<ProductGetRequest, ProductOutput>, ProductGetProcessor>();
            _services.AddScoped<IValidator<ProductGetRequest>, ProductGetValidator>();

            _services.AddScoped<IProcessor<ProductGetAllRequest, ProductOutput[]>, ProductGetAllProcessor>();
            _services.AddScoped<IValidator<ProductGetAllRequest>, ProductGetAllValidator>();

            _services.AddScoped<IProcessor<ProductPostRequest, ProductOutput>, ProductPostProcessor>();
            _services.AddScoped<IValidator<ProductPostRequest>, ProductPostValidator>();

            _services.AddScoped<IProcessor<ProductPutRequest, ProductOutput>, ProductPutProcessor>();
            _services.AddScoped<IValidator<ProductPutRequest>, ProductPutValidator>();

            _services.AddScoped<IProcessor<ProductDeleteRequest>, ProductDeleteProcessor>();
            _services.AddScoped<IValidator<ProductDeleteRequest>, ProductDeleteValidator>();
        }

        private void RegisterCustomerDependencies()
        {
            _services.AddSingleton<ICustomerDao, CachedCustomerDao>();
            _services.AddHostedService<CustomerCacheService>();

            _services.AddScoped<IProcessor<CustomerGetAllRequest, Customer[]>, CustomerGetAllProcessor>();
            _services.AddScoped<IValidator<CustomerGetAllRequest>, CustomerGetAllValidator>();
        }

        private void RegisterPublisherDependencies()
        {
            _services.AddScoped<IPublisherDao, PublisherDao>();

            _services.AddScoped<IProcessor<PublisherGetAllRequest, Publisher[]>, PublisherGetAllProcessor>();
            _services.AddScoped<IValidator<PublisherGetAllRequest>, PublisherGetAllValidator>();
            _services.AddHostedService<PublisherCacheService>();
        }

        private void RegisterMediaPlanDependencies()
        {
            _services.AddScoped<IMediaPlanDao, CachedMediaPlanDao>();
            _services.AddScoped<IMediaPlanProcessorHelper, MediaPlanProcessorHelper>();
            _services.AddScoped<IMediaPlanCommitHelper, MediaPlanCommitHelper>();
            _services.AddSingleton<IMediaPlanOutputCache, MediaPlanOutputCache>();

            _services.AddScoped<IProcessor<MediaPlanGetRequest, MediaPlanOutput>, MediaPlanGetProcessor>();
            _services.AddScoped<IValidator<MediaPlanGetRequest>, MediaPlanGetValidator>();

            _services.AddScoped<IProcessor<MediaPlanGetAllRequest, MediaPlanOutput[]>, MediaPlanGetAllProcessor>();
            _services.AddScoped<IValidator<MediaPlanGetAllRequest>, MediaPlanGetAllValidator>();

            _services.AddScoped<IProcessor<MediaPlanPostRequest, MediaPlanOutput>, MediaPlanPostProcessor>();
            _services.AddScoped<IValidator<MediaPlanPostRequest>, MediaPlanPostValidator>();

            _services.AddScoped<ICommitProcessor<MediaPlanCommitRequest>, MediaPlanCommitQueueProcessor>();
            _services.AddScoped<IValidator<MediaPlanCommitRequest>, MediaPlanCommitValidator>();

            _services.AddScoped<IProcessor<MediaPlanCommitGetRequest, MediaPlanCommitOperation>, MediaPlanCommitGetProcessor>();
            _services.AddScoped<IValidator<MediaPlanCommitGetRequest>, MediaPlanCommitGetValidator>();

            _services.AddScoped<IProcessor<MediaPlanPutRequest>, MediaPlanPutProcessor>();
            _services.AddScoped<IValidator<MediaPlanPutRequest>, MediaPlanPutValidator>();

            _services.AddScoped<IProcessor<MediaPlanDeleteRequest>, MediaPlanDeleteProcessor>();
            _services.AddScoped<IValidator<MediaPlanDeleteRequest>, MediaPlanDeleteValidator>();

            _services.AddScoped<IProcessor<MediaPlanDownloadRequest, FileDownloadDetails>, MediaPlanDownloadProcessor>();
            _services.AddScoped<IValidator<MediaPlanDownloadRequest>, MediaPlanDownloadValidator>();

            _services.AddScoped<IProcessor<MediaPlanGetAllForUserRequest, MediaPlanOutput[]>, MediaPlanGetAllForUserProcessor>();
            _services.AddScoped<IValidator<MediaPlanGetAllForUserRequest>, MediaPlanGetAllForUserValidator>();

            _services.AddHostedService<MediaPlanCommitBackgroundService>();
            _services.AddScoped<IMediaPlanCommitProcessor, MediaPlanCommitProcessor>();
        }

        private void RegisterLineDependencies()
        {
            _services.AddScoped<ILineDao, CachedLineDao>();
            _services.AddScoped<ILineProcessorHelper, LineProcessorHelper>();

            _services.AddScoped<IProcessor<LineGetRequest, LineOutput>, LineGetProcessor>();
            _services.AddScoped<IValidator<LineGetRequest>, LineGetValidator>();

            _services.AddScoped<IProcessor<LineGetAllRequest, LineOutput[]>, LineGetAllProcessor>();
            _services.AddScoped<IValidator<LineGetAllRequest>, LineGetAllValidator>();

            _services.AddScoped<IProcessor<LineGetApprovalsRequest, PublisherApprovalOutput>, LineGetApprovalsProcessor>();
            _services.AddScoped<IValidator<LineGetApprovalsRequest>, LineGetApprovalsValidator>();

            _services.AddScoped<IProcessor<LinePostRequest, LineOutput>, LinePostProcessor>();
            _services.AddScoped<IValidator<LinePostRequest>, LinePostValidator>();

            _services.AddScoped<IProcessor<LinePostPublisherApprovalRequest, PublisherApprovalOutput>, LinePostApprovalProcessor>();
            _services.AddScoped<IValidator<LinePostPublisherApprovalRequest>, LinePostApprovalValidator>();

            _services.AddScoped<IProcessor<LinePutRequest, LineOutput>, LinePutProcessor>();
            _services.AddScoped<IValidator<LinePutRequest>, LinePutValidator>();

            _services.AddScoped<IProcessor<LineDeleteRequest>, LineDeleteProcessor>();
            _services.AddScoped<IValidator<LineDeleteRequest>, LineDeleteValidator>();
        }

        private void RegisterCalendarDependencies()
        {
            _services.AddScoped<ICalendarDao, CalendarDao>();

            _services.AddScoped<IProcessor<CalendarGetAllRequest, CalendarSlot[]>, CalendarGetAllProcessor>();
            _services.AddScoped<IValidator<CalendarGetAllRequest>, CalendarGetAllValidator>();

            _services.AddScoped<IProcessor<CalendarGetByProductIdRequest, CalendarSlot[]>, CalendarGetByProductIdProcessor>();
            _services.AddScoped<IValidator<CalendarGetByProductIdRequest>, CalendarGetByProductIdValidator>();
        }


        private void RegisterPublisherApprovalDependencies()
        {
            _services.AddScoped<IPublisherApprovalDao, CachedPublisherApprovalDao>();
            _services.AddScoped<IPublisherApprovalOutputHelper, PublisherApprovalOutputHelper>();

            _services.AddScoped<IProcessor<PublisherApprovalGetAllRequest, PublisherApprovalOutput[]>, PublisherApprovalGetAllProcessor>();
            _services.AddScoped<IValidator<PublisherApprovalGetAllRequest>, PublisherApprovalGetAllValidator>();

            _services.AddScoped<IProcessor<PublisherApprovalPatchRequest, PublisherApproval>, PublisherApprovalPatchProcessor>();
            _services.AddScoped<IValidator<PublisherApprovalPatchRequest>, PublisherApprovalPatchValidator>();
        }

        private void RegisterScoreCardDependencies()
        {
            _services.AddScoped<IInventoryDao, InventoryDao>();
            _services.AddScoped<IForecastDao, ForecastDao>();
            _services.AddScoped<IForecastHelper, ForecastHelper>();

            _services.AddScoped<IProcessor<ScoreCardGetRequest, ScoreCardOutput>, ScoreCardGetProcessor>();
            _services.AddScoped<IValidator<ScoreCardGetRequest>, ScoreCardGetValidator>();
        }

        private void RegisterXandrApiDependencies()
        {
            _services.AddHttpClient();
            _services.AddSingleton<IHttpApiClient, HttpApiClient>();
            _services.AddSingleton<IXandrApiAuth, XandrApiAuth>();
            _services.AddSingleton(typeof(IXandrApiHelper<>), typeof(XandrApiHelper<>));
        }

        private void RegisterClientCenterDependencies()
        {
            // TODO: get client center endpoint from configuration
            var endpoint = _configuration.GetRequiredSection("ClientCenter:Endpoint").Value;
            var ccSecurityServiceChannelFactory = ClientCenterWcfHelper.GetClientChannelFactory<Microsoft.Advertiser.ClientCenter.Security.Proxy.IUserSecurity>("ClientCenter", endpoint + "/security", null, null);
            var ccMTServiceChannelFactory = ClientCenterWcfHelper.GetClientChannelFactory<Microsoft.Advertiser.ClientCenter.MT.Proxy.IClientCenterMiddleTier>("CcmtHttpsImpersonation", 
                endpoint + "/pmt", 
                null,
                _configuration.GetRequiredSection("AzureKeyVault:KeyVaultName").Value,
                null,
                HttpClientCredentialType.Certificate,
                _configuration.GetValue<string?>("ClientCenter:CcmtHttpsImpersonationTokenCertificateName"), 
                _configuration.GetValue<string?>("ClientCenter:ClientCertificateThumbprint"));
            _services.AddSingleton<IClientCenterClient>(c => new ClientCenterClient(ccSecurityServiceChannelFactory, ccMTServiceChannelFactory));
        }

        private void RegisterDaoDependencies()
        {
            _services.AddScoped<IProcDaoExecutor, ProcDaoExecutor>();
        }

        private void RegisterEmailPublisherDependencies()
        {
            _services.AddScoped<IEmailPublisher, EmailPublisher>();
        }
        private void RegisterBlobDependencies()
        {
            _services.AddScoped<IBlobHelper, BlobHelper>();
        }

        private void RegisterDRSDependencies()
        {
            _services.AddSingleton<IDRSAuthTokenProvider, DRSAuthTokenProvider>();
            _services.AddSingleton<IDRSClient, DRSClient>();
        }

        private void RegisterMonitoringDependencies()
        {
            _services.AddHostedService<XandrSyncMonitoringService>();
            _services.AddScoped<IXandrSyncMonitoringProcessor, XandrSyncMonitoringProcessor>();
            _services.AddScoped<IMonitoringDataAccessObject, MonitoringDataAccessObject>();
        }

        private void RegisterOfflineOperationDependencies()
        {
            _services.AddHostedService<OfflineOperationBackgroundService>();
            _services.AddScoped<IOfflineOperationsProcessor, OfflineOperationsProcessor>();
            _services.AddScoped<IOfflineOperationDao, OfflineOperationDao>();
            _services.AddSingleton<OfflineOperationsProcessorFactory>();

            #region Offline Job Configuration
            _services.AddScoped<IProcessor<ExportSubmissionRequest>, ExportSubmissionProcessor>();
            _services.AddScoped<IValidator<ExportSubmissionRequest>, ExportSubmissionValidator>();

            _services.AddScoped<IProcessor<ExportGetStatusRequest, ExportImportOperation>, ExportGetStatusProcessor>();
            _services.AddScoped<IValidator<ExportGetStatusRequest>, ExportGetStatusValidator>();

            _services.AddScoped<IProcessor<ImportSubmissionRequest, ExportImportOperation>, ImportSubmissionProcessor>();
            _services.AddScoped<IValidator<ImportSubmissionRequest>, ImportSubmissionValidator>();

            _services.AddScoped<IProcessor<ImportGetStatusRequest, ExportImportOperation>, ImportGetStatusProcessor>();
            _services.AddScoped<IValidator<ImportGetStatusRequest>, ImportGetStatusValidator>();

            _services.AddScoped<IProcessor<GetBlobFileNameRequest, string>, GetBlobFileNameProcessor>();
            _services.AddScoped<IValidator<GetBlobFileNameRequest>, GetBlobFileNameValidator>();

            _services.AddScoped<ImportHelper>();
            #endregion
        }
        private void RegisterKeyValueDependencies()
        {
            _services.AddScoped<IKeyValueDataAccessObject, KeyValueDataAccessObject>();
            _services.AddHostedService<KeyValueCacheService>();

            _services.AddScoped<IProcessor<KeyValueGetAllKeysRequest, XandrTargetingKey[]>, KeyValueGetAllKeysProcessor>();
            _services.AddScoped<IProcessor<KeyValueGetAllValuesRequest, XandrTargetingValue[]>, KeyValueGetAllValuesProcessor>();

            _services.AddScoped<IValidator<KeyValueGetAllKeysRequest>, KeyValueGetAllKeysValidator>();
            _services.AddScoped<IValidator<KeyValueGetAllValuesRequest>, KeyValueGetAllValuesValidator>();
        }

        private void RegisterDynamicConfigDependencies()
        {
            _services.AddSingleton<DynamicConfigWrapper>();
        }
    }
}
