﻿using FastDataPipeline.CosmosPub;
using FastDataPipeline.DBSub;
using FastDataPipeline.Contract.BatchConversion;
using FastDataPipeline.DWC;
using FastDataPipeline.EventQueue;
using FastDataPipeline.DB;
using FastDataPipeline.ClickhouseSub;
using FastDataPipeline.Contract.BatchBI;

namespace FastDataPipeline.JobFactory
{
    partial class JobFactory
    {
        // Upstream: Non-monetization Ads Data <<EMAIL>>
        // Downstream: BingAds BI Datamart DRI <<EMAIL>?
        private async Task CreateBatchConversionsPipeline(DBSubThrottleManager throttleManager, OperationCounter cosmosPubCounter, OperationCounter biSubCounter, CancellationToken cancellationToken)
        {
            // EventQueue
            var batchConversionsPubEventQueue = new PubEventQueueV1("BatchConvPubEvent", 4, storageProvider.GetEventTable(), storageProvider.GetEventContainer());
            eventQueues.Add(batchConversionsPubEventQueue);

            // Container
            containers.Add(CreateBatchConversionsPublisher(batchConversionsPubEventQueue, cosmosPubCounter));
            containers.Add(CreateBatchConversionsAccount2Subscriber(batchConversionsPubEventQueue, biSubCounter, throttleManager));
            containers.Add(CreateBatchConversionsAdGroup2Subscriber(batchConversionsPubEventQueue, biSubCounter, throttleManager));

            containers.Add(await CreateBatchConversionsClickhouseAccountSubscriber(batchConversionsPubEventQueue, cosmosPubCounter, cancellationToken));
            containers.Add(await CreateBatchConversionsClickhouseAdGroupSubscriber(batchConversionsPubEventQueue, cosmosPubCounter, cancellationToken));
        }

        private JobContainer CreateBatchConversionsPublisher(PubEventQueueV1 eventQueue, OperationCounter operationCounter)
        {
            int bufferSize = -1; //-1 means no boundedCapacity, if the buffer size is too small, we pay big spin cost when add into BlockingCollection. when BlockingCollection is empty, we pay big spin cost again when dequeue, need to move to a no spin framework.

            // slit count and reader count for different size of articles
            var smallOptions = new CosmosPubOperationOptions { SplitCount = 1, ReaderCount = 4, BufferCapacity = bufferSize, ReturnNewPosition = false, LineReaderType = LineReaderType.Csv, BlobBlockSize = 512 * 1024, CompressionBlockSize = 16 * 1024, BlobTimeoutInSeconds = 10 };
            var smallOptionsHotel = new CosmosPubOperationOptions { SplitCount = 1, ReaderCount = 16, BufferCapacity = bufferSize, ReturnNewPosition = false, LineReaderType = LineReaderType.Tsv, BlobBlockSize = 512 * 1024, CompressionBlockSize = 16 * 1024, BlobTimeoutInSeconds = 10 };
            var smallTsvOptions = new CosmosPubOperationOptions { SplitCount = 1, ReaderCount = 4, BufferCapacity = bufferSize, ReturnNewPosition = false, LineReaderType = LineReaderType.Tsv, BlobBlockSize = 512 * 1024, CompressionBlockSize = 16 * 1024, BlobTimeoutInSeconds = 10 };
            var smallUnicodeCsvOptions = new CosmosPubOperationOptions { SplitCount = 1, ReaderCount = 4, BufferCapacity = bufferSize, ReturnNewPosition = false, LineReaderType = LineReaderType.UnicodeCsv, BlobBlockSize = 512 * 1024, CompressionBlockSize = 16 * 1024, BlobTimeoutInSeconds = 10 };
            var smallUnicodeTsvOptions = new CosmosPubOperationOptions { SplitCount = 1, ReaderCount = 4, BufferCapacity = bufferSize, ReturnNewPosition = false, LineReaderType = LineReaderType.UnicodeTsv, BlobBlockSize = 512 * 1024, CompressionBlockSize = 16 * 1024, BlobTimeoutInSeconds = 10 };
            var smallQuotedCsvOptions = new CosmosPubOperationOptions { SplitCount = 1, ReaderCount = 4, BufferCapacity = bufferSize, ReturnNewPosition = false, LineReaderType = LineReaderType.QuotedCsv, BlobBlockSize = 512 * 1024, CompressionBlockSize = 16 * 1024, BlobTimeoutInSeconds = 10 };

            var operations = new List<CosmosPubOperation>
            {
                // AdGroup
                new CosmosPubMultipleOutputOperation<NonPartitionedAdUsage>(Article.Batch_AdUsage_Conv, cosmosArticleName: "AdUsage", dsvTableName: "NonPartitionedAdUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_AdUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_AdUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedAdFeedUsage>(Article.Batch_AdFeedUsage_Conv, cosmosArticleName: "AdFeedUsage", dsvTableName: "NonPartitionedAdFeedUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_FeedItemUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.FeedItemId), 2)
                    .AttachOutput(Article.Hourly_FeedItemUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.FeedItemId), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedGenderAgeUsage>(Article.Batch_GenderAgeUsage_Conv, cosmosArticleName: "GenderAgeUsage", dsvTableName: "NonPartitionedGenderAgeUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_GenderAgeUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_GenderAgeUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedAdAdExtensionClickTypeUsage>(Article.Batch_HourlyAdAdExtensionClickTypeUsage_Conv, cosmosArticleName: "AdAdExtensionClickTypeUsage", dsvTableName: "NonPartitionedAdAdExtensionClickTypeUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_AdAdExtensionClickTypeUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_AdAdExtensionClickTypeUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedElementAdUsage>(Article.Batch_HourlyElementAdUsage_Conv, cosmosArticleName: "ElementAdUsage", dsvTableName: "NonPartitionedElementAdUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_ElementAdUsageByOrder_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_ElementAdUsageByOrder_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedElementOrderItemUsage>(Article.Batch_HourlyElementOrderItemUsage_Conv, cosmosArticleName: "ElementOrderItemUsage", dsvTableName: "NonPartitionedElementOrderItemUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_ElementOrderItemUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_ElementOrderItemUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedOrderItemAdExtensionClickTypeUsage>(Article.Batch_HourlyOrderItemAdExtensionClickTypeUsage_Conv, cosmosArticleName: "OrderItemAdExtensionClickTypeUsage", dsvTableName: "NonPartitionedOrderItemAdExtensionClickTypeUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_OrderItemAdExtensionClickTypeUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_OrderItemAdExtensionClickTypeUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedLocationHourUsage>(Article.Batch_LocationHourUsage_Conv, cosmosArticleName: "LocationHourUsage", dsvTableName: "NonPartitionedLocationHourUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_LocationHourUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_LocationHourUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                // SubOrderItemUsage_NoIS is a sub fact for OrderItemUsage with limited dimension columns, to improve performance for Campaign UI calls
                new CosmosPubMultipleOutputOperation<NonPartitionedOrderItemUsage>(Article.Batch_OrderItemUsage_Conv, cosmosArticleName: "OrderItemUsage", dsvTableName: "NonPartitionedOrderItemUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_OrderItemUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_SubOrderItemUsage_NoIS_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_OrderItemUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_SubOrderItemUsage_NoIS_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedOrderTargetUsage>(Article.Batch_OrderTargetUsage_Conv, cosmosArticleName: "OrderTargetUsage", dsvTableName: "NonPartitionedOrderTargetUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_OrderTargetUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId, t.OrderId), 2)
                    .AttachOutput(Article.Hourly_OrderTargetUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId, t.OrderId), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedOrderUsage>(Article.Batch_OrderUsage_Conv, cosmosArticleName: "OrderUsage", dsvTableName: "NonPartitionedOrderUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_OrderUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_OrderUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedProfessionalDemographicsUsage>(Article.Batch_ProfessionalDemographicsUsage_Conv, cosmosArticleName: "ProfessionalDemographicsUsage", dsvTableName: "NonPartitionedProfessionalDemographicsUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_ProfessionalDemographicsUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_ProfessionalDemographicsUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedPublisherPlacementUsage>(Article.Batch_PublisherPlacementUsage_Conv, cosmosArticleName: "PublisherPlacementUsageV2", dsvTableName: "NonPartitionedPublisherPlacementUsageV2", options: smallUnicodeTsvOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_PublisherPlacementUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_PublisherPlacementUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedContentPerformanceUsage>(Article.Batch_ContentPerformanceUsage_Conv, cosmosArticleName: "ContentPerformanceUsage", dsvTableName: "NonPartitionedContentPerformanceUsage", options: smallUnicodeTsvOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_ContentPerformanceUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_ContentPerformanceUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedRadiusTargetedLocationHourUsage>(Article.Batch_RadiusTargetedLocationHourUsage_Conv, cosmosArticleName: "RadiusTargetedLocationHourUsage", dsvTableName: "NonPartitionedRadiusTargetedLocationHourUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_RadiusTargetedLocationHourUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_RadiusTargetedLocationHourUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedAutoTargetUsage>(Article.Batch_AutoTargetUsage_Conv, cosmosArticleName: "AutoTargetUsage", dsvTableName: "NonPartitionedAutoTargetUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_AutoTargetUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_AutoTargetUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedAdLandingPageUrlUsage>(Article.Batch_AdLandingPageUrlUsage_Conv, cosmosArticleName: "AdLandingPageUrlUsage", dsvTableName: "NonPartitionedAdLandingPageUrlUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_AdLandingPageUrlUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_AdLandingPageUrlUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedAutomatedExtensionUsage>(Article.Batch_AutomatedExtensionUsage_Conv, cosmosArticleName: "AutomatedExtensionUsage", dsvTableName: "NonPartitionedAutomatedExtensionUsage", options: smallTsvOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_AutomatedExtensionUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_AutomatedExtensionUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedSearchQueryUsage>(Article.SearchQueryReport_Conv, cosmosArticleName: "SearchQueryUsage", dsvTableName: "NonPartitionedSearchQueryUsage", options: smallUnicodeTsvOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_QueryUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_QueryUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedFeedItemAdExtensionUsage>(Article.Batch_FeedItemAdExtensionUsage_Conv, cosmosArticleName: "FeedItemAdExtensionUsage", dsvTableName: "NonPartitionedFeedItemAdExtensionUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_FeedItemAdExtensionUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.AdExtensionItemFeedItemId), 2)
                    .AttachOutput(Article.Hourly_FeedItemAdExtensionUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.AdExtensionItemFeedItemId), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedAssetUsage>(Article.Batch_AdAssetUsage_Conv, cosmosArticleName: "AdAssetUsage", dsvTableName: "NonPartitionedAdAssetUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_AssetUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 2)
                    .AttachOutput(Article.Hourly_AssetUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 2)
                    .AttachOutput(Article.Hourly_AssetSnapShotUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, (long)t.AssetId), 2)
                    .AttachOutput(Article.Hourly_AssetSnapShotUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, (long)t.AssetId), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedAssetCombinationUsage>(Article.Batch_AdAssetCombinationUsage_Conv, cosmosArticleName: "AdAssetCombinationUsage", dsvTableName: "NonPartitionedAdAssetCombinationUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_AssetCombinationUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 2)
                    .AttachOutput(Article.Hourly_AssetCombinationUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 2),

                 // SubCampaignUsage_NoIS is a sub fact for CampaignUsage with limited dimension columns, to improve performance for Campaign UI calls
                new CosmosPubMultipleOutputOperation<NonPartitionedCampaignUsage>(Article.Batch_CampaignUsage_Conv, cosmosArticleName: "CampaignUsage", dsvTableName: "NonPartitionedCampaignUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_CampaignUsage_Conv, t => GetBIAccountPartitionIds(t.CustomerId.Value, t.AccountId.Value), 2)
                    .AttachOutput(Article.Hourly_SubCampaignUsage_NoIS_Conv, t => GetBIAccountPartitionIds(t.CustomerId.Value, t.AccountId.Value), 2)
                    .AttachOutput(Article.Hourly_CampaignUsage_Conv_Clickhouse, t => GetBIClickhouseAccountPartitionIds(t.CustomerId.Value, t.AccountId.Value), 2)
                    .AttachOutput(Article.Hourly_SubCampaignUsage_NoIS_Conv_Clickhouse, t => GetBIClickhouseAccountPartitionIds(t.CustomerId.Value, t.AccountId.Value), 2),

                // SubAccountUsage_NoIS is a sub fact for AccountUsage with limited dimension columns, to improve performance for Campaign UI calls
                new CosmosPubMultipleOutputOperation<NonPartitionedAccountUsage>(Article.Batch_AccountUsage_Conv, cosmosArticleName: "AccountUsage", dsvTableName: "NonPartitionedAccountUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_AccountUsage_Conv, t => GetBIAccountPartitionIds(t.CustomerId.Value, t.AccountId.Value), 2)
                    .AttachOutput(Article.Hourly_SubAccountUsage_NoIS_Conv, t => GetBIAccountPartitionIds(t.CustomerId.Value, t.AccountId.Value), 2)
                    .AttachOutput(Article.Hourly_AccountUsage_Conv_Clickhouse, t => GetBIClickhouseAccountPartitionIds(t.CustomerId.Value, t.AccountId.Value), 2)
                    .AttachOutput(Article.Hourly_SubAccountUsage_NoIS_Conv_Clickhouse, t => GetBIClickhouseAccountPartitionIds(t.CustomerId.Value, t.AccountId.Value), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedOrderItemDDAUsage>(Article.Batch_OrderItemDDAUsage_Conv, cosmosArticleName: "OrderItemDDAUsage", dsvTableName: "NonPartitionedOrderItemDDAUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_OrderItemDDAUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_OrderItemDDAUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                // BSC
                new CosmosPubMultipleOutputOperation<NonPartitionedBSCProductOfferUsage>(Article.Batch_BSCProductOfferUsage_Conv, cosmosArticleName: "BSCProductOfferUsage", dsvTableName: "NonPartitionedBSCProductOfferUsage", options: smallQuotedCsvOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_BSC2BIProductOfferUsage_BSC2BI_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_BSC2BIProductOfferCampaignUsage_BSC2BI_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.GlobalOfferId), 2)
                    .AttachOutput(Article.Hourly_ProductOfferUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_ProductOfferCampaignUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.GlobalOfferId), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedBSCSearchQueryUsage>(Article.BSCSearchQueryReport_Conv, cosmosArticleName: "BSCSearchQueryUsage", dsvTableName: "NonPartitionedBSCSearchQueryUsage", options: smallUnicodeTsvOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_BSC2BIQueryUsage_BSC2BI_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_BSCQueryUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                // Vertical 
                new CosmosPubMultipleOutputOperation<NonPartitionedHotelVerticalUsage>(Article.Batch_HotelVerticalUsage_Conv, cosmosArticleName: "HotelVerticalUsage", dsvTableName: "NonPartitionedHotelVerticalUsage", options: smallOptionsHotel, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_HotelVerticalUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, Convert.ToInt64(t.VerticalItemId)), 2)
                    .AttachOutput(Article.Hourly_HotelVerticalUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, Convert.ToInt64(t.VerticalItemId)), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedHotelUsage>(Article.Batch_HotelUsage_Conv, cosmosArticleName: "HotelCampaignUsage", dsvTableName: "NonPartitionedHotelCampaignUsage", options: smallOptionsHotel, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_HotelUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 2)
                    .AttachOutput(Article.Hourly_HotelUsageByHotel_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.CampaignHotelId), 2)
                    .AttachOutput(Article.Hourly_HotelUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 2)
                    .AttachOutput(Article.Hourly_HotelUsageByHotel_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.CampaignHotelId), 2),

                new CosmosPubMultipleOutputOperation<NonPartitionedAssetGroupUsage>(Article.Batch_AssetGroupUsage_Conv, cosmosArticleName: "AssetGroupUsage", dsvTableName: "NonPartitionedAssetGroupUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_AssetGroupUsage_Conv, t => GetBIAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.AssetGroupId.Value), 2)
                    .AttachOutput(Article.Hourly_AssetGroupUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId.Value, t.AccountId.Value, t.AssetGroupId.Value), 2)
            };

            if (Config.IsProdOrTwp)
            {
                operations.Add(new CosmosPubMultipleOutputOperation<NonPartitionedCallDetailsUsage>(Article.HourlyCallDetailsUsage_Conv, cosmosArticleName: "CallDetailsUsage", dsvTableName: "NonPartitionedCallDetailsUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Hourly_CallDetailsUsage_Conv, t => GetBIAdGroupPartitionIds(MetadataFacade.GetAdvertiserCustomerId(t.AccountId.Value), t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Hourly_CallDetailsUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(MetadataFacade.GetAdvertiserCustomerId(t.AccountId.Value), t.AccountId.Value, t.OrderId.Value), 2));
            }

            IDwcEventMonitor dwcMonitor;

            Func<string, string, DateTime, int, string> dataFileNameFormat;

            if (Config.IsProdOrTwp)
            {
                dataFileNameFormat = (currentCosmos, cosmosArticleName, eventTime, partitionId)
                    =>
                $"https://be.{currentCosmos}.osdinfra.net/cosmos/sharedData.Ads.Prod/shares/adCenter.BICore.prod2/prod/pipelines/AdvBI_OnStripe_BatchOnly/Conversions/{eventTime:yyyy}/{eventTime:MM}/{eventTime:dd}/{eventTime:yyyyMMdd}_{eventTime:yyyyMMddHH}17_NonPartitioned{cosmosArticleName}.csv";
            }
            else
            {
                dataFileNameFormat = (currentCosmos, cosmosArticleName, eventTime, partitionId)
                    =>
                $"https://{currentCosmos}.osdinfra.net/cosmos/sharedData.Ads.Dev/local/twp/pipelines/AdvBI_OnStripe_BatchOnly/Conversions/{eventTime:yyyy}/{eventTime:MM}/{eventTime:dd}/{eventTime:yyyyMMdd}_{eventTime:yyyyMMddHH}17_NonPartitioned{cosmosArticleName}.csv";
            }

            Func<string, DateTime, string> dsvFileNameFormat;

            if (Config.IsProdOrTwp)
            {
                dsvFileNameFormat = (currentCosmos, eventTime)
                    =>
                $"https://be.{currentCosmos}.osdinfra.net/cosmos/sharedData.Ads.Prod/shares/adCenter.BICore.prod2/prod/pipelines/AdvBI_OnStripe_BatchOnly/Conversions/{eventTime:yyyy}/{eventTime:MM}/{eventTime:dd}/{eventTime:yyyyMMdd}_{eventTime:yyyyMMddHH}17_metadataAgg__100_0.dsv";
            }
            else
            {
                dsvFileNameFormat = (currentCosmos, eventTime)
                    =>
                $"https://{currentCosmos}.osdinfra.net/cosmos/sharedData.Ads.Dev/local/twp/pipelines/AdvBI_OnStripe_BatchOnly/Conversions/{eventTime:yyyy}/{eventTime:MM}/{eventTime:dd}/{eventTime:yyyyMMdd}_{eventTime:yyyyMMddHH}17_metadataAgg__100_0.dsv";
            }

            if (string.IsNullOrEmpty(this.envManagerConnectionString))
            {
                logger.LogError("Use test DWC Object");
                dwcMonitor = new DwcEventMonitor(eventName: "AdCenter_AdvBI_Agg_OnStripe_BatchOnlyConv_17_Done",
                                                dao: null,
                                                deltaInterval: TimeSpan.FromHours(1),
                                                dsvFileNameFormat: dsvFileNameFormat,
                                                dataFilePartitionIdMin: 0,
                                                dataFilePartitionIdMax: 0,
                                                dataFileNameFormat: dataFileNameFormat,
                                                defaultCosmos: "cosmos08");
            }
            else
            {
                dwcMonitor = new DwcEventMonitor(eventName: "AdCenter_AdvBI_Agg_OnStripe_BatchOnlyConv_17_Done",
                                                dao: sharedDataDwcDao,
                                                deltaInterval: TimeSpan.FromHours(1),
                                                dsvFileNameFormat: dsvFileNameFormat,
                                                dataFilePartitionIdMin: 0,
                                                dataFilePartitionIdMax: 0,
                                                dataFileNameFormat: dataFileNameFormat,
                                                envManagerConnectionString: this.envManagerConnectionString,
                                                groupName: "ConversionV2");
            }

            var container = new SimpleJobContainer("BatchConvCosmosPubContainer");

            var outputDwcDao = sharedDataDwcDao;
            var outputDwcEventName = "SA_Batch_Conversions_Pub_Done";

            var jobOptions = new CosmosPubJobOptions
            {
                SupportRestatement = true,
                MaxRestatementWaitTimeInMinute = 60,
                RestatementCoolDownTimeInSecond = 240,
                TrackCosmoStreamPositions = false,
                ChunkedPublish = false
            };

            container.AddJob(new CosmosPubJob("BatchConvPublication", container, operations, dwcMonitor, eventQueue, jobOptions, outputDwcDao, outputDwcEventName, storageProvider));
            return container;
        }

        private JobContainer CreateBatchConversionsAccount2Subscriber(PubEventQueueV1 eventQueue, OperationCounter operationCounter, DBSubThrottleManager throttleManager)
        {
            var processes = new List<DBArticleProcess>
            {
                //Note: order by article size desc (i.e. big articles first)
                new BIDBArticleProcess<NonPartitionedCampaignUsage>(Article.Hourly_CampaignUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyCampaignUsage"),
                new BIDBArticleProcess<NonPartitionedCampaignUsage>(Article.Hourly_SubCampaignUsage_NoIS_Conv, DBJobConfig.PartitionByPartitionId ,"Delta_HourlySubCampaignUsage_NoIS"),
                new BIDBArticleProcess<NonPartitionedAccountUsage>(Article.Hourly_AccountUsage_Conv, DBJobConfig.PartitionByPartitionId ,"Delta_HourlyAccountUsage"),
                new BIDBArticleProcess<NonPartitionedAccountUsage>(Article.Hourly_SubAccountUsage_NoIS_Conv, DBJobConfig.PartitionByPartitionId ,"Delta_HourlySubAccountUsage_NoIS"),
            };

            // Note: changing the operation id is a broken change
            // if some machines still run a different binary, it cannot find this operation name
            // then it will success silently. see header comment in Operation.cs
            var operation = new BIDBSubOperation("BatchConvAccount2DBSubOperation", processes, storageProvider, operationCounter);
            var subContainer = new DBSubContainer("BatchConvAccount2SubContainer", storageProvider, operation, eventQueue, throttleManager, 0);
            return subContainer;
        }

        private JobContainer CreateBatchConversionsAdGroup2Subscriber(PubEventQueueV1 eventQueue, OperationCounter operationCounter, DBSubThrottleManager throttleManager)
        {
            var processes = new List<DBArticleProcess>
            {
                // Note: order by article size desc (i.e. big articles first)
                new BIDBArticleProcess<NonPartitionedElementAdUsage>(Article.Hourly_ElementAdUsageByOrder_Conv, DBJobConfig.PartitionByPartitionId ,"Delta_HourlyElementAdUsageByOrder"),
                new BIDBArticleProcess<NonPartitionedAdAdExtensionClickTypeUsage>(Article.Hourly_AdAdExtensionClickTypeUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyAdAdExtensionClickTypeUsage"),
                new BIDBArticleProcess<NonPartitionedAdUsage>(Article.Hourly_AdUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyAdUsage"),
                new BIDBArticleProcess<NonPartitionedElementOrderItemUsage>(Article.Hourly_ElementOrderItemUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyElementOrderItemUsage"),
                new BIDBArticleProcess<NonPartitionedGenderAgeUsage>(Article.Hourly_GenderAgeUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyGenderAgeUsage"),
                new BIDBArticleProcess<NonPartitionedLocationHourUsage>(Article.Hourly_LocationHourUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyLocationHourUsage"),
                new BIDBArticleProcess<NonPartitionedOrderItemAdExtensionClickTypeUsage>(Article.Hourly_OrderItemAdExtensionClickTypeUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyOrderItemAdExtensionClickTypeUsage"),
                new BIDBArticleProcess<NonPartitionedOrderItemUsage>(Article.Hourly_OrderItemUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyOrderItemUsage"),
                new BIDBArticleProcess<NonPartitionedOrderItemUsage>(Article.Hourly_SubOrderItemUsage_NoIS_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlySubOrderItemUsage_NoIS"),
                new BIDBArticleProcess<NonPartitionedOrderTargetUsage>(Article.Hourly_OrderTargetUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyOrderTargetUsage"),
                new BIDBArticleProcess<NonPartitionedOrderUsage>(Article.Hourly_OrderUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyOrderUsage"),
                new BIDBArticleProcess<NonPartitionedPublisherPlacementUsage>(Article.Hourly_PublisherPlacementUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyPublisherPlacementUsage"),
                new BIDBArticleProcess<NonPartitionedContentPerformanceUsage>(Article.Hourly_ContentPerformanceUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyContentPerformanceUsage"),
                new BIDBArticleProcess<NonPartitionedRadiusTargetedLocationHourUsage>(Article.Hourly_RadiusTargetedLocationHourUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyRadiusTargetedLocationHourUsage"),
                new BIDBArticleProcess<NonPartitionedAutoTargetUsage>(Article.Hourly_AutoTargetUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyAutoTargetUsage"),
                new BIDBArticleProcess<NonPartitionedAdLandingPageUrlUsage>(Article.Hourly_AdLandingPageUrlUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyAdLandingPageUrlUsage"),
                new BIDBArticleProcess<NonPartitionedProfessionalDemographicsUsage>(Article.Hourly_ProfessionalDemographicsUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyProfessionalDemographicsUsage"),
                new BIDBArticleProcess<NonPartitionedAutomatedExtensionUsage>(Article.Hourly_AutomatedExtensionUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyAutomatedExtensionUsage"),
                new BIDBArticleProcess<NonPartitionedCallDetailsUsage>(Article.Hourly_CallDetailsUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyCallDetailsUsage"),
                new BIDBArticleProcess<NonPartitionedSearchQueryUsage>(Article.Hourly_QueryUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyQueryUsage",
                (request) => new IComputedColumn<NonPartitionedSearchQueryUsage>[]{new ComputedColumn<NonPartitionedSearchQueryUsage>("QueryHash", t => GetMurmurHashCodeCaseSensitive(t.Query))}),
                new BIDBArticleProcess<NonPartitionedAssetUsage>(Article.Hourly_AssetUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyAssetUsage"),
                new BIDBArticleProcess<NonPartitionedAssetUsage>(Article.Hourly_AssetSnapShotUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyAssetSnapShotUsage"),
                new BIDBArticleProcess<NonPartitionedAssetCombinationUsage>(Article.Hourly_AssetCombinationUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyAssetCombinationUsage_Conv"),
                new BIDBArticleProcess<NonPartitionedAdFeedUsage>(Article.Hourly_FeedItemUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyFeedItemUsage"),
                new BIDBArticleProcess<NonPartitionedFeedItemAdExtensionUsage>(Article.Hourly_FeedItemAdExtensionUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyFeedItemAdExtensionUsage"),
                new BIDBArticleProcess<NonPartitionedBSCProductOfferUsage>(Article.Hourly_BSC2BIProductOfferUsage_BSC2BI_Conv, DBJobConfig.PartitionByPartitionId, "HourlyProductOfferConversionUsage_delta", isBSCData2BI: true),
                new BIDBArticleProcess<NonPartitionedBSCSearchQueryUsage>(Article.Hourly_BSC2BIQueryUsage_BSC2BI_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyBSCQueryUsage", isBSCData2BI: true),
                new BIDBArticleProcess<NonPartitionedBSCProductOfferUsage>(Article.Hourly_BSC2BIProductOfferCampaignUsage_BSC2BI_Conv, DBJobConfig.PartitionByPartitionId, "HourlyProductOfferConversionCampaignUsage_delta", isBSCData2BI: true),
                new BIDBArticleProcess<NonPartitionedHotelVerticalUsage>(Article.Hourly_HotelVerticalUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyHotelVerticalUsage_Conv"),
                new BIDBArticleProcess<NonPartitionedHotelUsage>(Article.Hourly_HotelUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyHotelUsage_Conv"),
                new BIDBArticleProcess<NonPartitionedHotelUsage>(Article.Hourly_HotelUsageByHotel_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyHotelUsageByHotel_Conv"),
                new BIDBArticleProcess<NonPartitionedOrderItemDDAUsage>(Article.Hourly_OrderItemDDAUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyOrderItemDDAUsage"),
                new BIDBArticleProcess<NonPartitionedAssetGroupUsage>(Article.Hourly_AssetGroupUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_HourlyAssetGroupUsage")
            };

            // Note: changing the operation id is a broken change
            // if some machines still run a different binary, it cannot find this operation name
            // then it will success silently. see header comment in Operation.cs
            var operation = new BIDBSubOperation("BatchConvAdGroup2DBSubOperation", processes, storageProvider, operationCounter);
            var subContainer = new DBSubContainer("BatchConvAdGroup2SubContainer", storageProvider, operation, eventQueue, throttleManager, 0);
            return subContainer;
        }

        private async Task<JobContainer> CreateBatchConversionsClickhouseAccountSubscriber(PubEventQueueV1 eventQueue, OperationCounter operationCounter, CancellationToken cancellationToken)
        {
            var processes = new List<IClickhouseSubArticleProcess>
            {
                //Note: order by article size desc (i.e. big articles first)
                new ClickhouseBISubArticleProcess<NonPartitionedCampaignUsage>(Article.Hourly_CampaignUsage_Conv_Clickhouse, "BICH", "HourlyCampaignUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedCampaignUsage>(Article.Hourly_SubCampaignUsage_NoIS_Conv_Clickhouse, "BICH" ,"HourlySubCampaignUsage_NoIS"),
                new ClickhouseBISubArticleProcess<NonPartitionedAccountUsage>(Article.Hourly_AccountUsage_Conv_Clickhouse, "BICH" ,"HourlyAccountUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedAccountUsage>(Article.Hourly_SubAccountUsage_NoIS_Conv_Clickhouse, "BICH" ,"HourlySubAccountUsage_NoIS"),
            };

            // Note: changing the operation id is a broken change
            // if some machines still run a different binary, it cannot find this operation name
            // then it will success silently. see header comment in Operation.cs
            var operation = new ClickhouseSubOperation("BatchConvClickhouseAccountDBSubOperation", processes, operationCounter, storageProvider);
            var directoryOperation = new ClickhouseShareDirectoryOperation("BatchConvClickhouseAccountShareDirectoryOperation", processes, operationCounter);

            var bidbBlobConnection = Config.IsLocal ? config("BIDBClickhouseBlobConnection")
                : await this.saKeyVaultClient.GetConnectionStringAsync(config("SAKeyVaultName"), config("BIDBClickhouseBlobConnection"), logger, cancellationToken).ConfigureAwait(false);

            var storageAccount = config("BIDBClickhouseSourceStorageAccount");
            var bidbLoaderEventQueue = new ClickhouseLoaderEventQueue(storageAccount, "BIDBClickhouseLoaderEventQueue");
            var articleTableMapping = processes.ToDictionary(t => t.Article, t => t.TableName);
            var metaFileOperation = new ClickhouseMetaFileOperation("BatchConvClickhouseAccountMetaFileOperation", bidbLoaderEventQueue, articleTableMapping, operationCounter, true);


            var fileShare = config("BIDBClickhouseFileShareBatchConv");
            var container = new ClickhouseSubContainer("BatchConvClickhouseAccountSubContainer", storageAccount, bidbBlobConnection, operation, metaFileOperation, directoryOperation,
                fileShare, GetBatchConversionsClickhouseAccountRootDir, eventQueue, new ClickhouseSubJobOptions(), storageProvider, eventIdInMetaFiles: true, diskFileRoot: config("BIDBClickhouseDiskFileRoot"));
            return container;
        }

        private async Task<JobContainer> CreateBatchConversionsClickhouseAdGroupSubscriber(PubEventQueueV1 eventQueue, OperationCounter operationCounter, CancellationToken cancellationToken)
        {
            var processes = new List<IClickhouseSubArticleProcess>
            {
                // Note: order by article size desc (i.e. big articles first)
                new ClickhouseBISubArticleProcess<NonPartitionedElementAdUsage>(Article.Hourly_ElementAdUsageByOrder_Conv_Clickhouse, "BICH", "HourlyElementAdUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedAdAdExtensionClickTypeUsage>(Article.Hourly_AdAdExtensionClickTypeUsage_Conv_Clickhouse, "BICH", "HourlyAdAdExtensionClickTypeUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedAdUsage>(Article.Hourly_AdUsage_Conv_Clickhouse, "BICH", "HourlyAdUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedElementOrderItemUsage>(Article.Hourly_ElementOrderItemUsage_Conv_Clickhouse, "BICH", "HourlyElementOrderItemUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedGenderAgeUsage>(Article.Hourly_GenderAgeUsage_Conv_Clickhouse, "BICH", "HourlyGenderAgeUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedLocationHourUsage>(Article.Hourly_LocationHourUsage_Conv_Clickhouse, "BICH", "HourlyLocationHourUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedOrderItemAdExtensionClickTypeUsage>(Article.Hourly_OrderItemAdExtensionClickTypeUsage_Conv_Clickhouse, "BICH", "HourlyOrderItemAdExtensionClickTypeUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedOrderItemUsage>(Article.Hourly_OrderItemUsage_Conv_Clickhouse, "BICH", "HourlyOrderItemUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedOrderItemUsage>(Article.Hourly_SubOrderItemUsage_NoIS_Conv_Clickhouse, "BICH", "HourlySubOrderItemUsage_NoIS"),
                new ClickhouseBISubArticleProcess<NonPartitionedOrderTargetUsage>(Article.Hourly_OrderTargetUsage_Conv_Clickhouse, "BICH", "HourlyOrderTargetUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedOrderUsage>(Article.Hourly_OrderUsage_Conv_Clickhouse, "BICH", "HourlyOrderUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedPublisherPlacementUsage>(Article.Hourly_PublisherPlacementUsage_Conv_Clickhouse, "BICH", "HourlyPublisherPlacementUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedContentPerformanceUsage>(Article.Hourly_ContentPerformanceUsage_Conv_Clickhouse, "BICH", "HourlyContentPerformanceUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedRadiusTargetedLocationHourUsage>(Article.Hourly_RadiusTargetedLocationHourUsage_Conv_Clickhouse, "BICH", "HourlyRadiusTargetedLocationHourUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedAutoTargetUsage>(Article.Hourly_AutoTargetUsage_Conv_Clickhouse, "BICH", "HourlyAutoTargetUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedAdLandingPageUrlUsage>(Article.Hourly_AdLandingPageUrlUsage_Conv_Clickhouse, "BICH", "HourlyAdLandingPageUrlUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedAssetUsage>(Article.Hourly_AssetUsage_Conv_Clickhouse, "BICH", "HourlyAssetUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedAssetUsage>(Article.Hourly_AssetSnapShotUsage_Conv_Clickhouse, "BICH", "HourlyAssetSnapShotUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedProfessionalDemographicsUsage>(Article.Hourly_ProfessionalDemographicsUsage_Conv_Clickhouse, "BICH", "HourlyProfessionalDemographicsUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedAdFeedUsage>(Article.Hourly_FeedItemUsage_Conv_Clickhouse, "BICH", "HourlyAdFeedUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedFeedItemAdExtensionUsage>(Article.Hourly_FeedItemAdExtensionUsage_Conv_Clickhouse, "BICH", "HourlyFeedItemAdExtensionUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedAutomatedExtensionUsage>(Article.Hourly_AutomatedExtensionUsage_Conv_Clickhouse, "BICH", "HourlyAutomatedExtensionUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedOrderItemDDAUsage>(Article.Hourly_OrderItemDDAUsage_Conv_Clickhouse, "BICH", "HourlyOrderItemDDAUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedCallDetailsUsage>(Article.Hourly_CallDetailsUsage_Conv_Clickhouse, "BICH", "HourlyCallDetailsUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedSearchQueryUsage>(Article.Hourly_QueryUsage_Conv_Clickhouse, "BICH", "HourlyQueryUsage",
                (request) => new IComputedColumn<NonPartitionedSearchQueryUsage>[]{new ComputedColumn<NonPartitionedSearchQueryUsage>("QueryHash", t => GetMurmurHashCodeCaseSensitive(t.Query))}),
                new ClickhouseBISubArticleProcess<NonPartitionedAssetCombinationUsage>(Article.Hourly_AssetCombinationUsage_Conv_Clickhouse, "BICH", "HourlyAssetCombinationUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedAssetGroupUsage>(Article.Hourly_AssetGroupUsage_Conv_Clickhouse, "BICH", "HourlyAssetGroupUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedHotelVerticalUsage>(Article.Hourly_HotelVerticalUsage_Conv_Clickhouse, "BICHConv", "HourlyHotelVerticalUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedHotelUsage>(Article.Hourly_HotelUsage_Conv_Clickhouse, "BICHConv", "HourlyHotelUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedHotelUsage>(Article.Hourly_HotelUsageByHotel_Conv_Clickhouse, "BICHConv", "HourlyHotelUsageByHotel"),
                new ClickhouseBISubArticleProcess<NonPartitionedBSCProductOfferUsage>(Article.Hourly_ProductOfferUsage_Conv_Clickhouse, "BICHConv", "HourlyProductOfferUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedBSCProductOfferUsage>(Article.Hourly_ProductOfferCampaignUsage_Conv_Clickhouse, "BICHConv", "HourlyProductOfferCampaignUsage"),
                new ClickhouseBISubArticleProcess<NonPartitionedBSCSearchQueryUsage>(Article.Hourly_BSCQueryUsage_Conv_Clickhouse, "BICHConv", "HourlyBSCQueryUsage")
            };

            // Note: changing the operation id is a broken change
            // if some machines still run a different binary, it cannot find this operation name
            // then it will success silently. see header comment in Operation.cs
            var operation = new ClickhouseSubOperation("BatchConvClickhouseAdGroupDBSubOperation", processes, operationCounter, storageProvider);
            var directoryOperation = new ClickhouseShareDirectoryOperation("BatchConvClickhouseAdGroupShareDirectoryOperation", processes, operationCounter);

            var bidbBlobConnection = Config.IsLocal ? config("BIDBClickhouseBlobConnection")
                : await this.saKeyVaultClient.GetConnectionStringAsync(config("SAKeyVaultName"), config("BIDBClickhouseBlobConnection"), logger, cancellationToken).ConfigureAwait(false);

            var storageAccount = config("BIDBClickhouseSourceStorageAccount");
            var bidbLoaderEventQueue = new ClickhouseLoaderEventQueue(storageAccount, "BIDBClickhouseLoaderEventQueue");
            var articleTableMapping = processes.ToDictionary(t => t.Article, t => t.TableName);
            var metaFileOperation = new ClickhouseMetaFileOperation("BatchConvClickhouseAdGroupMetaFileOperation", bidbLoaderEventQueue, articleTableMapping, operationCounter, true);


            var fileShare = config("BIDBClickhouseFileShareBatchConv");
            var container = new ClickhouseSubContainer("BatchConvClickhouseAdGroupSubContainer", storageAccount, bidbBlobConnection, operation, metaFileOperation, directoryOperation,
                fileShare, GetBatchConversionsClickhouseAdGroupRootDir, eventQueue, new ClickhouseSubJobOptions(), storageProvider, eventIdInMetaFiles: true, diskFileRoot: config("BIDBClickhouseDiskFileRoot")); 
            return container;
        }

        private string GetBatchConversionsClickhouseAccountRootDir(int partitionId, DateTime eventTime)
        {
            return "Batch/BatchConversions/Account/" + eventTime.ToString("yyyy") + "/" + eventTime.ToString("MM") + "/" + eventTime.ToString("dd") + "/" +
                eventTime.ToString("HH") + "/" + partitionId;
        }

        private string GetBatchConversionsClickhouseAdGroupRootDir(int partitionId, DateTime eventTime)
        {
            return "Batch/BatchConversions/AdGroup/" + eventTime.ToString("yyyy") + "/" + eventTime.ToString("MM") + "/" + eventTime.ToString("dd") + "/" +
                eventTime.ToString("HH") + "/" + partitionId;
        }
    }
}
