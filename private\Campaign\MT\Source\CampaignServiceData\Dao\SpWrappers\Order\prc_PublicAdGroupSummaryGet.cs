﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.DAO
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using Microsoft.Data.SqlClient;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common;
    using Microsoft.AdCenter.Shared.MT.DAO;

    public static partial class SpWrappers
    {
        public static SqlCommand CreateGetAdGroupsSummaryByCampaignIdsFromAdgroupShardCommand(AccountCallContext context, IEnumerable<long> campaignIds, DateTime? lastSyncTime)
        {
            DataTable campaignIdsTvp = DataTableType.CreateFetchByIdTypeDataTable(campaignIds);
            SqlCommand cmd = SpWrapperUtil.MakeSqlCommandWithTracking(context.Logger, "dbo.prc_PublicOrderSummaryGetInShard_V41");

            SpWrapperUtil.AddAccountCallContextParameters(cmd, context);
            cmd.Parameters.Add(TableValuedParam("@CampaignIds", campaignIdsTvp));

            if (lastSyncTime.HasValue)
            {
                cmd.Parameters.Add(DateTimeParam("@LastSyncTime", lastSyncTime.Value));
            }

            return cmd;
        }

        public static SqlCommand OrderTargetChangeSummaryGetByCampaignIds(
            AccountCallContext context,
            IEnumerable<long> campaignIds,
            DateTime? lastSyncTime,
            int modBy,
            int modValue)
        {
            SqlCommand cmd = SPHelper.MakeSqlCommandWithTracking("dbo.prc_PublicOrderTargetChangeSummaryGetByCampaignIds_V50", context.Logger.CallTrackingData, 0);
            SpWrapperUtil.AddAccountCallContextParameters(cmd, context);
            DataTable campaignIdsTvp = DataTableType.CreateFetchByIdTypeDataTable(campaignIds);
            cmd.Parameters.Add(TableValuedParam("@CampaignIds", campaignIdsTvp));
            cmd.Parameters.Add(BigIntParam("@ModBy", modBy));
            cmd.Parameters.Add(BigIntParam("@Mod", modValue));

            if (lastSyncTime.HasValue)
            {
                cmd.Parameters.Add(DateTimeParam("@LastSyncTime", lastSyncTime.Value));
            }

            return cmd;
        }

        public static SqlCommand CreateGetAdGroupsSummaryByCampaignIdsCommandForAdExtension(AccountCallContext context, IEnumerable<long> campaignIds, DateTime? lastSyncTime)
        {
            var campaignIdsTvp = DataTableType.CreateFetchByIdTypeDataTable(campaignIds);
            var cmd = SPHelper.MakeSqlCommandWithTracking("dbo.prc_PublicOrderSummaryGetInMain_V50", context.Logger.CallTrackingData, 0);

            SpWrapperUtil.AddAccountCallContextParameters(cmd, context);
            cmd.Parameters.Add(TableValuedParam("@CampaignIds", campaignIdsTvp));

            if (lastSyncTime.HasValue)
            {
                cmd.Parameters.Add(DateTimeParam("@LastSyncTime", lastSyncTime.Value));
            }

            return cmd;
        }

        public static SqlCommand CreateGetAdextensionAdGroupsSummaryByCampaignIdsCommand(AccountCallContext context, IEnumerable<long> campaignIds, DateTime? lastSyncTime)
        {
            var campaignIdsTvp = DataTableType.CreateFetchByIdTypeDataTable(campaignIds);
            var cmd = SPHelper.MakeSqlCommandWithTracking("dbo.prc_PublicAdextensionOrderSummaryGetInMain_V50", context.Logger.CallTrackingData, 0);

            SpWrapperUtil.AddAccountCallContextParameters(cmd, context);
            cmd.Parameters.Add(TableValuedParam("@CampaignIds", campaignIdsTvp));

            if (lastSyncTime.HasValue)
            {
                cmd.Parameters.Add(DateTimeParam("@LastSyncTime", lastSyncTime.Value));
            }

            return cmd;
        }

        public static SqlCommand CreateGetAdextensionOrderSummaryGetFullInBatchesFromMainCommand(AccountCallContext context, IEnumerable<long> campaignIds, long startCampaignId, long startOrderId, int batchSize)
        {
            var campaignIdsTvp = DataTableType.CreateBigIntIdTypeDataTable(campaignIds);
            var cmd = SPHelper.MakeSqlCommandWithTracking("dbo.prc_PublicAdextensionOrderSummaryGetFullInBatchesFromMain", context.Logger.CallTrackingData, 0);

            SpWrapperUtil.AddAccountCallContextParameters(cmd, context);
            cmd.Parameters.Add(TableValuedParam("@CampaignIds", campaignIdsTvp));
            cmd.Parameters.Add(SpWrappers2.BigIntParam("@StartCampaignId", startCampaignId));
            cmd.Parameters.Add(SpWrappers2.BigIntParam("@StartOrderId", startOrderId));
            cmd.Parameters.Add(SpWrappers2.IntParam("@batchSize", batchSize));
                       
            return cmd;
        }


        public static SqlCommand CreateGetAdextensionOrderSummaryGetDeltaInBatchesFromMainCommand(AccountCallContext context, IEnumerable<long> campaignIds, long startCampaignId, long startOrderId, int batchSize, DateTime? lastSyncTime)
        {
            var campaignIdsTvp = DataTableType.CreateBigIntIdTypeDataTable(campaignIds);
            var cmd = SPHelper.MakeSqlCommandWithTracking("dbo.prc_PublicAdextensionOrderSummaryGetDeltaInBatchesFromMain", context.Logger.CallTrackingData, 0);

            SpWrapperUtil.AddAccountCallContextParameters(cmd, context);
            cmd.Parameters.Add(TableValuedParam("@CampaignIds", campaignIdsTvp));
            cmd.Parameters.Add(SpWrappers2.BigIntParam("@StartCampaignId", startCampaignId));
            cmd.Parameters.Add(SpWrappers2.BigIntParam("@StartOrderId", startOrderId));
            cmd.Parameters.Add(SpWrappers2.IntParam("@batchSize", batchSize));
            cmd.Parameters.Add(DateTimeParam("@LastSyncTime", lastSyncTime.Value));
           
            return cmd;
        }
    }
}
