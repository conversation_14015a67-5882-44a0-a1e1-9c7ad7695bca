﻿using OrderManagementSystem.Common;
using OrderManagementSystem.DataAccessObjects;
using OrderManagementSystem.Entities;
using OrderManagementSystem.Interfaces;
using OrderManagementSystem.Entities.Internal;
using OrderManagementSystem.Requests;

namespace OrderManagementSystem.Processors
{
    public class MediaPlanPutProcessor : BaseProcessor<MediaPlanPutRequest>
    {
        protected readonly IMediaPlanDao Dao;
        protected readonly ILineDao LineDao;
        protected readonly IMediaPlanOutputCache MediaPlanOutputCache;

        public MediaPlanPutProcessor(IValidator<MediaPlanPutRequest> validator, IMediaPlanDao dao, ILineDao lineDao, IMediaPlanOutputCache MediaPlanOutputCache) : base(validator)
        {
            this.Dao = dao;
            this.LineDao = lineDao;
            this.MediaPlanOutputCache = MediaPlanOutputCache;
        }

        public override async Task<Result> Execute(MediaPlanPutRequest request)
        {
            var result = new Result();
            try
            {
                result = await UpdateStatus(request);
                if (result.Failed) { return result; }

                var putResult = await this.Dao.Put(request);
                result.Merge(putResult);

            }
            finally
            {
                // Clear the media plan output cache 
                MediaPlanOutputCache.Clear();
            }

            return result;
        }

        private async Task<Result> UpdateStatus(MediaPlanPutRequest request)
        {
            var result = new Result();

            var getRequest = new MediaPlanGetRequest(request.Logger, request.MediaPlan.CustomerId, request.MediaPlan.Id);
            var existingMediaPlanResult = await Dao.Get(getRequest);
            result.Merge(existingMediaPlanResult);
            if (result.Failed) { return result; }

            var existingMediaPlan = existingMediaPlanResult.Entity!;
            var newMediaPlan = request.MediaPlan;

            switch (newMediaPlan.Status)
            {
                //newMediaPlan only has 4 possible states: Pitched, VerbalCommit, CustomerApproved, and _
                case MediaPlanStatus._:
                    //Default state. "Real" state depends on existing value + changes. 
                    newMediaPlan.Status = existingMediaPlan.Status;

                    if (newMediaPlan.StartDate != existingMediaPlan.StartDate
                        || newMediaPlan.EndDate != existingMediaPlan.EndDate
                        || newMediaPlan.TargetSpend != existingMediaPlan.TargetSpend)
                    {
                        //Meaningful changes occurred, so there is a state change. This reverts back to "draft" state from anywhere
                        newMediaPlan.Status = MediaPlanStatus.Draft;
                    }
                    break;
                case MediaPlanStatus.Draft:
                case MediaPlanStatus.Pitched:
                case MediaPlanStatus.VerbalCommit:
                case MediaPlanStatus.CustomerApproved:
                    if (existingMediaPlan.Status != newMediaPlan.Status)
                    {
                        Result<Line[]>? lineResult = await LineDao.Get(new LineGetAllRequest(request.Logger, request.MediaPlan.CustomerId, request.MediaPlan.Id));
                        var lineArray = lineResult?.Entity?.Where(line => line.Type == LineType.Roadblock).ToArray();
                        if (lineArray != null && lineArray.Length > 0)
                        {
                            // Rerunning an update on the Lines to ensure they are in sync with the MediaPlan status
                            var lineUpdateResult = await LineDao.Put(new LinePutAllRequest(request.Logger, request.MediaPlan.CustomerId, request.MediaPlan.Id, lineArray, newMediaPlan.Status));
                            result.Merge(lineUpdateResult);
                        }
                    }
                    break;
                case MediaPlanStatus.Committed:
                case MediaPlanStatus.Serving:
                    //Status came from media planner and has already been validated, so we just keep it
                    break;
                default:
                    result.AddError(new Error(source: $"{request}", message: ErrorMessage.InvalidStatus, [nameof(request.MediaPlan.Status)]));
                    break;
            }

            return result;
        }
    }
}
