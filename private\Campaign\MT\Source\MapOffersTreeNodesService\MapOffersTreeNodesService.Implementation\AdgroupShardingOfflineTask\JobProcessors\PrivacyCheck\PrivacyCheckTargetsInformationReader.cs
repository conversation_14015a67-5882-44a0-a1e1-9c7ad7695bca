namespace MapOffersTreeNodesService.Implementation.AdgroupShardingOfflineTask.JobProcessors.PrivacyCheck
{
    using System;
    using System.Collections;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.Common;
    using Microsoft.Data.SqlClient;
    using System.Linq;
    using System.Threading.Tasks;
    using Entities;
    using MapOffersTreeNodesService.Implementation.AdgroupShardingOfflineTask.JobProcessors.SharedHelper;
    using MapOffersTreeNodesService.Implementation.Clients;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.DAO;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO.Bulk;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.AdCenter.Shared.MT.DAO;
    using Microsoft.Advertiser.AdInsight.MT.Interfaces.AIM.AudienceInsight;
    using Microsoft.Advertiser.AdInsight.MT.Interfaces.AIM.AudienceInsight.Entities;
    using Microsoft.Advertiser.CampaignManagement.BusinessRules.Entities;
    using Microsoft.Advertising.ServiceLocation;

    internal class PrivacyCheckTargetsInformationReader
    {
        public static async Task GetValidJobsAndPrivacyCheckRequestInfo(
            ILogShared logger,
            JobBatch batch,
            ConcurrentBag<Job> failedJobs,
            ConcurrentBag<Job> invalidJobs,
            ConcurrentBag<Job> validJobsForPrivacyCheck,
            Dictionary<long, GetAudiencePrivacyCheckV3Request> targetGroupIdToRequestMapping,
            Dictionary<long, int> targetGroupIdToTargetVersionMapping,
            Dictionary<long, long> adGroupIdToTargetGroupIdMapping,
            Func<AccountCallContext, List<long>, IEnumerable<ILightWeightCampaign>> getLightWeightCampaignsInfoByIds,
            Func<AccountCallContext, long, List<long>, IEnumerable<AdGroup>> getAdGroupsByIds)
        {
            var jobs = RemoveDuplicatedJobs(logger, batch, invalidJobs);
            var adGroupShardConnection = batch.PollEntity.ConnectionProvider;

            IAccountDeletedCheckCaller accountDeletedCheckCaller = ServiceLocator.Current.Resolve<IAccountDeletedCheckCaller>();
            var validAccountIds = new List<int>();
            try
            {
                validAccountIds = accountDeletedCheckCaller.FilterOutDeletedAccountIds(jobs.Select(job => job.AccountId).ToList(), logger).ToList();
                logger.LogInfo($"Client center returned valid account list {string.Join(",", validAccountIds ?? new List<int>())}");
            }
            catch (Exception)
            {
                LogHelper.LogFailedJobsAsWarning(logger, jobs.ToList(), "Failed to check invalid accounts.");
                failedJobs.AddRange(jobs);
                return;
            }

            //TODO: Fetch Targets per AccountId(sproc constrain), when using secondary DB, we can make this parallel.
            IEnumerable<IGrouping<int, Job>> jobsByAccountId = jobs.GroupBy(j => j.AccountId);
            foreach (var jobsPerAccount in jobsByAccountId)
            {
                int accountIdForJobs = jobsPerAccount.Key;

                //not failing job because of account valid or not for now until we figure out the root cause
                bool isAccountValid = validAccountIds.Contains(accountIdForJobs);
                if (!isAccountValid)
                {
                    LogHelper.LogFailedJobsAsWarning(logger, jobsPerAccount.ToList(), "Account is invalid.");
                    invalidJobs.AddRange(jobsPerAccount.ToList());
                    continue;
                }

                var jobsWithEmptyOrderId = jobsPerAccount.Where(job => !job.OrderId.HasValue).ToList();
                if (jobsWithEmptyOrderId.Any())
                {
                    LogHelper.LogFailedJobsAsWarning(logger, jobsWithEmptyOrderId, "Order Id should not be empty.");
                    invalidJobs.AddRange(jobsWithEmptyOrderId);
                }

                var jobsWithOrderId = jobsPerAccount.Where(job => job.OrderId.HasValue).ToList();
                if (!jobsWithOrderId.Any())
                    continue;

                var nonDeletedAdGroupIds = new List<long>();
                var adGroupIdsWithIncompleteInformationSet = new HashSet<long>();
                var mainShardConnection = ConnectionProviderCampaignMT.CreateCampaignDbConnection(logger, (CallTrackingData)logger.CallTrackingData, accountIdForJobs);

                var success = await GetPrivacyCheckRequestAndVersion(
                    logger,
                    adGroupShardConnection,
                    mainShardConnection,
                    jobsWithOrderId,
                    nonDeletedAdGroupIds,
                    adGroupIdsWithIncompleteInformationSet,
                    targetGroupIdToRequestMapping,
                    targetGroupIdToTargetVersionMapping,
                    adGroupIdToTargetGroupIdMapping,
                    getLightWeightCampaignsInfoByIds,
                    getAdGroupsByIds).ConfigureAwait(false);

                if (!success)
                {
                    LogHelper.LogFailedJobsAsWarning(logger, jobsWithOrderId, "Failed to get target information from DB");
                    failedJobs.AddRange(jobsWithOrderId);
                    continue;
                }

                // Partial fail for incomplete information such as radius business location information
                if (adGroupIdsWithIncompleteInformationSet.Count > 0)
                {
                    var jobsWithIncompleteInformation = jobsWithOrderId.Where(job => adGroupIdsWithIncompleteInformationSet.Contains(job.OrderId.Value));
                    LogHelper.LogFailedJobsAsWarning(logger, jobsWithOrderId, $"Failed to get information of {jobsWithIncompleteInformation.Count()} jobs for AccountId : {accountIdForJobs} , jobIds: ({jobsWithIncompleteInformation.Aggregate(string.Empty, (current, next) => current + $"{next.Id} ")})");
                    failedJobs.AddRange(jobsWithIncompleteInformation);
                }

                invalidJobs.AddRange(jobsWithOrderId.Where(job => !nonDeletedAdGroupIds.Contains(job.OrderId.Value)));
                validJobsForPrivacyCheck.AddRange(jobsWithOrderId.Where(job => nonDeletedAdGroupIds.Contains(job.OrderId.Value) && !adGroupIdsWithIncompleteInformationSet.Contains(job.OrderId.Value)));
            }
        }

        private static ICollection<Job> RemoveDuplicatedJobs(ILogShared logger, JobBatch batch, ConcurrentBag<Job> invalidJobs)
        {
            var validJobs = new List<Job>();
            var jobs = batch.Jobs;
            var invalidJobIds = new List<int>();
            var groupsWithDuplication = jobs.GroupBy(job => new { job.AccountId, job.CampaignId, job.OrderId }).Where(x => x.Count() > 1);
            if (groupsWithDuplication.EnumerableIsNullOrEmpty())
            {
                return jobs;
            }
            foreach (var groupWithDuplication in groupsWithDuplication)
            {
                Job validJob = groupWithDuplication.OrderByDescending(job => job.Id).FirstOrDefault();
                var invalidJobsPerGroup = groupWithDuplication.Where(job => job.Id != validJob.Id);
                if (!invalidJobsPerGroup.EnumerableIsNullOrEmpty())
                {
                    var invalidJobIdsPerGroup = invalidJobsPerGroup.Select(i => i.Id);
                    logger.LogWarning($"{nameof(OrderLevelPrivacyCheckProcessor)}: Found duplicated jobs in the batches , job ids are: {String.Join(',', invalidJobIdsPerGroup)}, mark them as invalid jobs");
                    invalidJobs.AddRange(invalidJobsPerGroup);
                    invalidJobIds.AddRange(invalidJobIdsPerGroup);
                }
            }
            if (invalidJobIds.IsNullOrEmpty())
            {
                return jobs;
            }
            foreach (var job in jobs)
            {
                if (!invalidJobIds.Contains(job.Id))
                {
                    validJobs.Add(job);
                }
            }
            return validJobs;
        }

        /// <summary>
        ///     Get privacy check request and version for jobs ( associate with single adgroup ) of a single valid account
        ///     Returns false if encounters db error and true if none encountered
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="adGroupShardConnection"></param>
        /// <param name="mainShardConnection"></param>
        /// <param name="jobsWithOrderId"></param>
        /// <param name="nonDeletedAdGroupIds"></param>
        /// <param name="targetGroupIdToRequestMapping"></param>
        /// <param name="targetGroupIdToTargetVersionMapping"></param>
        /// <param name="adGroupIdToTargetGroupIdMapping"></param>
        /// <returns></returns>
        private static async Task<bool> GetPrivacyCheckRequestAndVersion(ILogShared logger,
            IConnectionProvider adGroupShardConnection,
            IConnectionProvider mainShardConnection,
            List<Job> jobsWithOrderId,
            List<long> nonDeletedAdGroupIds,
            HashSet<long> adGroupIdsWithIncompleteInformationSet,
            Dictionary<long, GetAudiencePrivacyCheckV3Request> targetGroupIdToRequestMapping,
            Dictionary<long, int> targetGroupIdToTargetVersionMapping,
            Dictionary<long, long> adGroupIdToTargetGroupIdMapping,
            Func<AccountCallContext, List<long>, IEnumerable<ILightWeightCampaign>> getLightWeightCampaignsInfoByIds,
            Func<AccountCallContext, long, List<long>, IEnumerable<AdGroup>> getAdGroupsByIds)
        {
            var targetGroupIdSet = new HashSet<long>();
            var targetGroupIdWithIncompleteInformationSet = new HashSet<long>();
            var ageIncludeListMapping = new Dictionary<long, List<AgeEnum>>();
            var ageExcludeListMapping = new Dictionary<long, List<AgeEnum>>();
            var genderIncludeListMapping = new Dictionary<long, List<GenderEnum>>();
            var genderExcludeListMapping = new Dictionary<long, List<GenderEnum>>();
            var audienceIncludeListMapping = new Dictionary<long, List<long>>();
            var audienceExcludeListMapping = new Dictionary<long, List<long>>();
            var companyIncludeListMapping = new Dictionary<long, List<long>>();
            var companyExcludeListMapping = new Dictionary<long, List<long>>();
            var industryIncludeListMapping = new Dictionary<long, List<long>>();
            var industryExcludeListMapping = new Dictionary<long, List<long>>();
            var jobFunctionIncludeListMapping = new Dictionary<long, List<long>>();
            var jobFunctionExcludeListMapping = new Dictionary<long, List<long>>();
            var locationIncludeListMapping = new Dictionary<long, List<long>>();
            var locationExcludeListMapping = new Dictionary<long, List<long>>();
            var radiusTargetValueIdGroupIdMapping = new Dictionary<long, long>();
            var radiusTargetValueIdInstanceMapping = new Dictionary<long, RadiusTarget>();
            var radiusTargetGroupIdValueIdMapping = new Dictionary<long, List<long>>();
            var radiusTargetValueIdWithoutLatitudeLongitude = new HashSet<long>();
            var targetGroupIdToCampaignIdMapping = new Dictionary<long, long>();
            var adGroupIdToCampaignIdMapping = new Dictionary<long, long>();

            // Read from adGroup shard
            DbDataReader dbDataReader =
                await
                    GetOrderTargetsFetchDbDataReader(logger, adGroupShardConnection, jobsWithOrderId)
                        .ConfigureAwait(false);
            if (dbDataReader == null)
            {
                return false;
            }

            var sampleJob = jobsWithOrderId.FirstOrDefault();
            var context = AccountCallContextCreator.Create(
                logger,
                sampleJob.AccountId,
                sampleJob.CustomerId);
            foreach(var job in jobsWithOrderId)
            {
                adGroupIdToCampaignIdMapping.TryAdd(job.OrderId.Value, job.CampaignId);
            }

            using (dbDataReader)
            {
                var targetGroupIdToIsExclusiveBitMaskMapping = new Dictionary<long, long>();
                const int resultSetCount = 6;
                for (var currentDataSetId = 0; currentDataSetId < resultSetCount; currentDataSetId++)
                {
                    switch (currentDataSetId)
                    {
                        case 0:
                            {
                                // Data set 1 -- TargetGroup info
                                var targetGroupIdOrdinal = new DataReaderOrdinal("TargetGroupId");
                                var orderIdOrdinal = new DataReaderOrdinal("OrderId");
                                var aimTargetVersionOrdinal = new DataReaderOrdinal("AIMTargetVersion");
                                var isAimTargetOrdinal = new DataReaderOrdinal("IsAIMTarget");
                                var isExclusiveBitMaskOrdinal = new DataReaderOrdinal("IsExclusiveBitMask");

                                while (dbDataReader.Read())
                                {
                                    bool isAimTarget = dbDataReader.GetBoolean(isAimTargetOrdinal.GetOrdinal(dbDataReader));
                                    if (!isAimTarget)
                                        continue;

                                    long targetGroupId = dbDataReader.GetInt64(targetGroupIdOrdinal.GetOrdinal(dbDataReader));
                                    long adGroupId = dbDataReader.GetInt64(orderIdOrdinal.GetOrdinal(dbDataReader));
                                    int aimTargetVersion = dbDataReader.GetInt32(aimTargetVersionOrdinal.GetOrdinal(dbDataReader));
                                    long isExclusiveBitMask = dbDataReader.GetInt64(isExclusiveBitMaskOrdinal.GetOrdinal(dbDataReader));

                                    targetGroupIdSet.Add(targetGroupId);
                                    targetGroupIdToRequestMapping.TryAdd(targetGroupId, new GetAudiencePrivacyCheckV3Request());
                                    targetGroupIdToTargetVersionMapping.TryAdd(targetGroupId, aimTargetVersion);
                                    targetGroupIdToIsExclusiveBitMaskMapping.TryAdd(targetGroupId, isExclusiveBitMask);
                                    adGroupIdToTargetGroupIdMapping.TryAdd(adGroupId, targetGroupId);
                                    adGroupIdToCampaignIdMapping.TryGetValue(adGroupId, out var campaignId);
                                    targetGroupIdToCampaignIdMapping.TryAdd(targetGroupId, campaignId);
                                }
                            }
                            break;

                        case 1:
                        case 2:
                        case 3:
                            // Data set 2/3/4 -- TargetGroupDetail info
                            if (dbDataReader.NextResult())
                            {
                                var targetGroupIdOrdinal = new DataReaderOrdinal("TargetGroupId");
                                var targetTypeIdOrdinal = new DataReaderOrdinal("TargetTypeId");
                                var targetValueIdOrdinal = new DataReaderOrdinal("TargetValueId");
                                var exclusionFlagOrdinal = new DataReaderOrdinal("ExclusionFlag");
                                var attributeOrdinal = new DataReaderOrdinal("Attribute");
                                var attribute2Ordinal = new DataReaderOrdinal("Attribute2");
                                var latitudeOrdinal = new DataReaderOrdinal("Latitude");
                                var longitudeOrdinal = new DataReaderOrdinal("Longitude");

                                while (dbDataReader.Read())
                                {
                                    long targetGroupId = dbDataReader.GetInt64(targetGroupIdOrdinal.GetOrdinal(dbDataReader));
                                    byte targetTypeId = dbDataReader.GetByte(targetTypeIdOrdinal.GetOrdinal(dbDataReader));

                                    // Supporting Int64 dbo.TargetGroupDetail.TargetValueId
                                    long targetValueId;
                                    try
                                    {
                                        // Until dbo.TargetGroupDetail.TargetValueId is changed from INT to BIGINT, GetInt64 will throw InvalidCastException.
                                        targetValueId = dbDataReader.GetInt64(targetValueIdOrdinal.GetOrdinal(dbDataReader));
                                    }
                                    catch (Exception)
                                    {
                                        // Retrieve dbo.TargetGroupDetail.TargetValueId as INT.
                                        targetValueId = dbDataReader.GetInt32(targetValueIdOrdinal.GetOrdinal(dbDataReader));
                                    }

                                    byte exclusionFlag = dbDataReader.GetByte(exclusionFlagOrdinal.GetOrdinal(dbDataReader));
                                    int? attribute = dbDataReader.GetNullableInt32(attributeOrdinal.GetOrdinal(dbDataReader));
                                    int? attribute2 = dbDataReader.GetNullableInt32(attribute2Ordinal.GetOrdinal(dbDataReader));

                                    var isExclusiveBitMask = targetGroupIdToIsExclusiveBitMaskMapping[targetGroupId];
                                    var isExclusiveBitArray = new BitArray(BitConverter.GetBytes(isExclusiveBitMask));
                                    var isExclusive = isExclusiveBitArray[targetTypeId];

                                    // only when config value is enabled will we consider exclusive targets, or they will be ignored
                                    if (!isExclusive && exclusionFlag != 1)
                                    {
                                        continue;
                                    }

                                    var targetType = (TargetDao.DBTargetType)targetTypeId;
                                    switch (targetType)
                                    {
                                        case TargetDao.DBTargetType.Age:
                                            AddKeyValueToMapping(
                                                 exclusionFlag == 1 ? ageExcludeListMapping : ageIncludeListMapping,
                                                 targetGroupId, MapIntToAgeEnum((int)targetValueId));
                                            break;
                                        case TargetDao.DBTargetType.Gender:
                                            AddKeyValueToMapping(
                                                 exclusionFlag == 1 ? genderExcludeListMapping : genderIncludeListMapping,
                                                 targetGroupId, MapIntToGenderEnum((int)targetValueId));
                                            break;
                                        case TargetDao.DBTargetType.GeoLocation:
                                        case TargetDao.DBTargetType.PostalCode:
                                            AddKeyValueToMapping(
                                                 exclusionFlag == 1 ? locationExcludeListMapping : locationIncludeListMapping,
                                                 targetGroupId, targetValueId);
                                            break;
                                        case TargetDao.DBTargetType.Audience:
                                        case TargetDao.DBTargetType.InMarket:
                                        case TargetDao.DBTargetType.Custom:
                                        case TargetDao.DBTargetType.ProductAudience:
                                        case TargetDao.DBTargetType.RemarketingSimilar:
                                        case TargetDao.DBTargetType.CustomerList:
                                        case TargetDao.DBTargetType.CombinedList:
                                        case TargetDao.DBTargetType.ImpressionBasedRemarketingList:
                                            AddKeyValueToMapping(
                                                exclusionFlag == 1 ? audienceExcludeListMapping : audienceIncludeListMapping,
                                                targetGroupId, targetValueId);
                                            break;
                                        case TargetDao.DBTargetType.CompanyName:
                                            AddKeyValueToMapping(
                                                exclusionFlag == 1 ? companyExcludeListMapping : companyIncludeListMapping,
                                                targetGroupId, targetValueId);
                                            break;
                                        case TargetDao.DBTargetType.Industry:
                                            AddKeyValueToMapping(
                                                exclusionFlag == 1 ? industryExcludeListMapping : industryIncludeListMapping,
                                                targetGroupId, targetValueId);
                                            break;
                                        case TargetDao.DBTargetType.JobFunction:
                                            AddKeyValueToMapping(
                                                exclusionFlag == 1 ? jobFunctionExcludeListMapping : jobFunctionIncludeListMapping,
                                                targetGroupId, targetValueId);
                                            break;
                                        case TargetDao.DBTargetType.Location:
                                            double? latitude = dbDataReader.GetDoubleOrNull(latitudeOrdinal.GetOrdinal(dbDataReader));
                                            double? longitude = dbDataReader.GetDoubleOrNull(longitudeOrdinal.GetOrdinal(dbDataReader));
                                            
                                            if (attribute != null && !radiusTargetValueIdInstanceMapping.ContainsKey(targetValueId))
                                            {
                                                if (latitude == null || longitude == null)
                                                {
                                                    radiusTargetValueIdWithoutLatitudeLongitude.Add(targetValueId);
                                                }

                                                radiusTargetValueIdInstanceMapping[targetValueId] = new RadiusTarget()
                                                {
                                                    Radius = Convert.ToInt32(attribute),
                                                    RadiusUnit = (RadiusUnit)Convert.ToInt32(attribute2),
                                                    CenterLatitude = Convert.ToDouble(latitude),
                                                    CenterLongitude = Convert.ToDouble(longitude),
                                                };
                                                AddKeyValueToMapping(radiusTargetGroupIdValueIdMapping, targetGroupId, targetValueId);
                                                radiusTargetValueIdGroupIdMapping.TryAdd(targetValueId, targetGroupId);
                                            }
                                            break;
                                    }
                                }
                            }
                            break;

                        case 4:
                            // Data set 5 -- Non-deleted OrderIds
                            if (dbDataReader.NextResult())
                            {
                                var orderIdOrdinal = new DataReaderOrdinal("OrderId");

                                while (dbDataReader.Read())
                                {
                                    long adGroupId = dbDataReader.GetInt64(orderIdOrdinal.GetOrdinal(dbDataReader));
                                    if (!nonDeletedAdGroupIds.Contains(adGroupId))
                                    {
                                        nonDeletedAdGroupIds.Add(adGroupId);
                                    }
                                }
                            }
                            break;
                    }
                }
            }

            // Stat the campaigns that need to call first
            var validJobs = new List<Job>();
            var campaignIdByAdGroupTargetGroupId = new Dictionary<long, long>();
            var targetGroupIdToOptimizedTargetingMapping = new Dictionary<long, bool?>();

            foreach(var job in jobsWithOrderId)
            {
                long adGroupId = job.OrderId.Value;
                if (nonDeletedAdGroupIds.Contains(adGroupId))
                {
                    validJobs.Add(job);
                    if(adGroupIdToTargetGroupIdMapping.TryGetValue(adGroupId, out var targetGroupId))
                    {
                        campaignIdByAdGroupTargetGroupId.Add(targetGroupId, job.CampaignId);
                    }
                    else
                    {
                        //add dummy target group id -1 it will be considered as no targets hence targeting to all
                        adGroupIdToTargetGroupIdMapping.TryAdd(adGroupId, -1);
                    }
                    if (DynamicConfigValues.OptimizedTargetingEnabled)
                    {
                        var adgroup = getAdGroupsByIds(context, job.CampaignId, new List<long> { adGroupId })?.FirstOrDefault();
                        if (adgroup != null)
                        {
                            targetGroupIdToOptimizedTargetingMapping.TryAdd(targetGroupId, adgroup.UseOptimizedTargeting);
                        }
                        else
                        {
                            logger.LogApplicationError($"no adgroup was returned by getAdGroupsByIds, AdgroupId is {adGroupId}");
                        }
                    }
                }
            }

            var inheritedCampaignIds = new HashSet<long>();

            var adGroupTargetGroupIdInheritLocation = new HashSet<long>();

            foreach(var targetGroupId in targetGroupIdSet)
            {
                if ((!locationExcludeListMapping.ContainsKey(targetGroupId)) && (!locationIncludeListMapping.ContainsKey(targetGroupId)) && (!radiusTargetGroupIdValueIdMapping.ContainsKey(targetGroupId)))
                {
                    if (campaignIdByAdGroupTargetGroupId.ContainsKey(targetGroupId))
                    {
                        inheritedCampaignIds.Add(campaignIdByAdGroupTargetGroupId[targetGroupId]);
                        adGroupTargetGroupIdInheritLocation.Add(targetGroupId);
                    }
                }
            }

            if (inheritedCampaignIds.Any())
            {
                logger.LogInfo($"Load campaign target for campaign ids: ({string.Join("", inheritedCampaignIds)})");

                var errorCampaignIdErrorCodeMapping = new Dictionary<long, int>();

                var campaignIdTargetInfoMapping = await GetCampaignTargetInfos(logger, mainShardConnection, validJobs, inheritedCampaignIds.ToList(), errorCampaignIdErrorCodeMapping, OperationType.AimOrderLevelPrivacyCheck).ConfigureAwait(false);
                if (campaignIdTargetInfoMapping == null)
                {
                    return false;
                }

                // Fill campaign targets
                foreach (var campaignId in campaignIdTargetInfoMapping.Keys)
                {
                    var adGroupTargetGroupIdList = campaignIdByAdGroupTargetGroupId.Where(x => x.Value == campaignId).Select(x => x.Key);

                    if (!adGroupTargetGroupIdList.Any())
                    {
                        continue;
                    }

                    var locationIncludedList = new List<long>();
                    var locationExcludedList = new List<long>();

                    var radiusTargetIdList = new List<long>();

                    foreach (var targetInfo in campaignIdTargetInfoMapping[campaignId])
                    {
                        var targetTypeId = (TargetDao.DBTargetType)targetInfo.TargetTypeId;
                        switch (targetTypeId)
                        {
                            case TargetDao.DBTargetType.GeoLocation:
                            case TargetDao.DBTargetType.PostalCode:
                                if (targetInfo.ExclusionFlag == 1)
                                {
                                    locationExcludedList.Add(targetInfo.TargetValueId);
                                }
                                else
                                {
                                    locationIncludedList.Add(targetInfo.TargetValueId);
                                }
                                break;

                            case TargetDao.DBTargetType.Location:
                                if (targetInfo.Attribute != null && !radiusTargetValueIdInstanceMapping.ContainsKey(targetInfo.TargetValueId))
                                {
                                    if (targetInfo.Latitude == null || targetInfo.Longitude == null)
                                    {
                                        radiusTargetValueIdWithoutLatitudeLongitude.Add(targetInfo.TargetValueId);
                                    }

                                    var radiusTarget = new RadiusTarget()
                                    {
                                        Radius = targetInfo.Attribute.Value,
                                        RadiusUnit = (RadiusUnit)targetInfo.Attribute2.Value,
                                        CenterLatitude = Convert.ToDouble(targetInfo.Latitude),
                                        CenterLongitude = Convert.ToDouble(targetInfo.Longitude),
                                    };

                                    radiusTargetValueIdInstanceMapping[targetInfo.TargetValueId] = radiusTarget;
                                    radiusTargetIdList.Add(targetInfo.TargetValueId);
                                }
                                break;
                        }
                    }

                    // Fill adgroups influenced by campaign target inheritance
                    foreach (var targetGroupId in adGroupTargetGroupIdList)
                    {
                        if (adGroupTargetGroupIdInheritLocation.Contains(targetGroupId))
                        {
                            locationExcludeListMapping.Add(targetGroupId, locationExcludedList);
                            locationIncludeListMapping.Add(targetGroupId, locationIncludedList);
                            foreach (var radiusTargetId in radiusTargetIdList)
                            {
                                AddKeyValueToMapping(radiusTargetGroupIdValueIdMapping, targetGroupId, radiusTargetId);
                                radiusTargetValueIdGroupIdMapping.TryAdd(radiusTargetId, targetGroupId);
                            }
                        }
                    }
                }
            }

            var campaigns = getLightWeightCampaignsInfoByIds(context, inheritedCampaignIds.ToList()).ToDictionary(c => c.Id, c => c);
            // Construct request mapping
            foreach (var targetGroupId in targetGroupIdSet)
            {
                var request = new GetAudiencePrivacyCheckV3Request();
                targetGroupIdToCampaignIdMapping.TryGetValue(targetGroupId, out var campaignId);
                campaigns.TryGetValue(campaignId, out var campaign);

                targetGroupIdToRequestMapping[targetGroupId] = request;

                request.Age = new SelectionOfAgeEnum
                {
                    Includes = ExtractDataFromDict(ageIncludeListMapping, targetGroupId),
                    Excludes = ExtractDataFromDict(ageExcludeListMapping, targetGroupId),
                };

                request.Gender = new SelectionOfGenderEnum
                {
                    Includes = ExtractDataFromDict(genderIncludeListMapping, targetGroupId),
                    Excludes = ExtractDataFromDict(genderExcludeListMapping, targetGroupId),
                };

                request.Location = new SelectionOflong()
                {
                    Excludes = ExtractDataFromDict(locationExcludeListMapping, targetGroupId),
                    Includes = ExtractDataFromDict(locationIncludeListMapping, targetGroupId)
                };

                request.Audience = new SelectionOflong()
                {
                    Excludes = ExtractDataFromDict(audienceExcludeListMapping, targetGroupId),
                    Includes = ExtractDataFromDict(audienceIncludeListMapping, targetGroupId)
                };

                request.Company = new SelectionOflong()
                {
                    Excludes = ExtractDataFromDict(companyExcludeListMapping, targetGroupId),
                    Includes = ExtractDataFromDict(companyIncludeListMapping, targetGroupId)
                };

                request.Industry = new SelectionOflong()
                {
                    Excludes = ExtractDataFromDict(industryExcludeListMapping, targetGroupId),
                    Includes = ExtractDataFromDict(industryIncludeListMapping, targetGroupId)
                };

                request.JobFunction = new SelectionOflong()
                {
                    Excludes = ExtractDataFromDict(jobFunctionExcludeListMapping, targetGroupId),
                    Includes = ExtractDataFromDict(jobFunctionIncludeListMapping, targetGroupId)
                };

                if (Config.Config.EnableBiddingStrategyTypeForPrivacyCheck)
                {
                    request.CampaignBiddingStrategy = (int?)(campaign?.DisplayBiddingStrategyType);
                    request.CampaignSubType = (int?)(campaign?.CampaignSubType);
                }

                IShoppingSettings shoppingSettings = (campaign?.CampaignSettings).OrEmpty().FirstOrDefault(cs => cs is IShoppingSettings) as IShoppingSettings;
                if (shoppingSettings != null)
                {
                    request.Feed = new Feed
                    {
                        FeedId = shoppingSettings.FeedId,
                        FeedType = shoppingSettings.FeedTypeId,
                        StoreId = shoppingSettings.ProviderId,
                    };
                }

                if (targetGroupIdToOptimizedTargetingMapping.TryGetValue(targetGroupId, out bool? useOptimizedTargeting))
                {
                    request.AutoTargeting = useOptimizedTargeting;
                }
            }

            foreach (var businessLocationId in radiusTargetValueIdWithoutLatitudeLongitude)
            {
                targetGroupIdWithIncompleteInformationSet.Add(radiusTargetValueIdGroupIdMapping[businessLocationId]);
            }

            // update radius targets to each request if the target group's radius target are all valid
            foreach (var targetGroupId in radiusTargetGroupIdValueIdMapping.Keys)
            {
                if (targetGroupIdWithIncompleteInformationSet.Contains(targetGroupId))
                {
                    continue;
                }
                var radiusTargetsArray = radiusTargetGroupIdValueIdMapping[targetGroupId].Select(x => radiusTargetValueIdInstanceMapping[x]).ToArray();

                targetGroupIdToRequestMapping[targetGroupId].RadiusTargets = radiusTargetsArray;
            }

            foreach(var adGroupId in nonDeletedAdGroupIds)
            {
                if (targetGroupIdWithIncompleteInformationSet.Contains(adGroupIdToTargetGroupIdMapping[adGroupId]))
                {
                    adGroupIdsWithIncompleteInformationSet.Add(adGroupId);
                }
            }

            return true;
        }

        private static T[] ExtractDataFromDict<T>(Dictionary<long, List<T>> listMapping, long targetGroupId)
        {
            return listMapping.ContainsKey(targetGroupId) ? listMapping[targetGroupId].ToArray() : null;
        }

        private static void AddKeyValueToMapping<T>(Dictionary<long, List<T>> dict, long key, T value)
        {
            if (!dict.ContainsKey(key))
            {
                dict.Add(key, new List<T>());
            }
            dict[key].Add(value);
        }

        private static GenderEnum MapIntToGenderEnum(int targetValueId)
        {
            var genderType = TargetDao.IntToGender(targetValueId);
            switch (genderType)
            {
                case GenderType.Unknown: return GenderEnum.Unknown;
                case GenderType.Male: return GenderEnum.Male;
                case GenderType.Female: return GenderEnum.Female;
            }

            throw new AdCenterInternalErrorException(
                $"Could not convert gender TargetValueId to AdInsight GenderEnum, value {targetValueId}");
        }

        private static AgeEnum MapIntToAgeEnum(int ageValueId)
        {
            var ageBucket = TargetDao.IntToAgeBucket(ageValueId);
            switch (ageBucket)
            {
                case AgeRange.Unknown: return AgeEnum.Unknown;
                case AgeRange.ZeroToSeventeen: return AgeEnum.ZeroToTwelve;
                case AgeRange.ThirteenToSeventeen: return AgeEnum.ThirteenToSevenTeen;
                case AgeRange.EighteenToTwentyFive: return AgeEnum.EighteenToTwentyFour;
                case AgeRange.TwentyFiveToThirtyFive: return AgeEnum.TwentyFiveToThirtyFour;
                case AgeRange.ThirtyFiveToFifty: return AgeEnum.ThirtyFiveToFourtyNine;
                case AgeRange.FiftyToSixtyFive: return AgeEnum.FiftyToSixtyFour;
                case AgeRange.SixtyFiveAndAbove: return AgeEnum.SixtyFiveAndAbove;
            }

            throw new AdCenterInternalErrorException(
                $"Could not convert age TargetValueId to AdInsight AgeEnum, value {ageValueId}");
        }

        private static async Task<DbDataReader> GetOrderTargetsFetchDbDataReader(
            ILogShared logger,
            IConnectionProvider connection,
            List<Job> jobsWithOrderId)
        {
            var sampleJob = jobsWithOrderId.FirstOrDefault();
            var context = AccountCallContextCreator.Create(
                logger,
                sampleJob.AccountId,
                sampleJob.CustomerId);

            var adGroupShardAdGroupIdWithParentIds = jobsWithOrderId.Select(job => new EntityIdWithParentId { EntityId = job.OrderId.Value, ParentId = job.CampaignId }).ToList();
            var adGroupIdWithParentIdsLineItems = new LineItemContainer<EntityIdWithParentId>(adGroupShardAdGroupIdWithParentIds, new Dictionary<int, List<CampaignManagementErrorDetail>>());

            var command = CreateOrderTargetsFetchByOrderIdsForPrivacyCheckCommand(context, adGroupIdWithParentIdsLineItems);

            return await CampaignDbQueueStore.ExecuteSqlCommandAsync(logger, command, connection).ConfigureAwait(false);
        }

        private static readonly List<short> TargetTypesForPrivacyCheck = new List<short>
        {
            (short) TargetDao.DBTargetType.Age,
            (short) TargetDao.DBTargetType.Gender,
            (short) TargetDao.DBTargetType.GeoLocation,
            (short) TargetDao.DBTargetType.Location,
            (short) TargetDao.DBTargetType.PostalCode,
            (short) TargetDao.DBTargetType.Audience,
            (short) TargetDao.DBTargetType.InMarket,
            (short) TargetDao.DBTargetType.Custom,
            (short) TargetDao.DBTargetType.ProductAudience,
            (short) TargetDao.DBTargetType.RemarketingSimilar,
            (short) TargetDao.DBTargetType.CustomerList,
            (short) TargetDao.DBTargetType.CombinedList,
            (short) TargetDao.DBTargetType.CompanyName,
            (short) TargetDao.DBTargetType.Industry,
            (short) TargetDao.DBTargetType.JobFunction,
            (short) TargetDao.DBTargetType.ImpressionBasedRemarketingList
        };

        private static readonly DataTable TargetsTableTypeForPrivacyCheck = DataTableType.CreateSmallIntEntityTypeDataTable(TargetTypesForPrivacyCheck);

        private static SqlParameter TableValuedParam(string parameterName, DataTable value)
        {
            SqlParameter param = new SqlParameter(parameterName, value);
            param.SqlDbType = SqlDbType.Structured;
            return param;
        }

        private static SqlParameter IntParam(string parameterName, int value)
        {
            SqlParameter param = new SqlParameter();
            param.ParameterName = parameterName;
            param.SqlDbType = SqlDbType.Int;
            param.Value = value;
            return param;
        }

        private static SqlCommand CreateOrderTargetsFetchByOrderIdsForPrivacyCheckCommand(AccountCallContext context, LineItemContainer<EntityIdWithParentId> adGroupIdWithParentIdLineItems)
        {
            var sprocToCall = "dbo.prc_PublicGetOrderTargetsByOrderIds_V50";
            SqlCommand command = SPHelper.MakeSqlCommandWithTracking(sprocToCall, context.Logger.CallTrackingData, 0);

            DataTable campaignIdOrderIdTable = DataTableType.CreateCampaignIdOrderIdTypeDataTable();
            foreach (var line in adGroupIdWithParentIdLineItems)
            {
                DataRow dataRow = campaignIdOrderIdTable.NewRow();
                dataRow[DataTableType.LineItemId] = line.LineItemId;
                dataRow[DataTableType.CampaignId] = line.LineItem.ParentId;
                dataRow[DataTableType.OrderId] = line.LineItem.EntityId;
                campaignIdOrderIdTable.Rows.Add(dataRow);
            }
            command.Parameters.Add(TableValuedParam("@OrderIds", campaignIdOrderIdTable));

            command.Parameters.Add(TableValuedParam("@TargetTypes", TargetsTableTypeForPrivacyCheck));
            //command.Parameters.Add(IntParam("@CustomerId", context.AdvertiserCustomerID));
            command.Parameters.Add(IntParam("@AccountId", (int)context.AccountId));
            command.Parameters.Add(IntParam("@UserId", context.UserId));
            return command;
        }

        private static async Task<DbDataReader> GetRadiusTargetsFetchDataReader(
            ILogShared logger,
            IConnectionProvider connection,
            List<Job> jobsWithOrderId,
            List<long> businessLocationIds,
            EntityType entityType)
        {
            var sampleJob = jobsWithOrderId.FirstOrDefault();
            var context = AccountCallContextCreator.Create(
                logger,
                sampleJob.AccountId,
                sampleJob.CustomerId);

            var command = SpWrappers.CreateGetBusinessLocationByIdsCommand(context, businessLocationIds, entityType);

            return await CampaignDbQueueStore.ExecuteSqlCommandAsync(logger, command, connection).ConfigureAwait(false);
        }

        public static async Task ConstructCampaignPrivacyCheckRequests(
            ILogShared logger,
            ConcurrentBag<Job> failedJobs,
            ConcurrentBag<Job> validJobsForPrivacyCheck,
            ConcurrentBag<Job> invalidJobsForPrivacyCheck,
            int accountId,
            List<Job> jobsPerAccount,
            Dictionary<long, GetAudiencePrivacyCheckV3Request> campaignIdToRequestMapping,
            Dictionary<long, List<TargetInfo>> campaignIdToTargetInfoMapping,
            Func<AccountCallContext, List<long>, IEnumerable<ILightWeightCampaign>> getLightWeightCampaignsInfoByIds)
        {
            var mainShardConnection = ConnectionProviderCampaignMT.CreateCampaignDbConnection(logger, (CallTrackingData)logger.CallTrackingData, accountId);

            var errorCampaignIdErrorCodeMapping = new Dictionary<long, int>();

            var tmpCampaignTargets = await GetCampaignTargetInfos(
                logger,
                mainShardConnection,
                jobsPerAccount,
                jobsPerAccount.Select(job => job.CampaignId).ToList(),
                errorCampaignIdErrorCodeMapping,
                OperationType.AimCampaignLevelPrivacyCheck).ConfigureAwait(false);

            if (tmpCampaignTargets == null)
            {
                failedJobs.AddRange(jobsPerAccount);

                LogHelper.LogFailedJobsAsWarning(logger, 
                    jobsPerAccount, 
                    $"Failed to get campaign target information for AccountId : {accountId}.", 
                    OperationType.AimCampaignLevelPrivacyCheck);

                return;
            }

            if (errorCampaignIdErrorCodeMapping.Count > 0)
            {
                // Jobs for invalid campaigns and accounts, "-100834" is for invalid campaign and "-101425" is for invalid account
                var jobsForInvalidCampaigns = jobsPerAccount
                    .Where(job => 
                        errorCampaignIdErrorCodeMapping.TryGetValue(job.CampaignId, out int errorCode) && 
                        (errorCode == -100834 || errorCode == -101425));

                var jobsWithOtherErrors = jobsPerAccount.Except(jobsForInvalidCampaigns).Where(job => errorCampaignIdErrorCodeMapping.ContainsKey(job.CampaignId)).ToList();

                LogHelper.LogFailedJobsAsWarning(logger,
                    jobsWithOtherErrors,
                    $"Error to get campaign target information for AccountId : {accountId}.",
                    OperationType.AimCampaignLevelPrivacyCheck);

                // Record jobs with exceptions
                failedJobs.AddRange(jobsWithOtherErrors);
                invalidJobsForPrivacyCheck.AddRange(jobsForInvalidCampaigns);

                // If no valid campaigns then return.
                if (errorCampaignIdErrorCodeMapping.Count == jobsPerAccount.Count)
                {
                    return;
                }
            }

            tmpCampaignTargets.ForEach(pair => campaignIdToTargetInfoMapping[pair.Key] = pair.Value);

            var ageIncludeListMapping = new Dictionary<long, List<AgeEnum>>();
            var ageExcludeListMapping = new Dictionary<long, List<AgeEnum>>();
            var genderIncludeListMapping = new Dictionary<long, List<GenderEnum>>();
            var genderExcludeListMapping = new Dictionary<long, List<GenderEnum>>();
            var audienceIncludeListMapping = new Dictionary<long, List<long>>();
            var audienceExcludeListMapping = new Dictionary<long, List<long>>();
            var companyIncludeListMapping = new Dictionary<long, List<long>>();
            var companyExcludeListMapping = new Dictionary<long, List<long>>();
            var industryIncludeListMapping = new Dictionary<long, List<long>>();
            var industryExcludeListMapping = new Dictionary<long, List<long>>();
            var jobFunctionIncludeListMapping = new Dictionary<long, List<long>>();
            var jobFunctionExcludeListMapping = new Dictionary<long, List<long>>();
            var locationIncludeListMapping = new Dictionary<long, List<long>>();
            var locationExcludeListMapping = new Dictionary<long, List<long>>();
            var radiusInstanceListMapping = new Dictionary<long, List<RadiusTarget>>();

            var campaignIdsWithIncompleteInformationSet = new HashSet<long>();

            // Fill campaign targets
            foreach (var pair in campaignIdToTargetInfoMapping)
            {
                var campaignId = pair.Key;
                var targetInfoList = pair.Value;

                foreach (var targetInfo in targetInfoList)
                {
                    if (targetInfo.BidType == BidType.BidOnly)
                    {
                        //only consume TargetAndBid targets
                        continue;
                    }

                    var targetTypeId = (TargetDao.DBTargetType)targetInfo.TargetTypeId;
                    switch (targetTypeId)
                    {
                        case TargetDao.DBTargetType.Age:
                            AddKeyValueToMapping(
                                targetInfo.ExclusionFlag == 1 ? ageExcludeListMapping : ageIncludeListMapping,
                                campaignId,
                                MapIntToAgeEnum((int)targetInfo.TargetValueId));
                            break;
                        case TargetDao.DBTargetType.Gender:
                            AddKeyValueToMapping(
                                 targetInfo.ExclusionFlag == 1 ? genderExcludeListMapping : genderIncludeListMapping,
                                 campaignId,
                                 MapIntToGenderEnum((int)targetInfo.TargetValueId));
                            break;
                        case TargetDao.DBTargetType.GeoLocation:
                        case TargetDao.DBTargetType.PostalCode:
                            AddKeyValueToMapping(
                                targetInfo.ExclusionFlag == 1 ? locationExcludeListMapping : locationIncludeListMapping,
                                campaignId,
                                targetInfo.TargetValueId);
                            break;
                        case TargetDao.DBTargetType.Audience:
                        case TargetDao.DBTargetType.InMarket:
                        case TargetDao.DBTargetType.Custom:
                        case TargetDao.DBTargetType.ProductAudience:
                        case TargetDao.DBTargetType.RemarketingSimilar:
                            AddKeyValueToMapping(
                                targetInfo.ExclusionFlag == 1 ? audienceExcludeListMapping : audienceIncludeListMapping,
                                campaignId,
                                targetInfo.TargetValueId);
                            break;
                        case TargetDao.DBTargetType.CompanyName:
                            AddKeyValueToMapping(
                                targetInfo.ExclusionFlag == 1 ? companyExcludeListMapping : companyIncludeListMapping,
                                campaignId,
                                targetInfo.TargetValueId);
                            break;
                        case TargetDao.DBTargetType.Industry:
                            AddKeyValueToMapping(
                                targetInfo.ExclusionFlag == 1 ? industryExcludeListMapping : industryIncludeListMapping,
                                campaignId, targetInfo.TargetValueId);
                            break;
                        case TargetDao.DBTargetType.JobFunction:
                            AddKeyValueToMapping(
                                targetInfo.ExclusionFlag == 1 ? jobFunctionExcludeListMapping : jobFunctionIncludeListMapping,
                                campaignId,
                                targetInfo.TargetValueId);
                            break;
                        case TargetDao.DBTargetType.Location:
                            if (targetInfo.Attribute != null && targetInfo.Latitude != null && targetInfo.Longitude != null)
                            {
                                AddKeyValueToMapping(
                                    radiusInstanceListMapping,
                                    campaignId,
                                    new RadiusTarget()
                                    {
                                        Radius = targetInfo.Attribute.Value,
                                        RadiusUnit = (RadiusUnit)targetInfo.Attribute2.Value,
                                        CenterLatitude = targetInfo.Latitude.Value,
                                        CenterLongitude = targetInfo.Longitude.Value
                                    });
                            }
                            else
                            {
                                campaignIdsWithIncompleteInformationSet.Add(campaignId);
                            }
                            break;
                    }
                }
            }

            if (campaignIdsWithIncompleteInformationSet.Any())
            {
                var jobsWithIncompleteInformation = jobsPerAccount.Where(job => campaignIdsWithIncompleteInformationSet.Contains(job.CampaignId));
                LogHelper.LogFailedJobsAsWarning(logger, jobsWithIncompleteInformation.ToList(), "Incomplete campaign target information");
                failedJobs.AddRange(jobsWithIncompleteInformation);
            }

            validJobsForPrivacyCheck.AddRange(jobsPerAccount.Where(job => !campaignIdsWithIncompleteInformationSet.Contains(job.CampaignId)));

            var campaignIds = campaignIdToTargetInfoMapping.Keys.ToList();
            var context = AccountCallContextCreator.Create(
                logger,
                accountId,
                jobsPerAccount.First().CustomerId);
            var campaigns = getLightWeightCampaignsInfoByIds(context, campaignIds).ToDictionary(c => c.Id, c => c);

            // Construct request mapping
            foreach (var campaignId in campaignIds)
            {
                if (campaignIdsWithIncompleteInformationSet.Contains(campaignId))
                {
                    continue;
                }

                campaigns.TryGetValue(campaignId, out var campaign);

                campaignIdToRequestMapping[campaignId] = new GetAudiencePrivacyCheckV3Request()
                {
                    AccountId = accountId,
                    Tracking = new Microsoft.Advertiser.AdInsight.MT.Interfaces.Common.CallTrackingData()
                    {
                        ClientApplication = logger.CallTrackingData.ClientApplication,
                        ClientEndPointAddress = logger.CallTrackingData.ClientEndPointAddress,
                        OperationSequence = logger.CallTrackingData.OperationSequence,
                        SessionId = logger.CallTrackingData.SessionId,
                        RequestId = logger.CallTrackingData.RequestId,
                        TrackingId = logger.CallTrackingData.TrackingId
                    },
                    Age = new SelectionOfAgeEnum
                    {
                        Includes = ExtractDataFromDict(ageIncludeListMapping, campaignId),
                        Excludes = ExtractDataFromDict(ageExcludeListMapping, campaignId),
                    },
                    Gender = new SelectionOfGenderEnum
                    {
                        Includes = ExtractDataFromDict(genderIncludeListMapping, campaignId),
                        Excludes = ExtractDataFromDict(genderExcludeListMapping, campaignId),
                    },
                    Location = new SelectionOflong()
                    {
                        Excludes = ExtractDataFromDict(locationExcludeListMapping, campaignId),
                        Includes = ExtractDataFromDict(locationIncludeListMapping, campaignId)
                    },
                    Audience = new SelectionOflong()
                    {
                        Excludes = ExtractDataFromDict(audienceExcludeListMapping, campaignId),
                        Includes = ExtractDataFromDict(audienceIncludeListMapping, campaignId)
                    },
                    Company = new SelectionOflong()
                    {
                        Excludes = ExtractDataFromDict(companyExcludeListMapping, campaignId),
                        Includes = ExtractDataFromDict(companyIncludeListMapping, campaignId)
                    },
                    Industry = new SelectionOflong()
                    {
                        Excludes = ExtractDataFromDict(industryExcludeListMapping, campaignId),
                        Includes = ExtractDataFromDict(industryIncludeListMapping, campaignId)
                    },
                    JobFunction = new SelectionOflong()
                    {
                        Excludes = ExtractDataFromDict(jobFunctionExcludeListMapping, campaignId),
                        Includes = ExtractDataFromDict(jobFunctionIncludeListMapping, campaignId)
                    },
                    RadiusTargets = radiusInstanceListMapping.GetOrDefault(campaignId)?.ToArray(),
                };

                if (Config.Config.EnableBiddingStrategyTypeForPrivacyCheck)
                {
                    campaignIdToRequestMapping[campaignId].CampaignBiddingStrategy = (int?)(campaign?.DisplayBiddingStrategyType);
                    campaignIdToRequestMapping[campaignId].CampaignSubType = (int?)(campaign?.CampaignSubType);
                }

                IShoppingSettings shoppingSettings = (campaign?.CampaignSettings).OrEmpty().FirstOrDefault(cs => cs is IShoppingSettings) as IShoppingSettings;
                if (shoppingSettings != null)
                {
                    campaignIdToRequestMapping[campaignId].Feed = new Feed
                    {
                        FeedId = shoppingSettings.FeedId,
                        FeedType = shoppingSettings.FeedTypeId,
                        StoreId = shoppingSettings.ProviderId,
                    };
                }

            }
        }

        private static async Task<Dictionary<long, List<TargetInfo>>> GetCampaignTargetInfos(
            ILogShared logger,
            IConnectionProvider connection,
            List<Job> validJobs,
            List<long> campaignIds,
            Dictionary<long, int> errorCampaignIdErrorCodeMapping,
            OperationType callerOperationType)
        {
            var sampleJob = validJobs.FirstOrDefault();

            var context = AccountCallContextCreator.Create(
                logger,
                sampleJob.AccountId,
                sampleJob.CustomerId);

            var command = SpWrappers.CreateCampaignTargetsFetchByCampaignIdsCommand(context, new LineItemContainer<long>(campaignIds, null));

            var campaignTargetInfoReader = await CampaignDbQueueStore.ExecuteSqlCommandAsync(logger, command, connection).ConfigureAwait(false);
            if (campaignTargetInfoReader == null)
            {
                logger.LogError($"Cannot get campaign level target information from main shard.");
                return null;
            }

            // Read target info from main shard
            var campaignTargetList = new List<TargetInfo>();
            var campaignTargetGroupIdCampaignIdMapping = new Dictionary<long, long>();

            using (campaignTargetInfoReader)
            {
                try
                {
                    // Force using v50 version
                    // Result from the new proc: prc_PublicGetCampaignTargetsByCampaignIds_V50
                    const int resultSetCount = 6;
                    for (var currentDataSetId = 0; currentDataSetId < resultSetCount; currentDataSetId++)
                    {
                        switch (currentDataSetId)
                        {
                            case 0: // Target group info
                                {
                                    var targetGroupIdOrdinal = new DataReaderOrdinal("TargetGroupId").GetOrdinal(campaignTargetInfoReader);
                                    var campaignIdOrdinal = new DataReaderOrdinal("CampaignId").GetOrdinal(campaignTargetInfoReader);

                                    while (campaignTargetInfoReader.Read())
                                    {
                                        long targetGroupId = campaignTargetInfoReader.GetInt64(targetGroupIdOrdinal);
                                        long campaignId = campaignTargetInfoReader.GetInt64(campaignIdOrdinal);

                                        campaignTargetGroupIdCampaignIdMapping.TryAdd(targetGroupId, campaignId);
                                    }
                                }

                                break;

                            case 1: // Age Gender Device DayTime CompanyName JobFunction Industry
                            case 2: // Location GeoLocation
                            case 3: // Radius
                            case 4: // Audience
                                if (campaignTargetInfoReader.NextResult())
                                {
                                    var targetGroupIdOrdinal = new DataReaderOrdinal("TargetGroupId");
                                    var targetTypeIdOrdinal = new DataReaderOrdinal("TargetTypeId");
                                    var targetValueIdOrdinal = new DataReaderOrdinal("TargetValueId");
                                    var attributeOrdinal = new DataReaderOrdinal("Attribute");
                                    var attribute2Ordinal = new DataReaderOrdinal("Attribute2");
                                    var exclusionFlagOrdinal = new DataReaderOrdinal("ExclusionFlag");
                                    var latitudeOrdinal = new DataReaderOrdinal("Latitude");
                                    var longitudeOrdinal = new DataReaderOrdinal("Longitude");

                                    while (campaignTargetInfoReader.Read())
                                    {
                                        long targetGroupId = campaignTargetInfoReader.GetInt64(targetGroupIdOrdinal.GetOrdinal(campaignTargetInfoReader));
                                        byte targetTypeId = campaignTargetInfoReader.GetByte(targetTypeIdOrdinal.GetOrdinal(campaignTargetInfoReader));

                                        // Supporting Int64 dbo.TargetGroupDetail.TargetValueId.
                                        long targetValueId;
                                        try
                                        {
                                            // Until dbo.TargetGroupDetail.TargetValueId is changed from INT to BIGINT, GetInt64 will throw InvalidCastException.
                                            targetValueId = campaignTargetInfoReader.GetInt64(targetValueIdOrdinal.GetOrdinal(campaignTargetInfoReader));
                                        }
                                        catch (Exception)
                                        {
                                            // Retrieve dbo.TargetGroupDetail.TargetValueId as INT.
                                            targetValueId = campaignTargetInfoReader.GetInt32(targetValueIdOrdinal.GetOrdinal(campaignTargetInfoReader));
                                        }

                                        int? attribute = null;
                                        int? attribute2 = null;
                                        bool isExclusive = true;
                                        double? latitude = null;
                                        double? longitude = null;

                                        if (currentDataSetId == 1 || currentDataSetId == 2 || currentDataSetId == 3)
                                        {
                                            attribute = campaignTargetInfoReader.GetNullableInt32(attributeOrdinal.GetOrdinal(campaignTargetInfoReader));
                                        }

                                        if (currentDataSetId == 1 || currentDataSetId == 3)
                                        {
                                            attribute2 = campaignTargetInfoReader.GetNullableInt32(attribute2Ordinal.GetOrdinal(campaignTargetInfoReader));
                                        }

                                        if (currentDataSetId == 4)
                                        {
                                            var isExclusiveBitMaskOrdinal = new DataReaderOrdinal("IsExclusiveBitMask").GetOrdinal(campaignTargetInfoReader);
                                            long isExclusiveBitMask = campaignTargetInfoReader.GetInt64(isExclusiveBitMaskOrdinal);
                                            isExclusive = ((1L << targetTypeId) & isExclusiveBitMask) != 0;
                                        }

                                        if (currentDataSetId == 3)
                                        {
                                            latitude = campaignTargetInfoReader.GetDoubleOrNull(latitudeOrdinal.GetOrdinal(campaignTargetInfoReader));
                                            longitude = campaignTargetInfoReader.GetDoubleOrNull(longitudeOrdinal.GetOrdinal(campaignTargetInfoReader));
                                        }

                                        byte exclusionFlag = 0;
                                        if (currentDataSetId != 3) // Radius target does not have exclusionFlag
                                        {
                                            Type type = campaignTargetInfoReader.GetFieldType(exclusionFlagOrdinal.GetOrdinal(campaignTargetInfoReader));
                                            if (type == typeof(byte))
                                            {
                                                exclusionFlag = campaignTargetInfoReader.GetByte(exclusionFlagOrdinal.GetOrdinal(campaignTargetInfoReader));
                                            }
                                            else if (type == typeof(Int32))
                                            {
                                                exclusionFlag = Convert.ToByte(campaignTargetInfoReader.GetInt32(exclusionFlagOrdinal.GetOrdinal(campaignTargetInfoReader)));
                                            }
                                            else
                                            {
                                                throw new NotSupportedException($"The data type of ExclusionFlag is not supported. Type is {type}");
                                            }
                                        }

                                        campaignTargetList.Add(new TargetInfo(targetGroupId, targetTypeId, targetValueId, attribute, attribute2,
                                            exclusionFlag, isExclusive ? BidType.TargetAndBid : BidType.BidOnly, latitude, longitude));
                                    }
                                }

                                break;

                            case 5:
                                if (campaignTargetInfoReader.NextResult())
                                {
                                    var lineItemIdOrdinal = new DataReaderOrdinal("LineItemId").GetOrdinal(campaignTargetInfoReader);
                                    var errorCodeOrdinal = new DataReaderOrdinal("ErrorCode").GetOrdinal(campaignTargetInfoReader);

                                    while (campaignTargetInfoReader.Read())
                                    {
                                        int lineItemId = campaignTargetInfoReader.GetInt32(lineItemIdOrdinal);
                                        int errorCode = campaignTargetInfoReader.GetInt32(errorCodeOrdinal);

                                        if (lineItemId < campaignIds.Count && lineItemId >= 0)
                                        {
                                            errorCampaignIdErrorCodeMapping.Add(campaignIds[lineItemId], errorCode);
                                        }
                                    }
                                }

                                break;
                        }
                    }
                }
                catch (SqlException sqlEx)
                {              
                    if (sqlEx.ErrorCode == -100135)
                    {
                        logger.LogWarning($"Operation :{callerOperationType}, batch error reading data from mainshard: {sqlEx}, campaigns with error: ({errorCampaignIdErrorCodeMapping.Aggregate(string.Empty, (current, next) => current + $"{next.Key}:{next.Value} ")}).");
                    }
                    else
                    {
                        logger.LogError($"Operation :{callerOperationType}, fatal error reading data from mainshard: {sqlEx}");
                        return null;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError($"Operation :{callerOperationType}, fatal error reading data from mainshard: {ex}");
                    return null;
                }
            }

            return campaignTargetList.Where(x => !errorCampaignIdErrorCodeMapping.ContainsKey(campaignTargetGroupIdCampaignIdMapping[x.TargetGroupId])).GroupBy(x => x.TargetGroupId).ToDictionary(k => campaignTargetGroupIdCampaignIdMapping[k.Key], v => v.ToList()); ;
        }

        private static T[] MergeEnumerableTargets<T>(long targetGroupId, Dictionary<long, List<T>> includeListMapping, Dictionary<long, List<T>> excludeListMapping, List<T> enumList)
        {
            List<T> tList = null;
            List<T> tExcludeList = null;

            if (includeListMapping != null && includeListMapping.ContainsKey(targetGroupId))
            {
                tList = includeListMapping[targetGroupId];
            }

            if (tList == null)
            {
                tList = new List<T>();
            }

            if (tList.Count == 0)
            {
                if (excludeListMapping != null && excludeListMapping.ContainsKey(targetGroupId))
                {
                    tExcludeList = excludeListMapping[targetGroupId];
                }

                if(tExcludeList == null)
                {
                    tExcludeList = new List<T>();
                }

                tList = enumList.Except(tExcludeList).ToList();
            }

            return tList.ToArray();
        }
    }
}