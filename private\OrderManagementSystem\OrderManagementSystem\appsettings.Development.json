{
  "ApplicationEnv": "CI-PME",
  "MetaDataService": {
    "Endpoint": "http://localhost:818",
    "KeyVaultName": "CampaignSecretsKVSI",
    "OrderManagementSystemDBKeyVaultName": "CampaignDB-KeyVaultSI",
    "OrderManagementSystemCustomerDBKeyVaultName": "CampaignDB-KeyVaultSI",
    "EnableDatabaseAADAuth": false,
    "AADAuthDatabaseName": ""
  },
  "AzureKeyVault": {
    "KeyVaultName": "CampaignSecretsKVSI",
    "OMSKeyVaultName": "CampaignOMSKVSI",
    "KeyVaultClientId": "644646e3-7089-4e32-b417-24edfa15f2f9",
    "OMSAdsUserPwdSecret": "OMSAdsUserPwd",
    "UseManagedIdentity": true
  },
  "KustoWriterConfig": {
    "KustoUrl": "https://ingest-bingadsppe.kusto.windows.net:443",
    "KustoApplicationId": "b3bcf3b4-7b61-4ad0-b18a-c938c1caaed4"
  },
  "ConfigurationOptions": {
    "AppConfigStoreName": "OMS-Config",
    "UseAzureAppConfig": false,
    "Label": "CI"
  },
  "ClientCenter": {
    "Endpoint": "https://ClientCenterMT.redmond.corp.microsoft.com:3089/clientcenter",
    "ClientCertificateThumbprint": "62c7b45a863f31602df6b805004c88018f83a989",
    "CcmtHttpsImpersonationTokenCertificateName": "ads-ccimpersonation-ci"
  },
  "XandrConfig": {
    "Username": "omsservicetest",
    "PasswordKV": "OMSXandrLoginPwd",
    "AuthTokenKV": "OMSXandrAuthToken",
    "MemberId": 16505
  },
  "AllowedPublisherIds": [
    2533173, // XBOX_PPE_PUBLISHER_ID
    2578529, // MSN_PPE_PUBLISHER_ID
    2597487, // MCG_PPE_PUBLISHER_ID
    2634549 //LOTUS_PPE_PUBLISHER_ID
  ],
  "TLSCertName": "",
  "ManagedIdentityId": "b3bcf3b4-7b61-4ad0-b18a-c938c1caaed4",
  "EmailConfig": {
    "Enabled": false,
    "ApplicationId": "4a2485c9-2264-4fac-b3dc-3c9079bd3892",
    "CertificateName": "bingads-campaignmt-clientcert-corp",
    "PublisherEndpoint": "https://mucp.api.account.microsoft-int.com/events/v1/trigger",
    "PublisherScope": "https://mucp.api.account.microsoft-int.com//.default"
  },
  "StorageAccountConfig": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=bingadsappsstorageci;ManagedIdentityId=bf7d8c98-83a4-40a9-8afe-6c2734a78e6f",
    "StorageAccountName": "bingadsappsstorageci"
  },
  "DRSConfig": {
    "DRSScope": "6239bb2a-c29d-4c51-9f47-16416e93b6bd/.default",
    "ServiceBaseURL": "https://moderndrs.cp.microsoft-int.com/",
    "PartnerId": "1d8cbf93-ab53-4d1c-8a84-0ba34a69561a",
    "DRSAppId": "bf7d8c98-83a4-40a9-8afe-6c2734a78e6f",
    "APIVersion": "2018-05-31"
  },
  "DynamicConfig": {
    "FilterSegmentCacheResults": "false",
    "IsImportEnabled": true,
    "EnableDMATargeting": true,
    "EnableKVPTargeting": true,
    //System
    "Sentinal": 1
  },
  //Test only configs
  "ImportFromLocalFileSystem": false
}