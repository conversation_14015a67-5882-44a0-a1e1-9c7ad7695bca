﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using OrderManagementSystem.Common;
using OrderManagementSystem.Configuration;
using OrderManagementSystem.Entities;
using OrderManagementSystem.Entities.External;
using System.Net;
using System.Text.Json;

namespace OrderManagementSystem.FunctionalTests
{
    [TestClass]
    public class LineTests
    {
        private static int CustomerId;
        private static long MediaPlanId;
        private static string? LineUrl;

        private static int PublisherId;
        private static string? ProductUrl;
        private static long ProductId1;
        private static long ProductId2;
        private static bool IsKVPTargetingEnabled;

        [ClassInitialize]
        public static void ClassInitialize(TestContext context)
        {
            CustomerId = 10929147; //Using real CustomerId mapped to "Microsoft_test" advertiser in seat 16505
            PublisherId = 2533173; //"Xbox-Test" publisher in seat 16505

            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development";

            var configuration = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile($"appsettings.{environment}.json", optional: true)
                .AddEnvironmentVariables()
                .Build();

            var dynamicConfig = new DynamicConfig();
            configuration.GetSection("DynamicConfig").Bind(dynamicConfig);

            IsKVPTargetingEnabled = dynamicConfig.EnableKVPTargeting;

            var optionsMonitor = new OptionsMonitorFake<DynamicConfig>(dynamicConfig);
            new DynamicConfigWrapper(optionsMonitor, NullLogger<DynamicConfigWrapper>.Instance);

            var mediaPlanUrl = Helper.GetMediaPlanEndpoint(CustomerId);

            MediaPlanInput mediaPlan = Helper.GetValidMediaPlan(nameof(LineTests));

            var result = Helper.PostMediaPlanAndValidateAsync(mediaPlanUrl, mediaPlan,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                }).Result;

            MediaPlanId = result!.Id;//mediaPlans!.First().Id;
            LineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId);

            ProductUrl = Helper.GetProductEndpoint(PublisherId);

            var productToCreate1 = Helper.GetValidProduct_Basic(ProductType.Rotational);
            SupplyTarget[] targets = [new SupplyTarget() { TargetTypeId = TargetType.Device, TargetPremium = 5.0 }, new SupplyTarget() { TargetTypeId = TargetType.Audience, TargetPremium = 2.0 }];
            productToCreate1.SupportedTargets = targets;

            var product1 = Helper.PostProductAndValidateAsync(ProductUrl!, productToCreate1,
                (response) =>
                {
                    return Task.CompletedTask; // Ensure a Task is always returned
                }).Result;
            ProductId1 = product1!.Id;

            var productToCreate2 = Helper.GetValidProduct_Basic(ProductType.Roadblock);
            productToCreate2.Slot = ProductTimeSlot.SixHour00;
            productToCreate2.SupportedTargets = targets;

            var product2 = Helper.PostProductAndValidateAsync(ProductUrl!, productToCreate2,
                (response) =>
                {
                    return Task.CompletedTask; // Ensure a Task is always returned
                }).Result;
            ProductId2 = product2!.Id;
        }

        [TestMethod]
        public async Task LineDateRangeValidationWithTimeZone()
        {
            // Create Media Plan with PST time zone
            var mediaPlanUrl = Helper.GetMediaPlanEndpoint(CustomerId);
            var mediaPlan = Helper.GetValidMediaPlan("Media Plan with PST time zone");
            mediaPlan.StartDate = DateTime.UtcNow.Date.AddDays(3);
            mediaPlan.EndDate = DateTime.UtcNow.Date.AddDays(4);
            var mediaPlanOutput = await Helper.PostMediaPlanAndValidateAsync(mediaPlanUrl, mediaPlan,
                async (response) =>
                {
                    await Task.CompletedTask;
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                });

            var mediaPlanId = mediaPlanOutput!.Id;

            // Create Line with EST time zone that fails validation
            var productUrl = Helper.GetProductEndpoint(PublisherId);
            var productToCreate = Helper.GetValidProduct_Basic(ProductType.Rotational);
            var product = Helper.PostProductAndValidateAsync(productUrl!, productToCreate,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                }).Result;
            var productId = product!.Id;

            var line = Helper.GetValidLine(productId, ProductType.Rotational);
            line.StartDate = DateTime.UtcNow.Date.AddDays(2).AddHours(21);
            line.EndDate = DateTime.UtcNow.Date.AddDays(5);
            var lineUrl = Helper.GetLineEndpoint(CustomerId, mediaPlanId);
            var resultLine = await Helper.PostLineAndValidateAsync(lineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.BadRequest, response.StatusCode); // Expecting failure
                    return Task.CompletedTask;
                });

            // Adjust Line dates to pass validation
            line.StartDate = DateTime.UtcNow.Date.AddDays(3).AddHours(5);
            line.EndDate = DateTime.UtcNow.Date.AddDays(3).AddHours(11);
            resultLine = await Helper.PostLineAndValidateAsync(lineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode); // Expecting success
                    return Task.CompletedTask;
                });

            // Validate the Line is within the Media Plan's date range
            Assert.IsNotNull(resultLine);
            Assert.IsTrue(resultLine!.StartDate >= mediaPlan.StartDate, $"Line start date {resultLine.StartDate} is not within Media Plan start date {mediaPlan.StartDate}");
            Assert.IsTrue(resultLine!.EndDate <= mediaPlan.EndDate, $"Line end date {resultLine.EndDate} is not within Media Plan end date {mediaPlan.EndDate}");
        }

        [TestMethod]
        public async Task PostLineWithTarget()
        {
            var line = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            line.Name = new string('a', 245) + Guid.NewGuid().ToString().Substring(0, 5);
            line.Description = new string('b', 245) + Guid.NewGuid().ToString().Substring(0, 5);
            line.Targets = new Target[]
                {
                    new DeviceTarget
                    {
                        DeviceTypes = [DeviceType.Mobile],
                    }
                };

            var resultLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            var target = resultLine!.Targets!.First();
            Assert.IsTrue(target is DeviceTarget);
            var deviceTarget = target as DeviceTarget;
            Assert.AreEqual(DeviceType.Mobile, deviceTarget!.DeviceTypes[0]);
            Assert.AreEqual(LineStatus.Approved, resultLine!.Status);

            line.Id = resultLine!.Id;
            ValidateLine(line, CustomerId, MediaPlanId, resultLine);
            // TODO: Delete the lines created in this test
        }

        [TestMethod]
        public async Task PostLineWithTarget_WithTargetPremiumAdjustments()
        {
            var line = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            line.Targets = new Target[]
                {
                    new DeviceTarget
                    {
                        DeviceTypes = [DeviceType.Mobile],
                    }
                };
            line.Cpm = 2.65;

            var resultLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            var target = resultLine!.Targets!.First();
            Assert.IsTrue(target is DeviceTarget);
            var deviceTarget = target as DeviceTarget;
            Assert.AreEqual(DeviceType.Mobile, deviceTarget!.DeviceTypes[0]);
            Assert.AreEqual(LineStatus.NeedApproval, resultLine!.Status);

            line.Id = resultLine!.Id;
            ValidateLine(line, CustomerId, MediaPlanId, resultLine);

            //Now we will create a new line that is identical except for Targeting, to prove that target premium is being applied
            var line2 = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            line2.Cpm = 2.65;

            var resultLine2 = await Helper.PostLineAndValidateAsync(LineUrl!, line2,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            Assert.IsNull(resultLine2!.Targets);
            Assert.AreEqual(LineStatus.Approved, resultLine2!.Status);

            line2.Id = resultLine2!.Id;
            ValidateLine(line2, CustomerId, MediaPlanId, resultLine2);
            // TODO: Delete the lines created in this test
        }

        [TestMethod]
        public async Task PostAndUpdateLineWithTarget_WithExchangeRate()
        {
            var japaneseProduct = Helper.GetValidProduct_Basic(ProductType.Rotational);
            japaneseProduct.CurrencyCode = "JPY";
            japaneseProduct.BaseCPMRate = 500;
            var product = Helper.PostProductAndValidateAsync(ProductUrl!, japaneseProduct,
                (response) =>
                {
                    return Task.CompletedTask; // Ensure a Task is always returned
                }).Result;

            var line = Helper.GetValidLine(product!.Id, ProductType.Rotational);
            line.Cpm = 5;  // if USD -> JPY is ever < 100, we can update this test

            var exchangeRate = 0M;
            DateTime? exchangeRateDate = null;
            var lineId = 0L;
            {
                var resultLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                    (response) =>
                    {
                        Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                        return Task.CompletedTask; // Ensure a Task is always returned
                    });

                lineId = resultLine!.Id;
                // before it's saved in DB, exchange rate is slightly higher precision
                Assert.IsNotNull(resultLine!.ExchangeRate, "exchange rate should not be null");
                exchangeRate = resultLine!.ExchangeRate.Value;
                Assert.IsNotNull(resultLine!.ExchangeRateDate, "exchange rate date should not be null");
                exchangeRateDate = resultLine!.ExchangeRateDate.Value;
                Assert.AreEqual(LineStatus.Approved, resultLine!.Status, "should be approved (using exchange rate)");
            }

            {
                line.Name = "Updated-" + line.Name;
                line.Cpm = 6;  // still outlandish if currency conversion is not applied

                var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, lineId);
                var resultUpdateLine = await Helper.UpdateLineAndValidateAsync(updateLineUrl, line,
                    (response) =>
                    {
                        Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                        return Task.CompletedTask; // Ensure a Task is always returned
                    });

                Assert.IsTrue(exchangeRate - resultUpdateLine!.ExchangeRate < 0.001M, "exchange rate should match");
                Assert.AreEqual(exchangeRateDate, resultUpdateLine!.ExchangeRateDate, "exchange rate date should be the same");
                Assert.AreEqual(LineStatus.Approved, resultUpdateLine!.Status, "should be approved (using exchange rate)");
            }

            {
                var getLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, lineId);
                var resultGetLine = await Helper.GetLineAndValidateAsync(getLineUrl,
                    (response) =>
                    {
                        Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                        return Task.CompletedTask; // Ensure a Task is always returned
                    });

                Assert.IsTrue(exchangeRate - resultGetLine!.ExchangeRate < 0.001M, "exchange rate should match");
                Assert.AreEqual(exchangeRateDate, resultGetLine!.ExchangeRateDate, "exchange rate date should be the same");
                Assert.AreEqual(LineStatus.Approved, resultGetLine!.Status, "should be approved (using exchange rate)");
            }
        }

        [TestMethod]
        public async Task PostLineWithCPD()
        {
            var line = GetValidLine(ProductId2);
            var resultLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            var resultCpd = resultLine!.Cpd;
            Assert.IsTrue(resultCpd.HasValue);
            Assert.AreEqual(LineStatus.NeedApproval, resultLine!.Status); //In CI/SI, Forecast will always be 0, and for Roadblocks we are always requesting approval if Available impressions is 0 (cannot calculate CPM without available impressions from YA)

            line.Id = resultLine!.Id;
            ValidateLine(line, CustomerId, MediaPlanId, resultLine);

            //Validate the units
            Assert.AreEqual(2, resultLine!.Units);

            // TODO: Delete the line created in this test
        }

        [TestMethod]
        public async Task ValidateMediaPlanEstimatedImpressions()
        {
            //Field comes from child lines. We will create a roadblock and 2 rotational Lines and validated estimated impressions works
            //Note: in CI/SI the roadblock will always have 0 estimated impressions from YA. This is just to confirm no errors
            var mediaPlanUrl = Helper.GetMediaPlanEndpoint(CustomerId);
            MediaPlanInput mediaPlanInput = Helper.GetValidMediaPlan(nameof(LineTests));

            var mediaPlan = Helper.PostMediaPlanAndValidateAsync(mediaPlanUrl, mediaPlanInput,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                }).Result;

            Assert.IsNotNull(mediaPlan);

            var rotationalLineImp = GetValidLine(ProductId1);
            rotationalLineImp.TargetImpressions = 1000;

            var rotationalLineSpend = GetValidLine(ProductId1);
            rotationalLineSpend.TargetImpressions = null;
            rotationalLineSpend.TargetSpend = 12;
            rotationalLineSpend.Cpm = 3; //Impressions should be 4000

            var roadblockLine = GetValidLine(ProductId2); //Impressions should be 0

            var lineUrl = Helper.GetLineEndpoint(CustomerId, mediaPlan.Id);

            var resultLine = await Helper.PostLineAndValidateAsync(lineUrl!, rotationalLineImp,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            resultLine = await Helper.PostLineAndValidateAsync(lineUrl!, rotationalLineSpend,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            resultLine = await Helper.PostLineAndValidateAsync(lineUrl!, roadblockLine,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            var mediaPlanGetUrl = Helper.GetMediaPlanEndpoint(CustomerId, mediaPlan.Id);
            var mediaPlanOutput = await Helper.GetMediaPlanAndValidateAsync(mediaPlanGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.IsNotNull(mediaPlanOutput);
            Assert.AreEqual(5000, mediaPlanOutput.EstimatedImpressions);
        }

        [TestMethod]
        public async Task PostLine_MultipleTargets()
        {
            //Create product that can support all targets
            var productToCreate = Helper.GetValidProduct_Basic(ProductType.Rotational);

            SupplyTarget[] supplyTargets = [
                new SupplyTarget() { TargetTypeId = TargetType.Audience, TargetPremium = 0.3 },
                new SupplyTarget() { TargetTypeId = TargetType.Location, TargetPremium = 0 },
                new SupplyTarget() { TargetTypeId = TargetType.Device, TargetPremium = 1 },
                new SupplyTarget() { TargetTypeId = TargetType.FrequencyAndRecency, TargetPremium = 0 },
            ];
            productToCreate.SupportedTargets = supplyTargets;

            var product = Helper.PostProductAndValidateAsync(ProductUrl!, productToCreate,
                (response) =>
                {
                    return Task.CompletedTask; // Ensure a Task is always returned
                }).Result;
            var productId = product!.Id;

            var line = Helper.GetValidLine(productId, ProductType.Rotational);

            line.Targets = new Target[]
            {
                new AudienceTarget() { Ids = [1], AudienceTargetingType = AudienceTargetingType.AND },
                new LocationTarget() { Ids = [2] },
                new LocationTarget() { Ids = [60] },
                new DeviceTarget() { DeviceTypes = [DeviceType.CTV] },
                new FrequencyAndRecencyTarget() { FrequencyAndRecencys = new[] { new FrequencyAndRecency() { Unit = FrequencyUnit.Day, Number = 2 }  } },
            };

            var resultLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            //Validate some of the target details (Frequency has some formulas applied, hence the specific check)
            var targets = resultLine!.Targets!;
            Assert.AreEqual(5, targets.Length);
            Assert.AreEqual(2, targets.Where(_ => _ is LocationTarget).Count());

            FrequencyAndRecencyTarget freq = (FrequencyAndRecencyTarget)targets.Where(_ => _ is FrequencyAndRecencyTarget).First();
            Assert.AreEqual(FrequencyUnit.Day, freq.FrequencyAndRecencys[0].Unit);
            Assert.AreEqual(2, freq.FrequencyAndRecencys[0].Number);
            Assert.AreEqual(LineStatus.Approved, resultLine!.Status);

            line.Id = resultLine!.Id;
            ValidateLine(line, CustomerId, MediaPlanId, resultLine!);
            // TODO: Delete the lines created in this test
        }

        [TestMethod]
        public async Task PostLine_Draft()
        {
            var line = GetDraftLine(ProductId1);

            var resultLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            var resultLineObj = resultLine!;
            Assert.AreEqual(line.Name, resultLineObj.Name);
            Assert.AreEqual(LineStatus.Draft, resultLineObj.Status);
            // TODO: Delete the lines created in this test
        }

        [TestMethod]
        public async Task PostLine_NeedApproval()
        {
            var line = GetValidLine(ProductId1);
            line.Cpm = 2.0;

            var resultLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            var resultLineObj = resultLine!;
            Assert.AreEqual(line.Name, resultLineObj.Name);
            Assert.AreEqual(LineStatus.NeedApproval, resultLineObj.Status);
            // TODO: Delete the lines created in this test
        }

        [TestMethod]
        public async Task PostLine_Lotus_NeedApproval()
        {
            var productToCreate = Helper.GetValidProduct_Basic(ProductType.Rotational);
            SupplyTarget[] targets = [new SupplyTarget() { TargetTypeId = TargetType.Device, TargetPremium = 5.0 }, new SupplyTarget() { TargetTypeId = TargetType.Audience, TargetPremium = 2.0 }];
            productToCreate.SupportedTargets = targets;
            productToCreate.PlacementIds = [36224052]; //[Lotus] - Default

            var lotusPublisherId = 2634549;
            var productUrl = Helper.GetProductEndpoint(lotusPublisherId);

            var product = Helper.PostProductAndValidateAsync(productUrl, productToCreate,
                (response) =>
                {
                    return Task.CompletedTask; // Ensure a Task is always returned
                }).Result;

            Assert.IsNotNull(product);
            var productId = product!.Id;

            var line = GetValidLine(productId);

            var resultLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                }, 
                (result) =>
                {
                    Assert.IsNotNull(result);
                    var warnings = result!.Warnings;
                    Assert.IsNotNull(warnings);
                    Assert.AreEqual(1, warnings.Count);
                    Assert.IsNotNull(warnings[0].Property);
                    Assert.AreEqual(1, warnings[0].Property!.Length);
                    Assert.AreEqual("ProjectFresno", warnings[0].Property![0]);
                });

            var resultLineObj = resultLine!;
            Assert.AreEqual(line.Name, resultLineObj.Name);
            Assert.AreEqual(LineStatus.NeedApproval, resultLineObj.Status);
            // TODO: Delete the lines created in this test
        }

        [TestMethod]
        public async Task GetAllLines()
        {
            var line1 = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            var createdLine1 = Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                }).Result;

            var line2 = Helper.GetValidLine(ProductId2, ProductType.Roadblock);
            var createdLine2 = await Helper.PostLineAndValidateAsync(LineUrl!, line2,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            var lines = await Helper.GetAllLinesAndValidateAsync(LineUrl!,
               async (response) =>
               {
                   Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                   await ValidateGetAllLines(response, new LineInput[] { line1!, line2! });
               });

            // TODO: Delete the lines created in this test
        }

        [TestMethod]
        public async Task GetAllLines_Paginated()
        {
            var line1 = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            var createdLine1 = Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                }).Result;

            var line2 = Helper.GetValidLine(ProductId2, ProductType.Roadblock);
            var createdLine2 = await Helper.PostLineAndValidateAsync(LineUrl!, line2,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            var paginatedLinesUrl = $"{LineUrl}?skip=1&top=1";
            var lines = await Helper.GetAllPaginatedLinesAndValidateAsync(paginatedLinesUrl!,
               async (response) =>
               {
                   Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                   await Task.CompletedTask;
               });
            Assert.IsNotNull(lines);
            Assert.IsTrue(lines.Count > lines!.Entity!.Length);
            Assert.IsTrue(lines.Entity!.Length == 1, "Top parameter not working as expected");
        }
        [TestMethod]
        public async Task GetAllLines_Sorted()
        {
            // Create 2 lines with predictable names and target impressions for sorting
            var line1 = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            line1.Name = "Line-A-" + Guid.NewGuid().ToString().Substring(0, 5);
            line1.TargetImpressions = 100;
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            var line2 = Helper.GetValidLine(ProductId2, ProductType.Roadblock);
            line2.Name = "Line-B-" + Guid.NewGuid().ToString().Substring(0, 5);
            line2.TargetImpressions = 200;
            var createdLine2 = await Helper.PostLineAndValidateAsync(LineUrl!, line2,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            // Validate sorting by name ascending
            var sortedByNameUrl = $"{LineUrl}?orderBy=Name";
            var lines = await Helper.GetAllLinesAndValidateAsync(sortedByNameUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });
            for (int i = 0; i < lines!.Length - 1; i++)
            {
                Assert.IsTrue(string.Compare(lines[i].Name, lines[i + 1].Name, StringComparison.Ordinal) <= 0);
            }

            // Validate sorting by target impressions ascending
            var sortedByImpressionsUrl = $"{LineUrl}?orderBy=EstimatedImpressions asc";
            lines = await Helper.GetAllLinesAndValidateAsync(sortedByImpressionsUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });
            for (int i = 0; i < lines!.Length - 1; i++)
            {
                Assert.IsTrue((lines[i].EstimatedImpressions ?? 0) <= (lines[i + 1].EstimatedImpressions ?? 0));
            }

            // Validate sorting by name descending
            var sortedByNameDescUrl = $"{LineUrl}?orderBy=Name desc";
            lines = await Helper.GetAllLinesAndValidateAsync(sortedByNameDescUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });
            for (int i = 0; i < lines!.Length - 1; i++)
            {
                Assert.IsTrue(string.Compare(lines[i].Name, lines[i + 1].Name, StringComparison.Ordinal) >= 0);
            }
        }

        [TestMethod]
        public async Task GetAllLines_Filtered()
        {
            // Use a base date within the media plan's range
            var baseDate = DateTime.UtcNow.Date.AddDays(3);

            // Line 1: Roadblock, TargetSpend only
            var line1 = Helper.GetValidLine(ProductId2, ProductType.Roadblock);
            line1.Name = "TestLineA";
            line1.StartDate = baseDate.AddDays(1);
            line1.EndDate = baseDate.AddDays(2);
            line1.TargetSpend = 200;
            line1.TargetImpressions = null;

            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            // Filter for line with name equal to "TestLineA"
            var request = $"{LineUrl}?filter=%5B%7B%22field%22:%22Name%22,%22operator%22:%22eq%22,%22value%22:%22TestLineA%22%7D%5D";
            var result = await Helper.GetAllLinesAndValidateAsync(request,
                async (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    await Task.CompletedTask;
                });
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Any(l => l.Name == "TestLineA"));

            // Filter for lines with StartDate greater than baseDate
            var startDateStr = baseDate.ToString("yyyy-MM-ddTHH:mm:ssZ");
            request = $"{LineUrl}?filter=%5B%7B%22field%22:%22StartDate%22,%22operator%22:%22gt%22,%22value%22:%22{Uri.EscapeDataString(startDateStr)}%22%7D%5D";
            result = await Helper.GetAllLinesAndValidateAsync(request,
                async (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    await Task.CompletedTask;
                });
            Assert.IsNotNull(result);
            foreach (var line in result)
            {
                Assert.IsTrue(line.StartDate > baseDate);
            }

            // Filter for lines with Budget less than 250
            request = $"{LineUrl}?filter=%5B%7B%22field%22:%22Budget%22,%22operator%22:%22lt%22,%22value%22:250%7D%5D";
            result = await Helper.GetAllLinesAndValidateAsync(request,
                async (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    await Task.CompletedTask;
                });
            Assert.IsNotNull(result);

            // Use multiple filters: StartDate >= baseDate AND TargetSpend >= 200
            request = $"{LineUrl}?filter=%5B%7B%22field%22:%22StartDate%22,%22operator%22:%22ge%22,%22value%22:%22{Uri.EscapeDataString(baseDate.ToString("yyyy-MM-ddTHH:mm:ssZ"))}%22%7D,%7B%22field%22:%22Budget%22,%22operator%22:%22ge%22,%22value%22:200%7D%5D";
            result = await Helper.GetAllLinesAndValidateAsync(request,
                async (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    await Task.CompletedTask;
                });
            Assert.IsNotNull(result);
            foreach (var line in result)
            {
                Assert.IsTrue(line.StartDate >= baseDate && line.TargetSpend >= 200);
            }
        }

        [TestMethod]
        public async Task GetSingleLine()
        {
            var line1 = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            var getLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var products = await Helper.GetLineAndValidateAsync(getLineUrl,
               async (response) =>
               {
                   await ValidateGetSingleLine(response, line1);
               });

            // TODO: Delete the line created in this test
        }

        [TestMethod]
        public async Task GetSingleLine_DeletedProduct()
        {
            var product = Helper.GetValidProduct_Basic(ProductType.Rotational);
            var result = await Helper.PostProductAndValidateAsync(ProductUrl!, product,
                (response) =>
                {
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            Assert.IsNotNull(result);
            var productId = result!.Id;

            var line1 = Helper.GetValidLine(productId, ProductType.Rotational);
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            //Delete underlying Product
            await Helper.DeleteProductAndValidateAsync(Helper.GetProductEndpoint(PublisherId, productId),
               (response) =>
               {
                   return Task.CompletedTask;
               });

            var getLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var line = await Helper.GetLineAndValidateAsync(getLineUrl,
               async (response) =>
               {
                   Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);

                   var responseString = await response.Content.ReadAsStringAsync();
                   LineOutput? actualLine = JsonSerializer.Deserialize<Result<LineOutput>>(responseString, Helper.serializerOptions)!.Entity;

                   Assert.IsNotNull(actualLine);

                   //TODO: once error/warning handling in response is closed, need to check that we are getting the Warning about the product deletion back.
               });

            // TODO: Delete the line created in this test
        }

        [TestMethod]
        public async Task UpdateLine()
        {
            var line1 = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            line1.TargetImpressions = null;
            line1.TargetSpend = 50;

            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            var line1Updated = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            line1Updated.Name = line1.Name;
            line1Updated.Description = "Updated-" + line1.Description;

            //Bug fix: switch from TargetSpend to TargetImpressions
            line1Updated.TargetImpressions = 15000;
            line1Updated.TargetSpend = null;

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var products = await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1Updated,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1);
               });

            // TODO: Delete the line created in this test
        }

        [TestMethod]
        public async Task UpdateLine_Cpd_MakeGoodLineType()
        {
            var line1 = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            var line1Updated = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            line1Updated.Cpd = 2.0;
            line1Updated.Cpm = null;
            line1Updated.TargetSpend = null;
            line1Updated.TargetImpressions = 1000;
            line1Updated.Type = LineType.MakeGood;

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var products = await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1Updated,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1);
               });

            // TODO: Delete the line created in this test
        }


        [TestMethod]
        public async Task UpdateLine_defaultLineType()
        {
            var line1 = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            line1.Type = LineType.Rotational;
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            var line1Updated = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            line1Updated.Type = LineType._;

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var products = await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1Updated,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1);
               });

            // TODO: Delete the line created in this test
        }

        [TestMethod]
        public async Task UpdateLine_AudienceTarget()
        {
            var line1 = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            line1.Targets = new Target[]
            {
                new AudienceTarget { IsExcluded = false, Ids = [38564514, 38564517], AudienceTargetingType = AudienceTargetingType.AND },
                new AudienceTarget { IsExcluded = true, Ids = [1, 38564516], AudienceTargetingType = AudienceTargetingType.AND }
            };

            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            //Validate AudienceTargetingType change
            var line1Updated = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            line1Updated.Name = "Updated-" + line1.Name;
            line1Updated.Description = "Updated-" + line1.Description;
            line1Updated.Targets = new Target[]
            {
                new AudienceTarget { IsExcluded = false, Ids = [38564514, 38564517], AudienceTargetingType = AudienceTargetingType.AND },
                new AudienceTarget { IsExcluded = true, Ids = [1, 38564516], AudienceTargetingType = AudienceTargetingType.OR }
            };

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1Updated,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1);
               });

            //Validate moving an "include" target to "exclude" and vice versa
            var line1Updated2 = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            line1Updated2.Name = "Updated2-" + line1.Name;
            line1Updated2.Description = "Updated2-" + line1.Description;
            line1Updated2.Targets = new Target[]
            {
                new AudienceTarget { IsExcluded = false, Ids = [38564514, 38564516], AudienceTargetingType = AudienceTargetingType.OR },
                new AudienceTarget { IsExcluded = true, Ids = [1, 38564517], AudienceTargetingType = AudienceTargetingType.AND }
            };

            await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1Updated2,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1);
               });

            // TODO: Delete the line created in this test
        }

        [TestMethod]
        public async Task UpdateLine_Status_PendingApprovalReset()
        {
            var line1 = GetValidLine(ProductId1);
            line1.Cpm = 2.0;
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            Assert.AreEqual(LineStatus.NeedApproval, createdLine1!.Status);

            var publisherApprovalEndpoint = Helper.GetLineApprovalsEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var publisherApproval = await Helper.PostLineApprovalsAndValidateAsync(publisherApprovalEndpoint,
                async (response) => {
                    await Task.CompletedTask;
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                });

            var lineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var lineGet = await Helper.GetLineAndValidateAsync(lineUrl,
               async (response) =>
               {
                   await Task.CompletedTask;
                   Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
               });

            Assert.AreEqual(LineStatus.PendingApproval, lineGet!.Status);

            var line1Updated = GetValidLine(ProductId1);
            line1Updated.Name = "Updated-" + line1.Name;
            line1Updated.Description = "Updated-" + line1.Description;
            line1Updated.Cpm = 2.1;

            var updatedLine = await Helper.UpdateLineAndValidateAsync(lineUrl, line1Updated,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1);
               });

            Assert.AreEqual(LineStatus.NeedApproval, updatedLine!.Status);

            // TODO: Delete the line created in this test
        }

        [TestMethod]
        public async Task UpdateLine_Status_PendingApprovalReset2()
        {
            //We will only change description and TargetImpressions, which should reset the status
            var line1 = GetValidLine(ProductId1);
            line1.Cpm = 2.0;
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            Assert.AreEqual(LineStatus.NeedApproval, createdLine1!.Status);

            var publisherApprovalEndpoint = Helper.GetLineApprovalsEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var publisherApproval = await Helper.PostLineApprovalsAndValidateAsync(publisherApprovalEndpoint,
                async (response) => {
                    await Task.CompletedTask;
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                });

            var lineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var lineGet = await Helper.GetLineAndValidateAsync(lineUrl,
               async (response) =>
               {
                   await Task.CompletedTask;
                   Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
               });

            Assert.AreEqual(LineStatus.PendingApproval, lineGet!.Status);

            var line1Updated = GetValidLine(ProductId1);
            line1Updated.Name = line1.Name;
            line1Updated.Description = "Updated-" + line1.Description;
            line1Updated.Cpm = 2.0;
            line1Updated.TargetImpressions = line1.TargetImpressions + 20;

            var updatedLine = await Helper.UpdateLineAndValidateAsync(lineUrl, line1Updated,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1);
               });

            Assert.AreEqual(LineStatus.NeedApproval, updatedLine!.Status);

            // TODO: Delete the line created in this test
        }

        [TestMethod]
        public async Task UpdateLine_Status_PendingApprovalNotReset()
        {
            var line1 = GetValidLine(ProductId1);
            line1.Cpm = 2.0;
            line1.Targets = [];
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                },
                (result) => Assert.IsTrue(result!.Warnings.Any(_ => _.Message == ErrorMessage.PublisherApprovalRequired)));

            Assert.AreEqual(LineStatus.NeedApproval, createdLine1!.Status);

            var publisherApprovalEndpoint = Helper.GetLineApprovalsEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var publisherApproval = await Helper.PostLineApprovalsAndValidateAsync(publisherApprovalEndpoint,
                async (response) => {
                    await Task.CompletedTask;
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                });

            var lineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var lineGet = await Helper.GetLineAndValidateAsync(lineUrl,
               async (response) =>
               {
                   await Task.CompletedTask;
                   Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
               });

            Assert.AreEqual(LineStatus.PendingApproval, lineGet!.Status);

            var line1Updated = GetValidLine(ProductId1);
            line1Updated.Name = line1.Name;
            line1Updated.Description = "Updated-" + line1.Description;
            line1Updated.StartDate = line1.StartDate;
            line1Updated.EndDate = line1.EndDate;
            line1Updated.Cpm = 2.0;
            line1Updated.Targets = [];

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var updatedLine = await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1Updated,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1);
               },
               (result) => Assert.IsFalse(result!.Warnings.Any(_ => _.Message == ErrorMessage.PublisherApprovalRequired)));

            Assert.AreEqual(LineStatus.PendingApproval, updatedLine!.Status);

            // TODO: Delete the line created in this test
        }

        [TestMethod]
        public async Task UpdateLine_Status_DraftToNeedApprovalWithNoCriticalChange()
        {
            var line1 = GetValidLine(ProductId1);
            line1.Cpm = 2.0;
            line1.InputStatus = LineInputStatus.Draft;
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            Assert.AreEqual(LineStatus.Draft, createdLine1!.Status);

            var line1Updated = GetValidLine(ProductId1);
            line1Updated.Name = "Updated-" + line1.Name;
            line1Updated.Description = "Updated-" + line1.Description;
            line1Updated.StartDate = line1.StartDate;
            line1Updated.EndDate = line1.EndDate;
            line1Updated.Cpm = 2.0;

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var updatedLine = await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1Updated,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1Updated);
               });

            Assert.AreEqual(LineStatus.NeedApproval, updatedLine!.Status);

            // TODO: Delete the line created in this test
        }

        //This test is used to confirm no PublisherApproval records are generated upon Draft Line update when staying in Draft state
        [TestMethod]
        public async Task UpdateLine_Status_DraftToDraft()
        {
            var line1 = GetDraftLine(ProductId1);
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            Assert.AreEqual(LineStatus.Draft, createdLine1!.Status);

            var line1Updated = GetDraftLine(ProductId1);
            line1Updated.Name = "Updated-" + line1.Name;
            line1Updated.Description = "Updated-" + line1.Description;

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var updatedLine = await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1Updated,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1);
               });

            Assert.AreEqual(LineStatus.Draft, updatedLine!.Status);

            // TODO: Delete the line created in this test
        }

        [TestMethod]
        public async Task UpdateLine_Status_DraftToApproved()
        {
            var line1 = GetDraftLine(ProductId1);
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            Assert.AreEqual(LineStatus.Draft, createdLine1!.Status);

            var line1Updated = GetValidLine(ProductId1);
            line1Updated.Name = "Updated-" + line1.Name;
            line1Updated.Description = "Updated-" + line1.Description;

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var updatedLine = await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1Updated,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1);
               });

            Assert.AreEqual(LineStatus.Approved, updatedLine!.Status);

            // TODO: Delete the line created in this test
        }

        [TestMethod]
        public async Task UpdateLine_Status_ApprovedToDraft()
        {
            var line1 = GetValidLine(ProductId1);
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            Assert.AreEqual(LineStatus.Approved, createdLine1!.Status);

            var line1Updated = GetValidLine(ProductId1);
            line1Updated.InputStatus = LineInputStatus.Draft;

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var updatedLine = await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1Updated,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1);
               });

            Assert.AreEqual(LineStatus.Draft, updatedLine!.Status);

            // TODO: Delete the line created in this test
        }

        [TestMethod]
        public async Task UpdateLine_Status_ApprovedToNeedApproval_Cpm()
        {
            var line1 = GetValidLine(ProductId1);
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            Assert.AreEqual(LineStatus.Approved, createdLine1!.Status);

            var line1Updated = GetValidLine(ProductId1);
            line1Updated.Cpm = 2.1;

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var updatedLine = await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1Updated,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1);
               });

            Assert.AreEqual(LineStatus.NeedApproval, updatedLine!.Status);

            // TODO: Delete the line created in this test
        }

        [TestMethod]
        public async Task UpdateLine_Status_Draft_NA_PA_NA_Draft()
        {
            var line1 = GetValidLine(ProductId1);
            line1.Cpm = 2.0;
            line1.InputStatus = LineInputStatus.Draft;
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            Assert.AreEqual(LineStatus.Draft, createdLine1!.Status);

            var line1Updated = GetValidLine(ProductId1);
            line1Updated.Name = "Updated-" + line1.Name;
            line1Updated.Description = "Updated-" + line1.Description;
            line1Updated.StartDate = line1.StartDate;
            line1Updated.EndDate = line1.EndDate;
            line1Updated.Cpm = 2.0;

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var updatedLine = await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1Updated,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1Updated);
               });

            Assert.AreEqual(LineStatus.NeedApproval, updatedLine!.Status);

            var publisherApprovalEndpoint = Helper.GetLineApprovalsEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var publisherApproval = await Helper.PostLineApprovalsAndValidateAsync(publisherApprovalEndpoint,
                async (response) => {
                    await Task.CompletedTask;
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                });

            var lineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var lineGet = await Helper.GetLineAndValidateAsync(lineUrl,
               async (response) =>
               {
                   await Task.CompletedTask;
                   Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
               });

            Assert.AreEqual(LineStatus.PendingApproval, lineGet!.Status);

            var line1Updated2 = GetValidLine(ProductId1);
            line1Updated2.Name = line1Updated.Name;
            line1Updated2.Description = line1Updated.Description;
            line1Updated2.StartDate = line1Updated.StartDate;
            line1Updated2.EndDate = line1Updated.EndDate!.Value.AddDays(1);
            line1Updated2.Cpm = 2.0;

            var updatedLine2 = await Helper.UpdateLineAndValidateAsync(lineUrl, line1Updated2,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1Updated2);
               });

            Assert.AreEqual(LineStatus.NeedApproval, updatedLine2!.Status);

            var line1Updated3 = GetValidLine(ProductId1);
            line1Updated3.Name = line1Updated.Name;
            line1Updated3.Description = line1Updated.Description;
            line1Updated3.StartDate = line1Updated2.StartDate;
            line1Updated3.EndDate = line1Updated2.EndDate;
            line1Updated3.Cpm = 2.1;
            line1Updated3.InputStatus = LineInputStatus.Draft;

            var updatedLine3 = await Helper.UpdateLineAndValidateAsync(lineUrl, line1Updated3,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1Updated3);
               });

            Assert.AreEqual(LineStatus.Draft, updatedLine3!.Status);

            // TODO: Delete the line created in this test
        }

        [TestMethod]
        public async Task UpdateLine_Status_MediaPlanStatusReset()
        {
            var line1 = GetValidLine(ProductId1);
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            Assert.AreEqual(LineStatus.Approved, createdLine1!.Status);

            //Update media plan to "Pitched"
            var mediaPlanGetUrl = Helper.GetMediaPlanEndpoint(CustomerId, MediaPlanId);
            var mediaPlanOutput = await Helper.GetMediaPlanAndValidateAsync(mediaPlanGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            var updatedMediaPlan = new MediaPlanInput
            {
                Name = mediaPlanOutput.Name,
                Description = mediaPlanOutput.Description,
                StartDate = mediaPlanOutput.StartDate,
                EndDate = mediaPlanOutput.EndDate,
                TargetSpend = mediaPlanOutput.TargetSpend,
                CurrencyCode = mediaPlanOutput.CurrencyCode,
                Contact = mediaPlanOutput.Contact,
                InputStatus = MediaPlanInputStatus.Pitched
            };

            await Helper.PutMediaPlanAndValidateAsync(mediaPlanGetUrl, updatedMediaPlan,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            mediaPlanOutput = await Helper.GetMediaPlanAndValidateAsync(mediaPlanGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.AreEqual(MediaPlanStatus.Pitched, mediaPlanOutput.Status);

            var line1Updated = GetValidLine(ProductId1);
            line1Updated.Cpm = 3.4;

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var updatedLine = await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1Updated,
               async (response) =>
               {
                   await ValidateUpdateLine(response, line1);
               });

            Assert.AreEqual(LineStatus.Approved, updatedLine!.Status);

            mediaPlanOutput = await Helper.GetMediaPlanAndValidateAsync(mediaPlanGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.AreEqual(MediaPlanStatus.Draft, mediaPlanOutput.Status);

            // TODO: Delete the line created in this test
        }

        [TestMethod]
        public async Task UpdateLine_CalendarSlots_MediaPlanStatusReset()
        {
            // Create two lines for the same product
            var lineIds = new long[2];
            var line = GetValidLine(ProductId2);
            var lineOutput = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });
            lineIds[0] = lineOutput!.Id;

            line = GetValidLine(ProductId2);
            lineOutput = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });
            lineIds[1] = lineOutput!.Id;

            //Update media plan to "Pitched"
            var mediaPlanGetUrl = Helper.GetMediaPlanEndpoint(CustomerId, MediaPlanId);
            var mediaPlanOutput = await Helper.GetMediaPlanAndValidateAsync(mediaPlanGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            var updatedMediaPlan = new MediaPlanInput
            {
                Name = mediaPlanOutput.Name,
                Description = mediaPlanOutput.Description,
                StartDate = mediaPlanOutput.StartDate,
                EndDate = mediaPlanOutput.EndDate,
                TargetSpend = mediaPlanOutput.TargetSpend,
                CurrencyCode = mediaPlanOutput.CurrencyCode,
                Contact = mediaPlanOutput.Contact,
                InputStatus = MediaPlanInputStatus.Pitched
            };

            await Helper.PutMediaPlanAndValidateAsync(mediaPlanGetUrl, updatedMediaPlan,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            mediaPlanOutput = await Helper.GetMediaPlanAndValidateAsync(mediaPlanGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.AreEqual(MediaPlanStatus.Pitched, mediaPlanOutput.Status);

            // Check CalendarSlots for the Lines are also Pitched
            var calendarGetUrl = Helper.GetCalendarEndpoint(PublisherId, mediaPlanOutput.StartDate, mediaPlanOutput.EndDate, ProductId2);
            var calendarSlots = await Helper.GetCalendarSlotsAndValidateAsync(calendarGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });
            var linkedSlots = calendarSlots!.Where(slot => slot.LinkedLines != null && slot.LinkedLines.Any(linkedLine => lineIds.Contains(linkedLine.LineId))).ToList();
            foreach (var slot in linkedSlots)
            {
                Assert.AreEqual(CalendarStatus.Pitched, slot.Status);
            }

            var lineUpdated = GetValidLine(ProductId2);
            lineUpdated.Cpd = 3.4;

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, lineIds[0]);
            var updatedLine = await Helper.UpdateLineAndValidateAsync(updateLineUrl, lineUpdated,
               async (response) =>
               {
                   await ValidateUpdateLine(response, lineUpdated);
               });

            Assert.AreEqual(LineStatus.NeedApproval, updatedLine!.Status);

            mediaPlanOutput = await Helper.GetMediaPlanAndValidateAsync(mediaPlanGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.AreEqual(MediaPlanStatus.Draft, mediaPlanOutput.Status);

            // Check CalendarSlots for the Lines are now Available
            calendarGetUrl = Helper.GetCalendarEndpoint(PublisherId, mediaPlanOutput.StartDate, mediaPlanOutput.EndDate, ProductId2);
            calendarSlots = await Helper.GetCalendarSlotsAndValidateAsync(calendarGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });
            linkedSlots = calendarSlots!.Where(slot => slot.LinkedLines != null && slot.LinkedLines.Any(linkedLine => lineIds.Contains(linkedLine.LineId))).ToList();
            foreach (var slot in linkedSlots)
            {
                Assert.AreEqual(CalendarStatus.Available, slot.Status);
            }

            // Delete the Lines created in this test
            var deleteLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, lineIds[0]);
            await Helper.DeleteLineAndValidateAsync(deleteLineUrl,
               async (response) =>
               {
                   await ValidateLineDelete(response);
               });

            deleteLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, lineIds[1]);
            await Helper.DeleteLineAndValidateAsync(deleteLineUrl,
               async (response) =>
               {
                   await ValidateLineDelete(response);
               });
        }

        [TestMethod]
        public async Task UpdateLine_CalendarSlots_MediaPlanStatusProgress()
        {
            // Create two lines for the same product
            var lineIds = new long[2];
            var line = GetValidLine(ProductId2);
            var lineOutput = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });
            lineIds[0] = lineOutput!.Id;

            line = GetValidLine(ProductId2);
            lineOutput = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });
            lineIds[1] = lineOutput!.Id;

            // Get the MediaPlan 
            var mediaPlanGetUrl = Helper.GetMediaPlanEndpoint(CustomerId, MediaPlanId);
            var mediaPlanOutput = await Helper.GetMediaPlanAndValidateAsync(mediaPlanGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            //Update media plan to "Pitched"
            var updatedMediaPlan = new MediaPlanInput
            {
                Name = mediaPlanOutput.Name,
                Description = mediaPlanOutput.Description,
                StartDate = mediaPlanOutput.StartDate,
                EndDate = mediaPlanOutput.EndDate,
                TargetSpend = mediaPlanOutput.TargetSpend,
                CurrencyCode = mediaPlanOutput.CurrencyCode,
                Contact = mediaPlanOutput.Contact,
                InputStatus = MediaPlanInputStatus.Pitched
            };

            await Helper.PutMediaPlanAndValidateAsync(mediaPlanGetUrl, updatedMediaPlan,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            mediaPlanOutput = await Helper.GetMediaPlanAndValidateAsync(mediaPlanGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.AreEqual(MediaPlanStatus.Pitched, mediaPlanOutput.Status);

            // Check CalendarSlots for the Lines are also Pitched
            var calendarGetUrl = Helper.GetCalendarEndpoint(PublisherId, mediaPlanOutput.StartDate, mediaPlanOutput.EndDate, ProductId2);
            var calendarSlots = await Helper.GetCalendarSlotsAndValidateAsync(calendarGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });
            var linkedSlots = calendarSlots!.Where(slot => slot.LinkedLines != null && slot.LinkedLines.Any(linkedLine => lineIds.Contains(linkedLine.LineId))).ToList();
            foreach (var slot in linkedSlots)
            {
                Assert.AreEqual(CalendarStatus.Pitched, slot.Status);
            }

            //Update media plan to "VerbalCommit"
            updatedMediaPlan = new MediaPlanInput
            {
                Name = mediaPlanOutput.Name,
                Description = mediaPlanOutput.Description,
                StartDate = mediaPlanOutput.StartDate,
                EndDate = mediaPlanOutput.EndDate,
                TargetSpend = mediaPlanOutput.TargetSpend,
                CurrencyCode = mediaPlanOutput.CurrencyCode,
                Contact = mediaPlanOutput.Contact,
                InputStatus = MediaPlanInputStatus.VerbalCommit
            };

            await Helper.PutMediaPlanAndValidateAsync(mediaPlanGetUrl, updatedMediaPlan,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            mediaPlanOutput = await Helper.GetMediaPlanAndValidateAsync(mediaPlanGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.AreEqual(MediaPlanStatus.VerbalCommit, mediaPlanOutput.Status);

            // Check CalendarSlots for the Lines are also VerbalCommit
            calendarGetUrl = Helper.GetCalendarEndpoint(PublisherId, mediaPlanOutput.StartDate, mediaPlanOutput.EndDate, ProductId2);
            calendarSlots = await Helper.GetCalendarSlotsAndValidateAsync(calendarGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });
            linkedSlots = calendarSlots!.Where(slot => slot.LinkedLines != null && slot.LinkedLines.Any(linkedLine => lineIds.Contains(linkedLine.LineId))).ToList();
            foreach (var slot in linkedSlots)
            {
                Assert.AreEqual(CalendarStatus.VerbalCommit, slot.Status);
            }

            //Update media plan to "CustomerApproved"
            updatedMediaPlan = new MediaPlanInput
            {
                Name = mediaPlanOutput.Name,
                Description = mediaPlanOutput.Description,
                StartDate = mediaPlanOutput.StartDate,
                EndDate = mediaPlanOutput.EndDate,
                TargetSpend = mediaPlanOutput.TargetSpend,
                CurrencyCode = mediaPlanOutput.CurrencyCode,
                Contact = mediaPlanOutput.Contact,
                InputStatus = MediaPlanInputStatus.CustomerApproved
            };

            await Helper.PutMediaPlanAndValidateAsync(mediaPlanGetUrl, updatedMediaPlan,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            mediaPlanOutput = await Helper.GetMediaPlanAndValidateAsync(mediaPlanGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.AreEqual(MediaPlanStatus.CustomerApproved, mediaPlanOutput.Status);

            // Check CalendarSlots for the Lines are also CustomerApproved
            calendarGetUrl = Helper.GetCalendarEndpoint(PublisherId, mediaPlanOutput.StartDate, mediaPlanOutput.EndDate, ProductId2);
            calendarSlots = await Helper.GetCalendarSlotsAndValidateAsync(calendarGetUrl,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });
            linkedSlots = calendarSlots!.Where(slot => slot.LinkedLines != null && slot.LinkedLines.Any(linkedLine => lineIds.Contains(linkedLine.LineId))).ToList();
            foreach (var slot in linkedSlots)
            {
                Assert.AreEqual(CalendarStatus.CustomerApproved, slot.Status);
            }

            // Delete the lines created in this test
            var deleteLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, lineIds[0]);
            await Helper.DeleteLineAndValidateAsync(deleteLineUrl,
               async (response) =>
               {
                   await ValidateLineDelete(response);
               });

            deleteLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, lineIds[1]);
            await Helper.DeleteLineAndValidateAsync(deleteLineUrl,
               async (response) =>
               {
                   await ValidateLineDelete(response);
               });
        }

        [TestMethod]
        public async Task DeleteLine()
        {
            var line1 = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask; // Ensure a Task is always returned
                });

            var line1Updated = Helper.GetValidLine(ProductId1, ProductType.Rotational);
            line1Updated.Name = "Updated-" + line1.Name;
            line1Updated.Description = "Updated-" + line1.Description;

            var deleteLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            await Helper.DeleteLineAndValidateAsync(deleteLineUrl,
               async (response) =>
               {
                   await ValidateLineDelete(response);
               });
        }

        [TestMethod]
        public async Task ReserveLine_Create()
        {
            // Line is created when requesting PublisherApproval, but we get warning and isReserved flag is set to false
            var product = Helper.GetValidProduct_Basic(ProductType.Roadblock, baseCpmRate: 0);
            var productOutput = await Helper.PostProductAndValidateAsync(ProductUrl!, product,
                async (response) =>
                {
                    await Task.CompletedTask;
                });
            var line1 = Helper.GetValidLine(productOutput!.Id, ProductType.Roadblock);
            line1.IsReserved = true;

            var createdLine = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                async (response) =>
                {
                    line1.IsReserved = false;
                    await ValidateGetSingleLine(response, line1, HttpStatusCode.Created, ErrorMessage.OperationNotAllowed);
                });

            var getLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine!.Id);
            var getLineResult = await Helper.GetLineAndValidateAsync(getLineUrl,
                async (response) =>
                {
                    await ValidateGetSingleLine(response, line1);
                });

        }

        [TestMethod]
        public async Task ReserveLine_Update()
        {
            // Create line with isRserved set to false
            var product = Helper.GetValidProduct_Basic(ProductType.Roadblock, baseCpmRate: 0);
            var productOutput = await Helper.PostProductAndValidateAsync(ProductUrl!, product,
                async (response) =>
                {
                    await Task.CompletedTask;
                });
            var line1 = Helper.GetValidLine(productOutput!.Id, ProductType.Roadblock);
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                async (response) =>
                {
                    await ValidateGetSingleLine(response, line1, HttpStatusCode.Created);
                });

            // Try enabling isReserved. Call should be successful but should have warnign and isreverved flag false
            line1.IsReserved = true;
            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1,
               async (response) =>
               {
                   line1.IsReserved = false;
                   await ValidateGetSingleLine(response, line1, expectedWarning: ErrorMessage.OperationNotAllowed);
               });

            // Get Publisher Approval for the line
            await Helper.GetPublisherApprovalForLine(CustomerId, MediaPlanId, createdLine1.Id, PublisherId);

            // Try adding isReserved again. Should succeed with isReserved true
            line1.IsReserved = true;
            await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1,
               async (response) =>
               {
                   await ValidateGetSingleLine(response, line1);
               });
        }

        [TestMethod]
        public async Task ReserveLine_AvailabilityChecks()
        {
            var reservedLine = await CreateReservedLine();

            // Create second line, should fail
            var line2 = Helper.GetValidLine(reservedLine.Product!.Id, ProductType.Roadblock);
            await Helper.PostLineAndValidateAsync(LineUrl!, line2,
                async (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.BadRequest, response.StatusCode);
                    await Task.CompletedTask; // Ensure a Task is always returned
                });

            // Update first line to cancel reservation
            var line1Updated = Helper.GetValidLine(reservedLine.Product!.Id, ProductType.Roadblock);
            line1Updated.Name = "Updated-" + reservedLine.Name;
            line1Updated.Description = "Updated-" + reservedLine.Description;
            line1Updated.IsReserved = false;

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, reservedLine!.Id);
            await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1Updated,
               async (response) =>
               {
                   Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                   await Task.CompletedTask;
               });

            // Try creating with line 2. Should succeed
            line2 = Helper.GetValidLine(reservedLine.Product!.Id, ProductType.Roadblock);
            await Helper.PostLineAndValidateAsync(LineUrl!, line2,
                async (response) =>
                {
                    await ValidateGetSingleLine(response, line2, HttpStatusCode.Created);
                });
        }

        [TestMethod]
        public async Task ReserveLine_AvailabilityChecks_WithDelete()
        {
            var reservedLine = await CreateReservedLine();

            // Create second line, should fail
            var line2 = Helper.GetValidLine(reservedLine.Product.Id, ProductType.Roadblock);
            await Helper.PostLineAndValidateAsync(LineUrl!, line2,
                async (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.BadRequest, response.StatusCode);
                    await Task.CompletedTask; // Ensure a Task is always returned
                });

            // Delete first line 
            var deleteLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, reservedLine!.Id);
            await Helper.DeleteLineAndValidateAsync(deleteLineUrl,
               async (response) =>
               {
                   Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                   await Task.CompletedTask;
               });

            // Try creating with line 2. Should succeed
            line2 = Helper.GetValidLine(reservedLine.Product!.Id, ProductType.Roadblock);
            await Helper.PostLineAndValidateAsync(LineUrl!, line2,
                async (response) =>
                {
                    await ValidateGetSingleLine(response, line2, HttpStatusCode.Created);
                });
        }

        [TestMethod]
        public async Task CreateLine_WithEvenlyPacing_ShouldSucceedForRotational()
        {
            // Explicitly define supported targets
            var rotationalProduct = Helper.GetValidProduct_Basic(ProductType.Rotational);
            rotationalProduct.SupportedTargets = new SupplyTarget[]
            {
                new SupplyTarget { TargetTypeId = TargetType.Device, TargetPremium = 1.0 },
                new SupplyTarget { TargetTypeId = TargetType.Audience, TargetPremium = 2.0 }
            };

            // Post product
            var createdProduct = await Helper.PostProductAndValidateAsync(ProductUrl!, rotationalProduct,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            var rotationalProductId = createdProduct!.Id;

            // Get valid line with Evenly pacing
            var line = Helper.GetValidLine(rotationalProductId, ProductType.Rotational, BudgetScheduleType.Evenly);

            // Post line
            var createdLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.IsNotNull(createdLine);
            Assert.AreEqual(BudgetScheduleType.Evenly, createdLine.BudgetScheduleType);
        }

        [TestMethod]
        public async Task UpdateLine_ChangePacingFromASAPToEvenly_ShouldSucceed()
        {
            var line = Helper.GetValidLine(ProductId1, ProductType.Rotational, BudgetScheduleType.ASAP);

            var createdLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            var lineToUpdate = Helper.GetValidLine(ProductId1, ProductType.Rotational, BudgetScheduleType.Evenly);
            lineToUpdate.Id = createdLine!.Id;
            lineToUpdate.Name = createdLine.Name + "_Updated";

            var updatedLine = await Helper.UpdateLineAndValidateAsync(
                Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine.Id),
                lineToUpdate,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.IsNotNull(updatedLine);
            Assert.AreEqual(BudgetScheduleType.Evenly, updatedLine!.BudgetScheduleType);
        }

        [TestMethod]
        public async Task Post_ValidationPass_EndDateWithinTimeZoneOffset()
        {
            // Create Media Plan with PST time zone
            var mediaPlanUrl = Helper.GetMediaPlanEndpoint(CustomerId);
            var mediaPlan = Helper.GetValidMediaPlan("Media Plan PST zone");
            mediaPlan.StartDate = DateTime.UtcNow.Date.AddDays(3);
            mediaPlan.EndDate = DateTime.UtcNow.Date.AddDays(4);
            var mediaPlanOutput = await Helper.PostMediaPlanAndValidateAsync(mediaPlanUrl, mediaPlan,
                async (response) =>
                {
                    await Task.CompletedTask;
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                });

            var mediaPlanId = mediaPlanOutput!.Id;

            // Create Line with EST time zone that is adjusted to still pass within the Media Plan range
            var productUrl = Helper.GetProductEndpoint(PublisherId);
            var productToCreate = Helper.GetValidProduct_Basic(ProductType.Rotational);
            var product = Helper.PostProductAndValidateAsync(productUrl!, productToCreate,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                }).Result;
            var productId = product!.Id;

            var line = Helper.GetValidLine(productId, ProductType.Rotational);
            line.StartDate = DateTime.UtcNow.Date.AddDays(3).AddHours(3); // EST -> still within plan after offset
            line.EndDate = DateTime.UtcNow.Date.AddDays(4).AddHours(-2); // EST -> before PST EndDate after conversion

            var lineUrl = Helper.GetLineEndpoint(CustomerId, mediaPlanId);
            var resultLine = await Helper.PostLineAndValidateAsync(lineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode); // Expecting success
                    return Task.CompletedTask;
                });

            // Validate the Line is within the Media Plan's date range
            Assert.IsNotNull(resultLine);
            Assert.IsTrue(resultLine!.StartDate >= mediaPlan.StartDate, $"Line start date {resultLine.StartDate} is not within Media Plan start date {mediaPlan.StartDate}");
            Assert.IsTrue(resultLine!.EndDate <= mediaPlan.EndDate, $"Line end date {resultLine.EndDate} is not within Media Plan end date {mediaPlan.EndDate}");
        }

        [TestMethod]
        public async Task PostLineWithCountryLocationTarget_ShouldSucceed()
        {
            var product = await CreateLocationTargetableProductAsync();
            var line = Helper.GetLineWithLocationTarget(product.Id, countryIds: new[] { 40 });

            var createdLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.IsNotNull(createdLine?.Targets);
            var target = createdLine.Targets!.OfType<LocationTarget>().First();
            Assert.AreEqual(1, target.Locations!.Length);
            Assert.AreEqual(TargetType.Country, target.Locations[0].Type);
        }

        [TestMethod]
        public async Task PostLineWithRegionLocationTarget_ShouldSucceed()
        {
            var product = await CreateLocationTargetableProductAsync();
            var line = Helper.GetLineWithLocationTarget(product.Id, regionIds: new[] { 10 });

            var createdLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            var target = createdLine!.Targets!.OfType<LocationTarget>().First();
            Assert.AreEqual(TargetType.Region, target.Locations![0].Type);
        }

        [TestMethod]
        public async Task PostLineWithCityLocationTarget_ShouldSucceed()
        {
            var product = await CreateLocationTargetableProductAsync();
            var line = Helper.GetLineWithLocationTarget(product.Id, cityIds: new[] { 101111 });

            var createdLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            var target = createdLine!.Targets!.OfType<LocationTarget>().First();
            Assert.AreEqual(TargetType.City, target.Locations![0].Type);
        }

        [TestMethod]
        public async Task PostLineWithCitiesLocationTarget_ShouldSucceed()
        {
            var product = await CreateLocationTargetableProductAsync();
            var line = Helper.GetLineWithLocationTarget(product.Id, cityIds: new[] { 101111, 101100 });

            var createdLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            var target = createdLine!.Targets!.OfType<LocationTarget>().First();
            Assert.AreEqual(TargetType.City, target.Locations![0].Type);
        }

        [TestMethod]
        public async Task PostLineWithCountryAndCityLocationTarget_ShouldSucceed()
        {
            var product = await CreateLocationTargetableProductAsync();
            var line = Helper.GetLineWithLocationTarget(product.Id, countryIds: new[] { 233 }, cityIds: new[] { 101111 });

            var createdLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.IsNotNull(createdLine?.Targets);
            var target = createdLine.Targets!.OfType<LocationTarget>().First();
            Assert.AreEqual(2, target.Locations!.Length);

            // Validate presence of both country and city
            Assert.IsTrue(target.Locations.Any(l => l.Type == TargetType.Country && l.Id == 233));
            Assert.IsTrue(target.Locations.Any(l => l.Type == TargetType.City && l.Id == 101111));
        }

        [TestMethod]
        public async Task PostLineWithRegionAndCityLocationTarget_ShouldSucceed()
        {
            var product = await CreateLocationTargetableProductAsync();
            var line = Helper.GetLineWithLocationTarget(product.Id, regionIds: new[] { 10 }, cityIds: new[] { 101111 });

            var createdLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.IsNotNull(createdLine?.Targets);
            var target = createdLine.Targets!.OfType<LocationTarget>().First();
            Assert.AreEqual(2, target.Locations!.Length);

            // Validate presence of both region and city
            Assert.IsTrue(target.Locations.Any(l => l.Type == TargetType.Region && l.Id == 10));
            Assert.IsTrue(target.Locations.Any(l => l.Type == TargetType.City && l.Id == 101111));
        }

        [TestMethod]
        public async Task PostLine_WithStringKeyValueTarget_ShouldSucceed()
        {
            if (IsKVPTargetingEnabled)
            {
                var productToCreate = Helper.GetValidProduct_Basic(ProductType.Rotational);
                productToCreate.SupportedTargets = new SupplyTarget[]
                {
                new SupplyTarget { TargetTypeId = TargetType.KeyValue, TargetPremium = 1.0 }
                };

                var product = await Helper.PostProductAndValidateAsync(ProductUrl!, productToCreate,
                    (response) =>
                    {
                        Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                        return Task.CompletedTask;
                    });

                var productId = product!.Id;

                var keyValueEntries = new[]
                {
                    new KeyValueEntry
                    {
                        Key = "device_type",
                        Values = new[] { "mobileweb" },
                        IsExcluded = false
                    }
                };

                var line = Helper.GetLineWithKeyValueTarget(productId, keyValueEntries);

                var createdLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                    (response) =>
                    {
                        Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                        return Task.CompletedTask;
                    });

                Assert.IsNotNull(createdLine?.Targets);
                var kvTarget = createdLine.Targets!.OfType<KeyValueTarget>().FirstOrDefault();
                Assert.IsNotNull(kvTarget, "KeyValue Target should be present on the line.");
                Assert.AreEqual(1, kvTarget.Entries.Length);
                Assert.AreEqual(LineStatus.Approved, createdLine.Status);
            }
            else 
            {
                Assert.Inconclusive("KVP Targeting is not enabled. Skipping test.");
                return;
            }
        }

        [TestMethod]
        public async Task PostLine_WithExcludeKeyValueTarget_ShouldSucceed()
        {
            var productToCreate = Helper.GetValidProduct_Basic(ProductType.Rotational);
            productToCreate.SupportedTargets = new SupplyTarget[]
            {
                new SupplyTarget { TargetTypeId = TargetType.KeyValue, TargetPremium = 1.5 }
            };

            var product = await Helper.PostProductAndValidateAsync(ProductUrl!, productToCreate,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            var productId = product!.Id;

            var keyValueEntries = new[]
            {
                new KeyValueEntry
                {
                    Key = "pub",
                    Values = new[] { "msn" },
                    IsExcluded = true
                }
            };
            
            var line = Helper.GetLineWithKeyValueTarget(productId, keyValueEntries, isExcluded: true);

            var createdLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.IsNotNull(createdLine?.Targets);
            var kvTarget = createdLine.Targets!.OfType<KeyValueTarget>().FirstOrDefault();
            Assert.IsNotNull(kvTarget, "KeyValue Target should be present on the line.");
            Assert.AreEqual(1, kvTarget.Entries.Length);
            Assert.AreEqual(LineStatus.Approved, createdLine.Status);
        }

        [TestMethod]
        public async Task PostLine_WithMultipleKeyValueTargets_ShouldSucceed()
        {
            var productToCreate = Helper.GetValidProduct_Basic(ProductType.Rotational);
            productToCreate.SupportedTargets = new SupplyTarget[]
            {
                new SupplyTarget { TargetTypeId = TargetType.KeyValue, TargetPremium = 1.0 }
            };

            var product = await Helper.PostProductAndValidateAsync(ProductUrl!, productToCreate,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            var productId = product!.Id;

            var line = Helper.GetValidLine(productId, ProductType.Rotational);
            line.Targets = new Target[]
            {
                new KeyValueTarget
                {
                    Entries = new[]
                    {
                        new KeyValueEntry { Key = "device_type", Values = new[] { "mobileweb" }, IsExcluded = false },
                        new KeyValueEntry { Key = "device_type",  Values = new[] { "desktopweb" }, IsExcluded = false },
                        new KeyValueEntry { Key = "pub",  Values = new[] { "msn" }, IsExcluded = false },
                        new KeyValueEntry { Key = "pub",  Values = new[] { "outlook" }, IsExcluded = false }
                    }
                }
            };

            var createdLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.IsNotNull(createdLine?.Targets);
            var kvTarget = createdLine.Targets!.OfType<KeyValueTarget>().FirstOrDefault();
            Assert.IsNotNull(kvTarget, "KeyValue Target should be present on the line.");
            Assert.AreEqual(4, kvTarget.Entries.Length);
            
            // Check for the presence of each key value entry type
            Assert.AreEqual(LineStatus.Approved, createdLine.Status);
        }

        [TestMethod]
        public async Task UpdateLine_AddKeyValueTarget_ShouldSucceed()
        {
            // First create a product that supports KeyValue targeting
            var productToCreate = Helper.GetValidProduct_Basic(ProductType.Rotational);
            productToCreate.SupportedTargets = new SupplyTarget[]
            {
                new SupplyTarget { TargetTypeId = TargetType.KeyValue, TargetPremium = 1.0 }
            };

            var product = await Helper.PostProductAndValidateAsync(ProductUrl!, productToCreate,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            var productId = product!.Id;

            // Create a line without any targeting
            var line = Helper.GetValidLine(productId, ProductType.Rotational);
            var createdLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.IsNull(createdLine?.Targets);

            // Now update the line to add KeyValue targeting
            var updatedLine = Helper.GetValidLine(productId, ProductType.Rotational);
            updatedLine.Id = createdLine!.Id;
            updatedLine.Targets = new Target[]
            {
                new KeyValueTarget
                {
                    Entries = new[]
                    {
                        new KeyValueEntry { Key = "device_type", Values = new[] {"mobileweb" }, IsExcluded = false },
                        new KeyValueEntry { Key = "device_type", Values = new[] { "desktopweb" }, IsExcluded = false }
                    }
                }
            };

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine.Id);
            var updatedLineResult = await Helper.UpdateLineAndValidateAsync(updateLineUrl, updatedLine,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.IsNotNull(updatedLineResult?.Targets);
            var kvTarget = updatedLineResult.Targets!.OfType<KeyValueTarget>().FirstOrDefault();
            Assert.IsNotNull(kvTarget, "KeyValue Target should be present on the line after update.");
            Assert.AreEqual(2, kvTarget.Entries.Length);
            Assert.IsTrue(kvTarget.Entries.Any(v => v.Key == "device_type" && v.Values.SequenceEqual(new[] { "mobileweb" })));
            Assert.IsTrue(kvTarget.Entries.Any(v => v.Key == "device_type" && v.Values.SequenceEqual(new[] { "desktopweb" })));
        }

        [TestMethod]
        public async Task UpdateLine_ChangeKeyValueTargetFromIncludeToExclude_ShouldSucceed()
        {
            // First create a product that supports KeyValue targeting
            var productToCreate = Helper.GetValidProduct_Basic(ProductType.Rotational);
            productToCreate.SupportedTargets = new SupplyTarget[]
            {
                new SupplyTarget { TargetTypeId = TargetType.KeyValue, TargetPremium = 1.0 }
            };

            var product = await Helper.PostProductAndValidateAsync(ProductUrl!, productToCreate,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            var productId = product!.Id;

            // Create a line with include KeyValue target
            var keyValueEntries = new[]
            {
                new KeyValueEntry { Key = "pub", Values = new[] { "msn" }, IsExcluded =false }
            };
            
            var line = Helper.GetLineWithKeyValueTarget(productId, keyValueEntries, isExcluded: false);
            var createdLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.IsNotNull(createdLine?.Targets);
            var kvTarget = createdLine.Targets!.OfType<KeyValueTarget>().FirstOrDefault();

            // Now update the line to change to exclude KeyValue targeting
            var updatedLine = Helper.GetValidLine(productId, ProductType.Rotational);
            updatedLine.Id = createdLine!.Id;
            updatedLine.Targets = new Target[]
            {
                new KeyValueTarget
                {
                    Entries = new[]
                    {
                        new KeyValueEntry { Key = "pub", Values = new[] { "msn" }, IsExcluded = false }
                    }
                }
            };

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine.Id);
            var updatedLineResult = await Helper.UpdateLineAndValidateAsync(updateLineUrl, updatedLine,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.IsNotNull(updatedLineResult?.Targets);
            kvTarget = updatedLineResult.Targets!.OfType<KeyValueTarget>().FirstOrDefault();
            Assert.IsNotNull(kvTarget, "KeyValue Target should be present on the line after update.");
            Assert.AreEqual(1, kvTarget.Entries.Length);
           }

        [TestMethod]
        public async Task UpdateLine_ModifyKeyValueTargetEntries_ShouldSucceed()
        {
            // First create a product that supports KeyValue targeting
            var productToCreate = Helper.GetValidProduct_Basic(ProductType.Rotational);
            productToCreate.SupportedTargets = new SupplyTarget[]
            {
                new SupplyTarget { TargetTypeId = TargetType.KeyValue, TargetPremium = 1.0 }
            };

            var product = await Helper.PostProductAndValidateAsync(ProductUrl!, productToCreate,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            var productId = product!.Id;

            // Create a line with KeyValue target
            var line = Helper.GetValidLine(productId, ProductType.Rotational);
            line.Targets = new Target[]
            {
                new KeyValueTarget
                {
                    Entries = new[]
                    {
                        new KeyValueEntry { Key = "market", Values = new[] { "us" }, IsExcluded = false },
                        new KeyValueEntry { Key = "device_type", Values = new[] { "mobileweb" }, IsExcluded = false }
                    }
                }
            };

            var createdLine = await Helper.PostLineAndValidateAsync(LineUrl!, line,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.Created, response.StatusCode);
                    return Task.CompletedTask;
                });

            // Now update the line to modify the KeyValue entries
            var updatedLine = Helper.GetValidLine(productId, ProductType.Rotational);
            updatedLine.Id = createdLine!.Id;
            updatedLine.Targets = new Target[]
            {
                new KeyValueTarget
                {
                    Entries = new[]
                    {
                        // Changed value
                        new KeyValueEntry { Key = "market", Values = new[] {"za"}, IsExcluded = false },
                        // Unchanged
                        new KeyValueEntry { Key = "device_type", Values = new[] { "desktopweb" },IsExcluded = false },
                        // Added new entry
                        new KeyValueEntry { Key = "pub", Values = new[] { "msn" },IsExcluded = false }
                    }
                }
            };

            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine.Id);
            var updatedLineResult = await Helper.UpdateLineAndValidateAsync(updateLineUrl, updatedLine,
                (response) =>
                {
                    Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                    return Task.CompletedTask;
                });

            Assert.IsNotNull(updatedLineResult?.Targets);
            var kvTarget = updatedLineResult.Targets!.OfType<KeyValueTarget>().FirstOrDefault();
            Assert.IsNotNull(kvTarget, "KeyValue Target should be present on the line after update.");
            Assert.AreEqual(3, kvTarget.Entries.Length);
           }

        private async Task<ProductOutput> CreateLocationTargetableProductAsync()
        {
            var productToCreate = Helper.GetValidProduct_Basic(ProductType.Rotational);

            productToCreate.SupportedTargets = new[]
            {
                new SupplyTarget { TargetTypeId = TargetType.Device, TargetPremium = 5 },
                new SupplyTarget { TargetTypeId = TargetType.Audience, TargetPremium = 2 },
                new SupplyTarget { TargetTypeId = TargetType.Location, TargetPremium = 0 }
                };


            var product = await Helper.PostProductAndValidateAsync(ProductUrl!, productToCreate,
                (response) => Task.CompletedTask);
            return product!;
        }

        private async Task<LineOutput> CreateReservedLine()
        {
            // Create line with isRserved set to false
            var product = Helper.GetValidProduct_Basic(ProductType.Roadblock, baseCpmRate: 0);
            var productOutput = await Helper.PostProductAndValidateAsync(ProductUrl!, product,
                async (response) =>
                {
                    await Task.CompletedTask;
                });
            var line1 = Helper.GetValidLine(productOutput!.Id, ProductType.Roadblock);
            var createdLine1 = await Helper.PostLineAndValidateAsync(LineUrl!, line1,
                async (response) =>
                {
                    await ValidateGetSingleLine(response, line1, HttpStatusCode.Created);
                });

            // Try enabling isReserved. Call should be successful but should have warnign and isreverved flag false
            line1.IsReserved = true;
            var updateLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1,
               async (response) =>
               {
                   line1.IsReserved = false;
                   await ValidateGetSingleLine(response, line1, expectedWarning: ErrorMessage.OperationNotAllowed);
               });

            // Get Publisher Approval for the line
            await Helper.GetPublisherApprovalForLine(CustomerId, MediaPlanId, createdLine1.Id, PublisherId);

            line1.IsReserved = true;
            await Helper.UpdateLineAndValidateAsync(updateLineUrl, line1,
               async (response) =>
               {
                   await ValidateGetSingleLine(response, line1);
               });

            var getLineUrl = Helper.GetLineEndpoint(CustomerId, MediaPlanId, createdLine1!.Id);
            var getLineResult = await Helper.GetLineAndValidateAsync(getLineUrl,
                async (response) =>
                {
                    await ValidateGetSingleLine(response, line1);
                });

            return getLineResult!;
        }


        private static LineInput GetValidLine(long productId)
        {
            var productType = productId == ProductId2 ? ProductType.Roadblock : ProductType.Rotational;
            var line = new LineInput
            {
                ProductId = productId,
                Name = "Line-" + Guid.NewGuid().ToString().Substring(0, 5),
                Description = "Desc",
                Cpm = productType != ProductType.Roadblock ? 3.0 : null,
                Cpd = productType == ProductType.Roadblock ? 1.0 : null,
                TargetImpressions = 10,
                StartDate = DateTime.UtcNow.Date.AddDays(3),
                EndDate = DateTime.UtcNow.Date.AddDays(4),
            };

            return line;
        }

        private static LineInput GetDraftLine(long productId)
        {
            var line = new LineInput
            {
                ProductId = productId,
                Name = "Line-" + Guid.NewGuid().ToString().Substring(0, 5),
                Description = "Desc",
                InputStatus = LineInputStatus.Draft
            };

            return line;
        }

        private async Task ValidateGetAllLines(HttpResponseMessage response, LineInput[] expectedLines, HttpStatusCode expectedStatusCode = HttpStatusCode.OK)
        {
            Assert.AreEqual(expectedStatusCode, response.StatusCode);

            if (expectedStatusCode == HttpStatusCode.OK)
            {
                var responseString = await response.Content.ReadAsStringAsync();
                LineOutput[]? returnedLines = JsonSerializer.Deserialize<Result<LineOutput[]>>(responseString, Helper.serializerOptions)!.Entity;

                Assert.IsNotNull(returnedLines);
                Assert.IsTrue(expectedLines.Length <= returnedLines?.Length);

                // for now, we do all of our tests in a single customer, so the getAll methods will return more lines then we created in a single test case
                // sort the expected and returned lines by name and validate each property

                foreach (var expectedLine in expectedLines)
                {
                    var actualLine = returnedLines?.FirstOrDefault(_ => _.Name == expectedLine.Name);
                    expectedLine.Id = expectedLine.Id <= 0 && actualLine != null ? actualLine.Id : expectedLine.Id;
                    ValidateLine(expectedLine, CustomerId, MediaPlanId, actualLine!);
                }
            }
        }

        private async Task ValidateLineDelete(HttpResponseMessage response, HttpStatusCode expectedStatusCode = HttpStatusCode.OK)
        {
            Assert.AreEqual(expectedStatusCode, response.StatusCode);

            // Validate Success conditions
            if (expectedStatusCode == HttpStatusCode.OK)
            {
                await Task.CompletedTask;
            }
        }

        private async Task ValidateUpdateLine(HttpResponseMessage response, LineInput expectedLine, HttpStatusCode expectedStatusCode = HttpStatusCode.OK)
        {
            Assert.AreEqual(expectedStatusCode, response.StatusCode);

            // Validate Success conditions
            if (expectedStatusCode == HttpStatusCode.OK)
            {
                await Task.CompletedTask;
            }
        }

        private async Task ValidateGetSingleLine(HttpResponseMessage response, LineInput expectedLine, HttpStatusCode expectedStatusCode = HttpStatusCode.OK, string? expectedWarning = null)
        {
            Assert.AreEqual(expectedStatusCode, response.StatusCode);

            if (expectedStatusCode == HttpStatusCode.OK || expectedStatusCode == HttpStatusCode.Created)
            {
                var responseString = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<Result<LineOutput>>(responseString, Helper.serializerOptions);
                LineOutput? actualLine = result!.Entity;
                Assert.IsNotNull(actualLine);
                expectedLine.Id = expectedLine.Id > 0 ? expectedLine.Id : actualLine.Id;
                ValidateLine(expectedLine, CustomerId, MediaPlanId, actualLine);

                if (!string.IsNullOrEmpty(expectedWarning))
                {
                    Assert.IsTrue(result.Warnings.Select(warning => warning.Message == expectedWarning).Any());
                }

            }
        }

        private static void ValidateLine(LineInput expectedLine, int expectedCustomerId, long expectedMediaPlanId, LineOutput actualLine)
        {
            Assert.AreEqual(expectedLine.Name, actualLine?.Name);
            Assert.AreEqual(expectedLine.Description, actualLine?.Description);
            Assert.AreEqual(expectedLine.Cpm, actualLine?.Cpm);
            Assert.AreEqual(expectedLine.Cpd, actualLine?.Cpd);
            Assert.AreEqual(expectedLine.StartDate, actualLine?.StartDate);

            // Adjust EndDateTime validation to handle both zero time and updated Hours
            if (expectedLine.EndDate.HasValue)
            {
                var expectedEndDateTime = expectedLine.EndDate.Value;
                var actualEndDateTime = actualLine?.EndDate;

                if (expectedEndDateTime.TimeOfDay == TimeSpan.Zero)
                {
                    // If expected EndDateTime has zero time, compare Time part by adding to it.
                    var expectedEndDateTimeWithUpdatedTime = expectedEndDateTime.Date.Add(new TimeSpan(expectedEndDateTime.Hour, 59, 59));
                    Assert.AreEqual(expectedEndDateTimeWithUpdatedTime, actualEndDateTime);
                }
                else if (expectedEndDateTime.Hour != 0)
                {
                    // If expected EndDateTime has some hour but minutes and seconds are zero, compare with updated minutes and seconds
                    var expectedEndDateTimeWithUpdatedTime = expectedEndDateTime.Date.Add(new TimeSpan(expectedEndDateTime.Hour, 59, 59));
                    Assert.AreEqual(expectedEndDateTimeWithUpdatedTime, actualEndDateTime);
                }
                else
                {
                    // Otherwise, compare the full DateTime
                    Assert.AreEqual(expectedEndDateTime, actualEndDateTime);
                }
            }

            Assert.AreEqual(expectedLine.ProductId, actualLine?.Product.Id);
            Assert.AreEqual(expectedCustomerId, actualLine?.Customer.Id);
            Assert.AreEqual(expectedMediaPlanId, actualLine?.MediaPlan.Id);

            Assert.AreEqual(expectedLine.TargetSpend, actualLine?.TargetSpend);
            Assert.AreEqual(expectedLine.TargetImpressions, actualLine?.TargetImpressions);
            Assert.IsNotNull(actualLine?.ScoreCard);
            Assert.IsTrue(expectedLine.Id == actualLine.ScoreCard.LineId);
            Assert.IsTrue(actualLine.ScoreCard.Total >= 0);
            Assert.IsTrue(actualLine.ScoreCard.Available >= 0);

            Assert.AreEqual(expectedLine.IsReserved, actualLine?.IsReserved);
        }

    }
}
