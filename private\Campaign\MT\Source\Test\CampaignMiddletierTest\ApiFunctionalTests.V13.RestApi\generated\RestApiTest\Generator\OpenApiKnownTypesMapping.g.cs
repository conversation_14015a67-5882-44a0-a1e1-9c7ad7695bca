﻿namespace ApiFunctionalTests.V13.RestApi;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization.Metadata;

public static partial class RestApiGeneration
{
    public partial class OpenApiKnownTypesMapping
    {
        public static Dictionary<Type, Dictionary<Type, string>> TypesMap = new Dictionary<Type, Dictionary<Type, string>>
        {
            // Microsoft.BingAds.CampaignManagement entities
            { typeof(Microsoft.BingAds.CampaignManagement.Ad), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.ResponsiveSearchAd), "ResponsiveSearch" },
                { typeof(Microsoft.BingAds.CampaignManagement.ResponsiveAd), "ResponsiveAd" },
                { typeof(Microsoft.BingAds.CampaignManagement.DynamicSearchAd), "DynamicSearch" },
                { typeof(Microsoft.BingAds.CampaignManagement.ExpandedTextAd), "ExpandedText" },
                { typeof(Microsoft.BingAds.CampaignManagement.AppInstallAd), "AppInstall" },
                { typeof(Microsoft.BingAds.CampaignManagement.HotelAd), "Hotel" },
                { typeof(Microsoft.BingAds.CampaignManagement.ProductAd), "Product" },
                { typeof(Microsoft.BingAds.CampaignManagement.TextAd), "Text" },
                { typeof(Microsoft.BingAds.CampaignManagement.Ad), "Ad" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.AdExtension), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.LeadFormAdExtension), "LeadFormAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.LogoAdExtension), "LogoAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.DisclaimerAdExtension), "DisclaimerAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.VideoAdExtension), "VideoAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.FlyerAdExtension), "FlyerAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.FilterLinkAdExtension), "FilterLinkAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.PromotionAdExtension), "PromotionAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.PriceAdExtension), "PriceAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.StructuredSnippetAdExtension), "StructuredSnippetAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.ActionAdExtension), "ActionAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.SitelinkAdExtension), "SitelinkAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.CalloutAdExtension), "CalloutAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.DataTableAdExtension), "DataTableAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.ReviewAdExtension), "ReviewAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.NewsAdExtension), "NewsAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.AppAdExtension), "AppAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.ImageAdExtension), "ImageAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.CallAdExtension), "CallAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.LocationAdExtension), "LocationAdExtension" },
                { typeof(Microsoft.BingAds.CampaignManagement.AdExtension), "AdExtension" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.AdGroupCriterion), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.NegativeAdGroupCriterion), "NegativeAdGroupCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.BiddableAdGroupCriterion), "BiddableAdGroupCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.AdGroupCriterion), "AdGroupCriterion" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.ApplicationFault), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.AdApiFaultDetail), "AdApiFaultDetail" },
                { typeof(Microsoft.BingAds.CampaignManagement.EditorialApiFaultDetail), "EditorialApiFaultDetail" },
                { typeof(Microsoft.BingAds.CampaignManagement.ApiFaultDetail), "ApiFaultDetail" },
                { typeof(Microsoft.BingAds.CampaignManagement.ApplicationFault), "ApplicationFault" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.Asset), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.VideoAsset), "VideoAsset" },
                { typeof(Microsoft.BingAds.CampaignManagement.ImageAsset), "ImageAsset" },
                { typeof(Microsoft.BingAds.CampaignManagement.TextAsset), "TextAsset" },
                { typeof(Microsoft.BingAds.CampaignManagement.Asset), "Asset" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.Audience), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.ImpressionBasedRemarketingList), "ImpressionBasedRemarketingList" },
                { typeof(Microsoft.BingAds.CampaignManagement.CustomerList), "CustomerList" },
                { typeof(Microsoft.BingAds.CampaignManagement.CombinedList), "CombinedList" },
                { typeof(Microsoft.BingAds.CampaignManagement.SimilarRemarketingList), "SimilarRemarketingList" },
                { typeof(Microsoft.BingAds.CampaignManagement.ProductAudience), "Product" },
                { typeof(Microsoft.BingAds.CampaignManagement.InMarketAudience), "InMarket" },
                { typeof(Microsoft.BingAds.CampaignManagement.CustomAudience), "Custom" },
                { typeof(Microsoft.BingAds.CampaignManagement.RemarketingList), "RemarketingList" },
                { typeof(Microsoft.BingAds.CampaignManagement.Audience), "Audience" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.AudienceGroupDimension), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.ProfileDimension), "Profile" },
                { typeof(Microsoft.BingAds.CampaignManagement.AudienceDimension), "Audience" },
                { typeof(Microsoft.BingAds.CampaignManagement.GenderDimension), "Gender" },
                { typeof(Microsoft.BingAds.CampaignManagement.AgeDimension), "Age" },
                { typeof(Microsoft.BingAds.CampaignManagement.AudienceGroupDimension), "AudienceGroupDimension" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.BatchError), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.EditorialError), "EditorialError" },
                { typeof(Microsoft.BingAds.CampaignManagement.BatchError), "BatchError" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.BatchErrorCollection), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.EditorialErrorCollection), "EditorialErrorCollection" },
                { typeof(Microsoft.BingAds.CampaignManagement.BatchErrorCollection), "BatchErrorCollection" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.BiddingScheme), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.CostPerSaleBiddingScheme), "CostPerSale" },
                { typeof(Microsoft.BingAds.CampaignManagement.ManualCpaBiddingScheme), "ManualCpaBiddingScheme" },
                { typeof(Microsoft.BingAds.CampaignManagement.CommissionBiddingScheme), "CommissionBiddingScheme" },
                { typeof(Microsoft.BingAds.CampaignManagement.PercentCpcBiddingScheme), "PercentCpcBiddingScheme" },
                { typeof(Microsoft.BingAds.CampaignManagement.TargetImpressionShareBiddingScheme), "TargetImpressionShareBiddingScheme" },
                { typeof(Microsoft.BingAds.CampaignManagement.MaxConversionValueBiddingScheme), "MaxConversionValueBiddingScheme" },
                { typeof(Microsoft.BingAds.CampaignManagement.MaxRoasBiddingScheme), "MaxRoasBiddingScheme" },
                { typeof(Microsoft.BingAds.CampaignManagement.TargetRoasBiddingScheme), "TargetRoasBiddingScheme" },
                { typeof(Microsoft.BingAds.CampaignManagement.InheritFromParentBiddingScheme), "InheritFromParent" },
                { typeof(Microsoft.BingAds.CampaignManagement.ManualCpmBiddingScheme), "ManualCpm" },
                { typeof(Microsoft.BingAds.CampaignManagement.ManualCpvBiddingScheme), "ManualCpv" },
                { typeof(Microsoft.BingAds.CampaignManagement.EnhancedCpcBiddingScheme), "EnhancedCpc" },
                { typeof(Microsoft.BingAds.CampaignManagement.ManualCpcBiddingScheme), "ManualCpc" },
                { typeof(Microsoft.BingAds.CampaignManagement.TargetCpaBiddingScheme), "TargetCpa" },
                { typeof(Microsoft.BingAds.CampaignManagement.MaxConversionsBiddingScheme), "MaxConversions" },
                { typeof(Microsoft.BingAds.CampaignManagement.MaxClicksBiddingScheme), "MaxClicks" },
                { typeof(Microsoft.BingAds.CampaignManagement.BiddingScheme), "BiddingScheme" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.CampaignCriterion), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.BiddableCampaignCriterion), "BiddableCampaignCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.NegativeCampaignCriterion), "NegativeCampaignCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.CampaignCriterion), "CampaignCriterion" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.ConversionGoal), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.InStoreTransactionGoal), "InStoreTransaction" },
                { typeof(Microsoft.BingAds.CampaignManagement.OfflineConversionGoal), "OfflineConversion" },
                { typeof(Microsoft.BingAds.CampaignManagement.AppInstallGoal), "AppInstall" },
                { typeof(Microsoft.BingAds.CampaignManagement.EventGoal), "Event" },
                { typeof(Microsoft.BingAds.CampaignManagement.PagesViewedPerVisitGoal), "PagesViewedPerVisit" },
                { typeof(Microsoft.BingAds.CampaignManagement.DurationGoal), "Duration" },
                { typeof(Microsoft.BingAds.CampaignManagement.UrlGoal), "Url" },
                { typeof(Microsoft.BingAds.CampaignManagement.ConversionGoal), "ConversionGoal" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.Criterion), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.TopicCriterion), "TopicCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.PlacementCriterion), "PlacementCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.GenreCriterion), "GenreCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.DealCriterion), "DealCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.StoreCriterion), "StoreCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.ProfileCriterion), "ProfileCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.AudienceCriterion), "AudienceCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.LocationIntentCriterion), "LocationIntentCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.LocationCriterion), "LocationCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.RadiusCriterion), "RadiusCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.GenderCriterion), "GenderCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.DayTimeCriterion), "DayTimeCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.DeviceCriterion), "DeviceCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.AgeCriterion), "AgeCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.Webpage), "Webpage" },
                { typeof(Microsoft.BingAds.CampaignManagement.ProductScope), "ProductScope" },
                { typeof(Microsoft.BingAds.CampaignManagement.HotelLengthOfStayCriterion), "HotelLengthOfStayCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.HotelDateSelectionTypeCriterion), "HotelDateSelectionTypeCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.HotelCheckInDayCriterion), "HotelCheckInDayCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.HotelCheckInDateCriterion), "HotelCheckInDateCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.HotelAdvanceBookingWindowCriterion), "HotelAdvanceBookingWindowCriterion" },
                { typeof(Microsoft.BingAds.CampaignManagement.HotelGroup), "HotelGroup" },
                { typeof(Microsoft.BingAds.CampaignManagement.ProductPartition), "ProductPartition" },
                { typeof(Microsoft.BingAds.CampaignManagement.Criterion), "Criterion" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.CriterionBid), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.RateBid), "RateBid" },
                { typeof(Microsoft.BingAds.CampaignManagement.BidMultiplier), "BidMultiplier" },
                { typeof(Microsoft.BingAds.CampaignManagement.FixedBid), "FixedBid" },
                { typeof(Microsoft.BingAds.CampaignManagement.CriterionBid), "CriterionBid" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.CriterionCashback), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.CashbackAdjustment), "CashbackAdjustment" },
                { typeof(Microsoft.BingAds.CampaignManagement.CriterionCashback), "CriterionCashback" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.ImportJob), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.FileImportJob), "FileImportJob" },
                { typeof(Microsoft.BingAds.CampaignManagement.GoogleImportJob), "GoogleImportJob" },
                { typeof(Microsoft.BingAds.CampaignManagement.ImportJob), "ImportJob" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.ImportOption), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.FileImportOption), "FileImportOption" },
                { typeof(Microsoft.BingAds.CampaignManagement.GoogleImportOption), "GoogleImportOption" },
                { typeof(Microsoft.BingAds.CampaignManagement.ImportOption), "ImportOption" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.Media), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.Image), "Image" },
                { typeof(Microsoft.BingAds.CampaignManagement.Media), "Media" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.MediaRepresentation), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.ImageMediaRepresentation), "ImageMediaRepresentation" },
                { typeof(Microsoft.BingAds.CampaignManagement.MediaRepresentation), "MediaRepresentation" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.RemarketingRule), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.CustomEventsRule), "CustomEvents" },
                { typeof(Microsoft.BingAds.CampaignManagement.PageVisitorsWhoDidNotVisitAnotherPageRule), "PageVisitorsWhoDidNotVisitAnotherPage" },
                { typeof(Microsoft.BingAds.CampaignManagement.PageVisitorsWhoVisitedAnotherPageRule), "PageVisitorsWhoVisitedAnotherPage" },
                { typeof(Microsoft.BingAds.CampaignManagement.PageVisitorsRule), "PageVisitors" },
                { typeof(Microsoft.BingAds.CampaignManagement.RemarketingRule), "RemarketingRule" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.RuleItem), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.NumberRuleItem), "Number" },
                { typeof(Microsoft.BingAds.CampaignManagement.StringRuleItem), "String" },
                { typeof(Microsoft.BingAds.CampaignManagement.RuleItem), "RuleItem" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.Setting), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.NewCustomerAcquisitionGoalSetting), "NewCustomerAcquisitionGoalSetting" },
                { typeof(Microsoft.BingAds.CampaignManagement.ThirdPartyMeasurementSetting), "ThirdPartyMeasurementSetting" },
                { typeof(Microsoft.BingAds.CampaignManagement.AppSetting), "AppSetting" },
                { typeof(Microsoft.BingAds.CampaignManagement.VanityPharmaSetting), "VanityPharmaSetting" },
                { typeof(Microsoft.BingAds.CampaignManagement.CallToActionSetting), "CallToActionSetting" },
                { typeof(Microsoft.BingAds.CampaignManagement.PerformanceMaxSetting), "PerformanceMaxSetting" },
                { typeof(Microsoft.BingAds.CampaignManagement.ResponsiveSearchAdsSetting), "ResponsiveSearchAdsSetting" },
                { typeof(Microsoft.BingAds.CampaignManagement.HotelSetting), "HotelSetting" },
                { typeof(Microsoft.BingAds.CampaignManagement.DisclaimerSetting), "DisclaimerSetting" },
                { typeof(Microsoft.BingAds.CampaignManagement.CoOpSetting), "CoOpSetting" },
                { typeof(Microsoft.BingAds.CampaignManagement.TargetSetting), "TargetSetting" },
                { typeof(Microsoft.BingAds.CampaignManagement.DynamicSearchAdsSetting), "DynamicSearchAdsSetting" },
                { typeof(Microsoft.BingAds.CampaignManagement.DynamicFeedSetting), "DynamicFeedSetting" },
                { typeof(Microsoft.BingAds.CampaignManagement.ShoppingSetting), "ShoppingSetting" },
                { typeof(Microsoft.BingAds.CampaignManagement.VerifiedTrackingSetting), "VerifiedTrackingSetting" },
                { typeof(Microsoft.BingAds.CampaignManagement.Setting), "Setting" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.SharedEntity), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.AccountPlacementInclusionList), "AccountPlacementInclusionList" },
                { typeof(Microsoft.BingAds.CampaignManagement.AccountPlacementExclusionList), "AccountPlacementExclusionList" },
                { typeof(Microsoft.BingAds.CampaignManagement.BrandList), "BrandList" },
                { typeof(Microsoft.BingAds.CampaignManagement.AccountNegativeKeywordList), "AccountNegativeKeywordList" },
                { typeof(Microsoft.BingAds.CampaignManagement.PlacementExclusionList), "PlacementExclusionList" },
                { typeof(Microsoft.BingAds.CampaignManagement.NegativeKeywordList), "NegativeKeywordList" },
                { typeof(Microsoft.BingAds.CampaignManagement.SharedList), "SharedList" },
                { typeof(Microsoft.BingAds.CampaignManagement.SharedEntity), "SharedEntity" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.SharedList), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.AccountPlacementInclusionList), "AccountPlacementInclusionList" },
                { typeof(Microsoft.BingAds.CampaignManagement.AccountPlacementExclusionList), "AccountPlacementExclusionList" },
                { typeof(Microsoft.BingAds.CampaignManagement.BrandList), "BrandList" },
                { typeof(Microsoft.BingAds.CampaignManagement.AccountNegativeKeywordList), "AccountNegativeKeywordList" },
                { typeof(Microsoft.BingAds.CampaignManagement.PlacementExclusionList), "PlacementExclusionList" },
                { typeof(Microsoft.BingAds.CampaignManagement.NegativeKeywordList), "NegativeKeywordList" },
                { typeof(Microsoft.BingAds.CampaignManagement.SharedList), "SharedList" } }
            },
            { typeof(Microsoft.BingAds.CampaignManagement.SharedListItem), new Dictionary<Type, string> {                
                { typeof(Microsoft.BingAds.CampaignManagement.NegativeKeyword), "NegativeKeyword" },
                { typeof(Microsoft.BingAds.CampaignManagement.Site), "Site" },
                { typeof(Microsoft.BingAds.CampaignManagement.BrandItem), "BrandItem" },
                { typeof(Microsoft.BingAds.CampaignManagement.NegativeSite), "NegativeSite" },
                { typeof(Microsoft.BingAds.CampaignManagement.SharedListItem), "SharedListItem" } }
            },

       };
    }
}