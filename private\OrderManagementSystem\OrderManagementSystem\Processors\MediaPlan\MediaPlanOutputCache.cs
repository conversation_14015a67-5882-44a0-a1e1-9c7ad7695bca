using OrderManagementSystem.Entities.External;

namespace OrderManagementSystem.Processors
{
    public interface IMediaPlanOutputCache
    {
        List<MediaPlanOutput>? GetOutput();
        void SetOutput(List<MediaPlanOutput> output);
        int Count();
        void Clear();
    }

    public class MediaPlanOutputCache : IMediaPlanOutputCache
    {
        private List<MediaPlanOutput>? _cache;
        private readonly ReaderWriterLockSlim _lock = new();

        public MediaPlanOutputCache() { }

        // Returns the entire cached list, or null if not set
        public List<MediaPlanOutput>? GetOutput()
        {
            _lock.EnterReadLock();
            try
            {
                return _cache;
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        // Replaces the entire cache with the provided list
        public void SetOutput(List<MediaPlanOutput> output)
        {
            _lock.EnterWriteLock();
            try
            {
                _cache = output;
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        // Returns the count of items in the cache, or 0 if not set
        public int Count()
        {
            _lock.EnterReadLock();
            try
            {
                return _cache?.Count ?? 0;
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        // Clears the cache
        public void Clear()
        {
            _lock.EnterWriteLock();
            try
            {
                _cache?.Clear();
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }
    }
}