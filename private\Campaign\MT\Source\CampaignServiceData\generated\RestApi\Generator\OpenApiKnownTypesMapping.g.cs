﻿namespace CampaignServiceData;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization.Metadata;

public static partial class RestApiGeneration
{
    public partial class OpenApiKnownTypesMapping
    {
        public static Dictionary<Type, Dictionary<Type, string>> TypesMap = new Dictionary<Type, Dictionary<Type, string>>
        {
            // Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2.CustomColumn entities
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2.CustomColumn.CustomColumnNode), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2.CustomColumn.CustomColumnConstantNode), "CustomColumnConstantNode" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2.CustomColumn.CustomColumnDataNode), "CustomColumnDataNode" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2.CustomColumn.CustomColumnBinaryOperatorNode), "CustomColumnBinaryOperatorNode" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2.CustomColumn.CustomColumnUnaryOperatorNode), "CustomColumnUnaryOperatorNode" } }
            },

            // Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy entities
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterFault), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterAuthenticationFault), "AdCenterAuthenticationFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterSystemFault), "AdCenterSystemFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterInternalErrorFault), "AdCenterInternalErrorFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterAuthorizationFault), "AdCenterAuthorizationFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterFault), "AdCenterFault" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.ApplicationFault), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterAuthenticationFault), "AdCenterAuthenticationFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterSystemFault), "AdCenterSystemFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterInternalErrorFault), "AdCenterInternalErrorFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterAuthorizationFault), "AdCenterAuthorizationFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterFault), "AdCenterFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.PipelineFault), "PipelineFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.SharedFault), "SharedFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdApiFaultDetail), "AdApiFaultDetail" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.ApplicationFault), "ApplicationFault" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.PipelineFault), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterAuthenticationFault), "AdCenterAuthenticationFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterSystemFault), "AdCenterSystemFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterInternalErrorFault), "AdCenterInternalErrorFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterAuthorizationFault), "AdCenterAuthorizationFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterFault), "AdCenterFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.PipelineFault), "PipelineFault" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.SharedFault), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterAuthenticationFault), "AdCenterAuthenticationFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterSystemFault), "AdCenterSystemFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterInternalErrorFault), "AdCenterInternalErrorFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterAuthorizationFault), "AdCenterAuthorizationFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.AdCenterFault), "AdCenterFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.PipelineFault), "PipelineFault" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Proxy.SharedFault), "SharedFault" } }
            },

       };
    }
}