﻿using OrderManagementSystem.Common;
using OrderManagementSystem.DataAccessObjects;
using OrderManagementSystem.Entities;
using OrderManagementSystem.Entities.External;
using OrderManagementSystem.Interfaces;
using OrderManagementSystem.Requests;

namespace OrderManagementSystem.Processors
{
    public class MediaPlanGetAllProcessor : BaseProcessor<MediaPlanGetAllRequest, MediaPlanOutput[]>
    {
        protected readonly IMediaPlanDao Dao;
        protected readonly IMediaPlanProcessorHelper ProcessorHelper;
        protected readonly IMediaPlanOutputCache MediaPlanOutputCache;

        public MediaPlanGetAllProcessor(IValidator<MediaPlanGetAllRequest> validator, IMediaPlanDao dao, IMediaPlanProcessorHelper processorHelper, IMediaPlanOutputCache mediaPlanOutputCache) : base(validator)
        {
            this.Dao = dao;
            this.ProcessorHelper = processorHelper;
            this.MediaPlanOutputCache = mediaPlanOutputCache;
        }

        public override async Task<Result<MediaPlanOutput[]>> Execute(MediaPlanGetAllRequest request)
        {
            // Try to pull from cache first
            var cacheOutput = MediaPlanOutputCache.GetOutput();
            if (cacheOutput != null && cacheOutput.Count != 0)
            {
                cacheOutput = cacheOutput.Where(mp => mp.Customer.Id == request.CustomerId).ToList();
                cacheOutput = FilterSortPageOutput(cacheOutput, request, out int cachedCount);
                var cachedResult = new Result<MediaPlanOutput[]>();
                cachedResult.Entity = cacheOutput.ToArray();
                cachedResult.Count = cachedCount;
                return cachedResult;
            }

            var result = await this.Dao.Get(request);

            var resultOutput = new Result<MediaPlanOutput[]>();
            resultOutput.Merge(result, true);
            if (resultOutput.Failed) { return resultOutput; }

            var customerResult = ProcessorHelper.GetCustomer(request.Logger, request.CustomerId);
            resultOutput.Merge(customerResult);
            if (resultOutput.Failed) { return resultOutput; }

            var customerMap = new Dictionary<int, Customer> { { customerResult.Entity!.Id, customerResult.Entity } };
            List<MediaPlanOutput> outputList = await ProcessorHelper.GetAllEstimatedImpressions(request.Logger, customerMap, result.Entity!.ToArray());

            outputList = FilterSortPageOutput(outputList, request, out int count);
            resultOutput.Entity = outputList.ToArray();
            resultOutput.Count = count;

            return resultOutput;
        }

        private List<MediaPlanOutput> FilterSortPageOutput(List<MediaPlanOutput> outputList, MediaPlanGetAllRequest request, out int count)
        {
            outputList = request.Filter != null ? ProcessorHelper.Filter(outputList, request.Filter)!.ToList() : outputList;

            // Tracking the count after filtering
            count = outputList.Count;

            outputList = request.OrderBy != null ? ProcessorHelper.Sort(outputList, request.OrderBy)!.ToList() : outputList;
            outputList = request.Skip != null ? outputList.Skip((int)request.Skip).ToList() : outputList;
            outputList = request.Top != null ? outputList.Take((int)request.Top).ToList() : outputList;

            return outputList;
        }
    }
}
