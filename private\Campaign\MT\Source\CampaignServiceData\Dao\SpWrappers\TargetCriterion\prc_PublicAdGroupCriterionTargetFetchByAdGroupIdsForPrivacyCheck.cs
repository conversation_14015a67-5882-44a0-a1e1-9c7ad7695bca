﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.DAO
{
    using System.Collections.Generic;
    using System.Data;
    using Microsoft.Data.SqlClient;
    using Common;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using Microsoft.AdCenter.Shared.MT.DAO;

    public static partial class SpWrappers
    {
        private static readonly List<short> TargetTypesForPrivacyCheck = new List<short>
        {
            (short) TargetDao.DBTargetType.Age,
            (short) TargetDao.DBTargetType.Gender,
            (short) TargetDao.DBTargetType.GeoLocation,
            (short) TargetDao.DBTargetType.Location,
            (short) TargetDao.DBTargetType.PostalCode,
            (short) TargetDao.DBTargetType.Audience,
            (short) TargetDao.DBTargetType.InMarket,
            (short) TargetDao.DBTargetType.Custom,
            (short) TargetDao.DBTargetType.ProductAudience,
            (short) TargetDao.DBTargetType.RemarketingSimilar,
            (short) TargetDao.DBTargetType.CompanyName,
            (short) TargetDao.DBTargetType.Industry,
            (short) TargetDao.DBTargetType.JobFunction
        };
        private static readonly DataTable TargetsTableTypeForPrivacyCheck = DataTableType.CreateSmallIntEntityTypeDataTable(TargetTypesForPrivacyCheck);

        public static SqlCommand CreateOrderTargetsFetchByOrderIdsForPrivacyCheckCommand(AccountCallContext context, LineItemContainer<EntityIdWithParentId> adGroupIdWithParentIdLineItems)
        {
            var sprocToCall = "dbo.prc_PublicGetOrderTargetsByOrderIds_V50";
            SqlCommand command = SPHelper.MakeSqlCommandWithTracking(sprocToCall, context.Logger.CallTrackingData, 0);

            DataTable campaignIdOrderIdTable = DataTableType.CreateCampaignIdOrderIdTypeDataTable();
            foreach (var line in adGroupIdWithParentIdLineItems)
            {
                DataRow dataRow = campaignIdOrderIdTable.NewRow();
                dataRow[DataTableType.LineItemId] = line.LineItemId;
                dataRow[DataTableType.CampaignId] = line.LineItem.ParentId;
                dataRow[DataTableType.OrderId] = line.LineItem.EntityId;
                campaignIdOrderIdTable.Rows.Add(dataRow);
            }
            command.Parameters.Add(TableValuedParam("@OrderIds", campaignIdOrderIdTable));

            command.Parameters.Add(TableValuedParam("@TargetTypes", TargetsTableTypeForPrivacyCheck));
            //command.Parameters.Add(IntParam("@CustomerId", context.AdvertiserCustomerID));
            command.Parameters.Add(IntParam("@AccountId", (int)context.AccountId));
            command.Parameters.Add(IntParam("@UserId", context.UserId));
            return command;
        }
    }
}
