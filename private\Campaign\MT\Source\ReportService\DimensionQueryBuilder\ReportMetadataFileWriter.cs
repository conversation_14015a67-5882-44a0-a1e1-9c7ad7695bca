﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ReportService
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.IO;
    using System.Threading;
    using Microsoft.AdCenter.Shared.MT;
    using ProtoBuf;

#pragma warning disable CS0618 // Type or member is obsolete
    public class ReportMetadataFileWriter
    {
        protected readonly ILogShared logger;
        protected readonly Action<ProtoWriter, object>[] columnWriters;
        protected readonly Stream stream;
        protected readonly string[] dbTypes;

        public ReportMetadataFileWriter(ILogShared logger, Stream stream, string[] dbTypes)
        {
            this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
            this.stream = stream ?? throw new ArgumentNullException(nameof(stream));
            this.dbTypes = dbTypes ?? throw new ArgumentNullException(nameof(dbTypes));
            this.columnWriters = new Action<ProtoWriter, object>[dbTypes.Length];

            for (int i = 0; i < dbTypes.Length; i++)
            {
                columnWriters[i] = DBTypeToColumnWriters(dbTypes[i], i + 1);
            }
        }

        public int WriteRowsData(IEnumerable<IList<object>> rows, CancellationToken cancelToken)
        {
            int totalRows = 0;
            using (var writer = ProtoWriter.Create(stream, null, null))
            {
                foreach (var row in rows)
                {
                    if (cancelToken.IsCancellationRequested)
                    {
                        this.logger.LogInfo($"{totalRows} rows were written to proto data file before token was cancelled.");
                        cancelToken.ThrowIfCancellationRequested();
                    }

                    ProtoWriter.WriteFieldHeader(1, WireType.StartGroup, writer);
                    var token = ProtoWriter.StartSubItem(row, writer);

                    int i = 0;
                    foreach (var value in row)
                    {
                        if (value != null && value != DBNull.Value)
                        {
                            columnWriters[i](writer, value);
                        }

                        i++;
                    }

                    totalRows++;
                    ProtoWriter.EndSubItem(token, writer);
                }

                writer.Close();
            }

            return totalRows;
        }

        public int WriteRowsData(IEnumerator<IList<object>> rows, CancellationToken cancelToken)
        {
            int totalRows = 0;
            using (var writer = ProtoWriter.Create(stream, null, null))
            {
                while (rows.MoveNext())
                {
                    var row = rows.Current;
                    if (cancelToken.IsCancellationRequested)
                    {
                        this.logger.LogInfo($"{totalRows} rows were written to proto data file before token was cancelled.");
                        cancelToken.ThrowIfCancellationRequested();
                    }

                    ProtoWriter.WriteFieldHeader(1, WireType.StartGroup, writer);
                    var token = ProtoWriter.StartSubItem(row, writer);

                    int i = 0;
                    try
                    {
                        foreach (var value in row)
                        {
                            if (value != null && value != DBNull.Value)
                            {
                                columnWriters[i](writer, value);
                            }

                            i++;
                        }
                    }
                    catch (Exception ex)
                    {
                        this.logger.LogError($"ReportMetadataFileWriter.WriteRowsData: Exception happened when writing {i}th column. DbTypes: {string.Join(",", this.dbTypes)}, row data: {string.Join(",", row)}, Exception is {ex}");

                        throw;
                    }

                    totalRows++;
                    ProtoWriter.EndSubItem(token, writer);
                }

                writer.Close();
            }

            return totalRows;
        }

        public int WriteRowsData(IDataReader dataReader, CancellationToken cancelToken)
        {
            int totalRows = 0;
            using (var writer = ProtoWriter.Create(stream, null, null))
            {
                while (dataReader.Read())
                {
                    if (cancelToken.IsCancellationRequested)
                    {
                        this.logger.LogInfo($"{totalRows} rows were written to proto data file before token was cancelled.");
                        break;
                    }

                    var objArray = new object[dataReader.FieldCount];
                    dataReader.GetValues(objArray);

                    ProtoWriter.WriteFieldHeader(1, WireType.StartGroup, writer);
                    var token = ProtoWriter.StartSubItem(objArray, writer);

                    int i = 0;
                    foreach (var value in objArray)
                    {
                        if (value != null && value != DBNull.Value)
                        {
                            columnWriters[i](writer, value);
                        }

                        i++;
                    }

                    totalRows++;
                    ProtoWriter.EndSubItem(token, writer);
                }

                writer.Close();
            }

            return totalRows;
        }


        private static Action<ProtoWriter, object> DBTypeToColumnWriters(string dbTypeName, int fieldIndex)
        {
            switch (dbTypeName.ToLowerInvariant())
            {
                case "varchar":
                case "nvarchar":
                case "char":
                case "nchar":
                case "string":
                case "nullable(string)":
                case "nullable(fixedstring(8))":
                case "nullable(fixedstring(3))":
                    return (ProtoWriter writer, object value) =>
                    {
                        ProtoWriter.WriteFieldHeader(fieldIndex, WireType.String, writer);
                        ProtoWriter.WriteString(value.ToString(), writer);
                    };
                case "tinyint":
                case "uint8":
                    return (ProtoWriter writer, object value) =>
                    {
                        ProtoWriter.WriteFieldHeader(fieldIndex, WireType.Variant, writer);
                        ProtoWriter.WriteByte(SafeObjectCaster.SafeObjectCast<byte>(value), writer);
                    };
                case "int8":
                    return (ProtoWriter writer, object value) =>
                    {
                        ProtoWriter.WriteFieldHeader(fieldIndex, WireType.Variant, writer);
                        ProtoWriter.WriteSByte(SafeObjectCaster.SafeObjectCast<sbyte>(value), writer);
                    };
                case "smallint":
                case "int16":
                    return (ProtoWriter writer, object value) =>
                    {
                        ProtoWriter.WriteFieldHeader(fieldIndex, WireType.Variant, writer);
                        ProtoWriter.WriteInt16(SafeObjectCaster.SafeObjectCast<short>(value), writer);
                    };
                case "uint16":
                case "ushort":
                    return (ProtoWriter writer, object value) =>
                    {
                        ProtoWriter.WriteFieldHeader(fieldIndex, WireType.Variant, writer);
                        ProtoWriter.WriteUInt16(SafeObjectCaster.SafeObjectCast<ushort>(value), writer);
                    };
                case "integer":
                case "int":
                case "int32":
                    return (ProtoWriter writer, object value) =>
                    {
                        ProtoWriter.WriteFieldHeader(fieldIndex, WireType.Variant, writer);
                        ProtoWriter.WriteInt32(SafeObjectCaster.SafeObjectCast<int>(value), writer);
                    };
                case "uint":
                case "uint32":
                    return (ProtoWriter writer, object value) =>
                    {
                        ProtoWriter.WriteFieldHeader(fieldIndex, WireType.Variant, writer);
                        ProtoWriter.WriteUInt32(SafeObjectCaster.SafeObjectCast<uint>(value), writer);
                    };
                case "bigint":
                case "int64":
                case "long":
                case "nullable(int64)":
                    return (ProtoWriter writer, object value) =>
                    {
                        ProtoWriter.WriteFieldHeader(fieldIndex, WireType.Variant, writer);
                        ProtoWriter.WriteInt64(SafeObjectCaster.SafeObjectCast<long>(value), writer);
                    };
                case "ulong":
                case "uint64":
                    return (ProtoWriter writer, object value) =>
                    {
                        ProtoWriter.WriteFieldHeader(fieldIndex, WireType.Variant, writer);
                        ProtoWriter.WriteUInt64(SafeObjectCaster.SafeObjectCast<ulong>(value), writer);
                    };
                case "double":
                case "float":
                case "nullable(float64)":
                    return (ProtoWriter writer, object value) =>
                    {
                        ProtoWriter.WriteFieldHeader(fieldIndex, WireType.Fixed64, writer);
                        ProtoWriter.WriteDouble((double)value, writer);
                    };
                case "decimal":
                case "money":
                case "smallmoney":
                case "numeric":
                case "decimal128(12)":
                case "decimal128(4)":
                    return (ProtoWriter writer, object value) =>
                    {
                        ProtoWriter.WriteFieldHeader(fieldIndex, WireType.Fixed64, writer);
                        ProtoWriter.WriteDouble((double)(decimal)value, writer);
                    };
                case "nullable(decimal128(12))":
                case "nullable(decimal128(4))":
                    return (ProtoWriter writer, object value) =>
                    {
                        ProtoWriter.WriteFieldHeader(fieldIndex, WireType.Fixed64, writer);
                        ProtoWriter.WriteDouble((double)(decimal?)value, writer);
                    };
                case "datetime":
                case "datetime2":
                case "smalldatetime":
                case "date":
                case "time":
                    return (ProtoWriter writer, object value) =>
                    {
                        ProtoWriter.WriteFieldHeader(fieldIndex, WireType.Variant, writer);
                        ProtoWriter.WriteInt64(((DateTime)value).Ticks, writer);
                    };
                case "variant":
                    return (ProtoWriter writer, object value) =>
                    {
                        // Handle Dictionary<int, decimal> and Dictionary<int, decimal?>
                        if (value is IDictionary<int, decimal> dictIntDecimal)
                        {
                            ProtoWriter.WriteFieldHeader(fieldIndex, WireType.StartGroup, writer);
                            var token = ProtoWriter.StartSubItem(value, writer);
                            foreach (var kvp in dictIntDecimal)
                            {
                                // Write each entry as a group with key and value fields
                                ProtoWriter.WriteFieldHeader(1, WireType.Variant, writer);
                                ProtoWriter.WriteInt32(kvp.Key, writer);

                                ProtoWriter.WriteFieldHeader(2, WireType.Fixed64, writer);
                                ProtoWriter.WriteDouble((double)kvp.Value, writer);
                            }
                            ProtoWriter.EndSubItem(token, writer);
                        }
                        else if (value is IDictionary<int, decimal?> dictIntDecimalNullable)
                        {
                            ProtoWriter.WriteFieldHeader(fieldIndex, WireType.StartGroup, writer);
                            var token = ProtoWriter.StartSubItem(value, writer);
                            foreach (var kvp in dictIntDecimalNullable)
                            {
                                ProtoWriter.WriteFieldHeader(1, WireType.Variant, writer);
                                ProtoWriter.WriteInt32(kvp.Key, writer);

                                if (kvp.Value.HasValue)
                                {
                                    ProtoWriter.WriteFieldHeader(2, WireType.Fixed64, writer);
                                    ProtoWriter.WriteDouble((double)kvp.Value.Value, writer);
                                }
                                // If value is null, you may choose to skip writing the value field
                            }
                            ProtoWriter.EndSubItem(token, writer);
                        }
                        else
                        {
                            throw new InvalidCastException($"Expected Dictionary<int, decimal> or Dictionary<int, decimal?> for variant, got {value?.GetType()}");
                        }
                    };
                default:
                    throw new NotImplementedException($"Unexpected db data type:{dbTypeName}");
            }
        }
    }
#pragma warning restore CS0618 // Type or member is obsolete
}
