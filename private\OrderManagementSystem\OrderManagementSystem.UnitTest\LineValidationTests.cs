﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using OrderManagementSystem.Common;
using OrderManagementSystem.Configuration;
using OrderManagementSystem.Controllers;
using OrderManagementSystem.DataAccessObjects;
using OrderManagementSystem.Entities;
using OrderManagementSystem.Entities.Internal;
using OrderManagementSystem.Interfaces;
using OrderManagementSystem.Monetize;
using OrderManagementSystem.Requests;
using OrderManagementSystem.Validators;

namespace OrderManagementSystem.UnitTest
{
    [TestClass]
    public class LineValidationTests
    {
        private static readonly int CustomerId = 123456;
        private static readonly long MediaPlanId = 100;
        private static readonly long ProductId1 = 300;
        private static readonly long ProductId2 = 301;

        private static ServiceProvider _serviceProvider;
        private static Mock<IProductDao> _rotationalProductMock;
        private static Mock<IProductDao> _roadblockProductMock;
        private static Mock<IProductDao> _sovProductMock;
        private static Mock<ICustomerDao> _customerMock;
        private static Mock<IMediaPlanDao> _mediaPlanMock;
        private static Mock<ISegmentDao> _segmentMock;
        private static Mock<ICountryDao> _countryMock;
        private static Mock<ILocationDataAccessObject> _locationMock;
        private static Mock<IDMADataAccessObject> _dmaTargetMock;
        private static Mock<IKeyValueDataAccessObject> _keyValueMock;
        private static HttpContext _httpContext;

        [ClassInitialize]
        public static void ClassInitialize(TestContext context)
        {
            var builder = WebApplication.CreateBuilder();
            builder.Services.AddHttpClient();
            builder.Services.AddSingleton<IProcDaoExecutor, ProcDaoExecutor>();
            builder.Services.AddSingleton<IProductDao, CachedProductDao>();
            builder.Services.AddSingleton<ICustomerDao, CachedCustomerDao>();
            builder.Services.AddSingleton<IMediaPlanDao, CachedMediaPlanDao>();
            builder.Services.AddSingleton<ILineDao, CachedLineDao>();
            builder.Services.AddSingleton<ISegmentDao, SegmentDao>();
            builder.Services.AddSingleton<ICountryDao, CountryDao>();
            builder.Services.AddSingleton<ILocationDataAccessObject, LocationDataAccessObject>();
            builder.Services.AddScoped<IDMADataAccessObject, DMADataAccessObject>();
            builder.Services.AddSingleton<IKeyValueDataAccessObject, KeyValueDataAccessObject>();
            _serviceProvider = builder.Services.BuildServiceProvider();

            //Create mock for OMS DAOs
            _customerMock = new Mock<ICustomerDao>();
            _customerMock.Setup(get => get.Get(It.IsAny<CustomerGetRequest>())).ReturnsAsync(new Result<Customer>()
            {
                Entity = new Customer { Id = CustomerId, Name = "TestCustomer", Timezone =  XandrTimeZoneHelper.GetMonetizeTimeZone("US/Pacific") }
            });

            _mediaPlanMock = new Mock<IMediaPlanDao>();
            _mediaPlanMock.Setup(get => get.Get(It.IsAny<MediaPlanGetRequest>())).ReturnsAsync(new Result<MediaPlan>()
            {
                Entity = GetValidExistingMediaPlan()
            });

            _rotationalProductMock = new Mock<IProductDao>();
            _rotationalProductMock.Setup(get => get.GetByProductId(It.IsAny<ProductGetRequest>())).ReturnsAsync(new Result<Product>()
            {
                Entity = GetValidExistingProduct(ProductType.Rotational)
            });
            _rotationalProductMock.Setup(get => get.GetNoPublisher(It.IsAny<ProductGetNoPublisherRequest>())).ReturnsAsync(new Result<Product[]>()
            {
                Entity = [GetValidExistingProduct(ProductType.Rotational)]
            });

            _sovProductMock = new Mock<IProductDao>();
            _sovProductMock.Setup(get => get.GetByProductId(It.IsAny<ProductGetRequest>())).ReturnsAsync(new Result<Product>()
            {
                Entity = GetValidExistingProduct(ProductType.SOV)
            });
            _sovProductMock.Setup(get => get.GetNoPublisher(It.IsAny<ProductGetNoPublisherRequest>())).ReturnsAsync(new Result<Product[]>()
            {
                Entity = [GetValidExistingProduct(ProductType.SOV)]
            });

            _roadblockProductMock = new Mock<IProductDao>();
            _roadblockProductMock.Setup(get => get.GetByProductId(It.IsAny<ProductGetRequest>())).ReturnsAsync(new Result<Product>()
            {
                Entity = GetValidExistingProduct(ProductType.Roadblock)
            });
            _roadblockProductMock.Setup(get => get.GetNoPublisher(It.IsAny<ProductGetNoPublisherRequest>())).ReturnsAsync(new Result<Product[]>()
            {
                Entity = [GetValidExistingProduct(ProductType.Roadblock)]
            });

            //Create mock for Xandr-based DAOs
            _segmentMock = new Mock<ISegmentDao>();
            _segmentMock.Setup(get => get.Get(It.IsAny<SegmentGetAllRequest>(), It.IsAny<bool>(), It.IsAny<bool>())).ReturnsAsync(new Result<XandrSegment[]>()
            {
                Entity = new[] { new XandrSegment { Id = -100, ShortName = "Test1" }, new XandrSegment { Id = -101, ShortName = "Test2" } }
            });

            _countryMock = new Mock<ICountryDao>();
            _countryMock.Setup(get => get.Get(It.IsAny<CountryGetAllRequest>(), It.IsAny<bool>())).ReturnsAsync(new Result<XandrCountry[]>()
            {
                Entity = [new() { Id = -1, Name = "Country1", Code = "C1" }, new() { Id = -2, Name = "Country2", Code = "C2" }]
            });

            _locationMock = new Mock<ILocationDataAccessObject>();

            _locationMock.Setup(l => l.GetAllCountries(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrCountry[]>
                {
                    Entity = new[]
                    {
                        new XandrCountry { Id = -1, Name = "USA", Code = "US" }
                    }
                });

            _locationMock.Setup(l => l.GetAllRegions(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrRegion[]>
                {
                    Entity = new[]
                    {
            new XandrRegion { Id = -10, CountryId = -1, Name = "California" }
                    }
                });

            _locationMock.Setup(l => l.GetCitiesByIdsAsync(It.IsAny<IEnumerable<int>>(), It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrCity[]>
                {
                    Entity = new[]
                    {
            new XandrCity { Id = -100, Name = "Los Angeles", RegionId = -10, CountryId = -1 }
                    }
                });

            _dmaTargetMock = new Mock<IDMADataAccessObject>();
            _dmaTargetMock.Setup(m => m.GetAllAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()))
                .ReturnsAsync(new Result<XandrDMA[]>
                {
                    Entity = new[] { new XandrDMA { Id = 1, Name = "DMA Target 1", Country = "USA" } }
                });

            _keyValueMock = new Mock<IKeyValueDataAccessObject>();
            _keyValueMock.Setup(m => m.GetValue("Gender", "Male", It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingValue>
                {
                    Entity = new XandrTargetingValue
                    {
                        Name = "Male",
                        TargetingKeyId = 123
                    }
                });

            _httpContext = new DefaultHttpContext();

        }

        [TestMethod]
        public void Post_ValidationFail_MismatchLineProdType()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                Type = LineType.Roadblock,
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                TargetImpressions = 100000
            };
            var product = new Product
            {
                Type = ProductType.Rotational,
                PlacementIds = new long[] { 1 },
                Name = "TestProduct",
                Family = "TestFamily",
                Contacts = new string[] { "<EMAIL>" },
                CurrencyCode = "USD",
                BaseCPMRate = 1.0,
                TimeZoneId = 1
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);
            var result = validator.Validate(postRequest).Result;
            Assert.IsFalse(result.Succeeded);
        }
        [TestMethod]
        public void Post_ValidationPass_UnknownLineType()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                Type = LineType._,
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public void Post_ValidationPass()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded);
        }
        
        [TestMethod]
        public void ValidateTypePass_MakeGood()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);

            var line = new Line
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Type = LineType.MakeGood,
                Name = "TestLine",
                Description = "Line Description",
                Status = LineStatus.Draft,
                Cpd = null,
                Cpm = null,
                TargetImpressions = null,
                TargetSpend = null,
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
            };

            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, line, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public void ValidateTypePass_Bonus()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _roadblockProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);

            var line = new Line
            {
                MediaPlanId = 100,
                ProductId = ProductId1,
                Type = LineType.Bonus,
                Name = "TestLine",
                Description = "Line Description",
                Status = LineStatus.Draft,
                Cpd = null,
                Cpm = null,
                TargetImpressions = null,
                TargetSpend = null,
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
            };

            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, line, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded);
        }


        [TestMethod]
        public void Post_ValidationPass_Targeting()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                Targets =
                [
                    new AudienceTarget() { Ids = [-100], AudienceTargetingType = AudienceTargetingType.AND },
                    new LocationTarget() { Ids = [-1] },
                    new LocationTarget() { Ids = [-2] },
                    new DeviceTarget() { DeviceTypes = [DeviceType.CTV] },
                    new FrequencyAndRecencyTarget() { FrequencyAndRecencys = new[] { new FrequencyAndRecency() { Unit = FrequencyUnit.Hour, Number = 12 } } },
                ],
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Console.WriteLine(result.ToString());
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public void Post_ValidationFail_Status()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                Status = LineStatus.Approved,
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("ReadOnlyValue"));
        }

        [TestMethod]
        public void Post_ValidationFail_StartDate1()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddMonths(-1),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("NotInParentRange"));
        }

        [TestMethod]
        public void Post_ValidationFail_StartDate2()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(4),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("InvalidRange"));
        }

        [TestMethod]
        public void Post_ValidationFail_StartDate3()
        {
            //Customer is EST, Line is PST. After translation, Line will be outside the start range, so should fail
            var mediaPlanForTest = GetValidExistingMediaPlan();
            mediaPlanForTest.StartDate = DateTime.UtcNow.Date.AddDays(2);

            var mediaPlanMock = new Mock<IMediaPlanDao>();
            mediaPlanMock.Setup(get => get.Get(It.IsAny<MediaPlanGetRequest>())).ReturnsAsync(new Result<MediaPlan>()
            {
                Entity = mediaPlanForTest
            });

            var validator = new LinePostValidator(_customerMock.Object, mediaPlanMock.Object, _roadblockProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = MediaPlanId,
                ProductId = ProductId1,
                Name = "ValidationTest",
                Description = "Desc",
                Cpm = null,
                Cpd = 3.0,
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3)
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("NotInParentRange"));
        }

        [TestMethod]
        public void Post_ValidationFail_EndDate1()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddMonths(2),
                Cpm = 3.1,
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("NotInParentRange"));
        }

        [TestMethod]
        public void Post_ValidationFail_EndDate2()
        {
            // Create a mock for the product with a MaxBookingDate
            var productForTest = GetValidExistingProduct(ProductType.Rotational);
            productForTest.MaxBookingDate = DateTime.UtcNow.Date.AddDays(1);

            var productMock = new Mock<IProductDao>();
            productMock.Setup(get => get.GetNoPublisher(It.IsAny<ProductGetNoPublisherRequest>())).ReturnsAsync(new Result<Product[]>()
            {
                Entity = [productForTest]
            });

            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, productMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("EndDateBeyondProductMaxBookingDate"));
        }

        [TestMethod]
        public void Post_ValidationFail_Targeting_UnsupportedTargetType()
        {
            var productMock = new Mock<IProductDao>();
            productMock.Setup(get => get.GetNoPublisher(It.IsAny<ProductGetNoPublisherRequest>())).ReturnsAsync(new Result<Product[]>()
            {
                Entity = [new Product()
                {
                    PublisherId = -123,
                    Id = 1,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Family = "Fam",
                    Contacts = ["testuser", "someone"],
                    PlacementIds = [-1234],
                    Type = ProductType.Rotational,
                    BaseCPMRate = 3.2,
                    MaxPercentDiscount = 15.0,
                    CurrencyCode = "USD",
                    SupportedTargets = [
                        new SupplyTarget() { TargetTypeId = TargetType.DayPart, TargetPremium = 0 },
                    ],
                    TimeZoneId = TimeZoneTestConstant.PST_TIMEZONE_ID
                }]
            });

            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, productMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                Targets =
                [
                    new AudienceTarget() { Ids = [-100], AudienceTargetingType = AudienceTargetingType.AND },
                    new LocationTarget() { Ids = [-1] },
                    new LocationTarget() { Ids = [-2] },
                    new DeviceTarget() { DeviceTypes = [DeviceType.CTV] },
                    new FrequencyAndRecencyTarget() { FrequencyAndRecencys = new[] { new FrequencyAndRecency() { Unit = FrequencyUnit.Hour, Number = 12 } } },
                ],
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("UnsupportedTargetType"));
        }

        [TestMethod]
        public void Post_ValidationFail_Targeting_InvalidSegment()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                Targets =
                [
                    new AudienceTarget() { Ids = [-102], AudienceTargetingType = AudienceTargetingType.AND }
                ],
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("InvalidSegmentForTargeting"));
        }

        [TestMethod]
        public void Post_ValidationFail_Targeting_InvalidAudienceTargetingType()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                Targets =
                [
                    new AudienceTarget() { Ids = [-100], AudienceTargetingType = AudienceTargetingType._ }
                ],
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("InvalidValue"));
        }

        [TestMethod]
        public void Post_ValidationFail_Targeting_InvalidAudienceTargets()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                Targets =
                [
                    new AudienceTarget() { Ids = [-100], AudienceTargetingType = AudienceTargetingType.AND },
                    new AudienceTarget() { Ids = [-101], AudienceTargetingType = AudienceTargetingType.OR }
                ],
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("MultipleAudienceTargetsOfSameTimeNotSupported"));
        }

        [TestMethod]
        public void Post_ValidationFail_Targeting_InvalidLocation()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                Targets =
                [
                    new LocationTarget() { Ids = [-102] }
                ],
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("InvalidLocationForTargeting"));
        }

        [TestMethod]
        public void Post_ValidationFail_Targeting_InvalidLocation2()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                Targets =
                [
                    new LocationTarget() { Locations = [new Location() { Id = 100, IsExcluded = false }] }
                ],
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("LocationTargetInvalidData"));
        }

        [TestMethod]
        public void Post_ValidationFail_Targeting_InvalidLocation3()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                Targets =
                [
                    new LocationTarget() { Locations = [new Location() { Id = 100, IsExcluded = false, Type = TargetType.DMA }] }
                ],
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("LocationTargetInvalidData"));
        }

        [TestMethod]
        public void Post_ValidationFail_Targeting_InvalidFAR()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                Targets =
                [
                    new FrequencyAndRecencyTarget() { FrequencyAndRecencys = [new FrequencyAndRecency { Unit = FrequencyUnit.Day, Number = 3}, 
                        new FrequencyAndRecency { Unit = FrequencyUnit.Hour, Number = 10},
                        new FrequencyAndRecency { Unit = FrequencyUnit.Week, Number = 5},
                        new FrequencyAndRecency { Unit = FrequencyUnit.Month, Number = 2},
                        new FrequencyAndRecency { Unit = FrequencyUnit.Lifetime, Number = 1},
                        new FrequencyAndRecency { MinutesPerImpression = 10}, 
                        new FrequencyAndRecency { Unit = FrequencyUnit.Day, Number = 10}] }
                ],
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("OnlyOneDailyFrequencyTargetSupported"));
        }

        [TestMethod]
        public void Post_ValidationFail_Rotational1()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("TooFewValues"));
        }

        [TestMethod]
        public void Post_ValidationFail_Rotational2()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                TargetImpressions = 100000,
                TargetSpend = 1223.45
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("TooManyValues"));
        }

        [TestMethod]
        [Ignore] //Ignore test until Make-good/Bonus is GA from UI
        public void Post_ValidationFail_CpdLessThanZero()
        {
            var postValidator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _roadblockProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var invalidLine = new Line
            {
                MediaPlanId = MediaPlanId,
                ProductId = ProductId1,
                Name = "ValidationTest",
                Description = "Desc",
                Cpm = null,
                Cpd = 0,
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3)
            };

            var validateResult = postValidator.Validate(new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, invalidLine, _httpContext)).Result;
            Assert.IsNotNull(validateResult);
            Assert.IsFalse(validateResult.Succeeded, "post validation should not be successful");

            (string[] actualProperty, string actualMessage) = GetErrorMessageFromResult(validateResult);
            Assert.AreEqual("Cpd", actualProperty[0], "Error Property does not match");
            Assert.AreEqual("InvalidValue", actualMessage, "Error Message does not match");
        }


        [TestMethod]
        public void Post_ValidationFail_CpdNonRoadBlock()
        {
            var postValidator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var invalidLine = new Line
            {
                MediaPlanId = MediaPlanId,
                ProductId = ProductId1,
                Name = "ValidationTest",
                Description = "Desc",
                Cpm = 3.0,
                Cpd = 2.0,
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                TargetImpressions = 100000
            };

            var validateResult = postValidator.Validate(new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, invalidLine, _httpContext)).Result;
            Assert.IsNotNull(validateResult);
            Assert.IsFalse(validateResult.Succeeded, "post validation should not be successful");

            (string[] actualProperty, string actualMessage) = GetErrorMessageFromResult(validateResult);
            Assert.AreEqual("Cpd", actualProperty[0], "Error Property does not match");
            Assert.AreEqual("InvalidValue", actualMessage, "Error Message does not match");
        }

        [TestMethod]
        public void Post_ValidationFail_MediaPlanInCommitState()
        {
            var mediaPlanMock = new Mock<IMediaPlanDao>();
            var mediaPlan = GetValidExistingMediaPlan();
            mediaPlan.CommitStatus = OfflineOperationStatus.Queued;
            mediaPlan.CommitModifiedDateTime = DateTime.UtcNow.AddSeconds(-30);
            mediaPlanMock.Setup(get => get.Get(It.IsAny<MediaPlanGetRequest>())).ReturnsAsync(new Result<MediaPlan>()
            {
                Entity = mediaPlan
            });

            var validator = new LinePostValidator(_customerMock.Object, mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                TargetImpressions = 100000
            };
            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded, "post validation should not be successful");

            (string[] actualProperty, string actualMessage) = GetErrorMessageFromResult(result);
            Assert.AreEqual("MediaPlanId", actualProperty[0], "Error Property does not match");
            Assert.AreEqual("UpdatesDuringCommitNotAllowed", actualMessage, "Error Message does not match");
        }

        [TestMethod]
        public void Delete_ValidationPass()
        {
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>()
            {
                Entity = new Line()
                {
                    MediaPlanId = 100,
                    Id = 1234,
                    ProductId = ProductId2,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Status = LineStatus.Approved,
                    StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                    EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                    Cpm = 3.1,
                    TargetImpressions = 100000
                }
            });

            var validator = new LineDeleteValidator(lineMock.Object, _rotationalProductMock.Object);
            var postRequest = new LineDeleteRequest(new Mock<ILogger<LineController>>().Object, CustomerId, MediaPlanId, 1234, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public void Delete_ValidationFail_InvalidLine()
        {
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>()
            {
                Entity = null,
                Errors = [new Error("LineGetProcessor", "-1")]
            });

            var validator = new LineDeleteValidator(lineMock.Object, _rotationalProductMock.Object);
            var postRequest = new LineDeleteRequest(new Mock<ILogger<LineController>>().Object, CustomerId, MediaPlanId, 1234, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);

            var errors = result.Errors;
            Assert.AreEqual(2, errors.Count, "Error count does not match");
            var error = errors.Last(); //First error is the error from LineGetProcessor not having a valid Line. 2nd error is from Delete validator

            Assert.AreEqual("Id", error.Property[0], "Error Property does not match");
            Assert.AreEqual("NotFound", error.Message, "Error Message does not match");
        }

        [TestMethod]
        public void Delete_ValidationPass_Committed()
        {
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>()
            {
                Entity = new Line()
                {
                    MediaPlanId = 100,
                    Id = 1234,
                    ProductId = ProductId2,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Status = LineStatus.Synced,
                    StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                    EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                    Cpm = 3.1,
                    TargetImpressions = 100000,
                    MonetizeLineItemId = 100,
                    MonetizeBudgetIntervalId = 1000,
                }
            });

            var product = GetValidExistingProduct(ProductType.Rotational);
            var productMock = new Mock<IProductDao>();
            productMock.Setup(p => p.GetNoPublisher(It.IsAny<ProductGetNoPublisherRequest>()))
                .ReturnsAsync(new Result<Product[]> { Entity = new[] { product } });

            var validator = new LineDeleteValidator(lineMock.Object, productMock.Object);
            var postRequest = new LineDeleteRequest(new Mock<ILogger<LineController>>().Object, CustomerId, MediaPlanId, 1234, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public void Delete_ValidationFail_Committed()
        {
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>()
            {
                Entity = new Line()
                {
                    MediaPlanId = 100,
                    Id = 1234,
                    ProductId = ProductId2,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Status = LineStatus.Synced,
                    StartDateTime = DateTime.UtcNow.Date.AddDays(-1),
                    EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                    Cpm = 3.1,
                    TargetImpressions = 100000,
                    MonetizeLineItemId = 100,
                    MonetizeBudgetIntervalId = 1000,
                }
            });

            var product = GetValidExistingProduct(ProductType.Rotational);
            var productMock = new Mock<IProductDao>();
            productMock.Setup(p => p.GetNoPublisher(It.IsAny<ProductGetNoPublisherRequest>()))
                .ReturnsAsync(new Result<Product[]> { Entity = new[] { product } });

            var validator = new LineDeleteValidator(lineMock.Object, productMock.Object);
            var postRequest = new LineDeleteRequest(new Mock<ILogger<LineController>>().Object, CustomerId, MediaPlanId, 1234, _httpContext);

            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded, "Delete validation should not be successful");

            (string[] actualProperty, string actualMessage) = GetErrorMessageFromResult(result);
            Assert.AreEqual("Id", actualProperty[0], "Error Property does not match");
            Assert.AreEqual("OperationNotAllowed", actualMessage, "Error Message does not match");
        }

        [TestMethod]
        public void Put_ValidationPass()
        {
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>()
            {
                Entity = new Line()
                {
                    MediaPlanId = 100,
                    Id = 1234,
                    ProductId = ProductId2,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Status = LineStatus.Approved,
                    StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                    EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                    Cpm = 3.1,
                    TargetImpressions = 100000
                }
            });

            var mediaPlan = GetValidExistingMediaPlan();
            mediaPlan.EndDate = DateTime.UtcNow.Date.AddDays(5);

            _mediaPlanMock.Setup(get => get.Get(It.IsAny<MediaPlanGetRequest>())).ReturnsAsync(new Result<MediaPlan>()
            {
                Entity = mediaPlan
            });

            var validator = new LinePutValidator(_customerMock.Object, _mediaPlanMock.Object, lineMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                Id = 1234,
                ProductId = ProductId2,
                Name = "ValidationTest_update",
                Description = "Desc_update",
                StartDateTime = DateTime.UtcNow.Date.AddDays(3),
                EndDateTime = DateTime.UtcNow.Date.AddDays(4),
                Cpm = 3.0,
                TargetImpressions = 100000
            };
            var putRequest = new LinePutRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(putRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public void Put_ValidationPass_SOV()
        {
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>()
            {
                Entity = new Line()
                {
                    MediaPlanId = 100,
                    Id = 1234,
                    ProductId = ProductId2,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Status = LineStatus.Approved,
                    StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                    EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                    Cpm = 3.1,
                    TargetImpressions = null,
                    PercentImpressions = 10,
                }
            });

            var mediaPlan = GetValidExistingMediaPlan();
            mediaPlan.EndDate = DateTime.UtcNow.Date.AddDays(5);

            _mediaPlanMock.Setup(get => get.Get(It.IsAny<MediaPlanGetRequest>())).ReturnsAsync(new Result<MediaPlan>()
            {
                Entity = mediaPlan
            });

            var validator = new LinePutValidator(_customerMock.Object, _mediaPlanMock.Object, lineMock.Object, _sovProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                Id = 1234,
                ProductId = ProductId2,
                Name = "ValidationTest_update",
                Description = "Desc_update",
                StartDateTime = DateTime.UtcNow.Date.AddDays(3),
                EndDateTime = DateTime.UtcNow.Date.AddDays(4),
                Cpm = 3.0,
                TargetImpressions = null,
                PercentImpressions = 50,
            };
            var putRequest = new LinePutRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(putRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public void Put_ValidationFail_MediaPlan()
        {
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>()
            {
                Entity = new Line()
                {
                    MediaPlanId = 99,
                    Id = 1234,
                    ProductId = ProductId2,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Status = LineStatus.Approved,
                    StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                    EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                    Cpm = 3.1,
                    TargetImpressions = 100000
                }
            });

            var validator = new LinePutValidator(_customerMock.Object, _mediaPlanMock.Object, lineMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                Id = 1234,
                ProductId = ProductId2,
                Name = "ValidationTest_update",
                Description = "Desc_update",
                StartDateTime = DateTime.UtcNow.Date.AddDays(3),
                EndDateTime = DateTime.UtcNow.Date.AddDays(4),
                Cpm = 3.0,
                TargetImpressions = 100000
            };
            var putRequest = new LinePutRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(putRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("ReadOnlyValue"));
        }

        [TestMethod]
        public void Put_ValidationFail_Product()
        {
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>()
            {
                Entity = new Line()
                {
                    MediaPlanId = 100,
                    Id = 1234,
                    ProductId = 2,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Status = LineStatus.Approved,
                    StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                    EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                    Cpm = 3.1,
                    TargetImpressions = 100000
                }
            });

            var validator = new LinePutValidator(_customerMock.Object, _mediaPlanMock.Object, lineMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                Id = 1234,
                ProductId = ProductId2,
                Name = "ValidationTest_update",
                Description = "Desc_update",
                StartDateTime = DateTime.UtcNow.Date.AddDays(3),
                EndDateTime = DateTime.UtcNow.Date.AddDays(4),
                Cpm = 3.0,
                TargetImpressions = 100000
            };
            var putRequest = new LinePutRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(putRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("ReadOnlyValue"));
        }

        [TestMethod]
        public void Put_ValidationFail_EndDate()
        {
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>()
            {
                Entity = new Line()
                {
                    MediaPlanId = 100,
                    Id = 1234,
                    ProductId = ProductId2,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Status = LineStatus.Approved,
                    StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                    EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                    Cpm = 3.1,
                    TargetImpressions = 100000
                }
            });

            // Create a mock for the product with a MaxBookingDate
            var product = GetValidExistingProduct(ProductType.Rotational);
            product.MaxBookingDate = DateTime.UtcNow.Date.AddDays(3);

            var productMock = new Mock<IProductDao>();
            productMock.Setup(p => p.GetNoPublisher(It.IsAny<ProductGetNoPublisherRequest>()))
                .ReturnsAsync(new Result<Product[]> { Entity = new[] { product } });


            var validator = new LinePutValidator(_customerMock.Object, _mediaPlanMock.Object, lineMock.Object, productMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                Id = 1234,
                ProductId = ProductId2,
                Name = "ValidationTest_update",
                Description = "Desc_update",
                StartDateTime = DateTime.UtcNow.Date.AddDays(3),
                EndDateTime = DateTime.UtcNow.Date.AddDays(4),
                Cpm = 3.0,
                TargetImpressions = 100000
            };
            var putRequest = new LinePutRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(putRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("EndDateBeyondProductMaxBookingDate"));
        }

        [TestMethod]
        public void Put_ValidationFail_Synced1()
        {
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>()
            {
                Entity = new Line()
                {
                    MediaPlanId = 100,
                    Id = 1234,
                    ProductId = ProductId2,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Status = LineStatus.Synced,
                    StartDateTime = DateTime.UtcNow.Date.AddDays(-1),
                    EndDateTime = DateTime.UtcNow.Date.AddDays(4),
                    Cpm = 3.1,
                    TargetImpressions = 100000,
                    MonetizeLineItemId = 1,
                    MonetizeBudgetIntervalId = 10
                }
            });

            var validator = new LinePutValidator(_customerMock.Object, _mediaPlanMock.Object, lineMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                Id = 1234,
                ProductId = ProductId2,
                Name = "ValidationTest_update",
                Description = "Desc_update",
                StartDateTime = DateTime.UtcNow.Date.AddDays(1),
                EndDateTime = DateTime.UtcNow.Date.AddDays(4),
                Cpm = 3.0,
                TargetImpressions = 100000,
                MonetizeLineItemId = 1,
                MonetizeBudgetIntervalId = 10
            };
            var putRequest = new LinePutRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(putRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("ReadOnlyValue"));
        }

        [TestMethod]
        public void Put_ValidationFail_Synced2()
        {
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>()
            {
                Entity = new Line()
                {
                    MediaPlanId = 100,
                    Id = 1234,
                    ProductId = ProductId2,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Status = LineStatus.Synced,
                    StartDateTime = DateTime.UtcNow.Date.AddDays(-2),
                    EndDateTime = DateTime.UtcNow.Date.AddDays(-1),
                    Cpm = 3.1,
                    TargetImpressions = 100000,
                    MonetizeLineItemId = 1,
                    MonetizeBudgetIntervalId = 10
                }
            });

            var validator = new LinePutValidator(_customerMock.Object, _mediaPlanMock.Object, lineMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                Id = 1234,
                ProductId = ProductId2,
                Name = "ValidationTest_update",
                Description = "Desc_update",
                StartDateTime = DateTime.UtcNow.Date.AddDays(-2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(1),
                Cpm = 3.0,
                TargetImpressions = 100000,
                MonetizeLineItemId = 1,
                MonetizeBudgetIntervalId = 10
            };
            var putRequest = new LinePutRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(putRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("ReadOnlyValue"));
        }

        [TestMethod]
        public void Put_ValidationFail_SyncedTargeting1()
        {
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>()
            {
                Entity = new Line()
                {
                    MediaPlanId = 100,
                    Id = 1234,
                    ProductId = ProductId2,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Status = LineStatus.Synced,
                    StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                    EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                    Cpm = 3.1,
                    TargetImpressions = 100000,
                    MonetizeLineItemId = 1,
                    MonetizeBudgetIntervalId = 10
                }
            });

            var validator = new LinePutValidator(_customerMock.Object, _mediaPlanMock.Object, lineMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                Id = 1234,
                ProductId = ProductId2,
                Name = "ValidationTest_update",
                Description = "Desc_update",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.0,
                Targets =
                [
                    new AudienceTarget() { Ids = [-100], AudienceTargetingType = AudienceTargetingType.AND },
                    new LocationTarget() { Ids = [-1] },
                    new LocationTarget() { Ids = [-2] },
                    new DeviceTarget() { DeviceTypes = [DeviceType.CTV] },
                    new FrequencyAndRecencyTarget() { FrequencyAndRecencys = new[] { new FrequencyAndRecency() { Unit = FrequencyUnit.Hour, Number = 12 } } },
                ],
                TargetImpressions = 100000,
                MonetizeLineItemId = 1,
                MonetizeBudgetIntervalId = 10
            };
            var putRequest = new LinePutRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(putRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded);
            //Assert.IsTrue(result.ToString().Contains("ReadOnlyValue"));
        }

        [TestMethod]
        public void Put_ValidationFail_SyncedTargeting2()
        {
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>()
            {
                Entity = new Line()
                {
                    MediaPlanId = 100,
                    Id = 1234,
                    ProductId = ProductId2,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Status = LineStatus.Synced,
                    StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                    EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                    Cpm = 3.1,
                    Targets =
                    [
                    new AudienceTarget() { Ids = [-100], AudienceTargetingType = AudienceTargetingType.AND },
                    new LocationTarget() { Ids = [-1] },
                    new LocationTarget() { Ids = [-2] },
                    new DeviceTarget() { DeviceTypes = [DeviceType.CTV] },
                        new FrequencyAndRecencyTarget() { FrequencyAndRecencys = new[] { new FrequencyAndRecency() { Unit = FrequencyUnit.Hour, Number = 12 } } },
                    ],
                    TargetImpressions = 100000,
                    MonetizeLineItemId = 1,
                    MonetizeBudgetIntervalId = 10
                }
            });

            var validator = new LinePutValidator(_customerMock.Object, _mediaPlanMock.Object, lineMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                Id = 1234,
                ProductId = ProductId2,
                Name = "ValidationTest_update",
                Description = "Desc_update",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.0,
                Targets =
                [
                    new AudienceTarget() { Ids = [-100], AudienceTargetingType = AudienceTargetingType.AND },
                    new LocationTarget() { Ids = [-1] },
                    new LocationTarget() { Ids = [-2] },
                    new DeviceTarget() { DeviceTypes = [DeviceType.Tablet] }, //This is the targeting option that has changed
                    new FrequencyAndRecencyTarget() { FrequencyAndRecencys = new[] { new FrequencyAndRecency() { Unit = FrequencyUnit.Hour, Number = 12 } } },
                ],
                TargetImpressions = 100000,
                MonetizeLineItemId = 1,
                MonetizeBudgetIntervalId = 10
            };
            var putRequest = new LinePutRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(putRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded);
            //Assert.IsTrue(result.ToString().Contains("ReadOnlyValue"));
        }

        [TestMethod]
        public void Put_ValidationFail_SyncedTargeting3()
        {
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>()
            {
                Entity = new Line()
                {
                    MediaPlanId = 100,
                    Id = 1234,
                    ProductId = ProductId2,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Status = LineStatus.Synced,
                    StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                    EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                    Cpm = 3.1,
                    Targets =
                    [
                    new AudienceTarget() { Ids = [-100], AudienceTargetingType = AudienceTargetingType.AND },
                    new LocationTarget() { Ids = [-1] },
                    new LocationTarget() { Ids = [-2] },
                    new DeviceTarget() { DeviceTypes = [DeviceType.CTV] },
                        new FrequencyAndRecencyTarget() { FrequencyAndRecencys = new[] { new FrequencyAndRecency() { Unit = FrequencyUnit.Hour, Number = 12 } } },
                    ],
                    TargetImpressions = 100000,
                    MonetizeLineItemId = 1,
                    MonetizeBudgetIntervalId = 10
                }
            });

            var validator = new LinePutValidator(_customerMock.Object, _mediaPlanMock.Object, lineMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                Id = 1234,
                ProductId = ProductId2,
                Name = "ValidationTest_update",
                Description = "Desc_update",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.0,
                Targets =
                [
                    new AudienceTarget() { Ids = [-100], AudienceTargetingType = AudienceTargetingType.OR }, //This is the targeting option that has changed
                    new LocationTarget() { Ids = [-1] },
                    new LocationTarget() { Ids = [-2] },
                    new DeviceTarget() { DeviceTypes = [DeviceType.CTV] },
                    new FrequencyAndRecencyTarget() { FrequencyAndRecencys = new[] { new FrequencyAndRecency() { Unit = FrequencyUnit.Hour, Number = 11 } } }, //This is the targeting option that has changed
                ],
                TargetImpressions = 100000,
                MonetizeLineItemId = 1,
                MonetizeBudgetIntervalId = 10
            };
            var putRequest = new LinePutRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(putRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded);
            //Assert.IsTrue(result.ToString().Contains("ReadOnlyValue"));
        }

        [TestMethod]
        public void Put_ValidationFail_MediaPlanInCommitState()
        {
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>()
            {
                Entity = new Line()
                {
                    MediaPlanId = 100,
                    Id = 1234,
                    ProductId = ProductId2,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Status = LineStatus.Approved,
                    StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                    EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                    Cpm = 3.1,
                    TargetImpressions = 100000
                }
            });

            var mediaPlanMock = new Mock<IMediaPlanDao>();
            var mediaPlan = GetValidExistingMediaPlan();
            mediaPlan.CommitStatus = OfflineOperationStatus.Queued;
            mediaPlan.CommitModifiedDateTime = DateTime.UtcNow.AddSeconds(-30);
            mediaPlanMock.Setup(get => get.Get(It.IsAny<MediaPlanGetRequest>())).ReturnsAsync(new Result<MediaPlan>()
            {
                Entity = mediaPlan
            });

            var validator = new LinePutValidator(_customerMock.Object, mediaPlanMock.Object, lineMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                MediaPlanId = 100,
                Id = 1234,
                ProductId = ProductId2,
                Name = "ValidationTest_update",
                Description = "Desc_update",
                StartDateTime = DateTime.UtcNow.Date.AddDays(3),
                EndDateTime = DateTime.UtcNow.Date.AddDays(4),
                Cpm = 3.0,
                TargetImpressions = 100000
            };
            var putRequest = new LinePutRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(putRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("UpdatesDuringCommitNotAllowed"));
        }

        [TestMethod]
        public void Post_ValidationPass_WithNoPlacements()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);

            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest_NoPlacements",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                TargetImpressions = 100000
            };

            // Set product with no placements
            var productWithNoPlacements = new Product()
            {
                PublisherId = -123,
                Id = ProductId2,
                Name = "ValidationTest_NoPlacements",
                Description = "Desc",
                Family = "Fam",
                Contacts = new[] { "<EMAIL>", "<EMAIL>" },
                CurrencyCode = "USD",
                BaseCPMRate = 3.2,
                TimeZoneId = TimeZoneTestConstant.PST_TIMEZONE_ID,
                PlacementIds = null,  // Optional, no placements
                Type = ProductType.Rotational
            };

            var productMock = new Mock<IProductDao>();
            productMock.Setup(p => p.GetByProductId(It.IsAny<ProductGetRequest>())).ReturnsAsync(new Result<Product>
            {
                Entity = productWithNoPlacements
            });

            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);
            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded, "Expected validation to pass even with no placements.");
        }

        [TestMethod]
        public void Post_ValidationPass_EmptyPlacements()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);

            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest_EmptyPlacements",
                Description = "Desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 3.1,
                TargetImpressions = 100000
            };

            // Simulate product with empty placements array
            var productWithEmptyPlacements = new Product()
            {
                PublisherId = -123,
                Id = ProductId2,
                Name = "ValidationTest_EmptyPlacements",
                Family = "Fam",
                Contacts = new[] { "<EMAIL>", "<EMAIL>" },
                CurrencyCode = "USD",
                BaseCPMRate = 3.2,
                TimeZoneId = TimeZoneTestConstant.PST_TIMEZONE_ID,
                PlacementIds = Array.Empty<long>(),  // Empty array (optional)
                Type = ProductType.Rotational
            };

            var productMock = new Mock<IProductDao>();
            productMock.Setup(p => p.GetByProductId(It.IsAny<ProductGetRequest>())).ReturnsAsync(new Result<Product>
            {
                Entity = productWithEmptyPlacements
            });

            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);
            var result = validator.Validate(postRequest).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded, "Expected validation to pass with empty placements.");
        }

        [TestMethod]
        public void GetAll_ValidationFail_InvalidSkip()
        {
            var validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, null,null,-1, null);
            var result = validator.Validate(request).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Failed, "Call should be failed");
            Assert.AreEqual("InvalidValue", result.Errors.First().Message, "Error Message does not match");
            Assert.AreEqual("Skip", result.Errors.First().Property[0], "Error Property does not match");
        }

        [TestMethod]
        public void GetAll_ValidationFail_InvalidTop()
        {
            var validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, null, null,null, -1);
            var result = validator.Validate(request).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Failed, "Call should be failed");
            Assert.AreEqual("InvalidValue", result.Errors.First().Message, "Error Message does not match");
            Assert.AreEqual("Top", result.Errors.First().Property[0], "Error Property does not match");
        }

        [TestMethod]
        public void GetAll_ValidationPass_Pagination()
        {
            var validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, null ,null,1, 1);
            var result = validator.Validate(request).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded, "Call should be successful");
        }

        [TestMethod]
        public void GetAll_ValidationFail_InvalidFilterInput()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "test", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("filter", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_InvalidFilterField()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"test\",\"operator\":\"eq\",\"value\":\"TestLine\"}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Field", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_InvalidFilterOperator()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Name\",\"operator\":\"test\",\"value\":\"TestLine\"}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidOperation", result.Errors[0].Message);
            Assert.AreEqual("Operator", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterId()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"ID\",\"operator\":\"gt\",\"value\":42}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidOperation", result.Errors[0].Message);
            Assert.AreEqual("Operator", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterStatus1()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Status\",\"operator\":\"gt\",\"value\":[\"Approved\", \"test\"]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidOperation", result.Errors[0].Message);
            Assert.AreEqual("Operator", result.Errors[0].Property[0]);
            Assert.AreEqual("InvalidValue", result.Errors[1].Message);
            Assert.AreEqual("Value", result.Errors[1].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterStatus2()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Status\",\"operator\":\"eq\",\"value\":[\"Approved\", \"Deleted\"]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterStatus3()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Status\",\"operator\":\"in\",\"value\":\"Approved\"}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterStatus4()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Status\",\"operator\":\"eq\",\"value\":\"test\"}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterType1()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Type\",\"operator\":\"gt\",\"value\":[\"Rotational\", \"test\"]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidOperation", result.Errors[0].Message);
            Assert.AreEqual("Operator", result.Errors[0].Property[0]);
            Assert.AreEqual("InvalidValue", result.Errors[1].Message);
            Assert.AreEqual("Value", result.Errors[1].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterType2()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Type\",\"operator\":\"eq\",\"value\":[\"Roadblock\", \"Bulk\"]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterType3()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Type\",\"operator\":\"in\",\"value\":\"Rotational\"}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterType4()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Type\",\"operator\":\"eq\",\"value\":\"test\"}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterDate1()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"StartDate\",\"operator\":\"eq\",\"value\":[\"2025-03-05T14:30:00\", \"2025-03-06T14:30:00\"]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterDate2()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"StartDate\",\"operator\":\"between\",\"value\":[\"2025-03-05T14:30:00\", \"test\"]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterDate3()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"StartDate\",\"operator\":\"between\",\"value\":[\"2025-03-05T14:30:00\"]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("TooFewValues", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterDate4()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"StartDate\",\"operator\":\"between\",\"value\":[\"2025-03-06T14:30:00\", \"2025-03-05T14:30:00\"]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterDate5()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"StartDate\",\"operator\":\"between\",\"value\":[\"2025-03-05T14:30:00\", \"2025-03-06T14:30:00\", \"2025-03-07T14:30:00\"]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("TooManyValues", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterDate6()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"StartDate\",\"operator\":\"between\",\"value\":\"2025-03-05T14:30:00\"}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterDate7()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"StartDate\",\"operator\":\"eq\",\"value\":\"test\"}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        public void GetAll_ValidationFail_FilterBudget1()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Budget\",\"operator\":\"gt\",\"value\":[40, 42]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterBudget2()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Budget\",\"operator\":\"between\",\"value\":[42, \"test\"]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterBudget3()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Budget\",\"operator\":\"between\",\"value\":[42]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("TooFewValues", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterBudget4()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Budget\",\"operator\":\"between\",\"value\":[42, 40]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterBudget5()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Budget\",\"operator\":\"between\",\"value\":[41, 42, 43]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("TooManyValues", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterBudget6()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Budget\",\"operator\":\"between\",\"value\":42}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterBudget7()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Budget\",\"operator\":\"eq\",\"value\":\"test\"}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterEstimate1()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"EstimatedImpressions\",\"operator\":\"gt\",\"value\":[40, 42]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterEstimate2()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"EstimatedImpressions\",\"operator\":\"between\",\"value\":[42, \"test\"]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterEstimate3()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"EstimatedImpressions\",\"operator\":\"between\",\"value\":[42]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("TooFewValues", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterEstimate4()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"EstimatedImpressions\",\"operator\":\"between\",\"value\":[42, 40]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterEstimate5()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"EstimatedImpressions\",\"operator\":\"between\",\"value\":[41, 42, 43]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("TooManyValues", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterEstimate6()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"EstimatedImpressions\",\"operator\":\"between\",\"value\":42}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterEstimate7()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"EstimatedImpressions\",\"operator\":\"eq\",\"value\":\"test\"}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("Value", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_FilterString()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Name\",\"operator\":\"gt\",\"value\":\"\"}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidOperation", result.Errors[0].Message);
            Assert.AreEqual("Operator", result.Errors[0].Property[0]);
            Assert.AreEqual("InvalidValue", result.Errors[1].Message);
            Assert.AreEqual("Value", result.Errors[1].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationSucceed_FilterStatus()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Status\",\"operator\":\"in\",\"value\":[\"Approved\", \"Deleted\"]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public void GetAll_ValidationSucceed_FilterDate()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"StartDate\",\"operator\":\"eq\",\"value\":\"2025-03-05T14:30:00\"}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public void GetAll_ValidationSucceed_FilterBudget()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Budget\",\"operator\":\"between\",\"value\":[40, 42]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public void GetAll_ValidationSucceed_FilterEstimate()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"EstimatedImpressions\",\"operator\":\"between\",\"value\":[40, 42]}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public void GetAll_ValidationSucceed_FilterString()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Name\",\"operator\":\"eq\",\"value\":\"TestLine\"}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public void GetAll_ValidationSucceed_FilterID()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"ID\",\"operator\":\"eq\",\"value\":\"43\"}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public void GetAll_ValidationSucceed_FilterType()
        {
            IValidator<LineGetAllRequest> validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, CustomerId, MediaPlanId, "[{\"field\":\"Type\",\"operator\":\"eq\",\"value\":\"Rotational\"}]", null, null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public async Task Post_ValidationPass_BudgetScheduleType_Asap_ForAnyLineType()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                Type = LineType.Rotational,
                MediaPlanId = MediaPlanId,
                ProductId = ProductId1,
                Name = "ASAP_Test_Line",
                Description = "Line with ASAP pacing",
                StartDateTime = DateTime.UtcNow.Date.AddDays(1),
                EndDateTime = DateTime.UtcNow.Date.AddDays(2),
                BudgetScheduleType = BudgetScheduleType.ASAP,
                Cpm = 5.0,
                TargetImpressions = 100000
            };

            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);
            var result = await validator.Validate(postRequest);

            Assert.IsTrue(result.Succeeded, "Validation should pass for ASAP pacing regardless of LineType.");
        }

        [TestMethod]
        public async Task Post_ValidationPass_BudgetScheduleType_Evenly_ForRotational()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _rotationalProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                Type = LineType.Rotational,
                MediaPlanId = MediaPlanId,
                ProductId = ProductId1,
                Name = "Evenly_Rotational",
                Description = "Line with EVENLY pacing",
                StartDateTime = DateTime.UtcNow.Date.AddDays(1),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                BudgetScheduleType = BudgetScheduleType.Evenly,
                Cpm = 3.5,
                TargetImpressions = 150000
            };

            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);
            var result = await validator.Validate(postRequest);

            Assert.IsTrue(result.Succeeded, "Validation should pass for EVENLY pacing with Rotational LineType.");
        }

        [TestMethod]
        public async Task Post_ValidationPass_BudgetScheduleType_Evenly_ForSOV()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _sovProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var validLine = new Line()
            {
                Type = LineType.SOV,
                MediaPlanId = MediaPlanId,
                ProductId = ProductId1,
                Name = "Evenly_SOV",
                Description = "Line with EVENLY pacing",
                StartDateTime = DateTime.UtcNow.Date.AddDays(1),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                BudgetScheduleType = BudgetScheduleType.Evenly,
                Cpm = 3.5,
                TargetImpressions = null,
                PercentImpressions = 10,
            };

            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);
            var result = await validator.Validate(postRequest);

            Assert.IsTrue(result.Succeeded, "Validation should pass for EVENLY pacing with Share of Voice LineType.");
        }

        [TestMethod]
        public async Task Post_ValidationFail_BudgetScheduleType_Evenly_ForRoadblock()
        {
            var validator = new LinePostValidator(_customerMock.Object, _mediaPlanMock.Object, _roadblockProductMock.Object, _segmentMock.Object, _countryMock.Object, _locationMock.Object, _keyValueMock.Object);
            var invalidLine = new Line()
            {
                Type = LineType.Roadblock,
                MediaPlanId = MediaPlanId,
                ProductId = ProductId2,
                Name = "Invalid_Evenly_Roadblock",
                Description = "Line with invalid EVENLY pacing",
                StartDateTime = DateTime.UtcNow.Date.AddDays(1),
                EndDateTime = DateTime.UtcNow.Date.AddDays(2),
                BudgetScheduleType = BudgetScheduleType.Evenly,
                Cpd = 50.0,
                TargetImpressions = 50000
            };

            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, invalidLine, _httpContext);
            var result = await validator.Validate(postRequest);

            Assert.IsTrue(result.Failed, "Validation should fail for EVENLY pacing with Roadblock LineType.");
            Assert.IsTrue(result.Errors.Any(e => e.Property?.Contains(nameof(postRequest.Line.BudgetScheduleType)) == true));
        }

        [TestMethod]
        public void Post_LineEndDateEqualToMediaPlanEndDate()
        {
            var validator = new LinePostValidator(
                _customerMock.Object,
                _mediaPlanMock.Object,
                _rotationalProductMock.Object,
                _segmentMock.Object,
                _countryMock.Object, _locationMock.Object, _keyValueMock.Object);

            var validLine = new Line()
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "ValidationTest_LineEndEqual",
                Description = "Line ends same day as media plan",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3), // media plan ends same day
                Cpm = 3.1,
                TargetImpressions = 100000
            };

            var mediaPlan = GetValidExistingMediaPlan();
            mediaPlan.EndDate = DateTime.UtcNow.Date.AddDays(3); // line and plan end same day
            _mediaPlanMock.Setup(get => get.Get(It.IsAny<MediaPlanGetRequest>())).ReturnsAsync(new Result<MediaPlan>()
            {
                Entity = mediaPlan
            });

            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, validLine, _httpContext);

            var result = validator.Validate(postRequest).Result;

            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded, "Validation should pass when Line.EndDateTime == MediaPlan.EndDate.");
        }

        [TestMethod]
        public void Post_ValidationPass_EndDateWithinTimeZoneOffset()
        {
            // Simulate a Media Plan that ends on Day 3 in Pacific Time (PST)
            var mediaPlanEndPst = DateTime.Now.Date.AddDays(3);
            var mediaPlanEndUnspecified = DateTime.SpecifyKind(mediaPlanEndPst, DateTimeKind.Unspecified);
            var pstZone = TimeZoneInfo.FindSystemTimeZoneById("Pacific Standard Time");
            var mediaPlanEndUtc = TimeZoneInfo.ConvertTimeToUtc(mediaPlanEndUnspecified, pstZone);

            var mediaPlanForTest = GetValidExistingMediaPlan();
            mediaPlanForTest.EndDate = mediaPlanEndUtc;

            var mediaPlanMock = new Mock<IMediaPlanDao>();
            mediaPlanMock.Setup(get => get.Get(It.IsAny<MediaPlanGetRequest>())).ReturnsAsync(new Result<MediaPlan>
            {
                Entity = mediaPlanForTest
            });

            var validator = new LinePostValidator(
                _customerMock.Object,
                mediaPlanMock.Object,
                _roadblockProductMock.Object,
                _segmentMock.Object,
                _countryMock.Object, _locationMock.Object, _keyValueMock.Object);

            // User in EST selects 9:00 PM on the same calendar day
            var lineEndEst = mediaPlanEndPst.AddHours(21); // 9:00 PM EST
            var lineEndEstUnspecified = DateTime.SpecifyKind(lineEndEst, DateTimeKind.Unspecified);
            var estZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
            var lineEndUtc = TimeZoneInfo.ConvertTimeToUtc(lineEndEstUnspecified, estZone);

            var line = new Line()
            {
                MediaPlanId = MediaPlanId,
                ProductId = ProductId1,
                Name = "EndDateOffsetPass",
                Description = "Line ends within plan after EST→PST adjustment",
                Cpd = 5,
                StartDateTime = mediaPlanEndUtc.AddDays(-1),
                EndDateTime = lineEndUtc
            };

            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, line, _httpContext);
            var result = validator.Validate(postRequest).Result;

            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded, "Expected validation to pass as Line.EndDateTime after EST to PST adjustment still stays within MediaPlan.EndDate.");
        }

        [TestMethod]
        public void Post_ValidationFail_EndDateWithTimeZoneOffset()
        {
            // Media Plan ends at midnight on Day 3 in Pacific Time (PST)
            var mediaPlanEndPst = DateTime.Now.Date.AddDays(3);
            var mediaPlanEndUnspecified = DateTime.SpecifyKind(mediaPlanEndPst, DateTimeKind.Unspecified);
            var pstZone = TimeZoneInfo.FindSystemTimeZoneById("Pacific Standard Time");
            var mediaPlanEndUtc = TimeZoneInfo.ConvertTimeToUtc(mediaPlanEndUnspecified, pstZone);

            var mediaPlan = GetValidExistingMediaPlan();
            mediaPlan.EndDate = mediaPlanEndUtc;

            var mediaPlanMock = new Mock<IMediaPlanDao>();
            mediaPlanMock.Setup(get => get.Get(It.IsAny<MediaPlanGetRequest>())).ReturnsAsync(new Result<MediaPlan>
            {
                Entity = mediaPlan
            });

            var validator = new LinePostValidator(
                _customerMock.Object,
                mediaPlanMock.Object,
                _roadblockProductMock.Object,
                _segmentMock.Object,
                _countryMock.Object, _locationMock.Object, _keyValueMock.Object);

            // User in EST selects 12:30 AM on Day 4 (appears valid in EST, but it's already Day 4 PST)
            var lineEndEst = mediaPlanEndPst.AddDays(1).AddHours(0.5); // 12:30 AM EST on Day 4
            var lineEndEstUnspecified = DateTime.SpecifyKind(lineEndEst, DateTimeKind.Unspecified);
            var estZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
            var lineEndUtc = TimeZoneInfo.ConvertTimeToUtc(lineEndEstUnspecified, estZone);

            var line = new Line()
            {
                MediaPlanId = MediaPlanId,
                ProductId = ProductId1,
                Name = "Fail_TimezoneEdge",
                Description = "Fails after EST→PST offset pushes date over",
                Cpd = 5,
                StartDateTime = mediaPlanEndUtc.AddDays(-1),
                EndDateTime = lineEndUtc
            };

            var postRequest = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, line, _httpContext);
            var result = validator.Validate(postRequest).Result;

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded, "Should fail: EST to PST pushes end into next day.");
            Assert.IsTrue(result.ToString().Contains("NotInParentRange"), "Expected NotInParentRange validation error.");
        }

        [TestMethod]
        public async Task CountryInclude_RegionInclude_CityInclude_NotInHierarchy_ShouldPass()
        {
            var locationTarget = new LocationTarget
            {
                Locations = new[]
                {
                    new Location { Id = 1, Type = TargetType.Country, IsExcluded = false },
                    new Location { Id = 100, Type = TargetType.Region, IsExcluded = false },
                    new Location { Id = 1000, Type = TargetType.City, IsExcluded = false }
                }
            };

            var cityMeta = new XandrCity { Id = 1000, Name = "Unlinked City", CountryId = 999, RegionId = 888 };
            var regionMeta = new XandrRegion { Id = 100, Name = "Unlinked Region", CountryId = 888 };
            var countryMeta = new XandrCountry { Id = 1, Name = "Unlinked Country", Code = "UC" };

            var locationMock = new Mock<ILocationDataAccessObject>();
            var countryMock = new Mock<ICountryDao>();

            locationMock.Setup(x => x.GetAllCountries(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrCountry[]> { Entity = new[] { countryMeta } });

            countryMock.Setup(x => x.Get(It.IsAny<CountryGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrCountry[]> { Entity = new[] { countryMeta } });

            locationMock.Setup(x => x.GetAllRegions(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrRegion[]> { Entity = new[] { regionMeta } });

            locationMock.Setup(x => x.GetCityById(It.IsAny<int>(), It.IsAny<ILogger>()))
                    .ReturnsAsync(new Result<XandrCity> { Entity = cityMeta });

            locationMock.Setup(x => x.GetCitiesByIdsAsync(It.IsAny<IEnumerable<int>>(), It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrCity[]> { Entity = new[] { cityMeta } });

            var line = new Line
            {
                Name = "Test Line",
                ProductId = ProductId1,
                Targets = new Target[] { locationTarget },
                StartDateTime = DateTime.UtcNow,
                EndDateTime = DateTime.UtcNow.AddDays(10),
                Cpm = 5.0,
                TargetImpressions = 100000
            };

            var customer = new Customer
            {
                Id = CustomerId,
                Name = "Test Customer",
                Timezone = new XandrTimeZone { Id = 1, Name = "UTC", Offset = "00:00", DisplayName = "UTC" }
            };

            var mediaPlan = new MediaPlan
            {
                Id = MediaPlanId,
                CustomerId = CustomerId,
                Name = "Test Plan",
                CurrencyCode = "USD",
                Contact = "<EMAIL>",
                EndDate = DateTime.UtcNow.AddDays(20)
            };

            var product = new Product
            {
                Id = ProductId1,
                PublisherId = 999,
                PlacementIds = new long[] { 1 },
                Name = "Test Product",
                Family = "Standard",
                Contacts = new[] { "<EMAIL>" },
                CurrencyCode = "USD",
                BaseCPMRate = 5.0,
                Type = ProductType.Rotational,
                TimeZoneId = 1,
                SupportedTargets = new SupplyTarget[]
                {
                    new SupplyTarget { TargetTypeId = TargetType.Country },
                    new SupplyTarget { TargetTypeId = TargetType.Region },
                    new SupplyTarget { TargetTypeId = TargetType.City },
                    new SupplyTarget { TargetTypeId = TargetType.Location }
                }
            };

            var logger = NullLogger.Instance;

            var result = await LineValidationHelper.ValidateFields(
                "TestCase",
                logger,
                line,
                customer,
                mediaPlan,
                product,
                _segmentMock.Object,
                countryMock.Object,
                locationMock.Object,
                _keyValueMock.Object
            );

            Assert.IsTrue(result.Succeeded, "Should pass because none of the targets are within the same hierarchy.");
        }

        [TestMethod]
        public async Task CountryInclude_CityExclude_ShouldPass()
        {
           
            var locationTarget = new LocationTarget
            {
                Locations = new[]
                {
                    new Location { Id = -1, Type = TargetType.Country, IsExcluded = false },     // USA
                    new Location { Id = 1000, Type = TargetType.City, IsExcluded = true }       // London (in UK)
                }
            };

            var cityMeta = new XandrCity
            {
                Id = 1000,
                Name = "London",
                CountryId = -2,   // UK
                RegionId = 200
            };

            var regionMeta = new XandrRegion
            {
                Id = 200,
                Name = "Greater London",
                CountryId = -2
            };

            var countryMeta = new XandrCountry
            {
                Id = -1,
                Name = "United States",
                Code = "US"
            };

            var locationMock = new Mock<ILocationDataAccessObject>();
            var countryMock = new Mock<ICountryDao>();

            locationMock.Setup(x => x.GetAllCountries(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrCountry[]> { Entity = new[] { countryMeta } });

            countryMock.Setup(x => x.Get(It.IsAny<CountryGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrCountry[]> { Entity = new[] { countryMeta } });

            locationMock.Setup(x => x.GetAllRegions(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrRegion[]> { Entity = new[] { regionMeta } });

            locationMock.Setup(x => x.GetCityById(It.IsAny<int>(), It.IsAny<ILogger>()))
                    .ReturnsAsync(new Result<XandrCity> { Entity = cityMeta });

            locationMock.Setup(x => x.GetCitiesByIdsAsync(It.IsAny<IEnumerable<int>>(), It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrCity[]> { Entity = new[] { cityMeta } });

            var line = new Line
            {
                Name = "Test Line",
                ProductId = ProductId1,
                Targets = new Target[] { locationTarget },
                StartDateTime = DateTime.UtcNow,
                EndDateTime = DateTime.UtcNow.AddDays(10),
                Cpm = 4.0,
                TargetImpressions = 90000
            };

            var customer = new Customer
            {
                Id = CustomerId,
                Name = "Test Customer",
                Timezone = new XandrTimeZone { Id = 1, Name = "UTC", Offset = "00:00", DisplayName = "UTC" }
            };

            var mediaPlan = new MediaPlan
            {
                Id = MediaPlanId,
                CustomerId = CustomerId,
                Name = "Test Plan",
                CurrencyCode = "USD",
                Contact = "<EMAIL>",
                EndDate = DateTime.UtcNow.AddDays(20)
            };

            var product = new Product
            {
                Id = ProductId1,
                PublisherId = 999,
                PlacementIds = new long[] { 1 },
                Name = "Test Product",
                Family = "Standard",
                Contacts = new[] { "<EMAIL>" },
                CurrencyCode = "USD",
                BaseCPMRate = 5.0,
                Type = ProductType.Rotational,
                TimeZoneId = 1,
                SupportedTargets = new SupplyTarget[]
                {
                    new SupplyTarget { TargetTypeId = TargetType.Country },
                    new SupplyTarget { TargetTypeId = TargetType.Region },
                    new SupplyTarget { TargetTypeId = TargetType.City },
                    new SupplyTarget { TargetTypeId = TargetType.Location }
                }
            };

            var logger = NullLogger.Instance;

            var result = await LineValidationHelper.ValidateFields(
                "TestCase",
                logger,
                line,
                customer,
                mediaPlan,
                product,
                _segmentMock.Object,
                countryMock.Object,
                locationMock.Object,
                _keyValueMock.Object
            );

            Assert.IsTrue(result.Succeeded, "Should pass because city exclusion is outside the included country.");
        }

        [TestMethod]
        public async Task MultipleLowerLevelExcludesWithCountryExclude_ShouldPass()
        {
         
            var locationTarget = new LocationTarget
            {
                Locations = new[]
                {
                    new Location { Id = -1, Type = TargetType.Country, IsExcluded = true },     // Exclude USA
                    new Location { Id = 1000, Type = TargetType.City, IsExcluded = true },      // City from UK
                    new Location { Id = 2000, Type = TargetType.City, IsExcluded = true },      // City from India
                    new Location { Id = 300, Type = TargetType.Region, IsExcluded = true }      // Region from Canada
                }
            };

            var cities = new[]
            {
                new XandrCity { Id = 1000, Name = "London", RegionId = 200, CountryId = -2 },   // UK
                new XandrCity { Id = 2000, Name = "Chennai", RegionId = 201, CountryId = -3 }   // India
            };

            var regions = new[]
            {
                new XandrRegion { Id = 300, Name = "Ontario", CountryId = -4 }                  // Canada
            };

            var locationMock = new Mock<ILocationDataAccessObject>();

            locationMock.Setup(x => x.GetCitiesByIdsAsync(It.IsAny<IEnumerable<int>>(), It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrCity[]> { Entity = cities });

            locationMock.Setup(x => x.GetAllRegions(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrRegion[]> { Entity = regions });

            locationMock.Setup(l => l.GetAllCountries(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrCountry[]>
                {
                    Entity = new[]
                    {
            new XandrCountry { Id = -1, Name = "USA", Code = "US" }
                    }
                });

            var line = new Line
            {
                Name = "Test Line",
                ProductId = ProductId1,
                Targets = new Target[] { locationTarget },
                StartDateTime = DateTime.UtcNow,
                EndDateTime = DateTime.UtcNow.AddDays(10),
                Cpm = 5.0,
                TargetImpressions = 150000
            };

            var customer = new Customer
            {
                Id = CustomerId,
                Name = "Test Customer",
                Timezone = new XandrTimeZone { Id = 1, Name = "UTC", Offset = "00:00", DisplayName = "UTC" }
            };

            var mediaPlan = new MediaPlan
            {
                Id = MediaPlanId,
                CustomerId = CustomerId,
                Name = "Test Plan",
                CurrencyCode = "USD",
                Contact = "<EMAIL>",
                EndDate = DateTime.UtcNow.AddDays(20)
            };

            var product = new Product
            {
                Id = ProductId1,
                PublisherId = 999,
                PlacementIds = new long[] { 1 },
                Name = "Test Product",
                Family = "Standard",
                Contacts = new[] { "<EMAIL>" },
                CurrencyCode = "USD",
                BaseCPMRate = 5.0,
                Type = ProductType.Rotational,
                TimeZoneId = 1,
                SupportedTargets = new SupplyTarget[]
                {
                    new SupplyTarget { TargetTypeId = TargetType.Country },
                    new SupplyTarget { TargetTypeId = TargetType.Region },
                    new SupplyTarget { TargetTypeId = TargetType.City },
                    new SupplyTarget { TargetTypeId = TargetType.Location }
                }
            };

            var logger = NullLogger.Instance;

            var result = await LineValidationHelper.ValidateFields(
                "TestCase",
                logger,
                line,
                customer,
                mediaPlan,
                product,
                _segmentMock.Object,
                _countryMock.Object,
                locationMock.Object,
                _keyValueMock.Object
            );

            Assert.IsTrue(result.Succeeded, "Should pass because excluded lower-level targets are not under the excluded country.");
        }

        [TestMethod]
        public async Task CountryInclude_RegionInclude_SameHierarchy_ShouldFail()
        {
           
            var locationTarget = new LocationTarget
            {
                Locations = new[]
                {
                    new Location { Id = -1, Type = TargetType.Country, IsExcluded = false }, // USA
                    new Location { Id = -10, Type = TargetType.Region, IsExcluded = false }  // California (USA)
                }
            };

            var regionMeta = new XandrRegion { Id = -10, Name = "California", CountryId = -1 };

            var locationMock = new Mock<ILocationDataAccessObject>();

            locationMock.Setup(x => x.GetAllRegions(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrRegion[]> { Entity = new[] { regionMeta } });

            locationMock.Setup(l => l.GetAllCountries(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrCountry[]>
                {
                    Entity = new[]
                    {
            new XandrCountry { Id = -1, Name = "USA", Code = "US" }
                    }
                });

            locationMock.Setup(l => l.GetCitiesByIdsAsync(It.IsAny<IEnumerable<int>>(), It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrCity[]>
                {
                    Entity = new[]
                    {
            new XandrCity { Id = -100, Name = "Los Angeles", RegionId = -10, CountryId = -1 }
                    }
                });

            var line = new Line
            {
                Name = "Test Line",
                ProductId = ProductId1,
                Targets = new Target[] { locationTarget },
                StartDateTime = DateTime.UtcNow,
                EndDateTime = DateTime.UtcNow.AddDays(10),
                Cpm = 4.0,
                TargetImpressions = 80000
            };

            var customer = new Customer
            {
                Id = CustomerId,
                Name = "Test Customer",
                Timezone = new XandrTimeZone { Id = 1, Name = "UTC", Offset = "00:00", DisplayName = "UTC" }
            };

            var mediaPlan = new MediaPlan
            {
                Id = MediaPlanId,
                CustomerId = CustomerId,
                Name = "Test Plan",
                CurrencyCode = "USD",
                Contact = "<EMAIL>",
                EndDate = DateTime.UtcNow.AddDays(20)
            };

            var product = new Product
            {
                Id = ProductId1,
                PublisherId = 999,
                PlacementIds = new long[] { 1 },
                Name = "Test Product",
                Family = "Standard",
                Contacts = new[] { "<EMAIL>" },
                CurrencyCode = "USD",
                BaseCPMRate = 5.0,
                Type = ProductType.Rotational,
                TimeZoneId = 1,
                SupportedTargets = new SupplyTarget[]
                {
                    new SupplyTarget { TargetTypeId = TargetType.Country },
                    new SupplyTarget { TargetTypeId = TargetType.Region },
                    new SupplyTarget { TargetTypeId = TargetType.Location }
                }
            };

            var logger = NullLogger.Instance;

            var result = await LineValidationHelper.ValidateFields(
                "TestCase",
                logger,
                line,
                customer,
                mediaPlan,
                product,
                _segmentMock.Object,
                _countryMock.Object,
                locationMock.Object,
                _keyValueMock.Object
            );

            Assert.IsFalse(result.Succeeded, "Should fail because Region is part of included Country.");
            Assert.IsTrue(result.ToString().Contains(ErrorMessage.ChildLocationsOfIncludedTargetCannotBeIncluded), "Expected hierarchy violation error.");
        }

        [TestMethod]
        public async Task CountryExclude_CityInclude_SameHierarchy_ShouldFail()
        {
           
            var locationTarget = new LocationTarget
            {
                Locations = new[]
                {
                    new Location { Id = -1, Type = TargetType.Country, IsExcluded = true },   // Exclude USA
                    new Location { Id = 1000, Type = TargetType.City, IsExcluded = false }    // Los Angeles (USA)
                }
            };

            var cityMeta = new XandrCity
            {
                Id = 1000,
                Name = "Los Angeles",
                CountryId = -1,
                RegionId = 999
            };

            var countryMeta = new XandrCountry
            {
                Id = -1,
                Name = "United States",
                Code = "US"
            };

            var regionMeta = new XandrRegion { Id = 999, Name = "Some Region", CountryId = -1 };

            var locationMock = new Mock<ILocationDataAccessObject>();
            var countryMock = new Mock<ICountryDao>();

            locationMock.Setup(x => x.GetAllCountries(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrCountry[]> { Entity = new[] { countryMeta } });

            countryMock.Setup(x => x.Get(It.IsAny<CountryGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrCountry[]> { Entity = new[] { countryMeta } });

            locationMock.Setup(x => x.GetAllRegions(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrRegion[]> { Entity = new[] { regionMeta } });

            locationMock.Setup(x => x.GetCityById(It.IsAny<int>(), It.IsAny<ILogger>()))
                    .ReturnsAsync(new Result<XandrCity> { Entity = cityMeta });

            locationMock.Setup(x => x.GetCitiesByIdsAsync(It.IsAny<IEnumerable<int>>(), It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrCity[]> { Entity = new[] { cityMeta } });

            var line = new Line
            {
                Name = "FailCase",
                ProductId = ProductId1,
                Targets = new Target[] { locationTarget },
                StartDateTime = DateTime.UtcNow,
                EndDateTime = DateTime.UtcNow.AddDays(5),
                Cpm = 3.0,
                TargetImpressions = 60000
            };

            var customer = new Customer
            {
                Id = CustomerId,
                Name = "Test Customer",
                Timezone = new XandrTimeZone { Id = 1, Name = "UTC", Offset = "00:00", DisplayName = "UTC" }
            };

            var mediaPlan = new MediaPlan
            {
                Id = MediaPlanId,
                CustomerId = CustomerId,
                Name = "Test Plan",
                CurrencyCode = "USD",
                Contact = "<EMAIL>",
                EndDate = DateTime.UtcNow.AddDays(20)
            };

            var product = new Product
            {
                Id = ProductId1,
                PublisherId = 999,
                PlacementIds = new long[] { 1 },
                Name = "Test Product",
                Family = "Standard",
                Contacts = new[] { "<EMAIL>" },
                CurrencyCode = "USD",
                BaseCPMRate = 5.0,
                Type = ProductType.Rotational,
                TimeZoneId = 1,
                SupportedTargets = new[]
                {
                    new SupplyTarget { TargetTypeId = TargetType.Country },
                    new SupplyTarget { TargetTypeId = TargetType.City },
                    new SupplyTarget { TargetTypeId = TargetType.Region },
                    new SupplyTarget { TargetTypeId = TargetType.Location }
                }
            };

            var result = await LineValidationHelper.ValidateFields(
                "TestCase",
                NullLogger.Instance,
                line,
                customer,
                mediaPlan,
                product,
                _segmentMock.Object,
                countryMock.Object,
                locationMock.Object,
                _keyValueMock.Object
            );

            Assert.IsFalse(result.Succeeded, "Should fail because city is in excluded country.");
            Assert.IsTrue(result.ToString().Contains(ErrorMessage.ChildLocationsOfExcludedTargetCannotBeTargeted), "Expected child-of-excluded hierarchy error.");
        }

        [TestMethod]
        public async Task MixedIncludeExcludeSameLevel_ShouldFail()
        {
            var locationTarget = new LocationTarget
            {
                Locations = new[]
                {
                    new Location { Id = -1, Type = TargetType.Country, IsExcluded = false },   // Include USA
                    new Location { Id = -2, Type = TargetType.Country, IsExcluded = true }    // Exclude UK
                }
            };

            var line = new Line
            {
                Name = "InvalidMix",
                ProductId = ProductId1,
                Targets = new Target[] { locationTarget },
                StartDateTime = DateTime.UtcNow,
                EndDateTime = DateTime.UtcNow.AddDays(7),
                Cpm = 4.5,
                TargetImpressions = 95000
            };

            var customer = new Customer
            {
                Id = CustomerId,
                Name = "Test Customer",
                Timezone = new XandrTimeZone { Id = 1, Name = "UTC", Offset = "00:00", DisplayName = "UTC" }
            };

            var mediaPlan = new MediaPlan
            {
                Id = MediaPlanId,
                CustomerId = CustomerId,
                Name = "Test Plan",
                CurrencyCode = "USD",
                Contact = "<EMAIL>",
                EndDate = DateTime.UtcNow.AddDays(20)
            };

            var product = new Product
            {
                Id = ProductId1,
                PublisherId = 999,
                PlacementIds = new long[] { 1 },
                Name = "Test Product",
                Family = "Standard",
                Contacts = new[] { "<EMAIL>" },
                CurrencyCode = "USD",
                BaseCPMRate = 5.0,
                Type = ProductType.Rotational,
                TimeZoneId = 1,
                SupportedTargets = new[]
                {
                    new SupplyTarget { TargetTypeId = TargetType.Country },
                    new SupplyTarget { TargetTypeId = TargetType.Region },
                    new SupplyTarget { TargetTypeId = TargetType.City },
                    new SupplyTarget { TargetTypeId = TargetType.Location }
                }
            };

            var result = await LineValidationHelper.ValidateFields(
                "TestCase",
                NullLogger.Instance,
                line,
                customer,
                mediaPlan,
                product,
                _segmentMock.Object,
                _countryMock.Object,
                _locationMock.Object, _keyValueMock.Object
            );
            Assert.IsFalse(result.Succeeded, "Cannot mix inclusion and exclusion for Country level");
        }

        [TestMethod]
        public async Task Put_ValidationFail_MixedIncludeExcludeSameLevel()
        {
            
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>
            {
                Entity = new Line
                {
                    MediaPlanId = MediaPlanId,
                    Id = 1234,
                    ProductId = ProductId1,
                    Name = "Existing Line",
                    Description = "Old description",
                    Status = LineStatus.Approved,
                    StartDateTime = DateTime.UtcNow,
                    EndDateTime = DateTime.UtcNow.AddDays(3),
                    Cpm = 3.0,
                    TargetImpressions = 50000
                }
            });

            var updatedLine = new Line
            {
                MediaPlanId = MediaPlanId,
                Id = 1234,
                ProductId = ProductId1,
                Name = "Updated Line",
                Description = "Updated description",
                StartDateTime = DateTime.UtcNow.AddDays(1),
                EndDateTime = DateTime.UtcNow.AddDays(4),
                Cpm = 4.5,
                TargetImpressions = 100000,
                Targets = new Target[]
                {
                    new LocationTarget
                    {
                        Locations = new[]
                        {
                            new Location { Id = -1, Type = TargetType.Country, IsExcluded = false }, // Include USA
                            new Location { Id = -2, Type = TargetType.Country, IsExcluded = true }  // Exclude UK
                        }
                    }
                }
            };

            var product = GetValidExistingProduct(ProductType.Rotational);
            product.SupportedTargets = new[]
            {
                new SupplyTarget { TargetTypeId = TargetType.Country },
                new SupplyTarget { TargetTypeId = TargetType.Location }
            };

            var productMock = new Mock<IProductDao>();
            productMock.Setup(p => p.GetNoPublisher(It.IsAny<ProductGetNoPublisherRequest>()))
                .ReturnsAsync(new Result<Product[]> { Entity = new[] { product } });


            var validator = new LinePutValidator(
                _customerMock.Object,
                _mediaPlanMock.Object,
                lineMock.Object,
                productMock.Object,
                _segmentMock.Object,
                _countryMock.Object,
                _locationMock.Object, _keyValueMock.Object
            );

            var putRequest = new LinePutRequest(
                new Mock<ILogger<LineController>>().Object,
                CustomerId,
                updatedLine,
                _httpContext
            );

            var result = await validator.Validate(putRequest);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded, "Cannot mix inclusion and exclusion for Country level");
        }

        [TestMethod]
        public async Task Put_ValidationFail_CountryInclude_RegionInclude_SameHierarchy()
        {
            
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>
            {
                Entity = new Line
                {
                    MediaPlanId = MediaPlanId,
                    Id = 1234,
                    ProductId = ProductId1,
                    Name = "Existing Line",
                    Description = "Old description",
                    Status = LineStatus.Approved,
                    StartDateTime = DateTime.UtcNow,
                    EndDateTime = DateTime.UtcNow.AddDays(2),
                    Cpm = 3.0,
                    TargetImpressions = 50000
                }
            });

            var updatedLine = new Line
            {
                MediaPlanId = MediaPlanId,
                Id = 1234,
                ProductId = ProductId1,
                Name = "Line with hierarchy violation",
                Description = "Includes Country and its Region",
                StartDateTime = DateTime.UtcNow.AddDays(1),
                EndDateTime = DateTime.UtcNow.AddDays(4),
                Cpm = 5.0,
                TargetImpressions = 100000,
                Targets = new Target[]
                {
                    new LocationTarget
                    {
                        Locations = new[]
                        {
                            new Location { Id = -1, Type = TargetType.Country, IsExcluded = false },  // Include USA
                            new Location { Id = -10, Type = TargetType.Region, IsExcluded = false }  // Include California (part of USA)
                        }
                    }
                }
            };

            // Region is part of Country (-1)
            var regionMeta = new XandrRegion
            {
                Id = -10,
                Name = "California",
                CountryId = -1
            };

            var locationMock = new Mock<ILocationDataAccessObject>();

            locationMock.Setup(x => x.GetAllRegions(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrRegion[]>
                {
                    Entity = new[] { regionMeta }
                });

            locationMock.Setup(l => l.GetAllCountries(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrCountry[]>
                {
                    Entity = new[]
                    {
            new XandrCountry { Id = -1, Name = "USA", Code = "US" }
                    }
                });

            locationMock.Setup(l => l.GetCitiesByIdsAsync(It.IsAny<IEnumerable<int>>(), It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrCity[]>
                {
                    Entity = new[]
                    {
            new XandrCity { Id = -100, Name = "Los Angeles", RegionId = -10, CountryId = -1 }
                    }
                });

            var product = GetValidExistingProduct(ProductType.Rotational);
            product.SupportedTargets = new[]
            {
                new SupplyTarget { TargetTypeId = TargetType.Country },
                new SupplyTarget { TargetTypeId = TargetType.Region },
                new SupplyTarget { TargetTypeId = TargetType.Location }
            };

            var productMock = new Mock<IProductDao>();
            productMock.Setup(p => p.GetNoPublisher(It.IsAny<ProductGetNoPublisherRequest>()))
                .ReturnsAsync(new Result<Product[]> { Entity = new[] { product } });


            var validator = new LinePutValidator(
                _customerMock.Object,
                _mediaPlanMock.Object,
                lineMock.Object,
                productMock.Object,
                _segmentMock.Object,
                _countryMock.Object,
                locationMock.Object,
                _keyValueMock.Object
            );

            var putRequest = new LinePutRequest(
                new Mock<ILogger<LineController>>().Object,
                CustomerId,
                updatedLine,
                _httpContext
            );

            var result = await validator.Validate(putRequest);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded, "Should fail due to hierarchy conflict.");
            Assert.IsTrue(result.Errors.Any(e => e.Message.Equals(ErrorMessage.ChildLocationsOfIncludedTargetCannotBeIncluded)),
                "Expected hierarchy rule violation for Region inside Country include.");
        }

        [TestMethod]
        public async Task Put_ValidationPass_CountryExclude_CityExclude_NotInHierarchy()
        {
           
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>
            {
                Entity = new Line
                {
                    MediaPlanId = MediaPlanId,
                    Id = 1234,
                    ProductId = ProductId1,
                    Name = "Existing Line",
                    Description = "Old description",
                    Status = LineStatus.Approved,
                    StartDateTime = DateTime.UtcNow,
                    EndDateTime = DateTime.UtcNow.AddDays(2),
                    Cpm = 3.0,
                    TargetImpressions = 50000
                }
            });

            var updatedLine = new Line
            {
                MediaPlanId = MediaPlanId,
                Id = 1234,
                ProductId = ProductId1,
                Name = "Updated Line",
                Description = "Exclude Country & unrelated Cities",
                StartDateTime = DateTime.UtcNow.AddDays(1),
                EndDateTime = DateTime.UtcNow.AddDays(4),
                Cpm = 5.0,
                TargetImpressions = 100000,
                Targets = new Target[]
                {
                    new LocationTarget
                    {
                        Locations = new[]
                        {
                            new Location { Id = -1, Type = TargetType.Country, IsExcluded = true },  // Exclude Country A
                            new Location { Id = -2000, Type = TargetType.City, IsExcluded = true },  // City not in Country A
                            new Location { Id = -2001, Type = TargetType.City, IsExcluded = true }   // Another city not in Country A
                        }
                    }
                }
            };

            var cityList = new[]
            {
                new XandrCity { Id = -2000, Name = "Paris", CountryId = -2, RegionId = -100 },     // Not in Country -1
                new XandrCity { Id = -2001, Name = "Tokyo", CountryId = -3, RegionId = -101 }      // Also not in Country -1
            };

            var locationMock = new Mock<ILocationDataAccessObject>();

            locationMock.Setup(x => x.GetCitiesByIdsAsync(It.IsAny<IEnumerable<int>>(), It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrCity[]>(cityList));

            locationMock.Setup(l => l.GetAllCountries(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrCountry[]>
                {
                    Entity = new[]
                    {
            new XandrCountry { Id = -1, Name = "USA", Code = "US" }
                    }
                });

            locationMock.Setup(l => l.GetAllRegions(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrRegion[]>
                {
                    Entity = new[]
                    {
            new XandrRegion { Id = -10, CountryId = -1, Name = "California" }
                    }
                });

            var product = GetValidExistingProduct(ProductType.Rotational);
            product.SupportedTargets = new[]
            {
                new SupplyTarget { TargetTypeId = TargetType.Country },
                new SupplyTarget { TargetTypeId = TargetType.City },
                new SupplyTarget { TargetTypeId = TargetType.Location }
            };


            var productMock = new Mock<IProductDao>();
            productMock.Setup(p => p.GetNoPublisher(It.IsAny<ProductGetNoPublisherRequest>()))
                .ReturnsAsync(new Result<Product[]> { Entity = new[] { product } });

            var validator = new LinePutValidator(
                _customerMock.Object,
                _mediaPlanMock.Object,
                lineMock.Object,
                productMock.Object,
                _segmentMock.Object,
                _countryMock.Object,
                locationMock.Object,
                _keyValueMock.Object
            );

            var putRequest = new LinePutRequest(
                new Mock<ILogger<LineController>>().Object,
                CustomerId,
                updatedLine,
                _httpContext
            );

            var result = await validator.Validate(putRequest);

            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded, "Validation should pass because excluded cities are not in the excluded country.");
        }

        [TestMethod]
        public async Task Put_ValidationFail_CountryInclude_CityInclude_SameHierarchy()
        {
            
            var lineMock = new Mock<ILineDao>();
            lineMock.Setup(get => get.Get(It.IsAny<LineGetRequest>())).ReturnsAsync(new Result<Line>
            {
                Entity = new Line
                {
                    MediaPlanId = MediaPlanId,
                    Id = 1234,
                    ProductId = ProductId1,
                    Name = "Existing Line",
                    Description = "Old description",
                    Status = LineStatus.Approved,
                    StartDateTime = DateTime.UtcNow,
                    EndDateTime = DateTime.UtcNow.AddDays(2),
                    Cpm = 3.0,
                    TargetImpressions = 50000
                }
            });

            var updatedLine = new Line
            {
                MediaPlanId = MediaPlanId,
                Id = 1234,
                ProductId = ProductId1,
                Name = "Updated Line",
                Description = "Include Country and its City",
                StartDateTime = DateTime.UtcNow.AddDays(1),
                EndDateTime = DateTime.UtcNow.AddDays(4),
                Cpm = 5.0,
                TargetImpressions = 100000,
                Targets = new Target[]
                {
                    new LocationTarget
                    {
                        Locations = new[]
                        {
                            new Location { Id = -1, Type = TargetType.Country, IsExcluded = false },
                            new Location { Id = -1000, Type = TargetType.City, IsExcluded = false }
                        }
                    }
                }
            };

            var cityMeta = new XandrCity
            {
                Id = -1000,
                Name = "New York",
                CountryId = -1,     // Matches included Country
                RegionId = -100
            };

            var locationMock = new Mock<ILocationDataAccessObject>();

            locationMock.Setup(x => x.GetCityById(It.IsAny<int>(), It.IsAny<ILogger>()))
                    .ReturnsAsync(new Result<XandrCity> { Entity = cityMeta });

            locationMock.Setup(l => l.GetAllCountries(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrCountry[]>
                {
                    Entity = new[]
                    {
            new XandrCountry { Id = -1, Name = "USA", Code = "US" }
                    }
                });

            locationMock.Setup(l => l.GetAllRegions(It.IsAny<LocationGetAllRequest>(), It.IsAny<bool>()))
                .ReturnsAsync(new Result<XandrRegion[]>
                {
                    Entity = new[]
                    {
            new XandrRegion { Id = -10, CountryId = -1, Name = "California" }
                    }
                });

            locationMock.Setup(l => l.GetCitiesByIdsAsync(It.IsAny<IEnumerable<int>>(), It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrCity[]>
                {
                    Entity = new[]
                    {
            new XandrCity { Id = -100, Name = "Los Angeles", RegionId = -10, CountryId = -1 }
                    }
                });

            var product = GetValidExistingProduct(ProductType.Rotational);
            product.SupportedTargets = new[]
            {
                new SupplyTarget { TargetTypeId = TargetType.Country },
                new SupplyTarget { TargetTypeId = TargetType.City },
                new SupplyTarget { TargetTypeId = TargetType.Location }
            };

            var productMock = new Mock<IProductDao>();
            productMock.Setup(p => p.GetNoPublisher(It.IsAny<ProductGetNoPublisherRequest>()))
                .ReturnsAsync(new Result<Product[]> { Entity = new[] { product } });

            var validator = new LinePutValidator(
                _customerMock.Object,
                _mediaPlanMock.Object,
                lineMock.Object,
                productMock.Object,
                _segmentMock.Object,
                _countryMock.Object,
                locationMock.Object,
                _keyValueMock.Object
            );

            var putRequest = new LinePutRequest(
                new Mock<ILogger<LineController>>().Object,
                CustomerId,
                updatedLine,
                _httpContext
            );

            var result = await validator.Validate(putRequest);

            Assert.IsNotNull(result);
            Assert.IsFalse(result.Succeeded, "Validation should fail because City is within included Country.");
            Assert.IsTrue(result.ToString().Contains(ErrorMessage.ChildLocationsOfIncludedTargetCannotBeIncluded),
                "Expected hierarchy violation for country-city targeting.");
        }

        [TestMethod]
        public void Post_ValidationPass_ValidIncludedDMATarget()
        {
            var dynamicConfig = new DynamicConfig { EnableDMATargeting = true };
            var configMock = new Mock<IOptionsMonitor<DynamicConfig>>();
            configMock.Setup(x => x.CurrentValue).Returns(dynamicConfig);
            new DynamicConfigWrapper(configMock.Object, NullLogger<DynamicConfigWrapper>.Instance);
            var validator = new LinePostValidator(
                _customerMock.Object,
                _mediaPlanMock.Object,
                _rotationalProductMock.Object,
                _segmentMock.Object,
                _countryMock.Object,
                _locationMock.Object, _keyValueMock.Object
            );

            var validLine = new Line
            {
                MediaPlanId = MediaPlanId,
                ProductId = ProductId1,
                Name = "Valid DMA Include",
                Description = "Test",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 4.0,
                TargetImpressions = 100000,

                Targets = new Target[]
                {
                    new DMATarget { Ids = new[] { 101 }, IsExcluded = false }
                }
            };
            var postRequest = new LinePostRequest(
                new Mock<ILogger<LineController>>().Object,
                CustomerId,
                validLine,
                _httpContext
            );

            var result = validator.Validate(postRequest).Result;
            Assert.IsTrue(result.Succeeded, "Expected success for a valid included DMATarget.");
        }


        [TestMethod]
        public void Post_ValidationPass_ValidExcludedDMATarget()
        {
            var dynamicConfig = new DynamicConfig { EnableDMATargeting = true };
            var configMock = new Mock<IOptionsMonitor<DynamicConfig>>();
            configMock.Setup(x => x.CurrentValue).Returns(dynamicConfig);
            new DynamicConfigWrapper(configMock.Object, NullLogger<DynamicConfigWrapper>.Instance);

            var validator = new LinePostValidator(
                _customerMock.Object,
                _mediaPlanMock.Object,
                _rotationalProductMock.Object,
                _segmentMock.Object,
                _countryMock.Object,
                _locationMock.Object, _keyValueMock.Object
            );

            var validLine = new Line
            {
                MediaPlanId = MediaPlanId,
                ProductId = ProductId1,
                Name = "Valid DMA Exclude",
                Description = "Test",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 4.0,
                TargetImpressions = 100000,
                Targets = new Target[]
                {
            new DMATarget { Ids = new[] { 202 }, IsExcluded = true }
                }
            };
            var postRequest = new LinePostRequest(
                new Mock<ILogger<LineController>>().Object,
                CustomerId,
                validLine,
                _httpContext
            );

            var result = validator.Validate(postRequest).Result;
            Assert.IsTrue(result.Succeeded, "Expected success for a valid excluded DMATarget.");
        }


        [TestMethod]
        public void Post_ValidationFail_NoDMATargetIds()
        {
            var dynamicConfig = new DynamicConfig { EnableDMATargeting = true };
            var configMock = new Mock<IOptionsMonitor<DynamicConfig>>();
            configMock.Setup(x => x.CurrentValue).Returns(dynamicConfig);
            new DynamicConfigWrapper(configMock.Object, NullLogger<DynamicConfigWrapper>.Instance);

            var validator = new LinePostValidator(
                _customerMock.Object,
                _mediaPlanMock.Object,
                _rotationalProductMock.Object,
                _segmentMock.Object,
                _countryMock.Object,
                _locationMock.Object, _keyValueMock.Object
            );

            var invalidLine = new Line
            {
                MediaPlanId = MediaPlanId,
                ProductId = ProductId1,
                Name = "No DMA Ids",
                Description = "Test",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 4.0,
                TargetImpressions = 100000,
                Targets = new Target[]
                {
                    new DMATarget { Ids = Array.Empty<int>(), IsExcluded = false }
                }
            };
            var postRequest = new LinePostRequest(
                new Mock<ILogger<LineController>>().Object,
                CustomerId,
                invalidLine,
                _httpContext
            );

            var result = validator.Validate(postRequest).Result;
            Assert.IsFalse(result.Succeeded, "Expected failure when DMATarget.Ids is empty.");
            StringAssert.Contains(result.Errors.First().Message, "DmaTargetIdsRequired");
        }

        [TestMethod]
        public void GetAll_ValidationFail_Sorting_ManyValues()
        {
            var validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, 123, 456, null, "Name asc test", null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("TooManyValues", result.Errors[0].Message);
            Assert.AreEqual("OrderBy", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_Sorting_InvalidValue()
        {
            var validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, 123, 456, null, "test", null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("OrderBy", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void GetAll_ValidationFail_Sorting_InvalidSortType()
        {
            var validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, 123, 456, null, "Name test", null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Failed);
            Assert.AreEqual("InvalidValue", result.Errors[0].Message);
            Assert.AreEqual("OrderBy", result.Errors[0].Property[0]);
        }

        [TestMethod]
        public void Line_GetAll_ValidationSucceed_Sorting()
        {
            var validator = new LineGetAllValidator();
            var request = new LineGetAllRequest(null, 123, 456, null, "Name asc", null, null);
            var result = validator.Validate(request).Result;
            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public void Post_ValidationFail_MixedIncludeExcludeDMATarget()
        {
            var dynamicConfig = new DynamicConfig { EnableDMATargeting = true };
            var configMock = new Mock<IOptionsMonitor<DynamicConfig>>();
            configMock.Setup(x => x.CurrentValue).Returns(dynamicConfig);
            new DynamicConfigWrapper(configMock.Object, NullLogger<DynamicConfigWrapper>.Instance);

            var validator = new LinePostValidator(
                _customerMock.Object,
                _mediaPlanMock.Object,
                _rotationalProductMock.Object,
                _segmentMock.Object,
                _countryMock.Object,
                _locationMock.Object, _keyValueMock.Object
            );

            var invalidLine = new Line
            {
                MediaPlanId = MediaPlanId,
                ProductId = ProductId1,
                Name = "Mixed DMA Include/Exclude",
                Description = "Test",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 4.0,
                TargetImpressions = 100000,
                Targets = new Target[]
                {
                    new DMATarget { Ids = new[] { 101 }, IsExcluded = false },
                    new DMATarget { Ids = new[] { 202 }, IsExcluded = true }
                }
            };
            var postRequest = new LinePostRequest(
                new Mock<ILogger<LineController>>().Object,
                CustomerId,
                invalidLine,
                _httpContext
            );

            var result = validator.Validate(postRequest).Result;
            Assert.IsFalse(result.Succeeded, "Expected failure when both include and exclude DMATargets are present.");
            StringAssert.Contains(result.Errors.First().Message, "OnlyOneDMATargetAllowed");
        }

        [TestMethod]
        public void Post_ValidationFail_MultipleDMATargets()
        {
            var dynamicConfig = new DynamicConfig { EnableDMATargeting = true };
            var configMock = new Mock<IOptionsMonitor<DynamicConfig>>();
            configMock.Setup(x => x.CurrentValue).Returns(dynamicConfig);
            new DynamicConfigWrapper(configMock.Object, NullLogger<DynamicConfigWrapper>.Instance);

            var validator = new LinePostValidator(
                _customerMock.Object,
                _mediaPlanMock.Object,
                _rotationalProductMock.Object,
                _segmentMock.Object,
                _countryMock.Object,
                _locationMock.Object, _keyValueMock.Object
            );

            var invalidLine = new Line
            {
                MediaPlanId = MediaPlanId,
                ProductId = ProductId1,
                Name = "Multiple DMATargets",
                Description = "Test",
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                Cpm = 4.0,
                TargetImpressions = 100000,
                Targets = new Target[]
                {
                    new DMATarget { Ids = new[] { 101 }, IsExcluded = false },
                    new DMATarget { Ids = new[] { 102 }, IsExcluded = false }
                }
            };
            var postRequest = new LinePostRequest(
                new Mock<ILogger<LineController>>().Object,
                CustomerId,
                invalidLine,
                _httpContext
            );

            var result = validator.Validate(postRequest).Result;
            Assert.IsFalse(result.Succeeded, "Expected failure when more than one DMATarget is present.");
            StringAssert.Contains(result.Errors.First().Message, "OnlyOneDMATargetAllowed");
        }

        [TestMethod]
        public void Post_ValidationFail_InvalidSOVPercentImpressions_TooHigh()
        {
            var validator = new LinePostValidator(
                _customerMock.Object,
                _mediaPlanMock.Object,
                _sovProductMock.Object,
                _segmentMock.Object,
                _countryMock.Object,
                _locationMock.Object, _keyValueMock.Object
            );

            var line = new Line
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "Invalid SOV Test",
                Description = "PercentImpressions too high",
                Type = LineType.SOV,
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                PercentImpressions = 101
            };

            var request = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, line, _httpContext);
            var result = validator.Validate(request).Result;

            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("SOVPercentImpressionsOutOfRange"));
        }

        [TestMethod]
        public void Post_ValidationFail_InvalidSOVPercentImpressions_TooLow()
        {
            var validator = new LinePostValidator(
                _customerMock.Object,
                _mediaPlanMock.Object,
                _sovProductMock.Object,
                _segmentMock.Object,
                _countryMock.Object,
                _locationMock.Object, _keyValueMock.Object
            );

            var line = new Line
            {
                MediaPlanId = 100,
                ProductId = ProductId2,
                Name = "Invalid SOV Test",
                Description = "PercentImpressions too low",
                Type = LineType.SOV,
                StartDateTime = DateTime.UtcNow.Date.AddDays(2),
                EndDateTime = DateTime.UtcNow.Date.AddDays(3),
                PercentImpressions = -1
            };

            var request = new LinePostRequest(new Mock<ILogger<LineController>>().Object, CustomerId, line, _httpContext);
            var result = validator.Validate(request).Result;

            Assert.IsFalse(result.Succeeded);
            Assert.IsTrue(result.ToString().Contains("SOVPercentImpressionsOutOfRange"));
        }

        [TestMethod]
        public async Task KeyValueInclude_Valid_ShouldPass()
        {
            var dynamicConfig = new DynamicConfig { EnableKVPTargeting = true };
            var configMock = new Mock<IOptionsMonitor<DynamicConfig>>();
            configMock.Setup(x => x.CurrentValue).Returns(dynamicConfig);
            new DynamicConfigWrapper(configMock.Object, NullLogger<DynamicConfigWrapper>.Instance);

            var mockKeyValueDao = new Mock<IKeyValueDataAccessObject>();

            mockKeyValueDao.Setup(x => x.GetValue("age", "18", It.IsAny<ILogger>()))
               .ReturnsAsync(new Result<XandrTargetingValue>
               {
                   Entity = new XandrTargetingValue
                   {
                       Name = "18",          
                       TargetingKeyId = 123
                   },
               });

            mockKeyValueDao.Setup(x => x.GetKey("age", It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingKey>
                {
                    Entity = new XandrTargetingKey
                    {
                        Name = "age",
                        Type = ValueTypes.String
                    },
                });

            mockKeyValueDao.Setup(x => x.GetAllValues(It.IsAny<KeyValueGetAllValuesRequest>(), It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingValue[]>
                {
                    Entity = new[]
                    {
                        new XandrTargetingValue
                        {
                            Name = "18",
                            TargetingKeyId = 123
                        }
                    },
                });

            var line = new Line
            {
                Name = "Test Line",
                Targets = new Target[]
                {
                    new KeyValueTarget
                    {
                        Entries = new[]
                        {
                            new KeyValueEntry { Key = "age", Values = new[] {"18" }, IsExcluded = false }
                        }
                    }
                }
            };

            var product = new Product
            {
                Name = "Test Product",
                Family = "TestFamily",
                CurrencyCode = "USD",
                BaseCPMRate = 1.0,
                Contacts = [],
                PlacementIds = [],
                TimeZoneId = 1,
                SupportedTargets = new[]
                {
                    new SupplyTarget { TargetTypeId = TargetType.KeyValue }
                }
            };

            var result = await LineValidationHelper.ValidateTargeting(
                source: "test",
                targets: line.Targets,
                product: product,
                segmentDao: null!,
                countryDao: null!,
                locationDao: null!,
                keyValueDao: mockKeyValueDao.Object,
                logger: NullLogger.Instance
            );

            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public async Task KeyValueExclude_Valid_ShouldPass()
        {
            var dynamicConfig = new DynamicConfig { EnableKVPTargeting = true };
            var configMock = new Mock<IOptionsMonitor<DynamicConfig>>();
            configMock.Setup(x => x.CurrentValue).Returns(dynamicConfig);
            new DynamicConfigWrapper(configMock.Object, NullLogger<DynamicConfigWrapper>.Instance);

            var mockKeyValueDao = new Mock<IKeyValueDataAccessObject>();

            mockKeyValueDao.Setup(x => x.GetValue("gender", "female", It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingValue>
                {
                    Entity = new XandrTargetingValue
                    {
                        Name = "female",
                        TargetingKeyId = 456
                    },
                });

            mockKeyValueDao.Setup(x => x.GetKey("gender", It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingKey>
                {
                    Entity = new XandrTargetingKey
                    {
                        Name = "gender",
                        Type = ValueTypes.String
                    },
                });

            mockKeyValueDao.Setup(x => x.GetAllValues(It.IsAny<KeyValueGetAllValuesRequest>(), It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingValue[]>
                {
                    Entity = new[]
                    {
                new XandrTargetingValue
                {
                    Name = "female",
                    TargetingKeyId = 456
                }
                    },
                });

            var line = new Line
            {
                Name = "Exclude Test Line",
                Targets = new Target[]
                {
            new KeyValueTarget
            {
                Entries = new[]
                {
                    new KeyValueEntry { Key = "gender", Values = new[] { "female" }, IsExcluded = false}
                }
            }
                }
            };

            var product = new Product
            {
                Name = "Exclude Product",
                Family = "TestFamily",
                CurrencyCode = "USD",
                BaseCPMRate = 2.0,
                Contacts = [],
                PlacementIds = [],
                TimeZoneId = 1,
                SupportedTargets = new[]
                {
            new SupplyTarget { TargetTypeId = TargetType.KeyValue }
        }
            };

            var result = await LineValidationHelper.ValidateTargeting(
                source: "test",
                targets: line.Targets,
                product: product,
                segmentDao: null!,
                countryDao: null!,
                locationDao: null!,
                keyValueDao: mockKeyValueDao.Object,
                logger: NullLogger.Instance
            );

            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public async Task KeyValueTarget_UnsupportedKeyOrValue_ShouldFail()
        {
            var dynamicConfig = new DynamicConfig { EnableKVPTargeting = true };
            var configMock = new Mock<IOptionsMonitor<DynamicConfig>>();
            configMock.Setup(x => x.CurrentValue).Returns(dynamicConfig);
            new DynamicConfigWrapper(configMock.Object, NullLogger<DynamicConfigWrapper>.Instance);

            var mockKeyValueDao = new Mock<IKeyValueDataAccessObject>();

            mockKeyValueDao.Setup(x => x.GetKey("behavior", It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingKey>
                {
                    Entity = new XandrTargetingKey
                    {
                        Name = "behavior",
                        Type = ValueTypes.String
                    },
                });

            mockKeyValueDao.Setup(x => x.GetValue("behavior", "unknown_value", It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingValue>
                {
                    Entity = new XandrTargetingValue
                    {
                        Name = "unknown_value",
                        TargetingKeyId = 999
                    },
                });

            // Simulate unsupported value by returning empty list
            mockKeyValueDao.Setup(x => x.GetAllValues(It.IsAny<KeyValueGetAllValuesRequest>(), It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingValue[]>
                {
                    Entity = Array.Empty<XandrTargetingValue>()
                });

            var line = new Line
            {
                Name = "UnsupportedKV Line",
                Targets = new Target[]
                {
            new KeyValueTarget
            {
                Entries = new[]
                {
                    new KeyValueEntry
                    {
                        Key = "behavior",
                        Values = new[] { "unknown_value" },
                        IsExcluded = false
                    }
                }
            }
                }
            };

            var product = new Product
            {
                Name = "UnsupportedKV Product",
                Family = "TestFamily",
                CurrencyCode = "USD",
                BaseCPMRate = 3.5,
                Contacts = [],
                PlacementIds = [],
                TimeZoneId = 1,
                SupportedTargets = new[]
                {
            new SupplyTarget { TargetTypeId = TargetType.KeyValue }
        }
            };

            var result = await LineValidationHelper.ValidateTargeting(
                source: "test",
                targets: line.Targets,
                product: product,
                segmentDao: null!,
                countryDao: null!,
                locationDao: null!,
                keyValueDao: mockKeyValueDao.Object,
                logger: NullLogger.Instance
            );

            Assert.IsTrue(result.Failed);
            Assert.IsTrue(result.Errors.Any(e => e.Message == ErrorMessage.InvalidValue));
        }

        [TestMethod]
        public async Task KeyValueTarget_MultipleIncludesForSameKey_ShouldPass()
        {
            var dynamicConfig = new DynamicConfig { EnableKVPTargeting = true };
            var configMock = new Mock<IOptionsMonitor<DynamicConfig>>();
            configMock.Setup(x => x.CurrentValue).Returns(dynamicConfig);
            new DynamicConfigWrapper(configMock.Object, NullLogger<DynamicConfigWrapper>.Instance);

            var mockKeyValueDao = new Mock<IKeyValueDataAccessObject>();

            mockKeyValueDao.Setup(x => x.GetKey("age", It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingKey>
                {
                    Entity = new XandrTargetingKey
                    {
                        Name = "age",
                        Type = ValueTypes.String
                    },
                });

            mockKeyValueDao.Setup(x => x.GetValue("age", "18-24", It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingValue>
                {
                    Entity = new XandrTargetingValue
                    {
                        Name = "18-24",
                        TargetingKeyId = 100
                    },
                });

            mockKeyValueDao.Setup(x => x.GetValue("age", "25-34", It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingValue>
                {
                    Entity = new XandrTargetingValue
                    {
                        Name = "25-34",
                        TargetingKeyId = 100
                    },
                });

            mockKeyValueDao.Setup(x => x.GetAllValues(It.IsAny<KeyValueGetAllValuesRequest>(), It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingValue[]>
                {
                    Entity = new[]
                    {
                new XandrTargetingValue { Name = "18-24", TargetingKeyId = 100 },
                new XandrTargetingValue { Name = "25-34", TargetingKeyId = 100 }
                    },
                });

            var line = new Line
            {
                Name = "Multi-Value Include Line",
                Targets = new Target[]
                {
            new KeyValueTarget
            {
                Entries = new[]
                {
                    new KeyValueEntry { Key = "age", Values = new[] { "18-24" }, IsExcluded = false },
                    new KeyValueEntry { Key = "age", Values = new[] { "25-34" }, IsExcluded = false }
                }
            }
                }
            };

            var product = new Product
            {
                Name = "Multi-Value Product",
                Family = "TestFamily",
                CurrencyCode = "USD",
                BaseCPMRate = 2.0,
                Contacts = [],
                PlacementIds = [],
                TimeZoneId = 1,
                SupportedTargets = new[]
                {
            new SupplyTarget { TargetTypeId = TargetType.KeyValue }
        }
            };

            var result = await LineValidationHelper.ValidateTargeting(
                source: "test",
                targets: line.Targets,
                product: product,
                segmentDao: null!,
                countryDao: null!,
                locationDao: null!,
                keyValueDao: mockKeyValueDao.Object,
                logger: NullLogger.Instance
            );

            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public async Task KeyValueTarget_IncludeAndExcludeSameValue_ShouldFail()
        {
            var dynamicConfig = new DynamicConfig { EnableKVPTargeting = true };
            var configMock = new Mock<IOptionsMonitor<DynamicConfig>>();
            configMock.Setup(x => x.CurrentValue).Returns(dynamicConfig);
            new DynamicConfigWrapper(configMock.Object, NullLogger<DynamicConfigWrapper>.Instance);

            var mockKeyValueDao = new Mock<IKeyValueDataAccessObject>();

            mockKeyValueDao.Setup(x => x.GetKey("age", It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingKey>
                {
                    Entity = new XandrTargetingKey
                    {
                        Name = "age",
                        Type = ValueTypes.String
                    },
                });

            mockKeyValueDao.Setup(x => x.GetValue("age", "18-24", It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingValue>
                {
                    Entity = new XandrTargetingValue
                    {
                        Name = "18-24",
                        TargetingKeyId = 100
                    },
                });

            mockKeyValueDao.Setup(x => x.GetAllValues(It.IsAny<KeyValueGetAllValuesRequest>(), It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingValue[]>
                {
                    Entity = new[]
                    {
                new XandrTargetingValue
                {
                    Name = "18-24",
                    TargetingKeyId = 100
                }
                    },
                });

            var line = new Line
            {
                Name = "IncludeExcludeSameValue Line",
                Targets = new Target[]
                {
            new KeyValueTarget
            {
                Entries = new[]
                {
                    new KeyValueEntry { Key = "age", Values = new[] { "18-24" }, IsExcluded = false }
                }
            },
            new KeyValueTarget
            {
                Entries = new[]
                {
                    new KeyValueEntry { Key = "age", Values = new[] { "18-24" }, IsExcluded = true }
                }
            }
                }
            };

            var product = new Product
            {
                Name = "IncludeExcludeSameValue Product",
                Family = "TestFamily",
                CurrencyCode = "USD",
                BaseCPMRate = 2.0,
                Contacts = [],
                PlacementIds = [],
                TimeZoneId = 1,
                SupportedTargets = new[]
                {
            new SupplyTarget { TargetTypeId = TargetType.KeyValue }
        }
            };

            var result = await LineValidationHelper.ValidateTargeting(
                source: "test",
                targets: line.Targets,
                product: product,
                segmentDao: null!,
                countryDao: null!,
                locationDao: null!,
                keyValueDao: mockKeyValueDao.Object,
                logger: NullLogger.Instance
            );

            Assert.IsTrue(result.Failed);
            Assert.IsTrue(result.Errors.Any(e => e.Message == ErrorMessage.KeyHasOverlappingIncludeExcludeValues));
        }

        [TestMethod]
        public async Task KeyValueTarget_InvalidKeyFormat_ShouldFail()
        {
            var dynamicConfig = new DynamicConfig { EnableKVPTargeting = true };
            var configMock = new Mock<IOptionsMonitor<DynamicConfig>>();
            configMock.Setup(x => x.CurrentValue).Returns(dynamicConfig);
            new DynamicConfigWrapper(configMock.Object, NullLogger<DynamicConfigWrapper>.Instance);

            var mockKeyValueDao = new Mock<IKeyValueDataAccessObject>();

            var line = new Line
            {
                Name = "Invalid Key Format Line",
                Targets = new Target[]
                {
                    new KeyValueTarget
                    {
                        Entries = new[]
                        {
                            new KeyValueEntry
                            {
                                Key = "age*",
                                Values = new[] { "18-24" },
                                IsExcluded = false
                            }
                        }
                    }
                }
            };

            var product = new Product
            {
                Name = "InvalidKey Product",
                Family = "TestFamily",
                CurrencyCode = "USD",
                BaseCPMRate = 2.0,
                Contacts = [],
                PlacementIds = [],
                TimeZoneId = 1,
                SupportedTargets = new[]
                {
                    new SupplyTarget { TargetTypeId = TargetType.KeyValue }
                }
            };

            var result = await LineValidationHelper.ValidateTargeting(
                source: "test",
                targets: line.Targets,
                product: product,
                segmentDao: null!,
                countryDao: null!,
                locationDao: null!,
                keyValueDao: mockKeyValueDao.Object,
                logger: NullLogger.Instance
            );

            Assert.IsTrue(result.Failed);
            Assert.IsTrue(result.Errors.Any(e => e.Message == ErrorMessage.InvalidKeyFormat));
        }

        [TestMethod]
        public async Task KeyValueTarget_InvalidValueFormat_ShouldFail()
        {
            var dynamicConfig = new DynamicConfig { EnableKVPTargeting = true };
            var configMock = new Mock<IOptionsMonitor<DynamicConfig>>();
            configMock.Setup(x => x.CurrentValue).Returns(dynamicConfig);
            new DynamicConfigWrapper(configMock.Object, NullLogger<DynamicConfigWrapper>.Instance);

            var mockKeyValueDao = new Mock<IKeyValueDataAccessObject>();

            mockKeyValueDao.Setup(x => x.GetKey("age", It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingKey>
                {
                    Entity = new XandrTargetingKey
                    {
                        Name = "age",
                        Type = ValueTypes.String
                    },
                });

            mockKeyValueDao.Setup(x => x.GetValue("age", "18#24", It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingValue>
                {
                    Errors = new[]
                    {
                new Error("KeyValue", ErrorMessage.InvalidValueFormat, property: ["age", "18#24"])
                    }
                });

            var line = new Line
            {
                Name = "Invalid Value Format Product",
                Targets = new Target[]
                {
            new KeyValueTarget
            {
                Entries = new[]
                {
                    new KeyValueEntry
                    {
                        Key = "age",
                        Values = new[] { "18#24" },
                        IsExcluded = false
                    }
                }
            }
                }
            };

            var product = new Product
            {
                Name = "InvalidValue Product",
                Family = "TestFamily",
                CurrencyCode = "USD",
                BaseCPMRate = 2.0,
                Contacts = [],
                PlacementIds = [],
                TimeZoneId = 1,
                SupportedTargets = new[]
                {
            new SupplyTarget { TargetTypeId = TargetType.KeyValue }
        }
            };

            var result = await LineValidationHelper.ValidateTargeting(
                source: "test",
                targets: line.Targets,
                product: product,
                segmentDao: null!,
                countryDao: null!,
                locationDao: null!,
                keyValueDao: mockKeyValueDao.Object,
                logger: NullLogger.Instance
            );

            Assert.IsTrue(result.Failed);
            Assert.IsTrue(result.Errors.Any(e => e.Message == ErrorMessage.InvalidValueFormat));
        }

        [TestMethod]
        public async Task KeyValueTarget_ValidKeyAndValueFormat_ShouldPass()
        {
            // Enable KVP targeting via config
            var dynamicConfig = new DynamicConfig { EnableKVPTargeting = true };
            var configMock = new Mock<IOptionsMonitor<DynamicConfig>>();
            configMock.Setup(x => x.CurrentValue).Returns(dynamicConfig);
            new DynamicConfigWrapper(configMock.Object, NullLogger<DynamicConfigWrapper>.Instance);

            // Mock the DAO
            var mockKeyValueDao = new Mock<IKeyValueDataAccessObject>();

            mockKeyValueDao.Setup(x => x.GetKey("age", It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingKey>
                {
                    Entity = new XandrTargetingKey
                    {
                        Name = "age",
                        Type = ValueTypes.String
                    },
                });

            mockKeyValueDao.Setup(x => x.GetValue("age", "18-24", It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingValue>
                {
                    Entity = new XandrTargetingValue
                    {
                        Name = "18-24",
                        TargetingKeyId = 123
                    },
                });

            mockKeyValueDao.Setup(x => x.GetAllValues(It.IsAny<KeyValueGetAllValuesRequest>(), It.IsAny<ILogger>()))
                .ReturnsAsync(new Result<XandrTargetingValue[]>
                {
                    Entity = new[]
                    {
                new XandrTargetingValue
                {
                    Name = "18-24",
                    TargetingKeyId = 123
                }
                    },
                });

            // Create a Line with KeyValue target
            var line = new Line
            {
                Name = "Valid Line",
                Targets = new Target[]
                {
            new KeyValueTarget
            {
                Entries = new[]
                {
                    new KeyValueEntry
                    {
                        Key = "age",
                        Values = new[] { "18-24" },
                        IsExcluded = false
                    }
                }
            }
                }
            };

            // Create a Product that supports KeyValue targeting
            var product = new Product
            {
                Name = "Valid Product",
                Family = "TestFamily",
                CurrencyCode = "USD",
                BaseCPMRate = 2.0,
                Contacts = [],
                PlacementIds = [],
                TimeZoneId = 1,
                SupportedTargets = new[]
                {
            new SupplyTarget { TargetTypeId = TargetType.KeyValue }
        }
            };

            // Run validation
            var result = await LineValidationHelper.ValidateTargeting(
                source: "test",
                targets: line.Targets,
                product: product,
                segmentDao: null!,
                countryDao: null!,
                locationDao: null!,
                keyValueDao: mockKeyValueDao.Object,
                logger: NullLogger.Instance
            );

            Assert.IsTrue(result.Succeeded);
        }

        [TestMethod]
        public void Post_ValidationPass_KeyValueTargeting()
        {
            var validator = new LinePostValidator(
                _customerMock.Object,
                _mediaPlanMock.Object,
                _rotationalProductMock.Object,
                _segmentMock.Object,
                _countryMock.Object,
                _locationMock.Object,
                _keyValueMock.Object
            );

            var line = new Line
            {
                MediaPlanId = MediaPlanId,
                ProductId = ProductId2,
                Name = "KvpTest",
                Description = "desc",
                StartDateTime = DateTime.UtcNow.Date.AddDays(1),
                EndDateTime = DateTime.UtcNow.Date.AddDays(2),
                Cpm = 5.0,
                TargetImpressions = 1000,
                Targets = new Target[]
                {
                    new KeyValueTarget
                    {
                        Entries = new[]
                        {
                            new KeyValueEntry
                            {
                                Key        = "Gender",
                                Values     = new[] { "Male" },
                                IsExcluded = false
                            }
                        }
                    }
                }
            };

            var request = new LinePostRequest(
                new Mock<ILogger<LineController>>().Object,
                CustomerId,
                line,
                _httpContext
            );

            var result = validator.Validate(request).Result;
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Succeeded, "Expected KVP targeting to validate successfully.");
        }

        [TestMethod]
        public async Task Post_ValidationFails_WhenKeyNotFound()
        {
            var dynamicConfig = new DynamicConfig { EnableKVPTargeting = true };
            var configMock = new Mock<IOptionsMonitor<DynamicConfig>>();
            configMock.Setup(x => x.CurrentValue).Returns(dynamicConfig);
            new DynamicConfigWrapper(configMock.Object, NullLogger<DynamicConfigWrapper>.Instance);

            _keyValueMock
              .Setup(m => m.GetKey("MissingKey", It.IsAny<ILogger>()))
              .ReturnsAsync(new Result<XandrTargetingKey>
              {
                  Entity = null
              });

            var validator = new LinePostValidator(
              _customerMock.Object,
              _mediaPlanMock.Object,
              _rotationalProductMock.Object,
              _segmentMock.Object,
              _countryMock.Object,
              _locationMock.Object,
              _keyValueMock.Object
            );

            var line = new Line
            {
                MediaPlanId = MediaPlanId,
                ProductId = ProductId2,
                Name = "BadKvp",
                StartDateTime = DateTime.UtcNow.AddDays(1),
                EndDateTime = DateTime.UtcNow.AddDays(2),
                Cpm = 1.0,
                TargetSpend = 100,
                Targets = new Target[] {
                  new KeyValueTarget {
                    Entries = new[] {
                      new KeyValueEntry {
                        Key        = "MissingKey",
                        Values     = new[] { "Anything" },
                        IsExcluded = false
                      }
                    }
                  }
                }
            };

            var request = new LinePostRequest(
              new Mock<ILogger<LineController>>().Object,
              CustomerId,
              line,
              _httpContext
            );

            var validation = await validator.Validate(request);

            Assert.IsFalse(validation.Succeeded, "Validator should have failed when the KVP key is missing.");
            StringAssert.Contains(
              validation.Errors.First().Message,
              ErrorMessage.InvalidKey
            );
        }

        [TestMethod]
        public async Task Post_ValidationFails_WhenUnsupportedTargetTypeProvided()
        {
            var product = GetValidExistingProduct(ProductType.Rotational);
            product.SupportedTargets = new[]
            {
                new SupplyTarget { TargetTypeId = TargetType.KeyValue, TargetPremium = 0 }
            };

            _rotationalProductMock
              .Setup(m => m.GetByProductId(It.IsAny<ProductGetRequest>()))
              .ReturnsAsync(new Result<Product> { Entity = product });
            _rotationalProductMock
              .Setup(m => m.GetNoPublisher(It.IsAny<ProductGetNoPublisherRequest>()))
              .ReturnsAsync(new Result<Product[]> { Entity = new[] { product } });

            var validator = new LinePostValidator(
              _customerMock.Object,
              _mediaPlanMock.Object,
              _rotationalProductMock.Object,
              _segmentMock.Object,
              _countryMock.Object,
              _locationMock.Object,
              _keyValueMock.Object
            );

            var line = new Line
            {
                MediaPlanId = MediaPlanId,
                ProductId = product.Id,
                Name = "UnsupportedTargetTest",
                StartDateTime = DateTime.UtcNow.AddDays(1),
                EndDateTime = DateTime.UtcNow.AddDays(2),
                Cpm = 1.0,
                TargetImpressions = 1000,
                Targets = new Target[]
                {
                new DeviceTarget { DeviceTypes = new[] { DeviceType.CTV } }
                }
            };

            var request = new LinePostRequest(
              new Mock<ILogger<LineController>>().Object,
              CustomerId,
              line,
              _httpContext
            );

            var validation = await validator.Validate(request);

            Assert.IsFalse(validation.Succeeded, "Validator should reject unsupported target types");
            StringAssert.Contains(
              validation.Errors.First().Message,
              ErrorMessage.UnsupportedTargetType
            );
        }


        private static MediaPlan GetValidExistingMediaPlan()
        {
            return new MediaPlan()
            {
                PublisherId = -123,
                CustomerId = CustomerId,
                Id = MediaPlanId,
                Name = "TestMediaPlan",
                Status = MediaPlanStatus.Draft,
                StartDate = DateTime.Now.Date.AddDays(-3),
                EndDate = DateTime.Now.Date.AddMonths(1),
                TargetSpend = 500,
                CurrencyCode = "USD",
                Contact = "<EMAIL>"
            };
        }

        private static Product GetValidExistingProduct(ProductType type)
        {
            if (type == ProductType.Roadblock)
            {
                return new Product()
                {
                    PublisherId = -123,
                    Id = ProductId1,
                    Name = "ValidationTest2",
                    Description = "Desc",
                    Family = "Fam",
                    Contacts = ["<EMAIL>", "<EMAIL>"],
                    ApprovalContact = "<EMAIL>",
                    PlacementIds = [-1234, -1235],
                    Type = ProductType.Roadblock,
                    Slot = ProductTimeSlot.AllDay,
                    BaseCPMRate = 3.2,
                    CurrencyCode = "USD",
                    MaxConsecutiveBookings = 3,
                    MaxPercentDiscount = 12.7,
                    MaxReservationInHours = 48,
                    TimeZoneId = TimeZoneTestConstant.EST_TIMEZONE_ID,
                    LinkedProductIds = new List<long>() { 111 }
                };
            }
            else
            {
                if (type == ProductType.SOV)
                {
                    return new Product()
                    {
                        PublisherId = -123,
                        Id = ProductId2,
                        Name = "ValidationTest",
                        Description = "Desc",
                        Family = "Fam",
                        Contacts = ["<EMAIL>", "<EMAIL>"],
                        PlacementIds = [-1234],
                        Type = ProductType.SOV,
                        BaseCPMRate = 3.2,
                        MaxPercentDiscount = 15.0,
                        CurrencyCode = "USD",
                        SupportedTargets = [
                            new SupplyTarget() { TargetTypeId = TargetType.Audience, TargetPremium = 0.3 },
                            new SupplyTarget() { TargetTypeId = TargetType.Location, TargetPremium = 0 },
                            new SupplyTarget() { TargetTypeId = TargetType.Device, TargetPremium = 1 },
                            new SupplyTarget() { TargetTypeId = TargetType.FrequencyAndRecency, TargetPremium = 0 },
                            new SupplyTarget() { TargetTypeId = TargetType.DayPart, TargetPremium = 0 },
                            new SupplyTarget() {TargetTypeId = TargetType.DMA, TargetPremium = 0},
                            new SupplyTarget() { TargetTypeId = TargetType.KeyValue, TargetPremium = 0}
                            ],
                        TimeZoneId = TimeZoneTestConstant.PST_TIMEZONE_ID
                    };
                }
                return new Product()
                {
                    PublisherId = -123,
                    Id = ProductId2,
                    Name = "ValidationTest",
                    Description = "Desc",
                    Family = "Fam",
                    Contacts = ["<EMAIL>", "<EMAIL>"],
                    PlacementIds = [-1234],
                    Type = ProductType.Rotational,
                    BaseCPMRate = 3.2,
                    MaxPercentDiscount = 15.0,
                    CurrencyCode = "USD",
                    SupportedTargets = [
                        new SupplyTarget() { TargetTypeId = TargetType.Audience, TargetPremium = 0.3 },
                        new SupplyTarget() { TargetTypeId = TargetType.Location, TargetPremium = 0 },
                        new SupplyTarget() { TargetTypeId = TargetType.Device, TargetPremium = 1 },
                        new SupplyTarget() { TargetTypeId = TargetType.FrequencyAndRecency, TargetPremium = 0 },
                        new SupplyTarget() { TargetTypeId = TargetType.DayPart, TargetPremium = 0 },
                        new SupplyTarget() {TargetTypeId = TargetType.DMA, TargetPremium = 0},
                        new SupplyTarget() { TargetTypeId = TargetType.KeyValue, TargetPremium = 0 }
                    ],
                    TimeZoneId = TimeZoneTestConstant.PST_TIMEZONE_ID
                };
            }
        }

        private static (string[] property, string message) GetErrorMessageFromResult(Result result)
        {
            var errors = result.Errors;
            Assert.AreEqual(1, errors.Count, "Error count does not match");
            var error = errors.First();
            return (error.Property, error.Message);
        }
    }
}
