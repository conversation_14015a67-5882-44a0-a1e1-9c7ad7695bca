namespace Microsoft.AdCenter.Shared.MT
{
    using Microsoft.AdCenter.Shared.Logging;
    using ProtoBuf;
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.IO;
    using System.Threading;
    using System.Threading.Tasks;

    [ProtoContract]
    public class AssetTextBlobMetadata
    {
        [ProtoMember(1)]
        public string TimeStamp;

        [ProtoMember(2)]
        [ProtoMap(DisableMap = true)]
        public Dictionary<int, (int LoadCnt, List<string> Paths)> Paths = new Dictionary<int, (int LoadCnt, List<string> Paths)>();
    }

    [ProtoContract]
    public class AssetText
    {
        [ProtoMember(1)]
        public long AssetId;

        [ProtoMember(2)]
        public string TextAsset;
    }

    [ProtoContract]
    public class AdAsset
    {
        [ProtoMember(1)]
        public long AssetId;

        public override bool Equals(object obj)
        {
            if (!(obj is AdAsset adAsset))
            {
                return false;
            }

            return AssetId == adAsset.AssetId;
        }

        public override int GetHashCode()
        {
            return AssetId.GetHashCode();
        }
    }

    public static class TextAssetAndMetatdataSerializeDeserialize
    {
        public static int RetryCount = 3;
        private static readonly int IdProtoMemberTag = 1;
        private static readonly int ContentProtoMemberTag = 2;

        public static void SerializeMetaDataToStream(Stream stream, AssetTextBlobMetadata metadata, ILogCommon logger, CancellationToken cancellationToken = default)
        {
            try
            {
                ProtoBuf.Serializer.Serialize<AssetTextBlobMetadata>(stream, metadata);
                stream.Flush();
            }
            catch (Exception ex)
            {
                logger.LogException(ex);
                throw;
            }
        }

        public static AssetTextBlobMetadata DeserializeMetadataFromStream(Stream stream, ILogCommon logger, CancellationToken cancellationToken = default)
        {
            var result = new AssetTextBlobMetadata();
            var retriesRemaining = RetryCount;
            while (retriesRemaining > 0)
            {
                try
                {
                    return ProtoBuf.Serializer.Deserialize<AssetTextBlobMetadata>(stream);
                }
                catch (TaskCanceledException)
                {
                    // Don't log anything for task cancellation exception.
                }
                catch (Exception ex)
                {
                    logger.LogException(ex);
                    retriesRemaining--;
                    if (retriesRemaining <= 0)
                    {
                        throw;
                    }
                }
            }

            return result;
        }

        public static void SerializeAdAssetToStream(Stream stream, IEnumerable<AdAsset> contents, ILogCommon logger, CancellationToken cancellationToken = default)
        {
            try
            {
                ProtoBuf.Serializer.Serialize<IEnumerable<AdAsset>>(stream, contents);
                stream.Flush();
            }
            catch (Exception ex)
            {
                logger.LogException(ex);
                throw;
            }
        }

        public static IEnumerable<AdAsset> DeserializeAdAssetFromStream(Stream stream, ILogCommon logger, CancellationToken cancellationToken = default)
        {
            var retriesRemaining = RetryCount;
            while (retriesRemaining > 0)
            {
                try
                {
                    return ProtoBuf.Serializer.Deserialize<IEnumerable<AdAsset>>(stream);
                }
                catch (TaskCanceledException)
                {
                    // Don't log anything for task cancellation exception.
                }
                catch (Exception ex)
                {
                    logger.LogException(ex);
                    retriesRemaining--;
                    if (retriesRemaining <= 0)
                    {
                        throw;
                    }
                }
            }

            return null;
        }

        public static void SerializeTextAssetToStream(Stream stream, IEnumerable<AssetText> contents, ILogCommon logger, CancellationToken cancellationToken = default)
        {
            try
            {
#pragma warning disable CS0618 // Type or member is obsolete
                using (var writer = ProtoWriter.Create(stream, null, null))
                {
                    foreach (var content in contents)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        ProtoWriter.WriteFieldHeader(1, WireType.StartGroup, writer);
                        var token = ProtoWriter.StartSubItem(content, writer);

                        ProtoWriter.WriteFieldHeader(IdProtoMemberTag, WireType.Variant, writer);
                        ProtoWriter.WriteInt64(content.AssetId, writer);

                        ProtoWriter.WriteFieldHeader(ContentProtoMemberTag, WireType.String, writer);
                        ProtoWriter.WriteString(content.TextAsset, writer);

                        ProtoWriter.EndSubItem(token, writer);
                    }
                    
                    writer.Close();
                }
#pragma warning restore CS0618 // Type or member is obsolete

                stream.Flush();
            }
            catch (Exception ex)
            {
                logger.LogException(ex);
                throw;
            }
        }

        public static Dictionary<long, AssetText> DeserializeTextAssetFromStream(Stream stream, ILogCommon logger, CancellationToken cancellationToken = default)
        {
            var contents = new Dictionary<long, AssetText>();
            var retriesRemaining = RetryCount;
            while (retriesRemaining > 0)
            {
                try
                {
                    contents = new Dictionary<long, AssetText>();
#pragma warning disable CS0618 // Type or member is obsolete
                    using (var reader = ProtoReader.Create(stream, null, null))
                    {
                        while (reader.ReadFieldHeader() > 0)
                        {
                            cancellationToken.ThrowIfCancellationRequested();

                            var token = ProtoReader.StartSubItem(reader);
                            var assetText = new AssetText();

                            reader.ReadFieldHeader();
                            var id = reader.ReadInt64();
                            assetText.AssetId = id;

                            reader.ReadFieldHeader();
                            var content = reader.ReadString();
                            assetText.TextAsset = content;

                            contents.Add(id, assetText);

                            reader.ReadFieldHeader();
                            ProtoReader.EndSubItem(token, reader);
                        }
                    }
#pragma warning restore CS0618 // Type or member is obsolete

                    return contents;
                }
                catch (TaskCanceledException)
                {
                    // Don't log anything for task cancellation exception.
                }
                catch (Exception ex)
                {
                    logger.LogException(ex);
                    retriesRemaining--;
                    if (retriesRemaining <= 0)
                    {
                        throw;
                    }
                }
            }

            return contents;
        }

        public static void DeserializeTextAssetStringFromStream(Stream stream, ILogCommon logger, ConcurrentDictionary<long, string> mergedAssetTextDic, CancellationToken cancellationToken = default)
        {
            var retriesRemaining = RetryCount;
            while (retriesRemaining > 0)
            {
                try
                {
#pragma warning disable CS0618 // Type or member is obsolete
                    using (var reader = ProtoReader.Create(stream, null, null))
                    {
                        while (reader.ReadFieldHeader() > 0)
                        {
                            cancellationToken.ThrowIfCancellationRequested();

                            var token = ProtoReader.StartSubItem(reader);

                            reader.ReadFieldHeader();
                            var id = reader.ReadInt64();

                            reader.ReadFieldHeader();
                            var content = reader.ReadString();

                            mergedAssetTextDic.AddOrUpdate(id, content, (x, y) => y);

                            reader.ReadFieldHeader();
                            ProtoReader.EndSubItem(token, reader);
                        }
                    }
#pragma warning restore CS0618 // Type or member is obsolete

                    return;
                }
                catch (TaskCanceledException)
                {
                    // Don't log anything for task cancellation exception.
                }
                catch (Exception ex)
                {
                    logger.LogException(ex);
                    retriesRemaining--;
                    if (retriesRemaining <= 0)
                    {
                        throw;
                    }
                }
            }
        }
    }
}
