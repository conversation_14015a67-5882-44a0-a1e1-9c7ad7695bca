﻿namespace Service.CampaignMT;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization.Metadata;

public static partial class RestApiGeneration
{
    public partial class OpenApiKnownTypesMapping
    {
        public static Dictionary<Type, Dictionary<Type, string>> TypesMap = new Dictionary<Type, Dictionary<Type, string>>
        {
            // Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13 entities
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Ad), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.TextAd), "Text" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ProductAd), "Product" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.HotelAd), "Hotel" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AppInstallAd), "AppInstall" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ExpandedTextAd), "ExpandedText" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DynamicSearchAd), "DynamicSearch" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ResponsiveAd), "ResponsiveAd" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ResponsiveSearchAd), "ResponsiveSearch" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdExtension), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.LocationAdExtension), "LocationAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CallAdExtension), "CallAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ImageAdExtension), "ImageAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AppAdExtension), "AppAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.NewsAdExtension), "NewsAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ReviewAdExtension), "ReviewAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DataTableAdExtension), "DataTableAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CalloutAdExtension), "CalloutAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SitelinkAdExtension), "SitelinkAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ActionAdExtension), "ActionAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.StructuredSnippetAdExtension), "StructuredSnippetAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.PriceAdExtension), "PriceAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.PromotionAdExtension), "PromotionAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.FilterLinkAdExtension), "FilterLinkAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.FlyerAdExtension), "FlyerAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.VideoAdExtension), "VideoAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DisclaimerAdExtension), "DisclaimerAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.LogoAdExtension), "LogoAdExtension" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.LeadFormAdExtension), "LeadFormAdExtension" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroupCriterion), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BiddableAdGroupCriterion), "BiddableAdGroupCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.NegativeAdGroupCriterion), "NegativeAdGroupCriterion" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Asset), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.TextAsset), "TextAsset" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ImageAsset), "ImageAsset" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.VideoAsset), "VideoAsset" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Audience), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RemarketingList), "RemarketingList" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CustomAudience), "Custom" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.InMarketAudience), "InMarket" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ProductAudience), "Product" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SimilarRemarketingList), "SimilarRemarketingList" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CombinedList), "CombinedList" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CustomerList), "CustomerList" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ImpressionBasedRemarketingList), "ImpressionBasedRemarketingList" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AudienceGroupDimension), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AgeDimension), "Age" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GenderDimension), "Gender" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AudienceDimension), "Audience" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ProfileDimension), "Profile" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BiddingScheme), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.MaxClicksBiddingScheme), "MaxClicks" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.MaxConversionsBiddingScheme), "MaxConversions" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.TargetCpaBiddingScheme), "TargetCpa" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ManualCpcBiddingScheme), "ManualCpc" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EnhancedCpcBiddingScheme), "EnhancedCpc" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ManualCpvBiddingScheme), "ManualCpv" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ManualCpmBiddingScheme), "ManualCpm" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.InheritFromParentBiddingScheme), "InheritFromParent" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.TargetRoasBiddingScheme), "TargetRoasBiddingScheme" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.MaxRoasBiddingScheme), "MaxRoasBiddingScheme" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.MaxConversionValueBiddingScheme), "MaxConversionValueBiddingScheme" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.TargetImpressionShareBiddingScheme), "TargetImpressionShareBiddingScheme" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.PercentCpcBiddingScheme), "PercentCpcBiddingScheme" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CommissionBiddingScheme), "CommissionBiddingScheme" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ManualCpaBiddingScheme), "ManualCpaBiddingScheme" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CostPerSaleBiddingScheme), "CostPerSale" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignCriterion), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.NegativeCampaignCriterion), "NegativeCampaignCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BiddableCampaignCriterion), "BiddableCampaignCriterion" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ConversionGoal), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UrlGoal), "Url" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DurationGoal), "Duration" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.PagesViewedPerVisitGoal), "PagesViewedPerVisit" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EventGoal), "Event" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AppInstallGoal), "AppInstall" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.OfflineConversionGoal), "OfflineConversion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.InStoreTransactionGoal), "InStoreTransaction" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Criterion), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ProductPartition), "ProductPartition" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.HotelGroup), "HotelGroup" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.HotelAdvanceBookingWindowCriterion), "HotelAdvanceBookingWindowCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.HotelCheckInDateCriterion), "HotelCheckInDateCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.HotelCheckInDayCriterion), "HotelCheckInDayCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.HotelDateSelectionTypeCriterion), "HotelDateSelectionTypeCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.HotelLengthOfStayCriterion), "HotelLengthOfStayCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ProductScope), "ProductScope" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Webpage), "Webpage" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AgeCriterion), "AgeCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeviceCriterion), "DeviceCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DayTimeCriterion), "DayTimeCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GenderCriterion), "GenderCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RadiusCriterion), "RadiusCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.LocationCriterion), "LocationCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.LocationIntentCriterion), "LocationIntentCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AudienceCriterion), "AudienceCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ProfileCriterion), "ProfileCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.StoreCriterion), "StoreCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DealCriterion), "DealCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GenreCriterion), "GenreCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.PlacementCriterion), "PlacementCriterion" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.TopicCriterion), "TopicCriterion" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CriterionBid), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.FixedBid), "FixedBid" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BidMultiplier), "BidMultiplier" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RateBid), "RateBid" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CriterionCashback), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CashbackAdjustment), "CashbackAdjustment" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ImportJob), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GoogleImportJob), "GoogleImportJob" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.FileImportJob), "FileImportJob" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ImportOption), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GoogleImportOption), "GoogleImportOption" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.FileImportOption), "FileImportOption" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Media), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Image), "Image" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.MediaRepresentation), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ImageMediaRepresentation), "ImageMediaRepresentation" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.MediaRepresentation), "MediaRepresentation" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RemarketingRule), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.PageVisitorsRule), "PageVisitors" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.PageVisitorsWhoVisitedAnotherPageRule), "PageVisitorsWhoVisitedAnotherPage" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.PageVisitorsWhoDidNotVisitAnotherPageRule), "PageVisitorsWhoDidNotVisitAnotherPage" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CustomEventsRule), "CustomEvents" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RuleItem), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.StringRuleItem), "String" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.NumberRuleItem), "Number" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Setting), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ShoppingSetting), "ShoppingSetting" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DynamicFeedSetting), "DynamicFeedSetting" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DynamicSearchAdsSetting), "DynamicSearchAdsSetting" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.TargetSetting), "TargetSetting" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CoOpSetting), "CoOpSetting" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.VerifiedTrackingSetting), "VerifiedTrackingSetting" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DisclaimerSetting), "DisclaimerSetting" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.HotelSetting), "HotelSetting" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ResponsiveSearchAdsSetting), "ResponsiveSearchAdsSetting" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.PerformanceMaxSetting), "PerformanceMaxSetting" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CallToActionSetting), "CallToActionSetting" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.VanityPharmaSetting), "VanityPharmaSetting" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AppSetting), "AppSetting" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ThirdPartyMeasurementSetting), "ThirdPartyMeasurementSetting" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.NewCustomerAcquisitionGoalSetting), "NewCustomerAcquisitionGoalSetting" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedEntity), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.NegativeKeywordList), "NegativeKeywordList" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.PlacementExclusionList), "PlacementExclusionList" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AccountNegativeKeywordList), "AccountNegativeKeywordList" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BrandList), "BrandList" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AccountPlacementExclusionList), "AccountPlacementExclusionList" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AccountPlacementInclusionList), "AccountPlacementInclusionList" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedList), "SharedList" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedList), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.NegativeKeywordList), "NegativeKeywordList" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.PlacementExclusionList), "PlacementExclusionList" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AccountNegativeKeywordList), "AccountNegativeKeywordList" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BrandList), "BrandList" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AccountPlacementExclusionList), "AccountPlacementExclusionList" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AccountPlacementInclusionList), "AccountPlacementInclusionList" } }
            },
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedListItem), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.NegativeKeyword), "NegativeKeyword" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.NegativeSite), "NegativeSite" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BrandItem), "BrandItem" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Site), "Site" } }
            },

            // Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2.CustomColumn entities
            { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2.CustomColumn.CustomColumnNode), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2.CustomColumn.CustomColumnConstantNode), "CustomColumnConstantNode" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2.CustomColumn.CustomColumnDataNode), "CustomColumnDataNode" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2.CustomColumn.CustomColumnBinaryOperatorNode), "CustomColumnBinaryOperatorNode" },
                { typeof(Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entity.Aggregator2.CustomColumn.CustomColumnUnaryOperatorNode), "CustomColumnUnaryOperatorNode" } }
            },

            // Microsoft.AdCenter.Shared.Api.V13.DataContracts entities
            { typeof(Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Shared.Api.V13.DataContracts.EditorialError), "EditorialError" },
                { typeof(Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError), "BatchError" } }
            },
            { typeof(Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchErrorCollection), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Shared.Api.V13.DataContracts.EditorialErrorCollection), "EditorialErrorCollection" },
                { typeof(Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchErrorCollection), "BatchErrorCollection" } }
            },

       };
    }
}