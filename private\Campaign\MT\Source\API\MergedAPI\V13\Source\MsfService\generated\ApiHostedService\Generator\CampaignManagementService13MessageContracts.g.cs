﻿using Microsoft.AdCenter.MsfRuntime;
using Microsoft.AdCenter.Shared.Api.V13;
using Microsoft.AdCenter.Shared.Api.V13.WsdlManager;
using System.Collections.Generic;
using System.Runtime.Serialization;

#if NETFULL
    using System.ServiceModel;
#else
using CoreWCF;
#endif

namespace HostedService.CampaignManagementService13
{
    [MessageContract]
    public class AddCampaignsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AccountId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Campaign[] Campaigns;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddCampaignsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddCampaignsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddCampaignsRequest
            {
                AccountId = AccountId,

                Campaigns = Campaigns
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddCampaignsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] CampaignIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddCampaignsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddCampaignsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.CampaignIds = result.CampaignIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetCampaignsByAccountIdRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AccountId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignType CampaignType;

        [MessageBodyMember(Order = 3)]
        [HideMeFromWsdl(WsdlConfiguration.Feature.AdScheduleTimeZoneSetting)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignsByAccountIdRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignsByAccountIdRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignsByAccountIdRequest
            {
                AccountId = AccountId,

                CampaignType = CampaignType,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetCampaignsByAccountIdResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Campaign[] Campaigns;

        public GetCampaignsByAccountIdResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignsByAccountIdResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Campaigns = result.Campaigns;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetCampaignsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AccountId;

        [MessageBodyMember(Order = 2)]
        public long[] CampaignIds;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignType CampaignType;

        [MessageBodyMember(Order = 5)]
        [HideMeFromWsdl(WsdlConfiguration.Feature.AdScheduleTimeZoneSetting)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignsByIdsRequest
            {
                AccountId = AccountId,

                CampaignIds = CampaignIds,

                CampaignType = CampaignType,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetCampaignsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Campaign[] Campaigns;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetCampaignsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Campaigns = result.Campaigns;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteCampaignsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AccountId;

        [MessageBodyMember(Order = 2)]
        public long[] CampaignIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteCampaignsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteCampaignsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteCampaignsRequest
            {
                AccountId = AccountId,

                CampaignIds = CampaignIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteCampaignsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteCampaignsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteCampaignsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateCampaignsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AccountId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Campaign[] Campaigns;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateCampaignsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateCampaignsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateCampaignsRequest
            {
                AccountId = AccountId,

                Campaigns = Campaigns
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateCampaignsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateCampaignsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateCampaignsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetNegativeSitesByCampaignIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AccountId;

        [MessageBodyMember(Order = 2)]
        public long[] CampaignIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNegativeSitesByCampaignIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNegativeSitesByCampaignIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNegativeSitesByCampaignIdsRequest
            {
                AccountId = AccountId,

                CampaignIds = CampaignIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetNegativeSitesByCampaignIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignNegativeSites[] CampaignNegativeSites;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetNegativeSitesByCampaignIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNegativeSitesByCampaignIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.CampaignNegativeSites = result.CampaignNegativeSites;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class SetNegativeSitesToCampaignsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AccountId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignNegativeSites[] CampaignNegativeSites;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetNegativeSitesToCampaignsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetNegativeSitesToCampaignsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetNegativeSitesToCampaignsRequest
            {
                AccountId = AccountId,

                CampaignNegativeSites = CampaignNegativeSites
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class SetNegativeSitesToCampaignsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public SetNegativeSitesToCampaignsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetNegativeSitesToCampaignsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetConfigValueRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public string ConfigKey;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConfigValueRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConfigValueRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConfigValueRequest
            {
                ConfigKey = ConfigKey
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetConfigValueResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public string ConfigValue;

        public GetConfigValueResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConfigValueResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ConfigValue = result.ConfigValue;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetBSCCountriesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBSCCountriesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBSCCountriesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBSCCountriesRequest();
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetBSCCountriesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public System.Collections.Generic.IList<string> CountryCodes;

        public GetBSCCountriesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBSCCountriesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.CountryCodes = result.CountryCodes;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddAdGroupsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long CampaignId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroup[] AdGroups;

        [MessageBodyMember(Order = 3)]
        public bool? ReturnInheritedBidStrategyTypes;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdGroupsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdGroupsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdGroupsRequest
            {
                CampaignId = CampaignId,

                AdGroups = AdGroups,

                ReturnInheritedBidStrategyTypes = ReturnInheritedBidStrategyTypes
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddAdGroupsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] AdGroupIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        [MessageBodyMember(Order = 0)]
        public string[] InheritedBidStrategyTypes;

        public AddAdGroupsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdGroupsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AdGroupIds = result.AdGroupIds;
            this.PartialErrors = result.PartialErrors;
            this.InheritedBidStrategyTypes = result.InheritedBidStrategyTypes;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteAdGroupsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long CampaignId;

        [MessageBodyMember(Order = 2)]
        public long[] AdGroupIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdGroupsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdGroupsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdGroupsRequest
            {
                CampaignId = CampaignId,

                AdGroupIds = AdGroupIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteAdGroupsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteAdGroupsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdGroupsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAdGroupsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long CampaignId;

        [MessageBodyMember(Order = 2)]
        public long[] AdGroupIds;

        [MessageBodyMember(Order = 3)]
        [HideMeFromWsdl(WsdlConfiguration.Feature.AdScheduleTimeZoneSetting)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroupAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdGroupsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdGroupsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdGroupsByIdsRequest
            {
                CampaignId = CampaignId,

                AdGroupIds = AdGroupIds,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAdGroupsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroup[] AdGroups;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetAdGroupsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdGroupsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AdGroups = result.AdGroups;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAdGroupsByCampaignIdRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long CampaignId;

        [MessageBodyMember(Order = 2)]
        [HideMeFromWsdl(WsdlConfiguration.Feature.AdScheduleTimeZoneSetting)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroupAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdGroupsByCampaignIdRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdGroupsByCampaignIdRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdGroupsByCampaignIdRequest
            {
                CampaignId = CampaignId,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAdGroupsByCampaignIdResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroup[] AdGroups;

        public GetAdGroupsByCampaignIdResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdGroupsByCampaignIdResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AdGroups = result.AdGroups;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateAdGroupsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long CampaignId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroup[] AdGroups;

        [MessageBodyMember(Order = 3)]
        public bool UpdateAudienceAdsBidAdjustment;

        [MessageBodyMember(Order = 4)]
        public bool? ReturnInheritedBidStrategyTypes;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdGroupsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdGroupsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdGroupsRequest
            {
                CampaignId = CampaignId,

                AdGroups = AdGroups,

                UpdateAudienceAdsBidAdjustment = UpdateAudienceAdsBidAdjustment,

                ReturnInheritedBidStrategyTypes = ReturnInheritedBidStrategyTypes
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateAdGroupsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        [MessageBodyMember(Order = 0)]
        public string[] InheritedBidStrategyTypes;

        public UpdateAdGroupsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdGroupsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;
            this.InheritedBidStrategyTypes = result.InheritedBidStrategyTypes;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetNegativeSitesByAdGroupIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long CampaignId;

        [MessageBodyMember(Order = 2)]
        public long[] AdGroupIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNegativeSitesByAdGroupIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNegativeSitesByAdGroupIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNegativeSitesByAdGroupIdsRequest
            {
                CampaignId = CampaignId,

                AdGroupIds = AdGroupIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetNegativeSitesByAdGroupIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroupNegativeSites[] AdGroupNegativeSites;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetNegativeSitesByAdGroupIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNegativeSitesByAdGroupIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AdGroupNegativeSites = result.AdGroupNegativeSites;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class SetNegativeSitesToAdGroupsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long CampaignId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroupNegativeSites[] AdGroupNegativeSites;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetNegativeSitesToAdGroupsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetNegativeSitesToAdGroupsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetNegativeSitesToAdGroupsRequest
            {
                CampaignId = CampaignId,

                AdGroupNegativeSites = AdGroupNegativeSites
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class SetNegativeSitesToAdGroupsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public SetNegativeSitesToAdGroupsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetNegativeSitesToAdGroupsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetGeoLocationsFileUrlRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public string Version;

        [MessageBodyMember(Order = 2)]
        public string LanguageLocale;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CompressionType? CompressionType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetGeoLocationsFileUrlRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetGeoLocationsFileUrlRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetGeoLocationsFileUrlRequest
            {
                Version = Version,

                LanguageLocale = LanguageLocale,

                CompressionType = CompressionType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetGeoLocationsFileUrlResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public string FileUrl;

        [MessageBodyMember(Order = 0)]
        public System.DateTime LastModifiedTimeUtc;

        [MessageBodyMember(Order = 0)]
        public System.DateTime? FileUrlExpiryTimeUtc;

        public GetGeoLocationsFileUrlResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetGeoLocationsFileUrlResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.FileUrl = result.FileUrl;
            this.LastModifiedTimeUtc = result.LastModifiedTimeUtc;
            this.FileUrlExpiryTimeUtc = result.FileUrlExpiryTimeUtc;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddAdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AdGroupId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Ad[] Ads;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdsRequest
            {
                AdGroupId = AdGroupId,

                Ads = Ads
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddAdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] AdIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddAdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AdIds = result.AdIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteAdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AdGroupId;

        [MessageBodyMember(Order = 2)]
        public long[] AdIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdsRequest
            {
                AdGroupId = AdGroupId,

                AdIds = AdIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteAdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteAdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAdsByEditorialStatusRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AdGroupId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdEditorialStatus EditorialStatus;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdType[] AdTypes;

        [MessageBodyMember(Order = 4)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdsByEditorialStatusRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdsByEditorialStatusRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdsByEditorialStatusRequest
            {
                AdGroupId = AdGroupId,

                EditorialStatus = EditorialStatus,

                AdTypes = AdTypes,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAdsByEditorialStatusResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Ad[] Ads;

        public GetAdsByEditorialStatusResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdsByEditorialStatusResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Ads = result.Ads;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAdsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AdGroupId;

        [MessageBodyMember(Order = 2)]
        public long[] AdIds;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdType[] AdTypes;

        [MessageBodyMember(Order = 4)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdsByIdsRequest
            {
                AdGroupId = AdGroupId,

                AdIds = AdIds,

                AdTypes = AdTypes,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAdsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Ad[] Ads;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetAdsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Ads = result.Ads;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAdsByAdGroupIdRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AdGroupId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdType[] AdTypes;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdsByAdGroupIdRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdsByAdGroupIdRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdsByAdGroupIdRequest
            {
                AdGroupId = AdGroupId,

                AdTypes = AdTypes,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAdsByAdGroupIdResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Ad[] Ads;

        public GetAdsByAdGroupIdResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdsByAdGroupIdResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Ads = result.Ads;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateAdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AdGroupId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Ad[] Ads;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdsRequest
            {
                AdGroupId = AdGroupId,

                Ads = Ads
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateAdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateAdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddKeywordsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AdGroupId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Keyword[] Keywords;

        [MessageBodyMember(Order = 3)]
        public bool? ReturnInheritedBidStrategyTypes;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddKeywordsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddKeywordsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddKeywordsRequest
            {
                AdGroupId = AdGroupId,

                Keywords = Keywords,

                ReturnInheritedBidStrategyTypes = ReturnInheritedBidStrategyTypes
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddKeywordsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] KeywordIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        [MessageBodyMember(Order = 0)]
        public string[] InheritedBidStrategyTypes;

        public AddKeywordsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddKeywordsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.KeywordIds = result.KeywordIds;
            this.PartialErrors = result.PartialErrors;
            this.InheritedBidStrategyTypes = result.InheritedBidStrategyTypes;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteKeywordsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AdGroupId;

        [MessageBodyMember(Order = 2)]
        public long[] KeywordIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteKeywordsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteKeywordsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteKeywordsRequest
            {
                AdGroupId = AdGroupId,

                KeywordIds = KeywordIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteKeywordsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteKeywordsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteKeywordsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetKeywordsByEditorialStatusRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AdGroupId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.KeywordEditorialStatus EditorialStatus;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.KeywordAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetKeywordsByEditorialStatusRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetKeywordsByEditorialStatusRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetKeywordsByEditorialStatusRequest
            {
                AdGroupId = AdGroupId,

                EditorialStatus = EditorialStatus,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetKeywordsByEditorialStatusResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Keyword[] Keywords;

        public GetKeywordsByEditorialStatusResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetKeywordsByEditorialStatusResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Keywords = result.Keywords;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetKeywordsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AdGroupId;

        [MessageBodyMember(Order = 2)]
        public long[] KeywordIds;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.KeywordAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetKeywordsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetKeywordsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetKeywordsByIdsRequest
            {
                AdGroupId = AdGroupId,

                KeywordIds = KeywordIds,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetKeywordsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Keyword[] Keywords;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetKeywordsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetKeywordsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Keywords = result.Keywords;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetKeywordsByAdGroupIdRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AdGroupId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.KeywordAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetKeywordsByAdGroupIdRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetKeywordsByAdGroupIdRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetKeywordsByAdGroupIdRequest
            {
                AdGroupId = AdGroupId,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetKeywordsByAdGroupIdResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Keyword[] Keywords;

        public GetKeywordsByAdGroupIdResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetKeywordsByAdGroupIdResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Keywords = result.Keywords;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateKeywordsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AdGroupId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Keyword[] Keywords;

        [MessageBodyMember(Order = 3)]
        public bool? ReturnInheritedBidStrategyTypes;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateKeywordsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateKeywordsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateKeywordsRequest
            {
                AdGroupId = AdGroupId,

                Keywords = Keywords,

                ReturnInheritedBidStrategyTypes = ReturnInheritedBidStrategyTypes
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateKeywordsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        [MessageBodyMember(Order = 0)]
        public string[] InheritedBidStrategyTypes;

        public UpdateKeywordsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateKeywordsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;
            this.InheritedBidStrategyTypes = result.InheritedBidStrategyTypes;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AppealEditorialRejectionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityIdToParentIdAssociation[] EntityIdToParentIdAssociations;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityType EntityType;

        [MessageBodyMember(Order = 0)]
        public string JustificationText;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AppealEditorialRejectionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AppealEditorialRejectionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AppealEditorialRejectionsRequest
            {
                EntityIdToParentIdAssociations = EntityIdToParentIdAssociations,

                EntityType = EntityType,

                JustificationText = JustificationText
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AppealEditorialRejectionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AppealEditorialRejectionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AppealEditorialRejectionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetEditorialReasonsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AccountId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityIdToParentIdAssociation[] EntityIdToParentIdAssociations;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityType EntityType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetEditorialReasonsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetEditorialReasonsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetEditorialReasonsByIdsRequest
            {
                AccountId = AccountId,

                EntityIdToParentIdAssociations = EntityIdToParentIdAssociations,

                EntityType = EntityType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetEditorialReasonsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public System.Collections.Generic.IList<Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EditorialReasonCollection> EditorialReasons;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetEditorialReasonsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetEditorialReasonsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.EditorialReasons = result.EditorialReasons;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAccountMigrationStatusesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long[] AccountIds;

        [MessageBodyMember(Order = 2)]
        public string MigrationType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAccountMigrationStatusesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAccountMigrationStatusesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAccountMigrationStatusesRequest
            {
                AccountIds = AccountIds,

                MigrationType = MigrationType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAccountMigrationStatusesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AccountMigrationStatusesInfo[] MigrationStatuses;

        public GetAccountMigrationStatusesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAccountMigrationStatusesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.MigrationStatuses = result.MigrationStatuses;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class SetAccountPropertiesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AccountProperty[] AccountProperties;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetAccountPropertiesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetAccountPropertiesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetAccountPropertiesRequest
            {
                AccountProperties = AccountProperties
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class SetAccountPropertiesResponse : ApiResponse
    {
        public SetAccountPropertiesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetAccountPropertiesResponse result, string trackingId)
       : base(result, trackingId)
        {
            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAccountPropertiesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AccountPropertyName[] AccountPropertyNames;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAccountPropertiesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAccountPropertiesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAccountPropertiesRequest
            {
                AccountPropertyNames = AccountPropertyNames
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAccountPropertiesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AccountProperty[] AccountProperties;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetAccountPropertiesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAccountPropertiesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AccountProperties = result.AccountProperties;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddAdExtensionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AccountId;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdExtension[] AdExtensions;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdExtensionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdExtensionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdExtensionsRequest
            {
                AccountId = AccountId,

                AdExtensions = AdExtensions
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddAdExtensionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdExtensionIdentity[] AdExtensionIdentities;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchErrorCollection[] NestedPartialErrors;

        public AddAdExtensionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdExtensionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AdExtensionIdentities = result.AdExtensionIdentities;
            this.NestedPartialErrors = result.NestedPartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAdExtensionsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AccountId;

        [MessageBodyMember(Order = 2)]
        public long[] AdExtensionIds;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdExtensionsTypeFilter AdExtensionType;

        [MessageBodyMember(Order = 4)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdExtensionAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionsByIdsRequest
            {
                AccountId = AccountId,

                AdExtensionIds = AdExtensionIds,

                AdExtensionType = AdExtensionType,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAdExtensionsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdExtension[] AdExtensions;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetAdExtensionsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AdExtensions = result.AdExtensions;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateAdExtensionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdExtension[] AdExtensions;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdExtensionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdExtensionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdExtensionsRequest
            {
                AccountId = AccountId,

                AdExtensions = AdExtensions
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateAdExtensionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchErrorCollection[] NestedPartialErrors;

        public UpdateAdExtensionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdExtensionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.NestedPartialErrors = result.NestedPartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteAdExtensionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AccountId;

        [MessageBodyMember(Order = 2)]
        public long[] AdExtensionIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdExtensionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdExtensionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdExtensionsRequest
            {
                AccountId = AccountId,

                AdExtensionIds = AdExtensionIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteAdExtensionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteAdExtensionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdExtensionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAdExtensionsEditorialReasonsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdExtensionIdToEntityIdAssociation[] AdExtensionIdToEntityIdAssociations;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssociationType AssociationType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionsEditorialReasonsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionsEditorialReasonsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionsEditorialReasonsRequest
            {
                AccountId = AccountId,

                AdExtensionIdToEntityIdAssociations = AdExtensionIdToEntityIdAssociations,

                AssociationType = AssociationType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAdExtensionsEditorialReasonsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdExtensionEditorialReasonCollection[] EditorialReasons;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetAdExtensionsEditorialReasonsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionsEditorialReasonsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.EditorialReasons = result.EditorialReasons;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class SetAdExtensionsAssociationsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdExtensionIdToEntityIdAssociation[] AdExtensionIdToEntityIdAssociations;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssociationType AssociationType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetAdExtensionsAssociationsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetAdExtensionsAssociationsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetAdExtensionsAssociationsRequest
            {
                AccountId = AccountId,

                AdExtensionIdToEntityIdAssociations = AdExtensionIdToEntityIdAssociations,

                AssociationType = AssociationType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class SetAdExtensionsAssociationsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public SetAdExtensionsAssociationsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetAdExtensionsAssociationsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAdExtensionsAssociationsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 0)]
        public long[] EntityIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdExtensionsTypeFilter AdExtensionType;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssociationType AssociationType;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdExtensionAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionsAssociationsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionsAssociationsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionsAssociationsRequest
            {
                AccountId = AccountId,

                EntityIds = EntityIds,

                AdExtensionType = AdExtensionType,

                AssociationType = AssociationType,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAdExtensionsAssociationsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdExtensionAssociationCollection[] AdExtensionAssociationCollection;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetAdExtensionsAssociationsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionsAssociationsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AdExtensionAssociationCollection = result.AdExtensionAssociationCollection;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteAdExtensionsAssociationsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdExtensionIdToEntityIdAssociation[] AdExtensionIdToEntityIdAssociations;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssociationType AssociationType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdExtensionsAssociationsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdExtensionsAssociationsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdExtensionsAssociationsRequest
            {
                AccountId = AccountId,

                AdExtensionIdToEntityIdAssociations = AdExtensionIdToEntityIdAssociations,

                AssociationType = AssociationType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteAdExtensionsAssociationsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteAdExtensionsAssociationsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdExtensionsAssociationsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAdExtensionIdsByAccountIdRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssociationType? AssociationType;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdExtensionsTypeFilter AdExtensionType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionIdsByAccountIdRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionIdsByAccountIdRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionIdsByAccountIdRequest
            {
                AccountId = AccountId,

                AssociationType = AssociationType,

                AdExtensionType = AdExtensionType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAdExtensionIdsByAccountIdResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long[] AdExtensionIds;

        public GetAdExtensionIdsByAccountIdResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdExtensionIdsByAccountIdResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AdExtensionIds = result.AdExtensionIds;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddMediaRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Media[] Media;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddMediaRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddMediaRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddMediaRequest
            {
                AccountId = AccountId,

                Media = Media
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddMediaResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long[] MediaIds;

        public AddMediaResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddMediaResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.MediaIds = result.MediaIds;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteMediaRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 0)]
        public long[] MediaIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteMediaRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteMediaRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteMediaRequest
            {
                AccountId = AccountId,

                MediaIds = MediaIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteMediaResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteMediaResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteMediaResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetMediaMetaDataByAccountIdRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.MediaEnabledEntityFilter MediaEnabledEntities;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Paging PageInfo;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.MediaAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetMediaMetaDataByAccountIdRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetMediaMetaDataByAccountIdRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetMediaMetaDataByAccountIdRequest
            {
                MediaEnabledEntities = MediaEnabledEntities,

                PageInfo = PageInfo,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetMediaMetaDataByAccountIdResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.MediaMetaData[] MediaMetaData;

        public GetMediaMetaDataByAccountIdResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetMediaMetaDataByAccountIdResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.MediaMetaData = result.MediaMetaData;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetMediaMetaDataByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] MediaIds;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.MediaAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetMediaMetaDataByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetMediaMetaDataByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetMediaMetaDataByIdsRequest
            {
                MediaIds = MediaIds,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetMediaMetaDataByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.MediaMetaData[] MediaMetaData;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetMediaMetaDataByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetMediaMetaDataByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.MediaMetaData = result.MediaMetaData;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetMediaAssociationsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] MediaIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.MediaEnabledEntityFilter MediaEnabledEntities;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetMediaAssociationsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetMediaAssociationsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetMediaAssociationsRequest
            {
                MediaIds = MediaIds,

                MediaEnabledEntities = MediaEnabledEntities
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetMediaAssociationsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        [MessageBodyMember(Order = 0)]
        public System.Collections.Generic.List<Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.MediaAssociation>[] MediaAssociations;

        public GetMediaAssociationsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetMediaAssociationsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;
            this.MediaAssociations = result.MediaAssociations;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAdGroupCriterionsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] AdGroupCriterionIds;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroupCriterionType CriterionType;

        [MessageBodyMember(Order = 1)]
        public long AdGroupId;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CriterionAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdGroupCriterionsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdGroupCriterionsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdGroupCriterionsByIdsRequest
            {
                AdGroupCriterionIds = AdGroupCriterionIds,

                CriterionType = CriterionType,

                AdGroupId = AdGroupId,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAdGroupCriterionsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroupCriterion[] AdGroupCriterions;

        public GetAdGroupCriterionsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAdGroupCriterionsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AdGroupCriterions = result.AdGroupCriterions;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddAdGroupCriterionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroupCriterion[] AdGroupCriterions;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroupCriterionType CriterionType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdGroupCriterionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdGroupCriterionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdGroupCriterionsRequest
            {
                AdGroupCriterions = AdGroupCriterions,

                CriterionType = CriterionType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddAdGroupCriterionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] AdGroupCriterionIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchErrorCollection[] NestedPartialErrors;

        public AddAdGroupCriterionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAdGroupCriterionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AdGroupCriterionIds = result.AdGroupCriterionIds;
            this.NestedPartialErrors = result.NestedPartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateAdGroupCriterionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroupCriterion[] AdGroupCriterions;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroupCriterionType CriterionType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdGroupCriterionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdGroupCriterionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdGroupCriterionsRequest
            {
                AdGroupCriterions = AdGroupCriterions,

                CriterionType = CriterionType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateAdGroupCriterionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchErrorCollection[] NestedPartialErrors;

        public UpdateAdGroupCriterionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAdGroupCriterionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.NestedPartialErrors = result.NestedPartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteAdGroupCriterionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AdGroupId;

        [MessageBodyMember(Order = 0)]
        public long[] AdGroupCriterionIds;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroupCriterionType CriterionType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdGroupCriterionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdGroupCriterionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdGroupCriterionsRequest
            {
                AdGroupId = AdGroupId,

                AdGroupCriterionIds = AdGroupCriterionIds,

                CriterionType = CriterionType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteAdGroupCriterionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteAdGroupCriterionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAdGroupCriterionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class ApplyProductPartitionActionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroupCriterionAction[] CriterionActions;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyProductPartitionActionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyProductPartitionActionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyProductPartitionActionsRequest
            {
                CriterionActions = CriterionActions
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class ApplyProductPartitionActionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] AdGroupCriterionIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public ApplyProductPartitionActionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyProductPartitionActionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AdGroupCriterionIds = result.AdGroupCriterionIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class ApplyHotelGroupActionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdGroupCriterionAction[] CriterionActions;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyHotelGroupActionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyHotelGroupActionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyHotelGroupActionsRequest
            {
                CriterionActions = CriterionActions
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class ApplyHotelGroupActionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] AdGroupCriterionIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public ApplyHotelGroupActionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyHotelGroupActionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AdGroupCriterionIds = result.AdGroupCriterionIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class ApplyAssetGroupListingGroupActionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssetGroupListingGroupAction[] ListingGroupActions;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyAssetGroupListingGroupActionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyAssetGroupListingGroupActionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyAssetGroupListingGroupActionsRequest
            {
                ListingGroupActions = ListingGroupActions
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class ApplyAssetGroupListingGroupActionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] AssetGroupListingGroupIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public ApplyAssetGroupListingGroupActionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyAssetGroupListingGroupActionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AssetGroupListingGroupIds = result.AssetGroupListingGroupIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAssetGroupListingGroupsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] AssetGroupListingGroupIds;

        [MessageBodyMember(Order = 0)]
        public long AssetGroupId;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssetGroupListingGroupAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupListingGroupsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupListingGroupsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupListingGroupsByIdsRequest
            {
                AssetGroupListingGroupIds = AssetGroupListingGroupIds,

                AssetGroupId = AssetGroupId,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAssetGroupListingGroupsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssetGroupListingGroup[] AssetGroupListingGroups;

        public GetAssetGroupListingGroupsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupListingGroupsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AssetGroupListingGroups = result.AssetGroupListingGroups;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetBMCStoresByCustomerIdRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BMCStoreAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBMCStoresByCustomerIdRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBMCStoresByCustomerIdRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBMCStoresByCustomerIdRequest
            {
                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetBMCStoresByCustomerIdResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BMCStore[] BMCStores;

        public GetBMCStoresByCustomerIdResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBMCStoresByCustomerIdResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.BMCStores = result.BMCStores;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddNegativeKeywordsToEntitiesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityNegativeKeyword[] EntityNegativeKeywords;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddNegativeKeywordsToEntitiesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddNegativeKeywordsToEntitiesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddNegativeKeywordsToEntitiesRequest
            {
                EntityNegativeKeywords = EntityNegativeKeywords
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddNegativeKeywordsToEntitiesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchErrorCollection[] NestedPartialErrors;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.IdCollection[] NegativeKeywordIds;

        public AddNegativeKeywordsToEntitiesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddNegativeKeywordsToEntitiesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.NestedPartialErrors = result.NestedPartialErrors;
            this.NegativeKeywordIds = result.NegativeKeywordIds;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetNegativeKeywordsByEntityIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public string EntityType;

        [MessageBodyMember(Order = 0)]
        public long[] EntityIds;

        [MessageBodyMember(Order = 0)]
        public long? ParentEntityId;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNegativeKeywordsByEntityIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNegativeKeywordsByEntityIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNegativeKeywordsByEntityIdsRequest
            {
                EntityType = EntityType,

                EntityIds = EntityIds,

                ParentEntityId = ParentEntityId
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetNegativeKeywordsByEntityIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityNegativeKeyword[] EntityNegativeKeywords;

        public GetNegativeKeywordsByEntityIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNegativeKeywordsByEntityIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;
            this.EntityNegativeKeywords = result.EntityNegativeKeywords;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteNegativeKeywordsFromEntitiesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityNegativeKeyword[] EntityNegativeKeywords;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteNegativeKeywordsFromEntitiesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteNegativeKeywordsFromEntitiesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteNegativeKeywordsFromEntitiesRequest
            {
                EntityNegativeKeywords = EntityNegativeKeywords
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteNegativeKeywordsFromEntitiesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchErrorCollection[] NestedPartialErrors;

        public DeleteNegativeKeywordsFromEntitiesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteNegativeKeywordsFromEntitiesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.NestedPartialErrors = result.NestedPartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetSharedEntitiesByAccountIdRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public string SharedEntityType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntitiesByAccountIdRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntitiesByAccountIdRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntitiesByAccountIdRequest
            {
                SharedEntityType = SharedEntityType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetSharedEntitiesByAccountIdResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedEntity[] SharedEntities;

        public GetSharedEntitiesByAccountIdResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntitiesByAccountIdResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.SharedEntities = result.SharedEntities;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetSharedEntitiesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public string SharedEntityType;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityScope? SharedEntityScope;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntitiesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntitiesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntitiesRequest
            {
                SharedEntityType = SharedEntityType,

                SharedEntityScope = SharedEntityScope
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetSharedEntitiesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedEntity[] SharedEntities;

        public GetSharedEntitiesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntitiesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.SharedEntities = result.SharedEntities;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddSharedEntityRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedEntity SharedEntity;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedListItem[] ListItems;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityScope? SharedEntityScope;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddSharedEntityRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddSharedEntityRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddSharedEntityRequest
            {
                SharedEntity = SharedEntity,

                ListItems = ListItems,

                SharedEntityScope = SharedEntityScope
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddSharedEntityResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long SharedEntityId;

        [MessageBodyMember(Order = 0)]
        public long[] ListItemIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddSharedEntityResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddSharedEntityResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.SharedEntityId = result.SharedEntityId;
            this.ListItemIds = result.ListItemIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetListItemsBySharedListRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedList SharedList;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityScope? SharedEntityScope;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetListItemsBySharedListRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetListItemsBySharedListRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetListItemsBySharedListRequest
            {
                SharedList = SharedList,

                SharedEntityScope = SharedEntityScope
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetListItemsBySharedListResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedListItem[] ListItems;

        public GetListItemsBySharedListResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetListItemsBySharedListResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ListItems = result.ListItems;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddListItemsToSharedListRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedListItem[] ListItems;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedList SharedList;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityScope? SharedEntityScope;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddListItemsToSharedListRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddListItemsToSharedListRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddListItemsToSharedListRequest
            {
                ListItems = ListItems,

                SharedList = SharedList,

                SharedEntityScope = SharedEntityScope
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddListItemsToSharedListResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long[] ListItemIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddListItemsToSharedListResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddListItemsToSharedListResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ListItemIds = result.ListItemIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateSharedEntitiesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedEntity[] SharedEntities;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityScope? SharedEntityScope;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateSharedEntitiesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateSharedEntitiesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateSharedEntitiesRequest
            {
                SharedEntities = SharedEntities,

                SharedEntityScope = SharedEntityScope
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateSharedEntitiesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateSharedEntitiesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateSharedEntitiesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteListItemsFromSharedListRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long[] ListItemIds;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedList SharedList;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityScope? SharedEntityScope;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteListItemsFromSharedListRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteListItemsFromSharedListRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteListItemsFromSharedListRequest
            {
                ListItemIds = ListItemIds,

                SharedList = SharedList,

                SharedEntityScope = SharedEntityScope
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteListItemsFromSharedListResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteListItemsFromSharedListResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteListItemsFromSharedListResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class SetSharedEntityAssociationsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedEntityAssociation[] Associations;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityScope? SharedEntityScope;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetSharedEntityAssociationsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetSharedEntityAssociationsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetSharedEntityAssociationsRequest
            {
                Associations = Associations,

                SharedEntityScope = SharedEntityScope
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class SetSharedEntityAssociationsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public SetSharedEntityAssociationsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetSharedEntityAssociationsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteSharedEntityAssociationsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedEntityAssociation[] Associations;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityScope? SharedEntityScope;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteSharedEntityAssociationsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteSharedEntityAssociationsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteSharedEntityAssociationsRequest
            {
                Associations = Associations,

                SharedEntityScope = SharedEntityScope
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteSharedEntityAssociationsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteSharedEntityAssociationsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteSharedEntityAssociationsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetSharedEntityAssociationsBySharedEntityIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public string EntityType;

        [MessageBodyMember(Order = 2)]
        public long[] SharedEntityIds;

        [MessageBodyMember(Order = 3)]
        public string SharedEntityType;

        [MessageBodyMember(Order = 4)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityScope? SharedEntityScope;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntityAssociationsBySharedEntityIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntityAssociationsBySharedEntityIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntityAssociationsBySharedEntityIdsRequest
            {
                EntityType = EntityType,

                SharedEntityIds = SharedEntityIds,

                SharedEntityType = SharedEntityType,

                SharedEntityScope = SharedEntityScope
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetSharedEntityAssociationsBySharedEntityIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedEntityAssociation[] Associations;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetSharedEntityAssociationsBySharedEntityIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntityAssociationsBySharedEntityIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Associations = result.Associations;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetSharedEntityAssociationsByEntityIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long[] EntityIds;

        [MessageBodyMember(Order = 2)]
        public string EntityType;

        [MessageBodyMember(Order = 3)]
        public string SharedEntityType;

        [MessageBodyMember(Order = 4)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityScope? SharedEntityScope;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntityAssociationsByEntityIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntityAssociationsByEntityIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntityAssociationsByEntityIdsRequest
            {
                EntityIds = EntityIds,

                EntityType = EntityType,

                SharedEntityType = SharedEntityType,

                SharedEntityScope = SharedEntityScope
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetSharedEntityAssociationsByEntityIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedEntityAssociation[] Associations;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetSharedEntityAssociationsByEntityIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSharedEntityAssociationsByEntityIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Associations = result.Associations;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteSharedEntitiesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SharedEntity[] SharedEntities;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityScope? SharedEntityScope;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteSharedEntitiesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteSharedEntitiesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteSharedEntitiesRequest
            {
                SharedEntities = SharedEntities,

                SharedEntityScope = SharedEntityScope
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteSharedEntitiesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteSharedEntitiesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteSharedEntitiesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetCampaignSizesByAccountIdRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long AccountId;

        [MessageBodyMember(Order = 2)]
        public long[] CampaignIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignSizesByAccountIdRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignSizesByAccountIdRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignSizesByAccountIdRequest
            {
                AccountId = AccountId,

                CampaignIds = CampaignIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetCampaignSizesByAccountIdResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignSize[] CampaignSizes;

        public GetCampaignSizesByAccountIdResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignSizesByAccountIdResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.CampaignSizes = result.CampaignSizes;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddCampaignCriterionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignCriterion[] CampaignCriterions;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignCriterionType CriterionType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddCampaignCriterionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddCampaignCriterionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddCampaignCriterionsRequest
            {
                CampaignCriterions = CampaignCriterions,

                CriterionType = CriterionType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddCampaignCriterionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] CampaignCriterionIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchErrorCollection[] NestedPartialErrors;

        public AddCampaignCriterionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddCampaignCriterionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.CampaignCriterionIds = result.CampaignCriterionIds;
            this.NestedPartialErrors = result.NestedPartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateCampaignCriterionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignCriterion[] CampaignCriterions;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignCriterionType CriterionType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateCampaignCriterionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateCampaignCriterionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateCampaignCriterionsRequest
            {
                CampaignCriterions = CampaignCriterions,

                CriterionType = CriterionType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateCampaignCriterionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchErrorCollection[] NestedPartialErrors;

        public UpdateCampaignCriterionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateCampaignCriterionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.NestedPartialErrors = result.NestedPartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteCampaignCriterionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] CampaignCriterionIds;

        [MessageBodyMember(Order = 0)]
        public long CampaignId;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignCriterionType CriterionType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteCampaignCriterionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteCampaignCriterionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteCampaignCriterionsRequest
            {
                CampaignCriterionIds = CampaignCriterionIds,

                CampaignId = CampaignId,

                CriterionType = CriterionType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteCampaignCriterionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteCampaignCriterionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteCampaignCriterionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetCampaignCriterionsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long CampaignId;

        [MessageBodyMember(Order = 0)]
        public long[] CampaignCriterionIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignCriterionType CriterionType;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CriterionAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignCriterionsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignCriterionsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignCriterionsByIdsRequest
            {
                CampaignId = CampaignId,

                CampaignCriterionIds = CampaignCriterionIds,

                CriterionType = CriterionType,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetCampaignCriterionsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignCriterion[] CampaignCriterions;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetCampaignCriterionsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignCriterionsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.CampaignCriterions = result.CampaignCriterions;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddBudgetsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Budget[] Budgets;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddBudgetsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddBudgetsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddBudgetsRequest
            {
                Budgets = Budgets
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddBudgetsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] BudgetIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddBudgetsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddBudgetsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.BudgetIds = result.BudgetIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateBudgetsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Budget[] Budgets;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateBudgetsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateBudgetsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateBudgetsRequest
            {
                Budgets = Budgets
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateBudgetsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateBudgetsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateBudgetsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteBudgetsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] BudgetIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteBudgetsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteBudgetsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteBudgetsRequest
            {
                BudgetIds = BudgetIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteBudgetsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteBudgetsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteBudgetsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetBudgetsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] BudgetIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBudgetsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBudgetsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBudgetsByIdsRequest
            {
                BudgetIds = BudgetIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetBudgetsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Budget[] Budgets;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetBudgetsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBudgetsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Budgets = result.Budgets;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetCampaignIdsByBudgetIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] BudgetIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignIdsByBudgetIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignIdsByBudgetIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignIdsByBudgetIdsRequest
            {
                BudgetIds = BudgetIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetCampaignIdsByBudgetIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.IdCollection[] CampaignIdCollection;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetCampaignIdsByBudgetIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignIdsByBudgetIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.CampaignIdCollection = result.CampaignIdCollection;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddBidStrategiesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BidStrategy[] BidStrategies;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddBidStrategiesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddBidStrategiesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddBidStrategiesRequest
            {
                BidStrategies = BidStrategies
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddBidStrategiesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] BidStrategyIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddBidStrategiesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddBidStrategiesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.BidStrategyIds = result.BidStrategyIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateBidStrategiesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BidStrategy[] BidStrategies;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateBidStrategiesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateBidStrategiesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateBidStrategiesRequest
            {
                BidStrategies = BidStrategies
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateBidStrategiesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateBidStrategiesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateBidStrategiesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteBidStrategiesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] BidStrategyIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteBidStrategiesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteBidStrategiesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteBidStrategiesRequest
            {
                BidStrategyIds = BidStrategyIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteBidStrategiesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteBidStrategiesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteBidStrategiesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetBidStrategiesByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] BidStrategyIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.PortfolioBidStrategyAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBidStrategiesByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBidStrategiesByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBidStrategiesByIdsRequest
            {
                BidStrategyIds = BidStrategyIds,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetBidStrategiesByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BidStrategy[] BidStrategies;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetBidStrategiesByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBidStrategiesByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.BidStrategies = result.BidStrategies;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetCampaignIdsByBidStrategyIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] BidStrategyIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignIdsByBidStrategyIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignIdsByBidStrategyIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignIdsByBidStrategyIdsRequest
            {
                BidStrategyIds = BidStrategyIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetCampaignIdsByBidStrategyIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.IdCollection[] CampaignIdCollection;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetCampaignIdsByBidStrategyIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetCampaignIdsByBidStrategyIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.CampaignIdCollection = result.CampaignIdCollection;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddAudienceGroupsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AudienceGroup[] AudienceGroups;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAudienceGroupsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAudienceGroupsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAudienceGroupsRequest
            {
                AudienceGroups = AudienceGroups
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddAudienceGroupsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] AudienceGroupIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddAudienceGroupsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAudienceGroupsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AudienceGroupIds = result.AudienceGroupIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateAudienceGroupsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AudienceGroup[] AudienceGroups;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAudienceGroupsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAudienceGroupsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAudienceGroupsRequest
            {
                AudienceGroups = AudienceGroups
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateAudienceGroupsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateAudienceGroupsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAudienceGroupsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteAudienceGroupsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] AudienceGroupIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAudienceGroupsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAudienceGroupsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAudienceGroupsRequest
            {
                AudienceGroupIds = AudienceGroupIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteAudienceGroupsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteAudienceGroupsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAudienceGroupsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAudienceGroupsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] AudienceGroupIds;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AudienceGroupAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudienceGroupsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudienceGroupsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudienceGroupsByIdsRequest
            {
                AudienceGroupIds = AudienceGroupIds,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAudienceGroupsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AudienceGroup[] AudienceGroups;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetAudienceGroupsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudienceGroupsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AudienceGroups = result.AudienceGroups;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddAssetGroupsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssetGroup[] AssetGroups;

        [MessageBodyMember(Order = 1)]
        public long CampaignId;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAssetGroupsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAssetGroupsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAssetGroupsRequest
            {
                AssetGroups = AssetGroups,

                CampaignId = CampaignId
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddAssetGroupsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] AssetGroupIds;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddAssetGroupsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAssetGroupsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AssetGroupIds = result.AssetGroupIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateAssetGroupsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long CampaignId;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssetGroup[] AssetGroups;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAssetGroupsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAssetGroupsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAssetGroupsRequest
            {
                CampaignId = CampaignId,

                AssetGroups = AssetGroups
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateAssetGroupsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateAssetGroupsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAssetGroupsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteAssetGroupsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long CampaignId;

        [MessageBodyMember(Order = 1)]
        public long[] AssetGroupIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAssetGroupsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAssetGroupsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAssetGroupsRequest
            {
                CampaignId = CampaignId,

                AssetGroupIds = AssetGroupIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteAssetGroupsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteAssetGroupsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAssetGroupsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAssetGroupsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long CampaignId;

        [MessageBodyMember(Order = 1)]
        public long[] AssetGroupIds;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssetGroupAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupsByIdsRequest
            {
                CampaignId = CampaignId,

                AssetGroupIds = AssetGroupIds,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAssetGroupsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssetGroup[] AssetGroups;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetAssetGroupsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AssetGroups = result.AssetGroups;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAssetGroupsByCampaignIdRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long CampaignId;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssetGroupAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupsByCampaignIdRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupsByCampaignIdRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupsByCampaignIdRequest
            {
                CampaignId = CampaignId,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAssetGroupsByCampaignIdResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssetGroup[] AssetGroups;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetAssetGroupsByCampaignIdResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupsByCampaignIdResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AssetGroups = result.AssetGroups;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAssetGroupsEditorialReasonsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityIdToParentIdAssociation[] AssetGroupIdToCampaignIdAssociations;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupsEditorialReasonsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupsEditorialReasonsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupsEditorialReasonsRequest
            {
                AccountId = AccountId,

                AssetGroupIdToCampaignIdAssociations = AssetGroupIdToCampaignIdAssociations
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAssetGroupsEditorialReasonsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssetGroupEditorialReasonCollection[] EditorialReasons;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetAssetGroupsEditorialReasonsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAssetGroupsEditorialReasonsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.EditorialReasons = result.EditorialReasons;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class SetAudienceGroupAssetGroupAssociationsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AudienceGroupAssetGroupAssociation[] AudienceGroupAssetGroupAssociations;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetAudienceGroupAssetGroupAssociationsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetAudienceGroupAssetGroupAssociationsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetAudienceGroupAssetGroupAssociationsRequest
            {
                AudienceGroupAssetGroupAssociations = AudienceGroupAssetGroupAssociations
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class SetAudienceGroupAssetGroupAssociationsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public SetAudienceGroupAssetGroupAssociationsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetAudienceGroupAssetGroupAssociationsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteAudienceGroupAssetGroupAssociationsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AudienceGroupAssetGroupAssociation[] AudienceGroupAssetGroupAssociations;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAudienceGroupAssetGroupAssociationsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAudienceGroupAssetGroupAssociationsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAudienceGroupAssetGroupAssociationsRequest
            {
                AudienceGroupAssetGroupAssociations = AudienceGroupAssetGroupAssociations
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteAudienceGroupAssetGroupAssociationsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteAudienceGroupAssetGroupAssociationsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAudienceGroupAssetGroupAssociationsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] AssetGroupIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsRequest
            {
                AssetGroupIds = AssetGroupIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AudienceGroupAssetGroupAssociation[] AudienceGroupAssetGroupAssociations;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudienceGroupAssetGroupAssociationsByAssetGroupIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AudienceGroupAssetGroupAssociations = result.AudienceGroupAssetGroupAssociations;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] AudienceGroupIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsRequest
            {
                AudienceGroupIds = AudienceGroupIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AudienceGroupAssetGroupAssociation[] AudienceGroupAssetGroupAssociations;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudienceGroupAssetGroupAssociationsByAudienceGroupIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AudienceGroupAssetGroupAssociations = result.AudienceGroupAssetGroupAssociations;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddAudiencesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Audience[] Audiences;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAudiencesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAudiencesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAudiencesRequest
            {
                Audiences = Audiences
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddAudiencesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 1)]
        public long?[] AudienceIds;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddAudiencesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddAudiencesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AudienceIds = result.AudienceIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateAudiencesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Audience[] Audiences;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAudiencesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAudiencesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAudiencesRequest
            {
                Audiences = Audiences
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateAudiencesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateAudiencesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAudiencesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteAudiencesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long[] AudienceIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAudiencesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAudiencesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAudiencesRequest
            {
                AudienceIds = AudienceIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteAudiencesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteAudiencesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteAudiencesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAudiencesByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long[] AudienceIds;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AudienceType Type;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AudienceAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudiencesByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudiencesByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudiencesByIdsRequest
            {
                AudienceIds = AudienceIds,

                Type = Type,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAudiencesByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Audience[] Audiences;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetAudiencesByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAudiencesByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Audiences = result.Audiences;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class ApplyCustomerListItemsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CustomerList CustomerListAudience;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyCustomerListItemsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyCustomerListItemsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyCustomerListItemsRequest
            {
                CustomerListAudience = CustomerListAudience
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class ApplyCustomerListItemsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public ApplyCustomerListItemsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyCustomerListItemsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class ApplyCustomerListUserDataRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CustomerListUserData CustomerListUserData;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyCustomerListUserDataRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyCustomerListUserDataRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyCustomerListUserDataRequest
            {
                CustomerListUserData = CustomerListUserData
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class ApplyCustomerListUserDataResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public ApplyCustomerListUserDataResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyCustomerListUserDataResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetUetTagsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long[] TagIds;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UetTagAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetUetTagsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetUetTagsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetUetTagsByIdsRequest
            {
                TagIds = TagIds,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetUetTagsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UetTag[] UetTags;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetUetTagsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetUetTagsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.UetTags = result.UetTags;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddUetTagsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UetTag[] UetTags;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddUetTagsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddUetTagsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddUetTagsRequest
            {
                UetTags = UetTags
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddUetTagsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UetTag[] UetTags;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddUetTagsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddUetTagsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.UetTags = result.UetTags;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateUetTagsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UetTag[] UetTags;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateUetTagsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateUetTagsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateUetTagsRequest
            {
                UetTags = UetTags
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateUetTagsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateUetTagsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateUetTagsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetConversionGoalsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long[] ConversionGoalIds;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ConversionGoalType ConversionGoalTypes;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ConversionGoalAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionGoalsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionGoalsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionGoalsByIdsRequest
            {
                ConversionGoalIds = ConversionGoalIds,

                ConversionGoalTypes = ConversionGoalTypes,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetConversionGoalsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ConversionGoal[] ConversionGoals;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetConversionGoalsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionGoalsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ConversionGoals = result.ConversionGoals;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetConversionGoalsByTagIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public long[] TagIds;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ConversionGoalType ConversionGoalTypes;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ConversionGoalAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionGoalsByTagIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionGoalsByTagIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionGoalsByTagIdsRequest
            {
                TagIds = TagIds,

                ConversionGoalTypes = ConversionGoalTypes,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetConversionGoalsByTagIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ConversionGoal[] ConversionGoals;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetConversionGoalsByTagIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionGoalsByTagIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ConversionGoals = result.ConversionGoals;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddConversionGoalsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ConversionGoal[] ConversionGoals;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddConversionGoalsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddConversionGoalsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddConversionGoalsRequest
            {
                ConversionGoals = ConversionGoals
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddConversionGoalsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 1)]
        public long?[] ConversionGoalIds;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddConversionGoalsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddConversionGoalsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ConversionGoalIds = result.ConversionGoalIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateConversionGoalsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ConversionGoal[] ConversionGoals;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateConversionGoalsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateConversionGoalsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateConversionGoalsRequest
            {
                ConversionGoals = ConversionGoals
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateConversionGoalsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateConversionGoalsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateConversionGoalsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class ApplyOfflineConversionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.OfflineConversion[] OfflineConversions;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyOfflineConversionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyOfflineConversionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyOfflineConversionsRequest
            {
                OfflineConversions = OfflineConversions
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class ApplyOfflineConversionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public ApplyOfflineConversionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyOfflineConversionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class ApplyOfflineConversionAdjustmentsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.OfflineConversionAdjustment[] OfflineConversionAdjustments;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyOfflineConversionAdjustmentsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyOfflineConversionAdjustmentsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyOfflineConversionAdjustmentsRequest
            {
                OfflineConversionAdjustments = OfflineConversionAdjustments
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class ApplyOfflineConversionAdjustmentsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public ApplyOfflineConversionAdjustmentsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyOfflineConversionAdjustmentsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class ApplyOnlineConversionAdjustmentsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.OnlineConversionAdjustment[] OnlineConversionAdjustments;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyOnlineConversionAdjustmentsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyOnlineConversionAdjustmentsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyOnlineConversionAdjustmentsRequest
            {
                OnlineConversionAdjustments = OnlineConversionAdjustments
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class ApplyOnlineConversionAdjustmentsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public ApplyOnlineConversionAdjustmentsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ApplyOnlineConversionAdjustmentsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetOfflineConversionReportsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public System.DateTime StartDateUtc;

        [MessageBodyMember(Order = 1)]
        public System.DateTime EndDateUtc;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetOfflineConversionReportsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetOfflineConversionReportsRequest request = Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetOfflineConversionReportsRequest.Create(StartDateUtc: StartDateUtc, EndDateUtc: EndDateUtc);
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetOfflineConversionReportsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DailySummary[] DailySummaries;

        public GetOfflineConversionReportsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetOfflineConversionReportsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.DailySummaries = result.DailySummaries;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddLabelsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Label[] Labels;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddLabelsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddLabelsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddLabelsRequest
            {
                Labels = Labels
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddLabelsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] LabelIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddLabelsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddLabelsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.LabelIds = result.LabelIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteLabelsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] LabelIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteLabelsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteLabelsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteLabelsRequest
            {
                LabelIds = LabelIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteLabelsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteLabelsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteLabelsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateLabelsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Label[] Labels;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateLabelsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateLabelsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateLabelsRequest
            {
                Labels = Labels
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateLabelsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateLabelsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateLabelsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetLabelsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Paging PageInfo;

        [MessageBodyMember(Order = 0)]
        public long[] LabelIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetLabelsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetLabelsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetLabelsByIdsRequest
            {
                PageInfo = PageInfo,

                LabelIds = LabelIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetLabelsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Label[] Labels;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetLabelsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetLabelsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Labels = result.Labels;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class SetLabelAssociationsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.LabelAssociation[] LabelAssociations;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityType EntityType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetLabelAssociationsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetLabelAssociationsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetLabelAssociationsRequest
            {
                LabelAssociations = LabelAssociations,

                EntityType = EntityType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class SetLabelAssociationsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public SetLabelAssociationsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SetLabelAssociationsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteLabelAssociationsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.LabelAssociation[] LabelAssociations;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityType EntityType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteLabelAssociationsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteLabelAssociationsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteLabelAssociationsRequest
            {
                LabelAssociations = LabelAssociations,

                EntityType = EntityType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteLabelAssociationsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteLabelAssociationsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteLabelAssociationsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetLabelAssociationsByEntityIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] EntityIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityType EntityType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetLabelAssociationsByEntityIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetLabelAssociationsByEntityIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetLabelAssociationsByEntityIdsRequest
            {
                EntityIds = EntityIds,

                EntityType = EntityType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetLabelAssociationsByEntityIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.LabelAssociation[] LabelAssociations;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetLabelAssociationsByEntityIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetLabelAssociationsByEntityIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.LabelAssociations = result.LabelAssociations;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetLabelAssociationsByLabelIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Paging PageInfo;

        [MessageBodyMember(Order = 0)]
        public long[] LabelIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.EntityType EntityType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetLabelAssociationsByLabelIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetLabelAssociationsByLabelIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetLabelAssociationsByLabelIdsRequest
            {
                PageInfo = PageInfo,

                LabelIds = LabelIds,

                EntityType = EntityType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetLabelAssociationsByLabelIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.LabelAssociation[] LabelAssociations;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetLabelAssociationsByLabelIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetLabelAssociationsByLabelIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.LabelAssociations = result.LabelAssociations;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddExperimentsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Experiment[] Experiments;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddExperimentsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddExperimentsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddExperimentsRequest
            {
                Experiments = Experiments
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddExperimentsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] ExperimentIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddExperimentsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddExperimentsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ExperimentIds = result.ExperimentIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteExperimentsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] ExperimentIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteExperimentsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteExperimentsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteExperimentsRequest
            {
                ExperimentIds = ExperimentIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteExperimentsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteExperimentsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteExperimentsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateExperimentsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Experiment[] Experiments;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateExperimentsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateExperimentsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateExperimentsRequest
            {
                Experiments = Experiments
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateExperimentsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateExperimentsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateExperimentsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetExperimentsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Paging PageInfo;

        [MessageBodyMember(Order = 0)]
        public long[] ExperimentIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetExperimentsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetExperimentsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetExperimentsByIdsRequest
            {
                PageInfo = PageInfo,

                ExperimentIds = ExperimentIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetExperimentsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Experiment[] Experiments;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetExperimentsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetExperimentsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Experiments = result.Experiments;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetProfileDataFileUrlRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ProfileType ProfileType;

        [MessageBodyMember(Order = 0)]
        public string LanguageLocale;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetProfileDataFileUrlRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetProfileDataFileUrlRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetProfileDataFileUrlRequest
            {
                ProfileType = ProfileType,

                LanguageLocale = LanguageLocale
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetProfileDataFileUrlResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public string FileUrl;

        [MessageBodyMember(Order = 0)]
        public System.DateTime LastModifiedTimeUtc;

        [MessageBodyMember(Order = 0)]
        public System.DateTime? FileUrlExpiryTimeUtc;

        public GetProfileDataFileUrlResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetProfileDataFileUrlResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.FileUrl = result.FileUrl;
            this.LastModifiedTimeUtc = result.LastModifiedTimeUtc;
            this.FileUrlExpiryTimeUtc = result.FileUrlExpiryTimeUtc;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class SearchCompaniesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public string CompanyNameFilter;

        [MessageBodyMember(Order = 0)]
        public string LanguageLocale;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SearchCompaniesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SearchCompaniesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SearchCompaniesRequest
            {
                CompanyNameFilter = CompanyNameFilter,

                LanguageLocale = LanguageLocale
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class SearchCompaniesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Company[] Companies;

        public SearchCompaniesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SearchCompaniesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Companies = result.Companies;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetFileImportUploadUrlRequest : CampaignManagementApiRequest, IMsfRequest
    {
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetFileImportUploadUrlRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetFileImportUploadUrlRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetFileImportUploadUrlRequest();
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetFileImportUploadUrlResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public string FileImportUploadUrl;

        public GetFileImportUploadUrlResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetFileImportUploadUrlResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.FileImportUploadUrl = result.FileImportUploadUrl;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddImportJobsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ImportJob[] ImportJobs;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddImportJobsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddImportJobsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddImportJobsRequest
            {
                ImportJobs = ImportJobs
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddImportJobsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long[] ImportJobIds;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddImportJobsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddImportJobsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ImportJobIds = result.ImportJobIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetImportResultsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public string ImportType;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Paging PageInfo;

        [MessageBodyMember(Order = 2)]
        public long[] ImportJobIds;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ImportAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetImportResultsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetImportResultsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetImportResultsRequest
            {
                ImportType = ImportType,

                PageInfo = PageInfo,

                ImportJobIds = ImportJobIds,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetImportResultsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ImportResult[] ImportResults;

        public GetImportResultsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetImportResultsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ImportResults = result.ImportResults;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetImportJobsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] ImportJobIds;

        [MessageBodyMember(Order = 1)]
        public string ImportType;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ImportAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetImportJobsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetImportJobsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetImportJobsByIdsRequest
            {
                ImportJobIds = ImportJobIds,

                ImportType = ImportType,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetImportJobsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ImportJob[] ImportJobs;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetImportJobsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetImportJobsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ImportJobs = result.ImportJobs;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteImportJobsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] ImportJobIds;

        [MessageBodyMember(Order = 1)]
        public string ImportType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteImportJobsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteImportJobsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteImportJobsRequest
            {
                ImportJobIds = ImportJobIds,

                ImportType = ImportType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteImportJobsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteImportJobsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteImportJobsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetImportEntityIdsMappingRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public string ImportType;

        [MessageBodyMember(Order = 1)]
        public long[] SourceEntityIds;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ImportEntityType ImportEntityType;

        [MessageBodyMember(Order = 3)]
        public long? SourceParentId;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetImportEntityIdsMappingRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetImportEntityIdsMappingRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetImportEntityIdsMappingRequest
            {
                ImportType = ImportType,

                SourceEntityIds = SourceEntityIds,

                ImportEntityType = ImportEntityType,

                SourceParentId = SourceParentId
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetImportEntityIdsMappingResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public System.Collections.Generic.KeyValuePair<long, long>[] EntityIdsMapping;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetImportEntityIdsMappingResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetImportEntityIdsMappingResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.EntityIdsMapping = result.EntityIdsMapping;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateImportJobsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ImportJob[] ImportJobs;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateImportJobsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateImportJobsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateImportJobsRequest
            {
                ImportJobs = ImportJobs
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateImportJobsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long[] ImportJobIds;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateImportJobsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateImportJobsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ImportJobIds = result.ImportJobIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddVideosRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Video[] Videos;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddVideosRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddVideosRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddVideosRequest
            {
                Videos = Videos
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddVideosResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long[] VideoIds;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddVideosResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddVideosResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.VideoIds = result.VideoIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteVideosRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] VideoIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteVideosRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteVideosRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteVideosRequest
            {
                VideoIds = VideoIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteVideosResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteVideosResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteVideosResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetVideosByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] VideoIds;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Paging PageInfo;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetVideosByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetVideosByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetVideosByIdsRequest
            {
                VideoIds = VideoIds,

                PageInfo = PageInfo
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetVideosByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Video[] Videos;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetVideosByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetVideosByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Videos = result.Videos;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateVideosRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.Video[] Videos;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateVideosRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateVideosRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateVideosRequest
            {
                Videos = Videos
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateVideosResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateVideosResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateVideosResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddCampaignConversionGoalsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignConversionGoal[] CampaignConversionGoal;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddCampaignConversionGoalsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddCampaignConversionGoalsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddCampaignConversionGoalsRequest
            {
                CampaignConversionGoal = CampaignConversionGoal
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddCampaignConversionGoalsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddCampaignConversionGoalsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddCampaignConversionGoalsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteCampaignConversionGoalsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignConversionGoal[] CampaignConversionGoal;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteCampaignConversionGoalsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteCampaignConversionGoalsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteCampaignConversionGoalsRequest
            {
                CampaignConversionGoal = CampaignConversionGoal
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteCampaignConversionGoalsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteCampaignConversionGoalsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteCampaignConversionGoalsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddDataExclusionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DataExclusion[] DataExclusions;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddDataExclusionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddDataExclusionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddDataExclusionsRequest
            {
                AccountId = AccountId,

                DataExclusions = DataExclusions
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddDataExclusionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] DataExclusionIds;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddDataExclusionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddDataExclusionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.DataExclusionIds = result.DataExclusionIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateDataExclusionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DataExclusion[] DataExclusions;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateDataExclusionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateDataExclusionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateDataExclusionsRequest
            {
                AccountId = AccountId,

                DataExclusions = DataExclusions
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateDataExclusionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateDataExclusionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateDataExclusionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteDataExclusionsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public long[] DataExclusionIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteDataExclusionsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteDataExclusionsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteDataExclusionsRequest
            {
                AccountId = AccountId,

                DataExclusionIds = DataExclusionIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteDataExclusionsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteDataExclusionsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteDataExclusionsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetDataExclusionsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public long[] DataExclusionIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetDataExclusionsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetDataExclusionsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetDataExclusionsByIdsRequest
            {
                AccountId = AccountId,

                DataExclusionIds = DataExclusionIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetDataExclusionsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DataExclusion[] DataExclusions;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetDataExclusionsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetDataExclusionsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.DataExclusions = result.DataExclusions;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetDataExclusionsByAccountIdRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetDataExclusionsByAccountIdRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetDataExclusionsByAccountIdRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetDataExclusionsByAccountIdRequest
            {
                AccountId = AccountId
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetDataExclusionsByAccountIdResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DataExclusion[] DataExclusions;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetDataExclusionsByAccountIdResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetDataExclusionsByAccountIdResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.DataExclusions = result.DataExclusions;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddSeasonalityAdjustmentsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SeasonalityAdjustment[] SeasonalityAdjustments;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddSeasonalityAdjustmentsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddSeasonalityAdjustmentsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddSeasonalityAdjustmentsRequest
            {
                AccountId = AccountId,

                SeasonalityAdjustments = SeasonalityAdjustments
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddSeasonalityAdjustmentsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] SeasonalityAdjustmentIds;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddSeasonalityAdjustmentsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddSeasonalityAdjustmentsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.SeasonalityAdjustmentIds = result.SeasonalityAdjustmentIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateSeasonalityAdjustmentsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SeasonalityAdjustment[] SeasonalityAdjustments;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateSeasonalityAdjustmentsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateSeasonalityAdjustmentsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateSeasonalityAdjustmentsRequest
            {
                AccountId = AccountId,

                SeasonalityAdjustments = SeasonalityAdjustments
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateSeasonalityAdjustmentsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateSeasonalityAdjustmentsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateSeasonalityAdjustmentsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteSeasonalityAdjustmentsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public long[] SeasonalityAdjustmentIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteSeasonalityAdjustmentsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteSeasonalityAdjustmentsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteSeasonalityAdjustmentsRequest
            {
                AccountId = AccountId,

                SeasonalityAdjustmentIds = SeasonalityAdjustmentIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteSeasonalityAdjustmentsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteSeasonalityAdjustmentsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteSeasonalityAdjustmentsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetSeasonalityAdjustmentsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public long[] SeasonalityAdjustmentIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSeasonalityAdjustmentsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSeasonalityAdjustmentsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSeasonalityAdjustmentsByIdsRequest
            {
                AccountId = AccountId,

                SeasonalityAdjustmentIds = SeasonalityAdjustmentIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetSeasonalityAdjustmentsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SeasonalityAdjustment[] SeasonalityAdjustments;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetSeasonalityAdjustmentsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSeasonalityAdjustmentsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.SeasonalityAdjustments = result.SeasonalityAdjustments;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetSeasonalityAdjustmentsByAccountIdRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSeasonalityAdjustmentsByAccountIdRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSeasonalityAdjustmentsByAccountIdRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSeasonalityAdjustmentsByAccountIdRequest
            {
                AccountId = AccountId
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetSeasonalityAdjustmentsByAccountIdResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SeasonalityAdjustment[] SeasonalityAdjustments;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetSeasonalityAdjustmentsByAccountIdResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSeasonalityAdjustmentsByAccountIdResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.SeasonalityAdjustments = result.SeasonalityAdjustments;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class CreateAssetGroupRecommendationRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public string[] FinalUrls;

        [MessageBodyMember(Order = 1)]
        public string Prompt;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationTextTone? TextTone;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateAssetGroupRecommendationRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateAssetGroupRecommendationRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateAssetGroupRecommendationRequest
            {
                FinalUrls = FinalUrls,

                Prompt = Prompt,

                TextTone = TextTone,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class CreateAssetGroupRecommendationResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssetGroup AssetGroup;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationImageSuggestion[] ImageSuggestions;

        [MessageBodyMember(Order = 2)]
        [DataMember(EmitDefaultValue = false)]
        public string PromptBrandWarning;

        public CreateAssetGroupRecommendationResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateAssetGroupRecommendationResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.AssetGroup = result.AssetGroup;
            this.ImageSuggestions = result.ImageSuggestions;
            this.PromptBrandWarning = result.PromptBrandWarning;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class CreateResponsiveAdRecommendationRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdSubType? AdSubType;

        [MessageBodyMember(Order = 1)]
        public string[] FinalUrls;

        [MessageBodyMember(Order = 2)]
        public string Prompt;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationTextTone? TextTone;

        [MessageBodyMember(Order = 4)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationVideoType? VideoType;

        [MessageBodyMember(Order = 5)]
        public long? BrandKitId;

        [MessageBodyMember(Order = 6)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateResponsiveAdRecommendationRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateResponsiveAdRecommendationRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateResponsiveAdRecommendationRequest
            {
                AdSubType = AdSubType,

                FinalUrls = FinalUrls,

                Prompt = Prompt,

                TextTone = TextTone,

                VideoType = VideoType,

                BrandKitId = BrandKitId,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class CreateResponsiveAdRecommendationResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ResponsiveAd ResponsiveAd;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationImageSuggestion[] ImageSuggestions;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationVideoSuggestion[] VideoSuggestions;

        [MessageBodyMember(Order = 3)]
        [DataMember(EmitDefaultValue = false)]
        public string PromptBrandWarning;

        [MessageBodyMember(Order = 4)]
        [DataMember(EmitDefaultValue = false)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationJobInfo JobInfo;

        public CreateResponsiveAdRecommendationResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateResponsiveAdRecommendationResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ResponsiveAd = result.ResponsiveAd;
            this.ImageSuggestions = result.ImageSuggestions;
            this.VideoSuggestions = result.VideoSuggestions;
            this.PromptBrandWarning = result.PromptBrandWarning;
            this.JobInfo = result.JobInfo;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class CreateResponsiveSearchAdRecommendationRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public string[] FinalUrls;

        [MessageBodyMember(Order = 1)]
        public string Prompt;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationTextTone? TextTone;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateResponsiveSearchAdRecommendationRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateResponsiveSearchAdRecommendationRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateResponsiveSearchAdRecommendationRequest
            {
                FinalUrls = FinalUrls,

                Prompt = Prompt,

                TextTone = TextTone
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class CreateResponsiveSearchAdRecommendationResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ResponsiveSearchAd ResponsiveSearchAd;

        public CreateResponsiveSearchAdRecommendationResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateResponsiveSearchAdRecommendationResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ResponsiveSearchAd = result.ResponsiveSearchAd;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class RefineAssetGroupRecommendationRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AssetGroup AssetGroup;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationTextRefineOperation[] TextRefineOperations;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationImageSuggestion[] ImageSuggestions;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationImageRefineOperation[] ImageRefineOperations;

        [MessageBodyMember(Order = 4)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RefineAssetGroupRecommendationRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RefineAssetGroupRecommendationRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RefineAssetGroupRecommendationRequest
            {
                AssetGroup = AssetGroup,

                TextRefineOperations = TextRefineOperations,

                ImageSuggestions = ImageSuggestions,

                ImageRefineOperations = ImageRefineOperations,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class RefineAssetGroupRecommendationResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationTextRefineResult[] TextRefineResults;

        [MessageBodyMember(Order = 1)]
        [DataMember(EmitDefaultValue = false)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationMediaRefineResult[] MediaRefineResults;

        public RefineAssetGroupRecommendationResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RefineAssetGroupRecommendationResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.TextRefineResults = result.TextRefineResults;
            this.MediaRefineResults = result.MediaRefineResults;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class RefineResponsiveAdRecommendationRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ResponsiveAd ResponsiveAd;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationTextRefineOperation[] TextRefineOperations;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationImageRefineOperation[] ImageRefineOperations;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationImageSuggestion[] ImageSuggestions;

        [MessageBodyMember(Order = 4)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationAdditionalField? ReturnAdditionalFields;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RefineResponsiveAdRecommendationRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RefineResponsiveAdRecommendationRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RefineResponsiveAdRecommendationRequest
            {
                ResponsiveAd = ResponsiveAd,

                TextRefineOperations = TextRefineOperations,

                ImageRefineOperations = ImageRefineOperations,

                ImageSuggestions = ImageSuggestions,

                ReturnAdditionalFields = ReturnAdditionalFields
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class RefineResponsiveAdRecommendationResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationTextRefineResult[] TextRefineResults;

        [MessageBodyMember(Order = 1)]
        [DataMember(EmitDefaultValue = false)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationMediaRefineResult[] MediaRefineResults;

        public RefineResponsiveAdRecommendationResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RefineResponsiveAdRecommendationResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.TextRefineResults = result.TextRefineResults;
            this.MediaRefineResults = result.MediaRefineResults;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class RefineResponsiveSearchAdRecommendationRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ResponsiveSearchAd ResponsiveSearchAd;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationTextRefineOperation[] TextRefineOperations;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RefineResponsiveSearchAdRecommendationRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RefineResponsiveSearchAdRecommendationRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RefineResponsiveSearchAdRecommendationRequest
            {
                ResponsiveSearchAd = ResponsiveSearchAd,

                TextRefineOperations = TextRefineOperations
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class RefineResponsiveSearchAdRecommendationResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationTextRefineResult[] TextRefineResults;

        public RefineResponsiveSearchAdRecommendationResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.RefineResponsiveSearchAdRecommendationResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.TextRefineResults = result.TextRefineResults;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetResponsiveAdRecommendationJobRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long JobId;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetResponsiveAdRecommendationJobRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetResponsiveAdRecommendationJobRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetResponsiveAdRecommendationJobRequest
            {
                JobId = JobId
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetResponsiveAdRecommendationJobResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ResponsiveAd ResponsiveAd;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationImageSuggestion[] ImageSuggestions;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationVideoSuggestion[] VideoSuggestions;

        [MessageBodyMember(Order = 3)]
        [DataMember(EmitDefaultValue = false)]
        public string PromptBrandWarning;

        [MessageBodyMember(Order = 4)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdRecommendationJobInfo JobInfo;

        public GetResponsiveAdRecommendationJobResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetResponsiveAdRecommendationJobResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ResponsiveAd = result.ResponsiveAd;
            this.ImageSuggestions = result.ImageSuggestions;
            this.VideoSuggestions = result.VideoSuggestions;
            this.PromptBrandWarning = result.PromptBrandWarning;
            this.JobInfo = result.JobInfo;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateConversionValueRulesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ConversionValueRule[] ConversionValueRules;

        [MessageBodyMember(Order = 1)]
        public int? Lcid;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateConversionValueRulesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateConversionValueRulesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateConversionValueRulesRequest
            {
                ConversionValueRules = ConversionValueRules,

                Lcid = Lcid
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateConversionValueRulesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateConversionValueRulesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateConversionValueRulesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateConversionValueRulesStatusRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] RuleIds;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ConversionValueRuleStatus Status;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateConversionValueRulesStatusRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateConversionValueRulesStatusRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateConversionValueRulesStatusRequest
            {
                RuleIds = RuleIds,

                Status = Status
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateConversionValueRulesStatusResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateConversionValueRulesStatusResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateConversionValueRulesStatusResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddConversionValueRulesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ConversionValueRule[] ConversionValueRules;

        [MessageBodyMember(Order = 1)]
        public int? Lcid;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddConversionValueRulesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddConversionValueRulesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddConversionValueRulesRequest
            {
                ConversionValueRules = ConversionValueRules,

                Lcid = Lcid
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddConversionValueRulesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long[] ConversionValueRuleIds;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddConversionValueRulesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddConversionValueRulesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ConversionValueRuleIds = result.ConversionValueRuleIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetConversionValueRulesByAccountIdRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public int? Lcid;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionValueRulesByAccountIdRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionValueRulesByAccountIdRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionValueRulesByAccountIdRequest
            {
                AccountId = AccountId,

                Lcid = Lcid
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetConversionValueRulesByAccountIdResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ConversionValueRule[] ConversionValueRules;

        public GetConversionValueRulesByAccountIdResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionValueRulesByAccountIdResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ConversionValueRules = result.ConversionValueRules;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetConversionValueRulesByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long[] RuleIds;

        [MessageBodyMember(Order = 1)]
        public int? Lcid;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionValueRulesByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionValueRulesByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionValueRulesByIdsRequest
            {
                RuleIds = RuleIds,

                Lcid = Lcid
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetConversionValueRulesByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ConversionValueRule[] ConversionValueRules;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetConversionValueRulesByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetConversionValueRulesByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.ConversionValueRules = result.ConversionValueRules;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddBrandKitsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BrandKit[] BrandKits;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddBrandKitsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddBrandKitsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddBrandKitsRequest
            {
                AccountId = AccountId,

                BrandKits = BrandKits
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddBrandKitsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] BrandKitIds;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddBrandKitsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddBrandKitsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.BrandKitIds = result.BrandKitIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateBrandKitsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BrandKit[] BrandKits;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateBrandKitsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateBrandKitsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateBrandKitsRequest
            {
                AccountId = AccountId,

                BrandKits = BrandKits
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateBrandKitsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateBrandKitsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateBrandKitsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class DeleteBrandKitsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public long[] BrandKitIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteBrandKitsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteBrandKitsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteBrandKitsRequest
            {
                AccountId = AccountId,

                BrandKitIds = BrandKitIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class DeleteBrandKitsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public DeleteBrandKitsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DeleteBrandKitsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class CreateBrandKitRecommendationRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public string FinalUrl;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateBrandKitRecommendationRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateBrandKitRecommendationRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateBrandKitRecommendationRequest
            {
                AccountId = AccountId,

                FinalUrl = FinalUrl
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class CreateBrandKitRecommendationResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BrandKit BrandKit;

        public CreateBrandKitRecommendationResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CreateBrandKitRecommendationResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.BrandKit = result.BrandKit;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class AddNewCustomerAcquisitionGoalsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.NewCustomerAcquisitionGoal[] NewCustomerAcquisitionGoals;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddNewCustomerAcquisitionGoalsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddNewCustomerAcquisitionGoalsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddNewCustomerAcquisitionGoalsRequest
            {
                NewCustomerAcquisitionGoals = NewCustomerAcquisitionGoals
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class AddNewCustomerAcquisitionGoalsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public long?[] NewCustomerAcquisitionGoalIds;

        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public AddNewCustomerAcquisitionGoalsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AddNewCustomerAcquisitionGoalsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.NewCustomerAcquisitionGoalIds = result.NewCustomerAcquisitionGoalIds;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateNewCustomerAcquisitionGoalsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.NewCustomerAcquisitionGoal[] NewCustomerAcquisitionGoals;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateNewCustomerAcquisitionGoalsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateNewCustomerAcquisitionGoalsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateNewCustomerAcquisitionGoalsRequest
            {
                NewCustomerAcquisitionGoals = NewCustomerAcquisitionGoals
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateNewCustomerAcquisitionGoalsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public UpdateNewCustomerAcquisitionGoalsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateNewCustomerAcquisitionGoalsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetNewCustomerAcquisitionGoalsByAccountIdRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNewCustomerAcquisitionGoalsByAccountIdRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNewCustomerAcquisitionGoalsByAccountIdRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNewCustomerAcquisitionGoalsByAccountIdRequest
            {
                AccountId = AccountId
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetNewCustomerAcquisitionGoalsByAccountIdResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.NewCustomerAcquisitionGoal[] NewCustomerAcquisitionGoals;

        public GetNewCustomerAcquisitionGoalsByAccountIdResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetNewCustomerAcquisitionGoalsByAccountIdResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.NewCustomerAcquisitionGoals = result.NewCustomerAcquisitionGoals;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetBrandKitsByAccountIdRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBrandKitsByAccountIdRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBrandKitsByAccountIdRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBrandKitsByAccountIdRequest
            {
                AccountId = AccountId
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetBrandKitsByAccountIdResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BrandKit[] BrandKits;

        public GetBrandKitsByAccountIdResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBrandKitsByAccountIdResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.BrandKits = result.BrandKits;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetBrandKitsByIdsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public long AccountId;

        [MessageBodyMember(Order = 1)]
        public long[] BrandKitIds;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBrandKitsByIdsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBrandKitsByIdsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBrandKitsByIdsRequest
            {
                AccountId = AccountId,

                BrandKitIds = BrandKitIds
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetBrandKitsByIdsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.BrandKit[] BrandKits;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetBrandKitsByIdsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetBrandKitsByIdsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.BrandKits = result.BrandKits;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetClipchampTemplatesRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public bool? Mock;

        [MessageBodyMember(Order = 1)]
        public int? MaxAdsCount;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.VideoTemplateFilter TemplateFilter;

        [MessageBodyMember(Order = 3)]
        public string Locale;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetClipchampTemplatesRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetClipchampTemplatesRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetClipchampTemplatesRequest
            {
                Mock = Mock,

                MaxAdsCount = MaxAdsCount,

                TemplateFilter = TemplateFilter,

                Locale = Locale
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetClipchampTemplatesResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public System.Collections.Generic.IList<Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.ClipchampTemplateInfo> Templates;

        public GetClipchampTemplatesResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetClipchampTemplatesResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Templates = result.Templates;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetSupportedClipchampAudioRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AudioFilter AudioFilter;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSupportedClipchampAudioRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSupportedClipchampAudioRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSupportedClipchampAudioRequest
            {
                AudioFilter = AudioFilter
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetSupportedClipchampAudioResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SupportedClipchampAudioData Data;

        public GetSupportedClipchampAudioResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSupportedClipchampAudioResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Data = result.Data;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetSupportedFontsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AdSubType AdSubType;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSupportedFontsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSupportedFontsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSupportedFontsRequest
            {
                AdSubType = AdSubType
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetSupportedFontsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.SupportedFontsData Data;

        public GetSupportedFontsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetSupportedFontsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Data = result.Data;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetHealthCheckRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.HealthCheckEntity[] HealthCheckEntities;

        [MessageBodyMember(Order = 1)]
        public string[] HealthCheckTypes;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetHealthCheckRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetHealthCheckRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetHealthCheckRequest
            {
                HealthCheckEntities = HealthCheckEntities,

                HealthCheckTypes = HealthCheckTypes
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetHealthCheckResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.HealthCheckMetadata[] HealthCheckResultsMetadata;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.HealthCheckData[] HealthCheckResults;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.HealthCheckError[] HealthCheckErrors;

        public GetHealthCheckResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetHealthCheckResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.HealthCheckResultsMetadata = result.HealthCheckResultsMetadata;
            this.HealthCheckResults = result.HealthCheckResults;
            this.HealthCheckErrors = result.HealthCheckErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetDiagnosticsRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.HealthCheckEntity[] HealthCheckEntities;

        [MessageBodyMember(Order = 1)]
        public string[] HealthCheckTypes;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetDiagnosticsRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetDiagnosticsRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetDiagnosticsRequest
            {
                HealthCheckEntities = HealthCheckEntities,

                HealthCheckTypes = HealthCheckTypes
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetDiagnosticsResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DiagnosticTileData DiagnosticTileData;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DiagnosticCategoryData[] DiagnosticCategoryData;

        [MessageBodyMember(Order = 2)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.DiagnosticCardData[] DiagnosticCardData;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.HealthCheckError[] DiagnosticErrors;

        public GetDiagnosticsResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetDiagnosticsResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.DiagnosticTileData = result.DiagnosticTileData;
            this.DiagnosticCategoryData = result.DiagnosticCategoryData;
            this.DiagnosticCardData = result.DiagnosticCardData;
            this.DiagnosticErrors = result.DiagnosticErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class GetAnnotationOptOutRequest : CampaignManagementApiRequest, IMsfRequest
    {
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAnnotationOptOutRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAnnotationOptOutRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAnnotationOptOutRequest();
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class GetAnnotationOptOutResponse : ApiResponse
    {
        [MessageBodyMember(Order = 0)]
        public string Justification;

        [MessageBodyMember(Order = 1)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AnnotationOptOut[] AnnotationOptOuts;

        [MessageBodyMember(Order = 2)]
        public bool IsAccountOptOut;

        [MessageBodyMember(Order = 3)]
        public bool IsCustomerOptOut;

        [MessageBodyMember(Order = 4)]
        public bool IsCustomerOptOutOfEverything;

        [MessageBodyMember(Order = 5)]
        public Microsoft.AdCenter.Shared.Api.V13.DataContracts.BatchError[] PartialErrors;

        public GetAnnotationOptOutResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.GetAnnotationOptOutResponse result, string trackingId)
       : base(result, trackingId)
        {
            this.Justification = result.Justification;
            this.AnnotationOptOuts = result.AnnotationOptOuts;
            this.IsAccountOptOut = result.IsAccountOptOut;
            this.IsCustomerOptOut = result.IsCustomerOptOut;
            this.IsCustomerOptOutOfEverything = result.IsCustomerOptOutOfEverything;
            this.PartialErrors = result.PartialErrors;

            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class UpdateAnnotationOptOutRequest : CampaignManagementApiRequest, IMsfRequest
    {
        [MessageBodyMember(Order = 1)]
        public string Justification;

        [MessageBodyMember(Order = 3)]
        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.AnnotationOptOut[] AnnotationOptOuts;

        public Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAnnotationOptOutRequest GetAppRequest()
        {
            Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAnnotationOptOutRequest request = new Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAnnotationOptOutRequest
            {
                Justification = Justification,

                AnnotationOptOuts = AnnotationOptOuts
            };
            base.PopulateAppRequest(request);
            return request;
        }

        object[] IMsfRequest.GetAppRequestParams()
        {
            return new object[] { this.GetAppRequest() };
        }

    }

    [MessageContract]
    public class UpdateAnnotationOptOutResponse : ApiResponse
    {
        public UpdateAnnotationOptOutResponse(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.UpdateAnnotationOptOutResponse result, string trackingId)
       : base(result, trackingId)
        {
            base.TrackingId = trackingId;
        }
    }

    [MessageContract]
    public class CampaignManagementApiRequest : ApiRequest
    {
        [MessageBodyMember(Order = 0)]
        [HideMeFromWsdl(WsdlConfiguration.Feature.TestConfiguration)]
        public System.Collections.Generic.Dictionary<string, string> OverrideConfigValuesFromTest;

        public void PopulateAppRequest(Microsoft.AdCenter.Advertiser.CampaignManagement.Api.DataContracts.V13.CampaignManagementApiRequest request)
        {
            request.OverrideConfigValuesFromTest = this.OverrideConfigValuesFromTest;
            base.PopulateAppRequest(request);
        }
    }

    [MessageContract]
    public class ApiRequest : UserNameRequestHeader
    {
        public void PopulateAppRequest(Microsoft.AdCenter.Shared.Api.V13.DataContracts.Request.ApiRequest request)
        {
        }
    }

    [MessageContract]
    public class ApiResponse : ResponseHeader
    {
        public ApiResponse(Microsoft.AdCenter.Shared.Api.V13.DataContracts.Response.ApiResponse result, string trackingId)

        {
        }
    }
}
