﻿using FastDataPipeline.ClickhouseSub;
using FastDataPipeline.Contract.FastBIConversion;
using FastDataPipeline.CosmosPub;
using FastDataPipeline.DB;
using FastDataPipeline.DBSub;
using FastDataPipeline.DWC;
using FastDataPipeline.EventQueue;

namespace FastDataPipeline.JobFactory
{
    partial class JobFactory
    {
        // Upstream: Non-monetization Ads Data <<EMAIL>>
        // Downstream: BingAds BI Datamart DRI <<EMAIL>>
        private async Task CreateFastBIConversionsV2Pipeline(OperationCounter pubCounter, OperationCounter subCounter, CancellationToken cancellationToken)
        {
            var fastBIConversionsPubEventQueue = new PubEventQueueV1("FastBIConvPubEvent", 6 * 12 /* cache 6 hour data */, storageProvider.GetEventTable(), storageProvider.GetEventContainer());
            eventQueues.Add(fastBIConversionsPubEventQueue);

            // Container
            containers.Add(CreateFastBIConversionsV2Publisher(fastBIConversionsPubEventQueue, pubCounter));
            containers.Add(CreateFastBIConversionsAccount2Subscriber(fastBIConversionsPubEventQueue, subCounter));
            containers.Add(CreateFastBIConversionsAdGroup2Subscriber(fastBIConversionsPubEventQueue, subCounter));

            containers.Add(await CreateFastBIConversionsClickhouseAccountSubscriber(fastBIConversionsPubEventQueue, pubCounter, cancellationToken));
            containers.Add(await CreateFastBIConversionsClickhouseAdGroupSubscriber(fastBIConversionsPubEventQueue, pubCounter, cancellationToken));
        }

        private JobContainer CreateFastBIConversionsV2Publisher(PubEventQueueV1 eventQueue, OperationCounter operationCounter)
        {
            var container = new SimpleJobContainer("FastBIConvV2CosmosPubContainer");
            int bufferSize = -1; //-1 means no boundedCapacity, if the buffer size is too small, we pay big spin cost when add into BlockingCollection. when BlockingCollection is empty, we pay big spin cost again when dequeue, need to move to a no spin framework.
            
            // slit count and reader count for different size of articles
            var smallOptions = new CosmosPubOperationOptions { SplitCount = 1, ReaderCount = 16, BufferCapacity = bufferSize, ReturnNewPosition = false, LineReaderType = LineReaderType.Tsv, BlobBlockSize = 512 * 1024, CompressionBlockSize = 16 * 1024, BlobTimeoutInSeconds = 10 };
            
            var operations = new List<CosmosPubOperation>
            {
                // AdGroup
                new CosmosPubMultipleOutputOperation<AdUsage>(container, Article.AdUsage_Conv, cosmosArticleName: "AdUsage", dsvTableName: "AdUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAdUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressAdUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<AdFeedUsage>(container, Article.AdFeedUsage_Conv, cosmosArticleName: "AdFeedUsage", dsvTableName: "AdFeedUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressFeedItemUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, t.FeedItemId), 2)
                    .AttachOutput(Article.Delta_InProgressFeedItemUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, t.FeedItemId), 2),

                new CosmosPubMultipleOutputOperation<GenderAgeUsage>(container, Article.GenderAgeUsage_Conv, cosmosArticleName: "GenderAgeUsage", dsvTableName: "GenderAgeUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressGenderAgeUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressGenderAgeUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<HourlyAdAdExtensionClickTypeUsage>(container, Article.HourlyAdAdExtensionClickTypeUsage_Conv, cosmosArticleName: "HourlyAdAdExtensionClickTypeUsage", dsvTableName: "HourlyAdAdExtensionClickTypeUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAdAdExtensionClickTypeUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressAdAdExtensionClickTypeUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<HourlyElementAdUsage>(container, Article.HourlyElementAdUsage_Conv, cosmosArticleName: "HourlyElementAdUsage", dsvTableName: "HourlyElementAdUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressElementAdUsageByOrder_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressElementAdUsageByOrder_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<HourlyElementOrderItemUsage>(container, Article.HourlyElementOrderItemUsage_Conv, cosmosArticleName: "HourlyElementOrderItemUsage", dsvTableName: "HourlyElementOrderItemUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressElementOrderItemUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressElementOrderItemUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<HourlyOrderItemAdExtensionClickTypeUsage>(container, Article.HourlyOrderItemAdExtensionClickTypeUsage_Conv, cosmosArticleName: "HourlyOrderItemAdExtensionClickTypeUsage", dsvTableName: "HourlyOrderItemAdExtensionClickTypeUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressOrderItemAdExtensionClickTypeUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressOrderItemAdExtensionClickTypeUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<LocationHourUsage>(container, Article.LocationHourUsage_Conv, cosmosArticleName: "LocationHourUsage", dsvTableName: "LocationHourUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressLocationHourUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressLocationHourUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),
 
                // SubOrderItemUsage_NoIS is a sub fact for OrderItemUsage with limited dimension columns, to improve performance for Campaign UI calls
                new CosmosPubMultipleOutputOperation<OrderItemUsage>(container, Article.OrderItemUsage_Conv, cosmosArticleName: "OrderItemUsage", dsvTableName: "OrderItemUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressOrderItemUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressSubOrderItemUsage_NoIS_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressOrderItemUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressSubOrderItemUsage_NoIS_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<HourlyGoalUsage>(container, Article.HourlyGoalUsage_Conv, cosmosArticleName: "HourlyGoalUsage", dsvTableName: "HourlyGoalUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressGoalUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, t.OrderId), 2)
                    .AttachOutput(Article.Delta_InProgressGoalUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, t.OrderId), 2),

                new CosmosPubMultipleOutputOperation<OrderTargetUsage>(container, Article.OrderTargetUsage_Conv, cosmosArticleName: "OrderTargetUsage", dsvTableName: "OrderTargetUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressOrderTargetUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId, t.OrderId), 2)
                    .AttachOutput(Article.Delta_InProgressOrderTargetUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId, t.OrderId), 2),

                new CosmosPubMultipleOutputOperation<OrderUsage>(container, Article.OrderUsage_Conv, cosmosArticleName: "OrderUsage", dsvTableName: "OrderUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressOrderUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressOrderUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<ProfessionalDemographicsUsage>(container, Article.ProfessionalDemographicsUsage_Conv, cosmosArticleName: "ProfessionalDemographicsUsage", dsvTableName: "ProfessionalDemographicsUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressProfessionalDemographicsUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressProfessionalDemographicsUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<PublisherPlacementUsage>(container, Article.PublisherPlacementUsage_Conv, cosmosArticleName: "PublisherPlacementUsage", dsvTableName: "PublisherPlacementUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressPublisherPlacementUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressPublisherPlacementUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<ContentPerformanceUsage>(container, Article.ContentPerformanceUsage_Conv, cosmosArticleName: "ContentPerformanceUsage", dsvTableName: "ContentPerformanceUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressContentPerformanceUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressContentPerformanceUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<RadiusTargetedLocationHourUsage>(container, Article.RadiusTargetedLocationHourUsage_Conv, cosmosArticleName: "RadiusTargetedLocationHourUsage", dsvTableName: "RadiusTargetedLocationHourUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressRadiusTargetedLocationHourUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressRadiusTargetedLocationHourUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<AutoTargetUsage>(container, Article.AutoTargetUsage_Conv, cosmosArticleName: "AutoTargetUsage", dsvTableName: "AutoTargetUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAutoTargetUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressAutoTargetUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<AdLandingPageUrlUsage>(container, Article.AdLandingPageUrlUsage_Conv, cosmosArticleName: "AdLandingPageUrlUsage", dsvTableName: "AdLandingPageUrlUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAdLandingPageUrlUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressAdLandingPageUrlUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<AutomatedExtensionUsage>(container, Article.AutomatedExtensionUsage_Conv, cosmosArticleName: "AutomatedExtensionUsage", dsvTableName: "AutomatedExtensionUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAutomatedExtensionUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressAutomatedExtensionUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<FeedItemAdExtensionUsage>(container, Article.FeedItemAdExtensionUsage_Conv, cosmosArticleName: "FeedItemAdExtensionUsage", dsvTableName: "FeedItemAdExtensionUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressFeedItemAdExtensionUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, t.AdExtensionItemFeedItemId), 2)
                    .AttachOutput(Article.Delta_InProgressFeedItemAdExtensionUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, t.AdExtensionItemFeedItemId), 2),

                new CosmosPubMultipleOutputOperation<AssetUsage>(container, Article.AdAssetUsage_Conv, cosmosArticleName: "AdAssetUsage", dsvTableName: "AdAssetUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAssetUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, t.OrderId), 2)
                    .AttachOutput(Article.Delta_InProgressAssetUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, t.OrderId), 2)
                    .AttachOutput(Article.Delta_InProgressAssetSnapShotUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, (long)t.AssetId), 2)
                    .AttachOutput(Article.Delta_InProgressAssetSnapShotUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, (long)t.AssetId), 2),

                // SubCampaignUsage_NoIS is a sub fact for CampaignUsage with limited dimension columns, to improve performance for Campaign UI calls
                new CosmosPubMultipleOutputOperation<CampaignUsage>(container, Article.CampaignUsage_Conv, cosmosArticleName: "CampaignUsage", dsvTableName: "CampaignUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressCampaignUsage_Conv, t => GetBIAccountPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressSubCampaignUsage_NoIS_Conv, t => GetBIAccountPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressCampaignUsage_Conv_Clickhouse, t => GetBIClickhouseAccountPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressSubCampaignUsage_NoIS_Conv_Clickhouse, t => GetBIClickhouseAccountPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value), 2),

                // SubAccountUsage_NoIS is a sub fact for AccountUsage with limited dimension columns, to improve performance for Campaign UI calls
                new CosmosPubMultipleOutputOperation<AccountUsage>(container, Article.AccountUsage_Conv, cosmosArticleName: "AccountUsage", dsvTableName: "AccountUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAccountUsage_Conv, t => GetBIAccountPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressSubAccountUsage_NoIS_Conv, t => GetBIAccountPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressAccountUsage_Conv_Clickhouse, t => GetBIClickhouseAccountPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressSubAccountUsage_NoIS_Conv_Clickhouse, t => GetBIClickhouseAccountPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value), 2),

                new CosmosPubMultipleOutputOperation<BSCProductOfferUsage>(container, Article.BSCProductOfferUsage_Conv, cosmosArticleName: "BSCProductOfferUsage", dsvTableName: "BSCProductOfferUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                     .AttachOutput(Article.Delta_InProgressBSC2BIProductOfferUsage_BSC2BI_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                     .AttachOutput(Article.Delta_InProgressBSC2BIProductOfferCampaignUsage_BSC2BI_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.GlobalOfferId), 2)
                     .AttachOutput(Article.Delta_InProgressBSCProductOfferUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                     .AttachOutput(Article.Delta_InProgressProductOfferCampaignUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.GlobalOfferId), 2),

                // Vertical
                new CosmosPubMultipleOutputOperation<HotelVerticalUsage>(container, Article.HotelVerticalUsage_Conv, cosmosArticleName: "HotelVerticalUsage", dsvTableName: "HotelVerticalUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InprogressHotelVerticalUsage_Vertical2BI_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, Convert.ToInt64(t.VerticalItemId)), 2)
                    .AttachOutput(Article.Delta_InprogressHotelVerticalUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, Convert.ToInt64(t.VerticalItemId)), 2),

                new CosmosPubMultipleOutputOperation<HotelBookingUsage>(container, Article.HotelBookingUsage_Conv, cosmosArticleName: "HotelBookingUsage", dsvTableName: "HotelBookingUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InprogressHotelBookingUsage_Vertical2BI, t=> GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, Convert.ToInt64(t.VerticalItemId)), 2)
                    .AttachOutput(Article.Delta_InprogressHotelBookingUsage_Clickhouse, t=> GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, Convert.ToInt64(t.VerticalItemId)), 2),

                new CosmosPubMultipleOutputOperation<HotelUsage>(container, Article.HotelUsage_Conv, cosmosArticleName: "HotelCampaignUsage", dsvTableName: "HotelCampaignUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressHotelUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, t.OrderId), 2)
                    .AttachOutput(Article.Delta_InProgressHotelUsageByHotel_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, t.CampaignHotelId), 2)
                    .AttachOutput(Article.Delta_InProgressHotelUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, t.OrderId), 2)
                    .AttachOutput(Article.Delta_InProgressHotelUsageByHotel_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId, t.AccountId, t.CampaignHotelId), 2),

                new CosmosPubMultipleOutputOperation<OrderItemDDAUsage>(container, Article.OrderItemDDAUsage_Conv, cosmosArticleName: "OrderItemDDAUsage", dsvTableName: "OrderItemDDAUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                   .AttachOutput(Article.Delta_InProgressOrderItemDDAUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId == null ? 0 : t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2)
                   .AttachOutput(Article.Delta_InProgressOrderItemDDAUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId == null ? 0 : t.CustomerId.Value, t.AccountId.Value, t.OrderId.Value), 2),

                new CosmosPubMultipleOutputOperation<AssetGroupUsage>(container, Article.AssetGroupUsage_Conv, cosmosArticleName: "AssetGroupUsage", dsvTableName: "AssetGroupUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAssetGroupUsage_Conv, t => GetBIAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.AssetGroupId.Value), 2)
                    .AttachOutput(Article.Delta_InProgressAssetGroupUsage_Conv_Clickhouse, t => GetBIClickhouseAdGroupPartitionIdsFiltered(t.DateKey, t.CustomerId.Value, t.AccountId.Value, t.AssetGroupId.Value), 2)
            };

            IDwcEventMonitor dwcMonitor;

            if (string.IsNullOrEmpty(this.envManagerConnectionString))
            {
                logger.LogError("Use test DWC Object");
                dwcMonitor = new ConversionsSparkOutputEventMonitorMock(TimeSpan.FromMinutes(5),
                    operations.Select(t => t.CosmosArticleName).ToArray(),
                    "https://cosmos08.osdinfra.net/cosmos/adCenter.bicore.prod2/local/prod/ConversionNRT/data/NRT/Aggs/metadataAggStreaming__100_0.dsv",
                    "https://cosmos08.osdinfra.net/cosmos/adCenter.bicore.prod2/local/prod/ConversionNRT/data/NRT/Aggs",
                    (baseFolder, cosmosArticleName, eventTime) => $"https://cosmos08.osdinfra.net/cosmos/adCenter.bicore.prod2/local/prod/ConversionNRT/data/NRT/Aggs/{eventTime:yyyy}/{eventTime:MM}/{eventTime:dd}/{eventTime:HH}/{eventTime:mm}/table={cosmosArticleName}/");
            }
            else if (Config.IsProdOrTwp)
            {
                dwcMonitor = new ConversionsSparkOutputEventMonitor(TimeSpan.FromMinutes(5),
                    operations.Select(t => t.CosmosArticleName).ToArray(),
                    (currentCosmos, sparkOutputFolder, dsvFileName) => $"https://be.{currentCosmos}.osdinfra.net/cosmos/adCenter.BICore.prod2/{sparkOutputFolder}/{dsvFileName}",
                    (currentCosmos, sparkOutputFolder, cosmosArticleName, eventTime) => $"https://be.{currentCosmos}.osdinfra.net/cosmos/adCenter.BICore.prod2/{sparkOutputFolder}/{eventTime:yyyy}/{eventTime:MM}/{eventTime:dd}/{eventTime:HH}/{eventTime:mm}/table={cosmosArticleName}/",
                    "AdsConv_NRT_NonSessionReportCreate",
                    "ConversionV2Streaming",
                    envManagerConnectionString);
            }
            else
            {
                dwcMonitor = new ConversionsSparkOutputEventMonitor(TimeSpan.FromMinutes(5),
                    operations.Select(t => t.CosmosArticleName).ToArray(),
                    (currentCosmos, sparkOutputFolder, dsvFileName) => $"https://be.{currentCosmos}.osdinfra.net/cosmos/shareddata.ads.dev/{sparkOutputFolder}/{dsvFileName}",
                    (currentCosmos, sparkOutputFolder, cosmosArticleName, eventTime) => $"https://be.{currentCosmos}.osdinfra.net/cosmos/shareddata.ads.dev/{sparkOutputFolder}/{eventTime:yyyy}/{eventTime:MM}/{eventTime:dd}/{eventTime:HH}/{eventTime:mm}/table={cosmosArticleName}/",
                    "AdsConv_NRT_NonSessionReportCreate",
                    "ConversionV2Streaming",
                    envManagerConnectionString);
            }

            var outputDwcDao = sharedDataDwcDao;
            var outputDwcEventName = "SA_FastBI_Conversions_Pub_Done";

            var jobOptions = new CosmosPubJobOptions
            {
                SupportRestatement = true,
                MaxRestatementWaitTimeInMinute = 30,
                RestatementCoolDownTimeInSecondGetter = () => DynamicConfig.Get().FastBIConvRestatementCoolDownTimeInSecond,
                TrackCosmoStreamPositions = false,
                ChunkedPublish = false,
                UpstreamCheckIntervalInSeconds = 2
            };

            container.AddJob(new CosmosPubJob("FastBIConvV2Publication", container, operations, dwcMonitor, eventQueue, jobOptions, outputDwcDao, outputDwcEventName, storageProvider));
            return container;
        }

        private JobContainer CreateFastBIConversionsAccount2Subscriber(PubEventQueueV1 eventQueue, OperationCounter operationCounter)
        {
            var processes = new List<DBArticleProcess>
            {
                //Note: order by article size desc (i.e. big articles first)
                new BIDBArticleProcess<CampaignUsage>(Article.Delta_InProgressCampaignUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressCampaignUsage_Conv"),
                new BIDBArticleProcess<CampaignUsage>(Article.Delta_InProgressSubCampaignUsage_NoIS_Conv, DBJobConfig.PartitionByPartitionId ,"Delta_InProgressSubCampaignUsage_NoIS_Conv"),
                new BIDBArticleProcess<AccountUsage>(Article.Delta_InProgressAccountUsage_Conv, DBJobConfig.PartitionByPartitionId ,"Delta_InProgressAccountUsage_Conv"),
                new BIDBArticleProcess<AccountUsage>(Article.Delta_InProgressSubAccountUsage_NoIS_Conv, DBJobConfig.PartitionByPartitionId ,"Delta_InProgressSubAccountUsage_NoIS_Conv")
            };

            // Note: changing the operation id is a broken change
            // if some machines still run a different binary, it cannot find this operation name
            // then it will success silently. see header comment in Operation.cs
            var operation = new BIDBSubOperation("FastBIConvAccount2DBSubOperation", processes, storageProvider, operationCounter);
            var subContainer = new DBSubContainer("FastBIConvAccount2SubContainer", storageProvider, operation, eventQueue);
            return subContainer;
        }

        private JobContainer CreateFastBIConversionsAdGroup2Subscriber(PubEventQueueV1 eventQueue, OperationCounter operationCounter)
        {
            var processes = new List<DBArticleProcess>
            {
                // Note: order by article size desc (i.e. big articles first)
                new BIDBArticleProcess<HourlyElementAdUsage>(Article.Delta_InProgressElementAdUsageByOrder_Conv, DBJobConfig.PartitionByPartitionId ,"Delta_InProgressElementAdUsageByOrder_Conv"),
                new BIDBArticleProcess<HourlyAdAdExtensionClickTypeUsage>(Article.Delta_InProgressAdAdExtensionClickTypeUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressAdAdExtensionClickTypeUsage_Conv"),
                new BIDBArticleProcess<AdUsage>(Article.Delta_InProgressAdUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressAdUsage_Conv"),
                new BIDBArticleProcess<HourlyElementOrderItemUsage>(Article.Delta_InProgressElementOrderItemUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressElementOrderItemUsage_Conv"),
                new BIDBArticleProcess<GenderAgeUsage>(Article.Delta_InProgressGenderAgeUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressGenderAgeUsage_Conv"),
                new BIDBArticleProcess<LocationHourUsage>(Article.Delta_InProgressLocationHourUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressLocationHourUsage_Conv"),
                new BIDBArticleProcess<HourlyOrderItemAdExtensionClickTypeUsage>(Article.Delta_InProgressOrderItemAdExtensionClickTypeUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressOrderItemAdExtensionClickTypeUsage_Conv"),
                new BIDBArticleProcess<OrderItemUsage>(Article.Delta_InProgressOrderItemUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressOrderItemUsage_Conv"),
                new BIDBArticleProcess<OrderItemUsage>(Article.Delta_InProgressSubOrderItemUsage_NoIS_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressSubOrderItemUsage_NoIS_Conv"),
                new BIDBArticleProcess<OrderTargetUsage>(Article.Delta_InProgressOrderTargetUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressOrderTargetUsage_Conv"),
                new BIDBArticleProcess<OrderUsage>(Article.Delta_InProgressOrderUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressOrderUsage_Conv"),
                new BIDBArticleProcess<PublisherPlacementUsage>(Article.Delta_InProgressPublisherPlacementUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressPublisherPlacementUsage_Conv"),
                new BIDBArticleProcess<ContentPerformanceUsage>(Article.Delta_InProgressContentPerformanceUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressContentPerformanceUsage_Conv"),
                new BIDBArticleProcess<RadiusTargetedLocationHourUsage>(Article.Delta_InProgressRadiusTargetedLocationHourUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressRadiusTargetedLocationHourUsage_Conv"),
                new BIDBArticleProcess<AutoTargetUsage>(Article.Delta_InProgressAutoTargetUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressAutoTargetUsage_Conv"),
                new BIDBArticleProcess<AdLandingPageUrlUsage>(Article.Delta_InProgressAdLandingPageUrlUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressAdLandingPageUrlUsage_Conv"),
                new BIDBArticleProcess<AssetUsage>(Article.Delta_InProgressAssetUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressAssetUsage_Conv"),
                new BIDBArticleProcess<AssetUsage>(Article.Delta_InProgressAssetSnapShotUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressAssetSnapShotUsage_Conv"),
                new BIDBArticleProcess<ProfessionalDemographicsUsage>(Article.Delta_InProgressProfessionalDemographicsUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressProfessionalDemographicsUsage_Conv"),
                new BIDBArticleProcess<AutomatedExtensionUsage>(Article.Delta_InProgressAutomatedExtensionUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressAutomatedExtensionUsage_Conv"),
                new BIDBArticleProcess<AdFeedUsage>(Article.Delta_InProgressFeedItemUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressFeedItemUsage_Conv"),
                new BIDBArticleProcess<FeedItemAdExtensionUsage>(Article.Delta_InProgressFeedItemAdExtensionUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressFeedItemAdExtensionUsage_Conv"),
                new BIDBArticleProcess<HourlyGoalUsage>(Article.Delta_InProgressGoalUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressGoalUsage"),
                new BIDBArticleProcess<BSCProductOfferUsage>(Article.Delta_InProgressBSC2BIProductOfferUsage_BSC2BI_Conv, DBJobConfig.PartitionByPartitionId, "InProgressProductOfferUsage_Conv_Delta", isBSCData2BI: true),
                new BIDBArticleProcess<HotelVerticalUsage>(Article.Delta_InprogressHotelVerticalUsage_Vertical2BI_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InprogressHotelVerticalUsage_Conv"),
                new BIDBArticleProcess<HotelBookingUsage>(Article.Delta_InprogressHotelBookingUsage_Vertical2BI, DBJobConfig.PartitionByPartitionId, "Delta_InprogressHotelBookingUsage"),
                new BIDBArticleProcess<BSCProductOfferUsage>(Article.Delta_InProgressBSC2BIProductOfferCampaignUsage_BSC2BI_Conv, DBJobConfig.PartitionByPartitionId, "InProgressProductOfferCampaignUsage_Conv_Delta", isBSCData2BI: true),
                new BIDBArticleProcess<HotelUsage>(Article.Delta_InProgressHotelUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressHotelUsage_Conv"),
                new BIDBArticleProcess<HotelUsage>(Article.Delta_InProgressHotelUsageByHotel_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressHotelUsageByHotel_Conv"),
                new BIDBArticleProcess<OrderItemDDAUsage>(Article.Delta_InProgressOrderItemDDAUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressOrderItemDDAUsage_Conv"),
                new BIDBArticleProcess<AssetGroupUsage>(Article.Delta_InProgressAssetGroupUsage_Conv, DBJobConfig.PartitionByPartitionId, "Delta_InProgressAssetGroupUsage_Conv")
        };

            // Note: changing the operation id is a broken change
            // if some machines still run a different binary, it cannot find this operation name
            // then it will success silently. see header comment in Operation.cs
            var operation = new BIDBSubOperation("FastBIConvAdGroup2DBSubOperation", processes, storageProvider, operationCounter);
            var subContainer = new DBSubContainer("FastBIConvAdGroup2SubContainer", storageProvider, operation, eventQueue);
            return subContainer;
        }

        private async Task<JobContainer> CreateFastBIConversionsClickhouseAccountSubscriber(PubEventQueueV1 eventQueue, OperationCounter operationCounter, CancellationToken cancellationToken)
        {
            var processes = new List<IClickhouseSubArticleProcess>
            {
                //Note: order by article size desc (i.e. big articles first)
                new ClickhouseBISubArticleProcess<CampaignUsage>(Article.Delta_InProgressCampaignUsage_Conv_Clickhouse, "BICHConv", "InProgressCampaignUsage_Conv"),
                new ClickhouseBISubArticleProcess<CampaignUsage>(Article.Delta_InProgressSubCampaignUsage_NoIS_Conv_Clickhouse, "BICHConv","InProgressSubCampaignUsage_NoIS_Conv"),
                new ClickhouseBISubArticleProcess<AccountUsage>(Article.Delta_InProgressAccountUsage_Conv_Clickhouse, "BICHConv","InProgressAccountUsage_Conv"),
                new ClickhouseBISubArticleProcess<AccountUsage>(Article.Delta_InProgressSubAccountUsage_NoIS_Conv_Clickhouse, "BICHConv","InProgressSubAccountUsage_NoIS_Conv")
            };

            // Note: changing the operation id is a broken change
            // if some machines still run a different binary, it cannot find this operation name
            // then it will success silently. see header comment in Operation.cs
            var operation = new ClickhouseSubOperation("FastBIConvClickhouseAccountDBSubOperation", processes, operationCounter, storageProvider);
            var directoryOperation = new ClickhouseShareDirectoryOperation("FastBIConvClickhouseAccountShareDirectoryOperation", processes, operationCounter);

            var bidbBlobConnection = Config.IsLocal ? config("BIDBClickhouseBlobConnection")
                : await this.saKeyVaultClient.GetConnectionStringAsync(config("SAKeyVaultName"), config("BIDBClickhouseBlobConnection"), logger, cancellationToken).ConfigureAwait(false);

            var storageAccount = config("BIDBClickhouseSourceStorageAccount");
            var bidbLoaderEventQueue = new ClickhouseLoaderEventQueue(storageAccount, "BIDBClickhouseLoaderEventQueue");
            var articleTableMapping = processes.ToDictionary(t => t.Article, t => t.TableName);
            var metaFileOperation = new ClickhouseMetaFileOperation("FastBIConvClickhouseAccountMetaFileOperation", bidbLoaderEventQueue, articleTableMapping, operationCounter, true);


            var fileShare = config("BIDBClickhouseFileShareFastBIConv");
            var container = new ClickhouseSubContainer("FastBIConvClickhouseAccountSubContainer", storageAccount, bidbBlobConnection, operation, metaFileOperation, directoryOperation,
                fileShare, GetFastBIConversionsClickhouseAccountRootDir, eventQueue, new ClickhouseSubJobOptions(), storageProvider, eventIdInMetaFiles: true, diskFileRoot: config("BIDBClickhouseDiskFileRoot"));
            return container;
        }

        private async Task<JobContainer> CreateFastBIConversionsClickhouseAdGroupSubscriber(PubEventQueueV1 eventQueue, OperationCounter operationCounter, CancellationToken cancellationToken)
        {
            var processes = new List<IClickhouseSubArticleProcess>
            {
                // Note: order by article size desc (i.e. big articles first)
                new ClickhouseBISubArticleProcess<HourlyElementAdUsage>(Article.Delta_InProgressElementAdUsageByOrder_Conv_Clickhouse, "BICHConv", "InProgressElementAdUsage_Conv"),
                new ClickhouseBISubArticleProcess<HourlyAdAdExtensionClickTypeUsage>(Article.Delta_InProgressAdAdExtensionClickTypeUsage_Conv_Clickhouse, "BICHConv", "InProgressAdAdExtensionClickTypeUsage_Conv"),
                new ClickhouseBISubArticleProcess<AdUsage>(Article.Delta_InProgressAdUsage_Conv_Clickhouse, "BICHConv", "InProgressAdUsage_Conv"),
                new ClickhouseBISubArticleProcess<HourlyElementOrderItemUsage>(Article.Delta_InProgressElementOrderItemUsage_Conv_Clickhouse, "BICHConv", "InProgressElementOrderItemUsage_Conv"),
                new ClickhouseBISubArticleProcess<GenderAgeUsage>(Article.Delta_InProgressGenderAgeUsage_Conv_Clickhouse, "BICHConv", "InProgressGenderAgeUsage_Conv"),
                new ClickhouseBISubArticleProcess<LocationHourUsage>(Article.Delta_InProgressLocationHourUsage_Conv_Clickhouse, "BICHConv", "InProgressLocationHourUsage_Conv"),
                new ClickhouseBISubArticleProcess<HourlyOrderItemAdExtensionClickTypeUsage>(Article.Delta_InProgressOrderItemAdExtensionClickTypeUsage_Conv_Clickhouse, "BICHConv", "InProgressOrderItemAdExtensionClickTypeUsage_Conv"),
                new ClickhouseBISubArticleProcess<OrderItemUsage>(Article.Delta_InProgressOrderItemUsage_Conv_Clickhouse, "BICHConv", "InProgressOrderItemUsage_Conv"),
                new ClickhouseBISubArticleProcess<OrderItemUsage>(Article.Delta_InProgressSubOrderItemUsage_NoIS_Conv_Clickhouse, "BICHConv", "InProgressSubOrderItemUsage_NoIS_Conv"),
                new ClickhouseBISubArticleProcess<OrderTargetUsage>(Article.Delta_InProgressOrderTargetUsage_Conv_Clickhouse, "BICHConv", "InProgressOrderTargetUsage_Conv"),
                new ClickhouseBISubArticleProcess<OrderUsage>(Article.Delta_InProgressOrderUsage_Conv_Clickhouse, "BICHConv", "InProgressOrderUsage_Conv"),
                new ClickhouseBISubArticleProcess<PublisherPlacementUsage>(Article.Delta_InProgressPublisherPlacementUsage_Conv_Clickhouse, "BICHConv", "InProgressPublisherPlacementUsage_Conv"),
                new ClickhouseBISubArticleProcess<ContentPerformanceUsage>(Article.Delta_InProgressContentPerformanceUsage_Conv_Clickhouse, "BICHConv", "InProgressContentPerformanceUsage_Conv"),
                new ClickhouseBISubArticleProcess<RadiusTargetedLocationHourUsage>(Article.Delta_InProgressRadiusTargetedLocationHourUsage_Conv_Clickhouse, "BICHConv", "InProgressRadiusTargetedLocationHourUsage_Conv"),
                new ClickhouseBISubArticleProcess<AutoTargetUsage>(Article.Delta_InProgressAutoTargetUsage_Conv_Clickhouse, "BICHConv", "InProgressAutoTargetUsage_Conv"),
                new ClickhouseBISubArticleProcess<AdLandingPageUrlUsage>(Article.Delta_InProgressAdLandingPageUrlUsage_Conv_Clickhouse, "BICHConv", "InProgressAdLandingPageUrlUsage_Conv"),
                new ClickhouseBISubArticleProcess<AssetUsage>(Article.Delta_InProgressAssetUsage_Conv_Clickhouse, "BICHConv", "InProgressAssetUsage_Conv"),
                new ClickhouseBISubArticleProcess<AssetUsage>(Article.Delta_InProgressAssetSnapShotUsage_Conv_Clickhouse, "BICHConv", "InProgressAssetSnapShotUsage_Conv"),
                new ClickhouseBISubArticleProcess<ProfessionalDemographicsUsage>(Article.Delta_InProgressProfessionalDemographicsUsage_Conv_Clickhouse, "BICHConv", "InProgressProfessionalDemographicsUsage_Conv"),
                new ClickhouseBISubArticleProcess<AdFeedUsage>(Article.Delta_InProgressFeedItemUsage_Conv_Clickhouse, "BICHConv", "InProgressAdFeedUsage_Conv"),
                new ClickhouseBISubArticleProcess<FeedItemAdExtensionUsage>(Article.Delta_InProgressFeedItemAdExtensionUsage_Conv_Clickhouse, "BICHConv", "InProgressFeedItemAdExtensionUsage_Conv"),
                new ClickhouseBISubArticleProcess<AutomatedExtensionUsage>(Article.Delta_InProgressAutomatedExtensionUsage_Conv_Clickhouse, "BICHConv", "InProgressAutomatedExtensionUsage_Conv"),
                new ClickhouseBISubArticleProcess<HourlyGoalUsage>(Article.Delta_InProgressGoalUsage_Conv_Clickhouse, "BICHConv", "InProgressGoalUsage_Conv"),
                new ClickhouseBISubArticleProcess<OrderItemDDAUsage>(Article.Delta_InProgressOrderItemDDAUsage_Conv_Clickhouse, "BICHConv", "InProgressOrderItemDDAUsage_Conv"),
                new ClickhouseBISubArticleProcess<AssetGroupUsage>(Article.Delta_InProgressAssetGroupUsage_Conv_Clickhouse, "BICHConv", "InProgressAssetGroupUsage_Conv"),
                new ClickhouseBISubArticleProcess<HotelVerticalUsage>(Article.Delta_InprogressHotelVerticalUsage_Conv_Clickhouse, "BICHConv", "InProgressHotelVerticalUsage_Conv"),
                new ClickhouseBISubArticleProcess<HotelBookingUsage>(Article.Delta_InprogressHotelBookingUsage_Clickhouse, "BICHConv", "InProgressHotelBookingUsage_Conv"),
                new ClickhouseBISubArticleProcess<HotelUsage>(Article.Delta_InProgressHotelUsage_Conv_Clickhouse, "BICHConv", "InProgressHotelUsage_Conv"),
                new ClickhouseBISubArticleProcess<HotelUsage>(Article.Delta_InProgressHotelUsageByHotel_Conv_Clickhouse, "BICHConv", "InProgressHotelUsageByHotel_Conv"),
                new ClickhouseBISubArticleProcess<BSCProductOfferUsage>(Article.Delta_InProgressBSCProductOfferUsage_Conv_Clickhouse, "BICHConv", "InProgressProductOfferUsage_Conv"),
                new ClickhouseBISubArticleProcess<BSCProductOfferUsage>(Article.Delta_InProgressProductOfferCampaignUsage_Conv_Clickhouse, "BICHConv", "InProgressProductOfferCampaignUsage_Conv")
            };

            // Note: changing the operation id is a broken change
            // if some machines still run a different binary, it cannot find this operation name
            // then it will success silently. see header comment in Operation.cs
            var operation = new ClickhouseSubOperation("FastBIConvClickhouseAdGroupDBSubOperation", processes, operationCounter, storageProvider);
            var directoryOperation = new ClickhouseShareDirectoryOperation("FastBIConvClickhouseShareDirectoryOperation", processes, operationCounter);

            var bidbBlobConnection = Config.IsLocal ? config("BIDBClickhouseBlobConnection")
                : await this.saKeyVaultClient.GetConnectionStringAsync(config("SAKeyVaultName"), config("BIDBClickhouseBlobConnection"), logger, cancellationToken).ConfigureAwait(false);

            var storageAccount = config("BIDBClickhouseSourceStorageAccount");
            var bidbLoaderEventQueue = new ClickhouseLoaderEventQueue(storageAccount, "BIDBClickhouseLoaderEventQueue");
            var articleTableMapping = processes.ToDictionary(t => t.Article, t => t.TableName);
            var metaFileOperation = new ClickhouseMetaFileOperation("FastBIConvClickhouseMetaFileOperation", bidbLoaderEventQueue, articleTableMapping, operationCounter, true);


            var fileShare = config("BIDBClickhouseFileShareFastBIConv");
            var container = new ClickhouseSubContainer("FastBIConvClickhouseAdGroupSubContainer", storageAccount, bidbBlobConnection, operation, metaFileOperation, directoryOperation,
                fileShare, GetFastBIConversionsClickhouseAdGroupRootDir, eventQueue, new ClickhouseSubJobOptions(), storageProvider, eventIdInMetaFiles: true, diskFileRoot: config("BIDBClickhouseDiskFileRoot"));
            return container;
        }

        // Conversions Spark jobs may generate empty files if there is no data for an article-delta
        // This is represented as a data row with dummy data that isn't filtered out before writing to cosmos
        // FDP filters these rows out using identifiers for dummy data (DateKey==0 && HourNum ==0 && LoadTime==”0001-01-01T00:00:00.000000”)
        // This prevents data from flowing to BI DBs or breaking on constraint checks for not nullable columns
        // Use this wrapper to return empty partition id for such rows

        private int[] GetBIAdGroupPartitionIdsFiltered(int? dateKey, long customerId, long accountId, long idOfThingThatIsSharded)
        {
            return dateKey == 0  ? Array.Empty<int>() : GetBIAdGroupPartitionIds(customerId, accountId, idOfThingThatIsSharded);
        }

        private int[] GetBIAccountPartitionIdsFiltered(int? dateKey, long customerId, long accountId)
        {
            return dateKey == 0 ? Array.Empty<int>() : GetBIAccountPartitionIds(customerId, accountId);
        }

        private int[] GetBIClickhouseAdGroupPartitionIdsFiltered(int? dateKey, long customerId, long accountId, long idOfThingThatIsSharded)
        {
            return dateKey == 0 ? Array.Empty<int>() : GetBIClickhouseAdGroupPartitionIds(customerId, accountId, idOfThingThatIsSharded);
        }

        private int[] GetBIClickhouseAccountPartitionIdsFiltered(int? dateKey, long customerId, long accountId)
        {
            return dateKey == 0 ? Array.Empty<int>() : GetBIClickhouseAccountPartitionIds(customerId, accountId);
        }

        private string GetFastBIConversionsClickhouseAccountRootDir(int partitionId, DateTime eventTime)
        {
            return "FastBI/Conversions/Account/" + eventTime.ToString("yyyy") + "/" + eventTime.ToString("MM") + "/" + eventTime.ToString("dd") + "/" +
                eventTime.ToString("HH") + "/" + partitionId;
        }

        private string GetFastBIConversionsClickhouseAdGroupRootDir(int partitionId, DateTime eventTime)
        {
            return "FastBI/Conversions/AdGroup/" + eventTime.ToString("yyyy") + "/" + eventTime.ToString("MM") + "/" + eventTime.ToString("dd") + "/" +
                eventTime.ToString("HH") + "/" + partitionId;
        }
    }
}
