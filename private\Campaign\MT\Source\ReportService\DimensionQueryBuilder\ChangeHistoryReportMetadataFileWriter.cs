﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ReportService
{
    using System.Collections.Generic;
    using System;
    using System.IO;
    using System.Threading;
    using Microsoft.AdCenter.Shared.MT;
    using ProtoBuf;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ReportService.DataContract;
    using System.Threading.Tasks;

#pragma warning disable CS0618 // Type or member is obsolete
    public class ChangeHistoryReportMetadataFileWriter : ReportMetadataFileWriter
    {
        private readonly DimensionRowDataAdapter dataAdapter;
        private readonly int accountIdOrdinal;
        private readonly int entityIdOrdinal;
        private readonly int adGroupIdOrdinal;
        private readonly int campaignIdOrdinal;
        private readonly int entityValueIdOrdinal;
        private readonly int attributeIdOrdinal;
        private readonly int oldValueOrdinal;
        private readonly int newValueOrdinal;
        public ChangeHistoryReportMetadataFileWriter(ILogShared logger, Stream stream, string[] dbTypes, DimensionRowDataAdapter dataAdapter,
            int entityIdOrdinal, int accountIdOrdinal, int entityValueIdOrdinal, int adGroupIdOrdinal, int campaignIdOrdinal, int attributeIdOrdinal, int oldValueOrdinal, int newValueOrdinal) : base(logger, stream, dbTypes)
        {
            this.dataAdapter = dataAdapter;
            this.entityIdOrdinal = entityIdOrdinal;
            this.accountIdOrdinal = accountIdOrdinal;
            this.entityValueIdOrdinal = entityValueIdOrdinal;
            this.adGroupIdOrdinal = adGroupIdOrdinal;
            this.campaignIdOrdinal = campaignIdOrdinal;
            this.attributeIdOrdinal = attributeIdOrdinal;
            this.oldValueOrdinal = oldValueOrdinal;
            this.newValueOrdinal = newValueOrdinal;
        }

        public int WriteChangeHistoryRowsData(IEnumerator<IList<object>> rows, CancellationToken cancelToken, bool isLatestEntityValueRequired, HashSet<ChangeHistoryEntityValueKey> entityValuesToFetchLatestData)
        {
            int totalRows = 0;
            using (var writer = ProtoWriter.Create(stream, null, null))
            {
                while (rows.MoveNext())
                {
                    var row = rows.Current;
                    if (cancelToken.IsCancellationRequested)
                    {
                        this.logger.LogInfo($"{totalRows} rows were written to proto data file before token was cancelled.");
                        cancelToken.ThrowIfCancellationRequested();
                    }

                    if (isLatestEntityValueRequired)
                    {
                        ChangeHistoryBlobDataHandler.CreateEntityValuesToFetchLatestData(row, entityValuesToFetchLatestData, dataAdapter,
                            entityIdOrdinal, accountIdOrdinal, entityValueIdOrdinal, adGroupIdOrdinal, campaignIdOrdinal);
                    }
                    ChangeHistoryBlobDataHandler.HandleAudienceAndTopicChangeHistoryRow(row, dataAdapter, attributeIdOrdinal, oldValueOrdinal, newValueOrdinal);

                    ProtoWriter.WriteFieldHeader(1, WireType.StartGroup, writer);
                    var token = ProtoWriter.StartSubItem(row, writer);

                    int i = 0;
                    try
                    {
                        foreach (var value in row)
                        {
                            if (value != null && value != DBNull.Value)
                            {
                                columnWriters[i](writer, value);
                            }

                            i++;
                        }
                    }
                    catch (Exception ex)
                    {
                        this.logger.LogError($"ChangeHistoryReportMetadataFileWriter.WriteRowsData: Exception happened when writing {i}th column. DbTypes: {string.Join(",", this.dbTypes)}, row data: {string.Join(",", row)}, Exception is {ex}");

                        throw;
                    }

                    totalRows++;
                    ProtoWriter.EndSubItem(token, writer);
                }

                writer.Close();
            }

            return totalRows;
        }

        public async Task<int> WriteChangeHistoryRowsDataAsync(IAsyncEnumerator<IList<object>> rows, CancellationToken cancelToken, bool isLatestEntityValueRequired, HashSet<ChangeHistoryEntityValueKey> entityValuesToFetchLatestData)
        {
            int totalRows = 0;
            using (var writer = ProtoWriter.Create(stream, null, null))
            {
                while (await rows.MoveNextAsync().ConfigureAwait(false))
                {
                    var row = rows.Current;
                    if (cancelToken.IsCancellationRequested)
                    {
                        this.logger.LogInfo($"{totalRows} rows were written to proto data file before token was cancelled.");
                        cancelToken.ThrowIfCancellationRequested();
                    }

                    if (isLatestEntityValueRequired)
                    {
                        ChangeHistoryBlobDataHandler.CreateEntityValuesToFetchLatestData(row, entityValuesToFetchLatestData, dataAdapter,
                            entityIdOrdinal, accountIdOrdinal, entityValueIdOrdinal, adGroupIdOrdinal, campaignIdOrdinal);
                    }
                    ChangeHistoryBlobDataHandler.HandleAudienceAndTopicChangeHistoryRow(row, dataAdapter, attributeIdOrdinal, oldValueOrdinal, newValueOrdinal);

                    ProtoWriter.WriteFieldHeader(1, WireType.StartGroup, writer);
                    var token = ProtoWriter.StartSubItem(row, writer);

                    int i = 0;
                    try
                    {
                        foreach (var value in row)
                        {
                            if (value != null && value != DBNull.Value)
                            {
                                columnWriters[i](writer, value);
                            }

                            i++;
                        }
                    }
                    catch (Exception ex)
                    {
                        this.logger.LogError($"ChangeHistoryReportMetadataFileWriter.WriteRowsData: Exception happened when writing {i}th column. DbTypes: {string.Join(",", this.dbTypes)}, row data: {string.Join(",", row)}, Exception is {ex}");
                        throw;
                    }

                    totalRows++;
                    ProtoWriter.EndSubItem(token, writer);
                }

                writer.Close();
            }

            return totalRows;
        }

    }
#pragma warning restore CS0618 // Type or member is obsolete
}
