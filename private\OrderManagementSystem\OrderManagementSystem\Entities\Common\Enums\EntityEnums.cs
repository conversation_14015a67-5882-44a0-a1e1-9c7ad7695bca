﻿namespace OrderManagementSystem.Entities
{
    #region Product
    public enum ProductType
    {
        _ = 0,

        Roadblock = 1,
        Rotational = 2,
        Bulk = 3,
        SOV = 6,
    }

    public enum ProductStatus
    {
        _ = 0,

        Active = 1,
        Deleted = 2,
    }

    public enum ProductTimeSlot
    {
        _ = 0,
        SixHour00 = 1,
        SixHour06 = 2,
        SixHour12 = 3,
        SixHour18 = 4,
        TwelveHour00 = 5,
        TwelveHour12 = 6,
        AllDay = 7,
    }
    #endregion

    #region Placement
    public enum PlacementState
    {
        _ = 0,
        Active = 1,
        Inactive = 2,
    }

    public enum UsesSizes
    {
        _ = 0,
        Never = 1,
        Sometimes = 2,
        Always = 3,
    }
    #endregion

    #region Line
    public enum LineType
    {
        _ = 0,

        Roadblock = 1,
        Rotational = 2,
        Bulk = 3,
        MakeGood = 4,
        Bonus = 5,
        SOV = 6,
    }

    public enum LineStatus
    {
        _ = 0,

        Draft = 1,
        PendingApproval = 3,
        Approved = 4,
        Synced = 5,
        UpdatedAfterSync = 6,
        Deleted = 7,
        NeedApproval = 8,
        Denied = 9,
    }

    public enum RateType
    {
        _ = 0,

        CPM = 1,
        CPD = 2,
    }

    [Flags]
    public enum  PublisherApprovalReason: Int64
    {
        _ = 0,
        Discount = 0b0001,
        YieldAnalyticsOverride = 0b0010,
        MakeGood = 0b0100,
        Bonus = 0b1000,
        ProjectFresno = 0b10000,
    }

    public enum PublisherApprovalStatus
    {
        _ = 0,

        Undecided = 1,
        Approved = 2,
        Denied = 3,
        Deleted = 4,
    }

    public enum PublisherApprovalType
    {
        _ = 0,

        Discount = 1,
        YieldAnalyticsOverride = 2,
    }

    public enum DecisionStatus
    {
        _ = 0,

        Undecided = 1,
        Approved = 2,
        Denied = 3,
    }

    public enum BudgetScheduleType
    {
        _ = 0,

        ASAP = 1,
        Evenly = 2
    }
    #endregion

    #region Media Plan
    public enum MediaPlanStatus
    {
        _ = 0,

        Draft = 1,
        CustomerApproved = 2,
        Committed = 3,
        Active = 4,
        Inactive = 5,
        Serving = 6,
        Completed = 7,
        Deleted = 8,
        Pitched = 9,
        VerbalCommit = 10,
        CommittedVerbalOnly = 11,
        ServingVerbalOnly = 12,
        CompletedVerbalOnly = 13,
    }
    #endregion

    #region Offline Operations
    public enum OfflineOperationType
    {
        _ = 0,

        Commit = 1,
        Export = 2,
        Import = 3,
    }

    public enum OfflineOperationStatus
    {
        _ = 0,

        Queued = 1,
        InProgress = 2,
        Completed = 3,
        Failed = 4,
    }

    public enum FailureCode
    {
        _ = 0,

        GeneralFailure = 1,
    }

    public enum ExportImportEntityType
    {
        _ = 0,

        Line = 1,
        MediaPlan = 2,
        LineTarget = 3,
    }

    public enum ImportStatus
    {
        _ = 0,

        Active,
        Deleted
    }

    public enum ImportJobStatus
    {
        _ = 0,

        InProgress = 1,
        Completed = 2,
        Failed = 3,
    }

    public enum ImportFileType
    {
        _ = 0,

        Csv = 1,
        Xlsx = 2,
    }
    #endregion

    #region Targeting
    public enum TargetTypeExternal
    {
        _ = 0,
        Audience = 1,
        Location = 2,
        Device = 3,
        DayPart = 4,
        FrequencyAndRecency = 5,
    }

    public enum TargetType
    {
        _ = 0,
        Audience = 1,
        Location = 2, // Deprecated - use Country
        Device = 3,
        DayPart = 4,
        FrequencyAndRecency = 5, //Product only, encapsulates TTIds 6-11
        HourFrequency = 6,
        DayFrequency = 7,
        Recency = 8,
        WeekFrequency = 9,
        MonthFrequency = 10,
        LifetimeFrequency = 11,
        Country = 12,
        Region = 13,
        City = 14,
        DMA = 15,
        KeyValue = 16,
    }

    public enum ValueTypes
    {
        _ = 0,
        Numeric = 1,
        String = 2,
        NumericArray = 3,
        StringArray = 4
    }

    public enum KeyValueExpressionOperationType
    {
        _ = 0,
        And = 1,
        Or = 2,
        Not = 3,
        In = 4,
        Eq = 5,
        Gt = 6,
        Lt = 7,
        Gte = 8,
        Lte = 9,
        Neq = 10
    }
    public enum KeyValueTargetingType
    {
        _ = 0,
        OR = 1,
        AND = 2
    }

    public enum AudienceTargetingType
    {
        _ = 0,
        OR = 1,
        AND = 2,
    }

    //Xandr API doc: https://learn.microsoft.com/en-us/xandr/digital-platform-api/profile-service#device-type-targets
    public enum DeviceType
    {
        _ = 0,

        Desktop = 1, // or Computers in DSP, map to pc in Xandr
        Mobile = 2,      // or Smartphones in DSP, map to phone in Xandr
        Tablet = 3,      // or Tablets in DSP, map to tablet in Xandr
        CTV = 4,         // or ConnectedTVs in DSP , map to tv, gameconsole and stb in Xandr
    }

    public enum FrequencyUnit
    {
        _ = 0,

        Hour = 1,
        Day = 2,
        Week = 3,
        Month = 4,
        Lifetime = 5
    }
    #endregion

    #region Monitoring
    public enum MonitoringErrorType
    {
        _ = 0,
        MonetizeCallFailure = 1,
        MonetizeEntityDoesNotExist = 2,
        MonetizeNameMismatch = 3,
        MonetizeIOLineCountMismatch = 4,
        MonetizeLIRevenueMismatch = 5,
    }
    #endregion
}
