namespace FastDataPipeline.Contract.FastBIConversion
{
    using ProtoBuf;
    using System;

    [ClickhouseCsvContract(schema: "BICHConv", columnList: new[] { nameof(DateKey), nameof(HourNum), nameof(AccountId), nameof(OrderId), nameof(PropertyUrl), nameof(DistributionChannelId),
        nameof(ConversionCount), nameof(AssistCount), nameof(AdvertiserReportedRevenue),
        nameof(DeviceOSId), nameof(DeviceTypeId), nameof(MatchTypeId), nameof(BiddedMatchTypeId), nameof(DeviceOSId2), nameof(CampaignId), nameof(NetworkId),
        "(t) => {return t.LoadTime.ToString(\"yyyy-MM-dd HH:mm:ss\");}", nameof(FullConversionCount), nameof(FullAdvertiserReportedRevenue), nameof(TargetValueId), nameof(TargetTypeId) },
        Header = "DateK<PERSON>,HourNum,AccountId,OrderId,PropertyUrl,DistributionChannelId,ConversionCnt,AssistCnt,AdvertiserReportedRevenue,DeviceOSId,DeviceTypeId,MatchTypeId,BiddedMatchTypeId,DeviceOSId2,CampaignId,NetworkId,LoadTime,FullConversionCnt,FullAdvertiserReportedRevenue,TargetValueId,TargetTypeId")]

    [ProtoContract]
    public partial class ContentPerformanceUsage
    {
        [ProtoMember(1)]
        [DbColumnName("DATEKEY")]
        public Int32? DateKey { get; set; }

        [ProtoMember(2)]
        [DbColumnName("HOURNUM")]
        public SByte? HourNum { get; set; }

        [ProtoMember(3)]
        [DbColumnName("ACCOUNTID")]
        public Int32? AccountId { get; set; }

        [ProtoMember(4)]
        [DbColumnName("ORDERID")]
        [AcceptableDsvType(typeof(UInt64?))]
        public Int64? OrderId { get; set; }

        [ProtoMember(5)]
        [DbColumnName("PROPERTYURL")]
        public String PropertyUrl { get; set; }

        [ProtoMember(6)]
        [DbColumnName("DISTRIBUTIONCHANNELID")]
        public Int16? DistributionChannelId { get; set; }

        [ProtoMember(7)]
        [DbColumnName("CONVERSIONCNT")]
        public Int32 ConversionCount { get; set; }

        [ProtoMember(8)]
        [DbColumnName("ASSISTCNT")]
        public Int32 AssistCount { get; set; }

        [ProtoMember(9)]
        [DbColumnName("ADVERTISERREPORTEDREVENUE")]
        public Decimal AdvertiserReportedRevenue { get; set; }

        [ProtoMember(10)]
        [DbColumnName("DEVICEOSID")]
        public Int32? DeviceOSId { get; set; }

        [ProtoMember(11)]
        [DbColumnName("DEVICETYPEID")]
        public SByte? DeviceTypeId { get; set; }

        [ProtoMember(12)]
        [DbColumnName("MATCHTYPEID")]
        public SByte? MatchTypeId { get; set; }

        [ProtoMember(13)]
        [DbColumnName("BIDDEDMATCHTYPEID")]
        public SByte? BiddedMatchTypeId { get; set; }

        [ProtoMember(14)]
        [DbColumnName("DEVICEOSID2")]
        public Int32? DeviceOSId2 { get; set; }

        [ProtoMember(15)]
        [DbColumnName("CAMPAIGNID")]
        [AcceptableDsvType(typeof(UInt64?))]
        public Int64? CampaignId { get; set; }

        [ProtoMember(16)]
        [DbColumnName("NETWORKID")]
        public SByte? NetworkId { get; set; }

        public Int32? CustomerId { get; set; }

        [ProtoMember(17)]
        [DbColumnName("LOADTIME")]
        public DateTime LoadTime { get; set; }

        [ProtoMember(18)]
        [DbColumnName("FULLCONVERSIONCNT")]
        public Int32 FullConversionCount { get; set; }

        [ProtoMember(19)]
        [DbColumnName("FULLADVERTISERREPORTEDREVENUE")]
        public Decimal FullAdvertiserReportedRevenue { get; set; }

        [ProtoMember(20)]
        [DbColumnName("TargetValueId")]
        [AcceptableDsvType(typeof(Int32))]
        public Int64 TargetValueId { get; set; }

        [ProtoMember(21)]
        [DbColumnName("TargetTypeId")]
        public Byte TargetTypeId { get; set; }
    }
}
