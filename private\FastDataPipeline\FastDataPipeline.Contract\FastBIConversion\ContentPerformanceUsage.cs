namespace FastDataPipeline.Contract.FastBIConversion
{
    using ProtoBuf;
    using System;

    [ClickhouseCsvContract(schema: "BICHConv", columnList: new[] { nameof(DateKey), nameof(HourNum), nameof(AccountId), nameof(OrderId), nameof(PropertyUrl), nameof(DistributionChannelId),
        nameof(ConversionCount), nameof(AssistCount), nameof(AdvertiserReportedRevenue),
        nameof(DeviceOSId), nameof(DeviceTypeId), nameof(MatchTypeId), nameof(BiddedMatchTypeId), nameof(DeviceOSId2), nameof(CampaignId), nameof(NetworkId),
        "(t) => {return t.LoadTime.ToString(\"yyyy-MM-dd HH:mm:ss\");}", nameof(FullConversionCount), nameof(FullAdvertiserReportedRevenue), nameof(TargetValueId), nameof(TargetTypeId),
        nameof(AdvertiserReportedRevenueAdjustment), nameof(FullAdvertiserReportedRevenueAdjustment), nameof(WebSiteCountry), nameof(AdLanguage),
        nameof(MobileAppName), nameof(MobileAppBundle), nameof(MobileAppStoreUrl), nameof(Downloads), nameof(FirstLaunches), nameof(Purchases), nameof(Subscriptions) },
        Header = "DateKey,HourNum,AccountId,OrderId,PropertyUrl,DistributionChannelId,ConversionCnt,AssistCnt,AdvertiserReportedRevenue,DeviceOSId,DeviceTypeId,MatchTypeId,BiddedMatchTypeId,DeviceOSId2,CampaignId,NetworkId,LoadTime,FullConversionCnt,FullAdvertiserReportedRevenue,TargetValueId,TargetTypeId,AdvertiserReportedRevenueAdjustment,FullAdvertiserReportedRevenueAdjustment,WebsiteCountry,AdLanguage,AppName,AppBundle,AppStoreUrl,Downloads,FirstLaunches,Purchases,Subscriptions")]

    [ProtoContract]
    public partial class ContentPerformanceUsage
    {
        [ProtoMember(1)]
        [DbColumnName("DATEKEY")]
        public Int32? DateKey { get; set; }

        [ProtoMember(2)]
        [DbColumnName("HOURNUM")]
        public SByte? HourNum { get; set; }

        [ProtoMember(3)]
        [DbColumnName("ACCOUNTID")]
        public Int32? AccountId { get; set; }

        [ProtoMember(4)]
        [DbColumnName("ORDERID")]
        [AcceptableDsvType(typeof(UInt64?))]
        public Int64? OrderId { get; set; }

        [ProtoMember(5)]
        [DbColumnName("PROPERTYURL")]
        public String PropertyUrl { get; set; }

        [ProtoMember(6)]
        [DbColumnName("DISTRIBUTIONCHANNELID")]
        public Int16? DistributionChannelId { get; set; }

        [ProtoMember(7)]
        [DbColumnName("CONVERSIONCNT")]
        public Int32 ConversionCount { get; set; }

        [ProtoMember(8)]
        [DbColumnName("ASSISTCNT")]
        public Int32 AssistCount { get; set; }

        [ProtoMember(9)]
        [DbColumnName("ADVERTISERREPORTEDREVENUE")]
        public Decimal AdvertiserReportedRevenue { get; set; }

        [ProtoMember(10)]
        [DbColumnName("DEVICEOSID")]
        public Int32? DeviceOSId { get; set; }

        [ProtoMember(11)]
        [DbColumnName("DEVICETYPEID")]
        public SByte? DeviceTypeId { get; set; }

        [ProtoMember(12)]
        [DbColumnName("MATCHTYPEID")]
        public SByte? MatchTypeId { get; set; }

        [ProtoMember(13)]
        [DbColumnName("BIDDEDMATCHTYPEID")]
        public SByte? BiddedMatchTypeId { get; set; }

        [ProtoMember(14)]
        [DbColumnName("DEVICEOSID2")]
        public Int32? DeviceOSId2 { get; set; }

        [ProtoMember(15)]
        [DbColumnName("CAMPAIGNID")]
        [AcceptableDsvType(typeof(UInt64?))]
        public Int64? CampaignId { get; set; }

        [ProtoMember(16)]
        [DbColumnName("NETWORKID")]
        public SByte? NetworkId { get; set; }

        public Int32? CustomerId { get; set; }

        [ProtoMember(17)]
        [DbColumnName("LOADTIME")]
        public DateTime LoadTime { get; set; }

        [ProtoMember(18)]
        [DbColumnName("FULLCONVERSIONCNT")]
        public Int32 FullConversionCount { get; set; }

        [ProtoMember(19)]
        [DbColumnName("FULLADVERTISERREPORTEDREVENUE")]
        public Decimal FullAdvertiserReportedRevenue { get; set; }

        [ProtoMember(20)]
        [DbColumnName("TargetValueId")]
        [AcceptableDsvType(typeof(Int32))]
        public Int64 TargetValueId { get; set; }

        [ProtoMember(21)]
        [DbColumnName("TargetTypeId")]
        public Byte TargetTypeId { get; set; }

        [ProtoMember(22)]
        [DbColumnName("AdvertiserReportedRevenueAdjustment")]
        public Decimal AdvertiserReportedRevenueAdjustment { get; set; }

        [ProtoMember(23)]
        [DbColumnName("FullAdvertiserReportedRevenueAdjustment")]
        public Decimal FullAdvertiserReportedRevenueAdjustment { get; set; }

        [ProtoMember(24)]
        [DbColumnName("WebsiteCountry")]
        public String WebSiteCountry { get; set; }

        [ProtoMember(25)]
        [DbColumnName("AdLanguage")]
        public String AdLanguage { get; set; }

        [ProtoMember(26)]
        [DbColumnName("AppName")]
        public String MobileAppName { get; set; }

        [ProtoMember(27)]
        [DbColumnName("AppBundle")]
        public String MobileAppBundle { get; set; }

        [ProtoMember(28)]
        [DbColumnName("AppStoreUrl")]
        public String MobileAppStoreUrl { get; set; }

        [ProtoMember(29)]
        [DbColumnName("Downloads")]
        public Decimal Downloads { get; set; }

        [ProtoMember(30)]
        [DbColumnName("FirstLaunches")]
        public Decimal FirstLaunches { get; set; }

        [ProtoMember(31)]
        [DbColumnName("Purchases")]
        public Decimal Purchases { get; set; }

        [ProtoMember(32)]
        [DbColumnName("Subscriptions")]
        public Decimal Subscriptions { get; set; }
    }
}
