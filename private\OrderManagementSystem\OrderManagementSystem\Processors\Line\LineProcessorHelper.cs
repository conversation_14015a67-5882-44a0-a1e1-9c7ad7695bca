using OrderManagementSystem.Common;
using OrderManagementSystem.Common.BusinessLogicHelpers;
using OrderManagementSystem.DataAccessObjects;
using OrderManagementSystem.Entities;
using OrderManagementSystem.Entities.External;
using OrderManagementSystem.Entities.Internal;
using OrderManagementSystem.Requests;
using OrderManagementSystem.Configuration;
using System.Text.Json;

namespace OrderManagementSystem.Processors
{
    public interface ILineProcessorHelper
    {
        public Task<Result<LineOutput>> FillRichObjects(ILogger logger, int customerId, Line line, ScoreCardOutput? scoreCardOutput = null, Product? product = null);
        public Task<Result> DeleteLineFromMonetize(LineDeleteRequest request, Line line);
        public Result UpdateLineType(ILogger logger, Line line, Product product);
        public bool IsApprovalNeeded(ILogger logger, Line line, Product product, ScoreCardOutput? scoreCardOutput, out PublisherApprovalType approvalType, out PublisherApprovalReason approvalReason);
        public bool CheckTargetingUpdated(Line curLine, Line prevLine);
        public Result HandleLineReservations(Line currentLine, Line? previousLine, Product product, string source);
        public IEnumerable<LineOutput>? Sort(List<LineOutput> result, String orderBy);
        public IEnumerable<LineOutput>? Filter(IEnumerable<LineOutput>? result, string filterString);
    }

    public class LineProcessorHelper : ILineProcessorHelper
    {
        private IProductDao _productDao;
        private ICustomerDao _customerDao;
        private IMediaPlanDao _mediaPlanDao;
        private IXandrApiHelper<string> _xandrApiHelper;
        private IPublisherDao _publisherDao;
        private IForecastDao _forecastDao;

        public static readonly Dictionary<string, Func<LineOutput, Object>> FieldMap = new Dictionary<string, Func<LineOutput, Object>>
        {
            { "Name", (ln => ln.Name) },
            { "ID", (ln => ln.Id) },
            { "Status", (ln => ln.Status) },
            { "Type", (ln => ln.Type) },
            { "StartDate", (ln => ln.StartDate!) },
            { "EndDate", (ln => ln.EndDate!) },
            { "Budget", (ln => ln.TargetSpend!) },
            { "EstimatedImpressions", (ln => ln.EstimatedImpressions!) },
        };

        public LineProcessorHelper(
            IProductDao productDao,
            ICustomerDao customerDao,
            IMediaPlanDao mediaPlanDao,
            IXandrApiHelper<string> xandrApiHelper,
            IPublisherDao publisherDao,
            IForecastDao forecastDao)
        {
            _productDao = productDao;
            _customerDao = customerDao;
            _mediaPlanDao = mediaPlanDao;
            _xandrApiHelper = xandrApiHelper;
            _publisherDao = publisherDao;
            _forecastDao = forecastDao;
        }

        public async Task<Result<LineOutput>> FillRichObjects(ILogger logger, int customerId, Line line, ScoreCardOutput? scoreCardOutput, Product? product = null)
        {
            var result = new Result<LineOutput>();

            //At Line level, we want to get the product whether it is active or deleted
            if (product == null)
            {
                //If Product is not provided, get Product details from DB
                var getProductRequest = new ProductGetNoPublisherRequest(logger, [line.ProductId], new List<ProductStatus>() { ProductStatus.Active, ProductStatus.Deleted });
                var productResult = await _productDao.GetNoPublisher(getProductRequest);
                result.Merge(productResult);
                if (result.Failed) { return result; }

                product = productResult.Entity![0];
            }

            var customerResult = await _customerDao.Get(new CustomerGetRequest(logger, customerId));
            result.Merge(customerResult);
            if (result.Failed) { return result; }

            var mediaPlanResult = await _mediaPlanDao.Get(new MediaPlanGetRequest(logger, customerId, line.MediaPlanId));
            result.Merge(mediaPlanResult);
            if (result.Failed) { return result; }

            var publisherResult = await _publisherDao.Get(new PublisherGetRequest(logger, product.PublisherId));
            result.Merge(publisherResult);
            if (result.Failed) { return result; }

            var entity = line.ToOutput(product.ToLiteOutput(publisherResult.Entity!), customerResult.Entity!, mediaPlanResult.Entity!.ToLiteOutput(), product.Slot, scoreCardOutput);
            
            // Update Units for SOV lines
            if (line.Type == LineType.SOV)
            {
                // Fetch the forecast for this line
                var forecastResult = await _forecastDao.Get(new ForecastGetRequest(logger, customerId, line.MediaPlanId, line.Id));

                int? percentImpressions = line.PercentImpressions;
                entity.Units = LineBusinessLogicHelper.GetTargetImpressionsForSOV(forecastResult.Entity != null ? forecastResult.Entity.TotalImpressions : 0, percentImpressions);
            }

            result.Entity = entity;

            return result;
        }

        public async Task<Result> DeleteLineFromMonetize(LineDeleteRequest request, Line line)
        {
            var result = new Result();

            var monetizeDeleteUrl = $"{XandrApiHelper.XandrLineItemCommitBaseUrl}?id={line.MonetizeLineItemId!}&advertiser_id={line.CustomerId}";
            var deleteResponse = await _xandrApiHelper.Delete(request, monetizeDeleteUrl);
            result.Merge(deleteResponse);

            return result;
        }

        public Result UpdateLineType(ILogger logger, Line line, Product product)
        {
            var result = new Result();

            if (line.Type != LineType._)
            {
                // If line.Type is already set and valid, use it
                return result;
            }

            // Otherwise, set the LineType based on the ProductType
            switch (product.Type)
            {
                case ProductType.Roadblock:
                    line.Type = LineType.Roadblock;
                    break;
                case ProductType.Rotational:
                    line.Type = LineType.Rotational;
                    break;
                case ProductType.Bulk:
                    line.Type = LineType.Bulk;
                    break;
                case ProductType.SOV:
                    line.Type = LineType.SOV;
                    break;
                default:
                    result.AddError(new Error(source: $"UpdateLineType, Line: {line}", message: ErrorMessage.InvalidValue, property: [nameof(line.Type)]));
                    break;
            }

            return result;
        }

        public bool IsApprovalNeeded(ILogger logger, Line line, Product product, ScoreCardOutput? scoreCardOutput, out PublisherApprovalType approvalType, out PublisherApprovalReason approvalReason)
        {
            approvalType = PublisherApprovalType._;
            approvalReason = PublisherApprovalReason._;
            var needApproval = false;

            //Check for YA override needed (skip in local/CI environments)
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")?.ToUpperInvariant() ?? ServiceHelper.TEST_ENVIRONMENT.ToUpperInvariant();
            var scoreCard = scoreCardOutput?.ToScoreCard();

            if (product.PublisherId == XandrConstants.LOTUS_PPE_PUBLISHER_ID)
            {
                //For this publisher we always need approval and only for this one reason
                needApproval = true;
                approvalReason = PublisherApprovalReason.ProjectFresno;
                return needApproval;
            }

            if (line.Type == LineType.MakeGood || line.Type == LineType.Bonus)
            {
                if (line.Type == LineType.MakeGood)
                {
                    approvalReason = PublisherApprovalReason.MakeGood;
                }
                else 
                {
                    approvalReason = PublisherApprovalReason.Bonus;
                }
                needApproval = true;
            }
            /**
             * Note: MakeGood/Bonus lines can also require Yield Analytics override approval. 
             * They may not have any spend (hence why we want to skip Discount check), but they 
             * do still try to book impressions to be served, and if YA doesn't have enough then 
             * they'd need that as an approval reason as well
             */
            if (environment != ServiceHelper.TEST_ENVIRONMENT.ToUpperInvariant() && LineBusinessLogicHelper.IsYieldAnalyticsApprovalNeeded(scoreCard, product.Type))
            {
                approvalType = PublisherApprovalType.YieldAnalyticsOverride;
                approvalReason |= PublisherApprovalReason.YieldAnalyticsOverride;
                needApproval = true;
            }

            //Check for discount approval needed - use exchange rate and target premiums, if applicable
            if (line.Type != LineType.MakeGood && line.Type != LineType.Bonus)
            {
                var baseCpm = product.BaseCPMRate;
                if (line.ExchangeRate.HasValue) { baseCpm *= (double)line.ExchangeRate.Value; }
                var productSupportedTargets = product.SupportedTargets!;
                var minCpm = GetBaseCpmWithTargetingPremium(baseCpm, productSupportedTargets!, line.Targets);
                if (product.MaxPercentDiscount.HasValue) { minCpm *= (100.0 - product.MaxPercentDiscount.Value) / 100.0; }
                if (line.Cpm.HasValue)
                {
                    if (line.Cpm < minCpm)
                    {
                        approvalType = PublisherApprovalType.Discount;
                        approvalReason |= PublisherApprovalReason.Discount;
                        needApproval = true;
                    }
                }
                else
                {
                    //CI will never have YA support, so always make Roadblock set to NeedApproval.
                    if (environment == ServiceHelper.TEST_ENVIRONMENT.ToUpperInvariant())
                    {
                        approvalType = PublisherApprovalType.YieldAnalyticsOverride;
                        approvalReason |= PublisherApprovalReason.YieldAnalyticsOverride;
                        needApproval = true;
                    }

                    //With validation already run, this is guaranteed to be CPD not null
                    var estimatedImpressions = scoreCardOutput?.Available ?? 0;

                    var totalCost = LineBusinessLogicHelper.CalculateTotalCostFromCpd(line.Cpd, line.StartDateTime!, line.EndDateTime!);
                    var estimatedCpm = LineBusinessLogicHelper.CalculateEstimatedCpm(totalCost, estimatedImpressions);
                    if (estimatedCpm < minCpm)
                    {
                        approvalType = PublisherApprovalType.Discount;
                        approvalReason |= PublisherApprovalReason.Discount;
                        needApproval = true;
                    }
                }
            }
            logger.LogInformation($"Line {line.Id} ({line.Name}) approval check: needApproval={needApproval}, approvalType={approvalType}, approvalReason={approvalReason}");
            return needApproval;
        }

        public bool CheckTargetingUpdated(Line curLine, Line prevLine)
        {
            var curTargets = curLine.Targets ?? Array.Empty<Target>();  // handle null vs empty array
            var prevTargets = prevLine.Targets ?? Array.Empty<Target>();  // handle null vs empty array

            if (curTargets.Length != prevTargets.Length) { return true; }
            foreach (var target in prevTargets)
            {
                if (!curTargets.Contains(target)) { return true; }
            }
            return false;
        }

        public static double GetBaseCpmWithTargetingPremium(double baseCpm, SupplyTarget[] supplyTargets, Target[]? lineTargets)
        {
            if (lineTargets == null) { return baseCpm; }

            var targetPremiums = new List<(short, double)>();
            foreach (var target in lineTargets)
            {
                var targetType = Target.GetTargetType(target);
                var supplyTarget = supplyTargets.First(supplyTarget => supplyTarget.TargetTypeId == targetType);  // existence already validated
                if (supplyTarget.TargetPremium == null) { continue; }
                targetPremiums.Add(((short)targetType, supplyTarget.TargetPremium.Value));
            }

            return LineBusinessLogicHelper.ApplyTargetsToBaseCpm(baseCpm, targetPremiums);
        }

        //Linked to PublisherApprovalOutputHelper.GetLineForPA
        public static LinePAOutput GetLineForPA(Line line, Product product, ProductTimeSlot? slot, ScoreCardOutput? scoreCard)
        {
            var rateType = RateType._;
            double? rate = null;
            var units = 0.0;
            var cost = 0.0;
            var effectiveDiscount = 0.0;

            var weightedCpm = line.ProductWeightedCPM.HasValue ? (double)line.ProductWeightedCPM.Value : (double)product.BaseCPMRate;

            switch (line.Type)
            {
                case LineType.Roadblock:
                    rateType = RateType.CPD;
                    var cpd = line.Cpd!.Value;  // cpd must be set for roadblock
                    rate = (double)cpd;

                    //var roadblockDuration = LineBusinessLogicHelper.CalculateRoadblockDuration(line.StartDate, line.EndDate) ?? 0;
                    units = LineBusinessLogicHelper.CalculateRoadblockDuration(line.StartDateTime, line.EndDateTime) ?? 0; //LineBusinessLogicHelper.CalculateRoadblockUnits(roadblockDuration, slot!.Value) ?? 0;
                    cost = LineBusinessLogicHelper.CalculateRoadblockCost(units, rate) ?? 0;

                    var availableImpressions = scoreCard == null ? 0 : scoreCard.Available;
                    var effectiveCpm = LineBusinessLogicHelper.CalculateEstimatedCpm(cost, availableImpressions);
                    effectiveDiscount = LineBusinessLogicHelper.GetEffectiveDiscount((double)weightedCpm, effectiveCpm);
                    break;
                case LineType.Rotational:
                case LineType.Bulk:
                default:
                    rateType = RateType.CPM;
                    rate = (double)line.Cpm!.Value;  // cpm must be set for rotational/bulk

                    units = LineBusinessLogicHelper.CalculateRotationalImpressions(line.TargetImpressions, line.TargetSpend, rate) ?? 0;
                    cost = LineBusinessLogicHelper.CalculateRotationalCost(line.TargetSpend, line.TargetImpressions, rate) ?? 0;

                    effectiveDiscount = LineBusinessLogicHelper.GetEffectiveDiscount((double)weightedCpm, rate.Value);
                    break;
                case LineType.MakeGood:
                case LineType.Bonus:
                    rateType = (product.Type == ProductType.Roadblock) ? RateType.CPD : RateType.CPM;
                    rate = 0;

                    units = LineBusinessLogicHelper.CalculateRotationalImpressions(line.TargetImpressions, line.TargetSpend, rate) ?? 0;
                    cost = 0;

                    effectiveDiscount = 100;
                    break;
                case LineType.SOV:
                    rateType = RateType.CPM;
                    rate = (double)line.Cpm!.Value;  // cpm must be set for SOV
                    units = LineBusinessLogicHelper.GetTargetImpressionsForSOV(scoreCard != null ? scoreCard.Total: 0, line.PercentImpressions);
                    cost = LineBusinessLogicHelper.GetSpend(line.Cpm, (long)units);
                    effectiveDiscount = LineBusinessLogicHelper.GetEffectiveDiscount((double)weightedCpm, rate.Value);
                    break;
            }

            return new LinePAOutput
            {
                Id = line.Id,
                Name = line.Name,
                RateType = rateType,
                Rate = rate ?? 0,
                EffectiveDiscount = effectiveDiscount,
                StartDate = line.StartDateTime!.Value,
                EndDate = line.EndDateTime!.Value,
                Units = units,
                Cost = cost,
            };
        }

        public Result HandleLineReservations(Line currentLine, Line? previousLine, Product product, string source)
        {
            var result = new Result();

            if (currentLine.IsReserved && previousLine?.IsReserved != true)
            {
                if (currentLine.Type != LineType.Roadblock)
                {
                    result.AddError(new Error(source: source, message: ErrorMessage.OperationNotAllowed, property: [nameof(currentLine.IsReserved)]));
                }

                if (currentLine.MonetizeLineItemId.HasValue || currentLine.Status == LineStatus.Denied)
                {
                    result.AddError(new Error(source: source, message: ErrorMessage.OperationNotAllowed, property: [nameof(currentLine.IsReserved)]));
                }

                if (!product.MaxReservationInHours.HasValue || product.MaxReservationInHours == 0)
                {
                    result.AddError(new Error(source: source, message: ErrorMessage.OperationNotAllowed, property: [nameof(currentLine.IsReserved)]));
                }

                if (currentLine.Status == LineStatus.NeedApproval || currentLine.Status == LineStatus.PendingApproval)
                {
                    result.AddWarning(new Warning(source: source, message: ErrorMessage.OperationNotAllowed, property: [nameof(currentLine.IsReserved)]));
                    currentLine.IsReserved = false;
                }

            }
            return result;
        }

        public IEnumerable<LineOutput>? Filter(IEnumerable<LineOutput>? result, string filterString)
        {
            var filters = JsonSerializer.Deserialize<FilterInput[]>(filterString, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? Array.Empty<FilterInput>();

            foreach (var filter in filters)
            {
                switch (filter.Field)
                {
                    case "ID":
                        filter.Value = filter.Value!.ToString()!;
                        result = filter.Operator == "eq"
                            ? result?.Where(pa => FieldMap[filter.Field](pa).ToString() == filter.Value.ToString())
                            : result?.Where(pa => FieldMap[filter.Field](pa).ToString()!.Contains(filter.Value.ToString()!));
                        break;

                    case "Name":
                        if (filter.Operator == "eq")
                        {
                            result = result?.Where(ln => FieldMap[filter.Field](ln)?.ToString() == filter.Value?.ToString());
                        }
                        else if (filter.Operator == "contains")
                        {
                            result = result?.Where(ln => FieldMap[filter.Field](ln)?.ToString()?.Contains(filter.Value?.ToString() ?? "") ?? false);
                        }
                        break;

                    case "Status":
                        if (filter.Operator == "eq")
                        {
                            var enumValue = Enum.Parse<LineStatus>(filter.Value?.ToString()!);
                            result = result?.Where(ln => ln.Status == enumValue);
                        }
                        else if (filter.Operator == "in")
                        {
                            var values = JsonSerializer.Deserialize<string[]>(JsonSerializer.Serialize(filter.Value));
                            var enums = values!.Select(v => Enum.Parse<LineStatus>(v)).ToArray();
                            result = result?.Where(ln => enums.Contains(ln.Status));
                        }
                        break;
                    case "Type":
                        if (filter.Operator == "eq")
                        {
                            var enumValue = Enum.Parse<LineType>(filter.Value?.ToString()!);
                            result = result?.Where(ln => ln.Type == enumValue);
                        }
                        else if (filter.Operator == "in")
                        {
                            var values = JsonSerializer.Deserialize<string[]>(JsonSerializer.Serialize(filter.Value));
                            var enums = values!.Select(v => Enum.Parse<LineType>(v)).ToArray();
                            result = result?.Where(ln => enums.Contains(ln.Type));
                        }
                        break;

                    case "Budget":
                    case "EstimatedImpressions":
                        if (filter.Operator == "eq")
                        {
                            result = result?.Where(p => FieldMap[filter.Field](p)?.ToString() == filter.Value?.ToString());
                        }
                        else if (filter.Operator == "lt")
                        {
                            var parsedDouble = double.Parse(filter.Value?.ToString()!);
                            result = result?.Where(p => Convert.ToDouble(FieldMap[filter.Field](p)) < parsedDouble);
                        }
                        else if (filter.Operator == "le")
                        {
                            var parsedDouble = double.Parse(filter.Value?.ToString()!);
                            result = result?.Where(p => Convert.ToDouble(FieldMap[filter.Field](p)) <= parsedDouble);
                        }
                        else if (filter.Operator == "gt")
                        {
                            var parsedDouble = double.Parse(filter.Value?.ToString()!);
                            result = result?.Where(p => Convert.ToDouble(FieldMap[filter.Field](p)) > parsedDouble);
                        }
                        else if (filter.Operator == "ge")
                        {
                            var parsedDouble = double.Parse(filter.Value?.ToString()!);
                            result = result?.Where(p => Convert.ToDouble(FieldMap[filter.Field](p)) >= parsedDouble);
                        }
                        else if (filter.Operator == "between")
                        {
                            var valueArray = JsonSerializer.Deserialize<double[]>(JsonSerializer.Serialize(filter.Value));
                            result = result?.Where(p =>
                                Convert.ToDouble(FieldMap[filter.Field](p)) >= valueArray![0] &&
                                Convert.ToDouble(FieldMap[filter.Field](p)) <= valueArray[1]);
                        }
                        break;
                    case "StartDate":
                    case "EndDate":
                        if (filter.Operator != "between")
                        {
                            filter.Value = DateTime.Parse(filter.Value!.ToString() ?? string.Empty).Date;
                            switch (filter.Operator)
                            {
                                case "eq":
                                    result = result?.Where(pa => FieldMap[filter.Field](pa) is DateTime dateTime
                                    && dateTime.Date == (DateTime)filter.Value);
                                    break;
                                case "neq":
                                    result = result?.Where(pa => FieldMap[filter.Field](pa) is DateTime dateTime
                                    && dateTime.Date != (DateTime)filter.Value);
                                    break;
                                case "lt":
                                    result = result?.Where(pa => FieldMap[filter.Field](pa) is DateTime dateTime
                                    && dateTime.Date < (DateTime)filter.Value);
                                    break;
                                case "le":
                                    result = result?.Where(pa => FieldMap[filter.Field](pa) is DateTime dateTime
                                    && dateTime.Date <= (DateTime)filter.Value);
                                    break;
                                case "ge":
                                    result = result?.Where(pa => FieldMap[filter.Field](pa) is DateTime dateTime
                                    && dateTime.Date >= (DateTime)filter.Value);
                                    break;
                                case "gt":
                                    result = result?.Where(pa => FieldMap[filter.Field](pa) is DateTime dateTime
                                    && dateTime.Date > (DateTime)filter.Value);
                                    break;
                                default:
                                    throw new ArgumentException($"Invalid operator: {filter.Operator}");
                            }
                        }
                        else
                        {
                            var dateStrings = JsonSerializer.Deserialize<String[]>(JsonSerializer.Serialize(filter.Value));
                            var dateTimes = dateStrings!.Select(v => DateTime.Parse(v)).ToArray();
                            result = result?.Where(pa => FieldMap[filter.Field](pa) is DateTime dateTime
                            && dateTime.Date >= dateTimes![0].Date && dateTime.Date <= dateTimes![1].Date);

                        }
                        break;
                }
            }
            return result;
        }

        public IEnumerable<LineOutput>? Sort(List<LineOutput> result, String orderBy)
        {
            if (orderBy.Trim().Contains(' '))
            {
                String[] parts = orderBy.Split(' ');
                string orderValue = parts[0];
                string orderType = parts[1];

                return orderType.ToLower() == "desc" ? result?.OrderByDescending(FieldMap[orderValue]) : result?.OrderBy(FieldMap[orderValue]);
            }

            return result?.OrderBy(FieldMap[orderBy]);
        }

        protected class FilterInput
        {
            public string Field { get; init; }
            public string Operator { get; init; }
            public object Value { get; set; }

            public FilterInput(string field, string @operator, object value)
            {
                Field = field;
                Operator = @operator;
                Value = value;
            }
        }
    }
}
