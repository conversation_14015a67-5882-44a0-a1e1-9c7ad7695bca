﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO
{
    using System.Collections.Generic;
    using System.Linq;
    using Common;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenter;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.DAO;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.Advertiser.CampaignManagement.BusinessRules.SharedEntityValidators.Specialized;
    using Microsoft.Advertiser.CampaignManagement.BusinessRules.SharedListItemValidators;

    public class AddSharedListItemsToSharedListEO : IAddSharedListItemsToSharedListEO
    {
        private readonly IAddListItemsToSharedListDao repository;
        private readonly ISystemLimitDao systemLimitDao;
        private readonly IGetSharedEntitiesDAO getSharedEntitiesDAO;

        public AddSharedListItemsToSharedListEO(IAddListItemsToSharedListDao repository, ISystemLimitDao systemLimitDao, IGetSharedEntitiesDAO getSharedEntitiesDAO)
        {
            this.repository = repository;
            this.systemLimitDao = systemLimitDao;
            this.getSharedEntitiesDAO = getSharedEntitiesDAO;
        }

        public BatchResult<AddUpdateResult> AddSharedListItemsToSharedList(BasicCallContext context, long sharedListId, List<SharedListItem> listItems, bool isSharedEntityCustomerLevel)
        {
            var result = new BatchResult<AddUpdateResult>();

            if (isSharedEntityCustomerLevel && !Pilot.Instance.IsEnabledForManagerAccountSharedWebsiteExclusions(context.Logger, context.Request))
            {
                result.AddError(CampaignManagementErrorCode.NotInPilotForManagerAccountSharedWebsiteExclusions);
                return result;
            }

            RequestContextValidator.ValidateRequestContext(result, context, isSharedEntityCustomerLevel);

            if (result.HasOperationErrors)
            {
                return result;
            }

            string listType;
            var itemType = listItems?.FirstOrDefault();

            if (isSharedEntityCustomerLevel)
            {
                listType = nameof(PlacementExclusionList);
            }
            else if (itemType is AccountPlacementExclusionListItem || itemType is NegativeSite)
            {
                listType = nameof(AccountPlacementExclusionList);
            }
            else if (itemType is BrandItem)
            {
                listType = nameof(BrandList);
            }
            else
            {
                if (sharedListId <= 0)
                {
                    result.Errors.Add(new CampaignManagementErrorDetail(CampaignManagementErrorCode.SharedListIdInvalid));
                    return result;
                }
                List<CampaignManagementErrorCode> errors = new List<CampaignManagementErrorCode>();
                if (!this.getSharedEntitiesDAO.TryGetNegativeKeywordListType(context, sharedListId,out listType, out errors))
                {
                    int i = 0;
                    foreach(var error in errors)
                    {
                        result.BatchErrors.Add(i , new CampaignManagementErrorDetail(error));
                        i++;
                    }

                    return result;
                }
            }

            bool isExclusionListType = (listType.Equals(nameof(PlacementExclusionList)) || listType.Equals(nameof(AccountPlacementExclusionList)));
            if (isExclusionListType && Pilot.Instance.IsUrlTrimmingEnabledForWebsiteExclusionLists())
            {
                PlacementExclusionListItemPreprocessor.TrimTrailingListItemWhitespace(listItems);
            }

            bool allowMobileAppBundleIdExclusion = isExclusionListType && DynamicConfigValues.AllowMobileAppBundleIdExclusion;
            ValidateSharedListItems(result, sharedListId, listType, listItems, allowMobileAppBundleIdExclusion, isSharedEntityCustomerLevel && Pilot.Instance.IsCustomerEnabledForAllowYahooExclusionInSWF(context.Logger, context.Request));

            if (result.ShouldInterruptOperation(listItems?.Count ?? 0, true))
            {
                return result;
            }

            int systemItemLimit = GetSystemListItemLimit(result, context, listType, isSharedEntityCustomerLevel);

            if (result.ShouldInterruptOperation(listItems.Count, true))
            {
                return result;
            }

            // Validate for BrandSafety List
            if (listType == nameof(PlacementExclusionList))
            {
                ValidateForBrandSafetyList(result, context, sharedListId, listType);
            }
            if (result.HasOperationErrors)
            {
                return result;
            }

            CallDao(context, result, listType, sharedListId, isSharedEntityCustomerLevel, listItems, systemItemLimit);

            return result;
        }

        private void ValidateSharedListItems(BatchResult<AddUpdateResult> result, long listId, string sharedEntityType, List<SharedListItem> listItems, bool allowMobileAppBundleIdExclusion, bool allowYahooExclusionInSWF)
        {
            var buisnessItems = listItems?.Cast<ISharedListItem>().ToList();
            SharedListItemCustomLengthProvider customLimitProvider = new SharedListItemCustomLengthProvider();
            if (sharedEntityType == nameof(AccountNegativeKeywordList))
            {
                customLimitProvider.MaxItemLength = MiddleTierConstants.MaxLengthNegativeKeyword;
                customLimitProvider.MaxNumberOfSharedListItems = ConfigValues.NegativeKeywordMaxPerAccountNegativeKeywordList;
            }
            else if (sharedEntityType == nameof(NegativeKeywordList))
            {
                customLimitProvider.MaxItemLength = MiddleTierConstants.MaxLengthNegativeKeyword;
                customLimitProvider.MaxNumberOfSharedListItems = ConfigValues.NegativeKeywordMaxPerList;
            }
            else
                customLimitProvider = null;

            var itemValidationResult = SharedListItemValidatorMiddleTier.ValidateSharedListItemsListItemAdd(
                buisnessItems,
                listId,
                sharedEntityType,
                customLimitProvider: customLimitProvider,
                disableOutlookMsn: DynamicConfigValues.DisableOutlookMsnNegativeSite,
                isBingAllowedForSiteExclusion: DynamicConfigValues.IsBingAllowedForSiteExclusion,
                allowMobileAppBundleIdExclusion: allowMobileAppBundleIdExclusion,
                allowYahooExclusionInSWF: allowYahooExclusionInSWF);

            result.ImportBusinessErrors(itemValidationResult.Errors);
        }

        private int GetSystemListItemLimit(BatchResult<AddUpdateResult> result, BasicCallContext context, string sharedEntityType, bool isSharedEntityCustomerLevel)
        {
            if (sharedEntityType.Equals(nameof(PlacementExclusionList)) || sharedEntityType.Equals(nameof(BrandList)))
            {
                return -1; // PlacementExclusionLists and BrandList currently rely on DB to know limits
            }

            var accountContext = new AccountCallContext(context.Logger, context.Request, context.AccountId.Value);
            BatchResult<int> nkwsPerListResult = new BatchResult<int>();
            if (sharedEntityType == nameof(AccountNegativeKeywordList))
            {
                nkwsPerListResult = this.systemLimitDao.GetSystemLimits(accountContext, new List<SystemLimitTypes> { SystemLimitTypes.NegativeKeywordsPerAccountNegativeKeywordList });
            }
            else
            {
                nkwsPerListResult = this.systemLimitDao.GetSystemLimits(accountContext, new List<SystemLimitTypes> { SystemLimitTypes.NegativeKeywordPerList });
            }
            
            if (!nkwsPerListResult.Succeeded)
            {
                result.Errors.AddRange(nkwsPerListResult.Errors);
                if (nkwsPerListResult.BatchErrors.TryGetValue(0, out List<CampaignManagementErrorDetail> batchErrors))
                {
                    result.Errors.AddRange(batchErrors);
                }

                return -1;
            }

            return nkwsPerListResult.Entities[0];
        }

        private void ValidateForBrandSafetyList(
          BaseResult result,
          BasicCallContext context,
          long sharedListId,
          string sharedEntityType)
        {
            var isBrandSaftyExclusionLists = new List<long>();
            var sharedEntityResult = this.getSharedEntitiesDAO.GetSharedEntitiesWithoutAssociationCounts(
                context,
                sharedEntityType,
                true);

            if (sharedEntityResult.Entities != null && sharedEntityResult.Entities.Count > 0)
            {
                var isBrandSafetysharedEntity = sharedEntityResult.Entities.Where(e => e.IsBrandSafety != null && e.IsBrandSafety == true).ToHashSet(e => e.Id);
                RequestContextValidator brandSafetyValidator = new RequestContextValidator();
                if (isBrandSafetysharedEntity.Any() && isBrandSafetysharedEntity.Contains(sharedListId) && !brandSafetyValidator.IsValidUserForBrandSafetyListOperation(context))
                {
                    result.AddError(CampaignManagementErrorCode.InvalidActionType);
                }
            }
        }

        private void CallDao(
            BasicCallContext context,
            BatchResult<AddUpdateResult> result,
            string sharedListType,
            long sharedListId,
            bool isSharedEntityCustomerLevel,
            List<SharedListItem> listItems,
            int systemItemLimit)
        {
            BatchResult<AddUpdateResult> daoResult = this.repository.AddListItemsToSharedList(context, result, sharedListType, sharedListId, isSharedEntityCustomerLevel, listItems, systemItemLimit);
            result.Merge(daoResult);
        }
    }
}
