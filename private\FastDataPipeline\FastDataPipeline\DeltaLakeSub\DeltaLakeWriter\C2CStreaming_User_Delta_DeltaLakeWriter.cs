//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by DeltaLakeWriterGen.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
using <PERSON><PERSON><PERSON>;
using Parquet.Data;
using Newtonsoft.Json;

namespace FastDataPipeline.DeltaLakeSub
{
    public class C2CStreaming_User_Delta_DeltaLakeWriter : IDeltaLakeWriter<FastDataPipeline.Contract.CDR.User_Delta>
    {
        private int rowGroupSize;
        private Stream stream;
        private bool appendFile;
        private FastDataPipeline.DeltaLakeSub.DeltaTableStats stats = new FastDataPipeline.DeltaLakeSub.DeltaTableStats();

        public int Version => 3;
        public string SchemaString => "{\"type\":\"struct\",\"fields\":[{\"name\":\"UserId\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"ArchiveDTim\",\"type\":\"timestamp\",\"nullable\":true,\"metadata\":{}},{\"name\":\"FraudStatusId\",\"type\":\"short\",\"nullable\":true,\"metadata\":{}},{\"name\":\"TradeScreeningStatusId\",\"type\":\"short\",\"nullable\":true,\"metadata\":{}},{\"name\":\"EventTime\",\"type\":\"timestamp\",\"nullable\":true,\"metadata\":{}},{\"name\":\"DeltaPartitionId\",\"type\":\"date\",\"nullable\":true,\"metadata\":{}},{\"name\":\"PartitionId\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"BatchId\",\"type\":\"short\",\"nullable\":true,\"metadata\":{}},{\"name\":\"PersonId\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"Timestamp\",\"type\":\"binary\",\"nullable\":true,\"metadata\":{}},{\"name\":\"RowId\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"Code\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"UsercontactMedia\",\"type\":\"short\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SecretAnswer\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SecretQuestionId\",\"type\":\"short\",\"nullable\":true,\"metadata\":{}},{\"name\":\"ActionFlag\",\"type\":\"short\",\"nullable\":true,\"metadata\":{}},{\"name\":\"ModifiedByUserId\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"CreatedByUserId\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"ModifiedDTim\",\"type\":\"timestamp\",\"nullable\":true,\"metadata\":{}},{\"name\":\"CreatedDTim\",\"type\":\"timestamp\",\"nullable\":true,\"metadata\":{}},{\"name\":\"LifeCycleStatusId\",\"type\":\"short\",\"nullable\":true,\"metadata\":{}},{\"name\":\"LCID\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"ContactInfoId\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AddressId\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"CustomerId\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"PUID\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"Email\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"JobTitle\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"LastName\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MiddleInitial\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"FirstName\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"UserName\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"PubProcessId\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"PublisherId\",\"type\":\"short\",\"nullable\":true,\"metadata\":{}}]}";
        public string Statistics => JsonConvert.SerializeObject(this.stats);

        // Variables for storing column values
        private List<int?> userId;
        private List<DateTimeOffset?> archiveDTim;
        private List<short?> fraudStatusId;
        private List<short?> tradeScreeningStatusId;
        private List<DateTimeOffset> eventTime;
        private List<DateTimeOffset> deltaPartitionId;
        private List<int> partitionId;
        private List<short> batchId;
        private List<long?> personId;
        private List<byte[]> timestamp;
        private List<long?> rowId;
        private List<string> code;
        private List<short?> usercontactMedia;
        private List<string> secretAnswer;
        private List<short?> secretQuestionId;
        private List<short?> actionFlag;
        private List<int?> modifiedByUserId;
        private List<int?> createdByUserId;
        private List<DateTimeOffset?> modifiedDTim;
        private List<DateTimeOffset?> createdDTim;
        private List<short?> lifeCycleStatusId;
        private List<int?> lCID;
        private List<int?> contactInfoId;
        private List<int?> addressId;
        private List<int?> customerId;
        private List<string> pUID;
        private List<string> email;
        private List<string> jobTitle;
        private List<string> lastName;
        private List<string> middleInitial;
        private List<string> firstName;
        private List<string> userName;
        private List<long> pubProcessId;
        private List<short> publisherId;
        private Func<FastDataPipeline.Contract.CDR.User_Delta, DateTimeOffset> func0 = t => FastDataPipeline.DeltaLakeSub.C2CDeltaLakeEventTimeUtil.Get();
        private Func<FastDataPipeline.Contract.CDR.User_Delta, DateTimeOffset> func1 = t => FastDataPipeline.DeltaLakeSub.C2CDeltaLakeEventTimeUtil.Get();
        private Func<FastDataPipeline.Contract.CDR.User_Delta, int> func2 = t => (t.UserId ?? 0) % 16;
        private Func<FastDataPipeline.Contract.CDR.User_Delta, short> func3 = t => 0;

        public C2CStreaming_User_Delta_DeltaLakeWriter(int rowGroupSize, int rowsInBatch, Stream stream)
        {
            this.rowGroupSize = rowGroupSize;
            this.stream = stream;
            this.appendFile = false;
            Reset(rowsInBatch);
        }

        public void Dispose()
        {
            GC.SuppressFinalize(this);
        }

        public void UpdateStream(Stream stream)
        {
            this.stream = stream;
            this.appendFile = false;
        }

        public bool Write(FastDataPipeline.Contract.CDR.User_Delta item)
        {
            //Input: single item
            //Method will write this item's column values to the respective column arrays
            userId.Add(item.UserId);
            archiveDTim.Add(item.ArchiveDTim);
            fraudStatusId.Add(item.FraudStatusId);
            tradeScreeningStatusId.Add(item.TradeScreeningStatusId);
            eventTime.Add(func0(item));
            deltaPartitionId.Add(func1(item));
            partitionId.Add(func2(item));
            batchId.Add(func3(item));
            personId.Add(item.PersonId);
            timestamp.Add(item.Timestamp);
            rowId.Add(item.RowId);
            code.Add(item.Code);
            usercontactMedia.Add(item.UsercontactMedia);
            secretAnswer.Add(item.SecretAnswer);
            secretQuestionId.Add(item.SecretQuestionId);
            actionFlag.Add(item.ActionFlag);
            modifiedByUserId.Add(item.ModifiedByUserId);
            createdByUserId.Add(item.CreatedByUserId);
            modifiedDTim.Add(item.ModifiedDTim);
            createdDTim.Add(item.CreatedDTim);
            lifeCycleStatusId.Add(item.LifeCycleStatusId);
            lCID.Add(item.LCID);
            contactInfoId.Add(item.ContactInfoId);
            addressId.Add(item.AddressId);
            customerId.Add(item.CustomerId);
            pUID.Add(item.PUID);
            email.Add(item.Email);
            jobTitle.Add(item.JobTitle);
            lastName.Add(item.LastName);
            middleInitial.Add(item.MiddleInitial);
            firstName.Add(item.FirstName);
            userName.Add(item.UserName);
            pubProcessId.Add(item.PubProcessId);
            publisherId.Add(item.PublisherId);

            if (userId.Count >= this.rowGroupSize)
            {
                return true;
            }

            return false;
        }

        public void Flush(int rowsInNextBatch)
        {
             this.stats = new FastDataPipeline.DeltaLakeSub.DeltaTableStats();
            //Method will create the Parquet columns with the data arrays created by Write method and actually upload the file
            var userIdColumn = new DataColumn(new DataField("UserId", typeof(int?)), userId.ToArray());
            var archiveDTimColumn = new DataColumn(new DataField("ArchiveDTim", typeof(DateTimeOffset?)), archiveDTim.ToArray());
            var fraudStatusIdColumn = new DataColumn(new DataField("FraudStatusId", typeof(short?)), fraudStatusId.ToArray());
            var tradeScreeningStatusIdColumn = new DataColumn(new DataField("TradeScreeningStatusId", typeof(short?)), tradeScreeningStatusId.ToArray());
            var eventTimeColumn = new DataColumn(new DataField("EventTime", typeof(DateTimeOffset)), eventTime.ToArray());
            var deltaPartitionIdColumn = new DataColumn(new DateTimeDataField("DeltaPartitionId", DateTimeFormat.Date), deltaPartitionId.ToArray());
            var partitionIdColumn = new DataColumn(new DataField("PartitionId", typeof(int)), partitionId.ToArray());
            var batchIdColumn = new DataColumn(new DataField("BatchId", typeof(short)), batchId.ToArray());
            var personIdColumn = new DataColumn(new DataField("PersonId", typeof(long?)), personId.ToArray());
            var timestampColumn = new DataColumn(new DataField("Timestamp", typeof(byte[])), timestamp.ToArray());
            var rowIdColumn = new DataColumn(new DataField("RowId", typeof(long?)), rowId.ToArray());
            var codeColumn = new DataColumn(new DataField("Code", typeof(string)), code.ToArray());
            var usercontactMediaColumn = new DataColumn(new DataField("UsercontactMedia", typeof(short?)), usercontactMedia.ToArray());
            var secretAnswerColumn = new DataColumn(new DataField("SecretAnswer", typeof(string)), secretAnswer.ToArray());
            var secretQuestionIdColumn = new DataColumn(new DataField("SecretQuestionId", typeof(short?)), secretQuestionId.ToArray());
            var actionFlagColumn = new DataColumn(new DataField("ActionFlag", typeof(short?)), actionFlag.ToArray());
            var modifiedByUserIdColumn = new DataColumn(new DataField("ModifiedByUserId", typeof(int?)), modifiedByUserId.ToArray());
            var createdByUserIdColumn = new DataColumn(new DataField("CreatedByUserId", typeof(int?)), createdByUserId.ToArray());
            var modifiedDTimColumn = new DataColumn(new DataField("ModifiedDTim", typeof(DateTimeOffset?)), modifiedDTim.ToArray());
            var createdDTimColumn = new DataColumn(new DataField("CreatedDTim", typeof(DateTimeOffset?)), createdDTim.ToArray());
            var lifeCycleStatusIdColumn = new DataColumn(new DataField("LifeCycleStatusId", typeof(short?)), lifeCycleStatusId.ToArray());
            var lCIDColumn = new DataColumn(new DataField("LCID", typeof(int?)), lCID.ToArray());
            var contactInfoIdColumn = new DataColumn(new DataField("ContactInfoId", typeof(int?)), contactInfoId.ToArray());
            var addressIdColumn = new DataColumn(new DataField("AddressId", typeof(int?)), addressId.ToArray());
            var customerIdColumn = new DataColumn(new DataField("CustomerId", typeof(int?)), customerId.ToArray());
            var pUIDColumn = new DataColumn(new DataField("PUID", typeof(string)), pUID.ToArray());
            var emailColumn = new DataColumn(new DataField("Email", typeof(string)), email.ToArray());
            var jobTitleColumn = new DataColumn(new DataField("JobTitle", typeof(string)), jobTitle.ToArray());
            var lastNameColumn = new DataColumn(new DataField("LastName", typeof(string)), lastName.ToArray());
            var middleInitialColumn = new DataColumn(new DataField("MiddleInitial", typeof(string)), middleInitial.ToArray());
            var firstNameColumn = new DataColumn(new DataField("FirstName", typeof(string)), firstName.ToArray());
            var userNameColumn = new DataColumn(new DataField("UserName", typeof(string)), userName.ToArray());
            var pubProcessIdColumn = new DataColumn(new DataField("PubProcessId", typeof(long)), pubProcessId.ToArray());
            var publisherIdColumn = new DataColumn(new DataField("PublisherId", typeof(short)), publisherId.ToArray());

            var schema = new Schema(
                userIdColumn.Field,
                archiveDTimColumn.Field,
                fraudStatusIdColumn.Field,
                tradeScreeningStatusIdColumn.Field,
                eventTimeColumn.Field,
                deltaPartitionIdColumn.Field,
                partitionIdColumn.Field,
                batchIdColumn.Field,
                personIdColumn.Field,
                timestampColumn.Field,
                rowIdColumn.Field,
                codeColumn.Field,
                usercontactMediaColumn.Field,
                secretAnswerColumn.Field,
                secretQuestionIdColumn.Field,
                actionFlagColumn.Field,
                modifiedByUserIdColumn.Field,
                createdByUserIdColumn.Field,
                modifiedDTimColumn.Field,
                createdDTimColumn.Field,
                lifeCycleStatusIdColumn.Field,
                lCIDColumn.Field,
                contactInfoIdColumn.Field,
                addressIdColumn.Field,
                customerIdColumn.Field,
                pUIDColumn.Field,
                emailColumn.Field,
                jobTitleColumn.Field,
                lastNameColumn.Field,
                middleInitialColumn.Field,
                firstNameColumn.Field,
                userNameColumn.Field,
                pubProcessIdColumn.Field,
                publisherIdColumn.Field);
            using (var parquetWriter = new ParquetWriter(schema, this.stream, append: appendFile))
            {
                using (ParquetRowGroupWriter groupWriter = parquetWriter.CreateRowGroup())
                {
                    groupWriter.WriteColumn(userIdColumn);
                    this.stats.MinValues.Add("UserId", userIdColumn.Statistics.MinValue);
                    this.stats.MaxValues.Add("UserId", userIdColumn.Statistics.MaxValue);
                    this.stats.NullCount.Add("UserId", userIdColumn.Statistics.NullCount);
                    groupWriter.WriteColumn(archiveDTimColumn);
                    this.stats.MinValues.Add("ArchiveDTim", archiveDTimColumn.Statistics.MinValue);
                    this.stats.MaxValues.Add("ArchiveDTim", archiveDTimColumn.Statistics.MaxValue);
                    this.stats.NullCount.Add("ArchiveDTim", archiveDTimColumn.Statistics.NullCount);
                    groupWriter.WriteColumn(fraudStatusIdColumn);
                    groupWriter.WriteColumn(tradeScreeningStatusIdColumn);
                    groupWriter.WriteColumn(eventTimeColumn);
                    groupWriter.WriteColumn(deltaPartitionIdColumn);
                    groupWriter.WriteColumn(partitionIdColumn);
                    groupWriter.WriteColumn(batchIdColumn);
                    groupWriter.WriteColumn(personIdColumn);
                    groupWriter.WriteColumn(timestampColumn);
                    groupWriter.WriteColumn(rowIdColumn);
                    groupWriter.WriteColumn(codeColumn);
                    groupWriter.WriteColumn(usercontactMediaColumn);
                    groupWriter.WriteColumn(secretAnswerColumn);
                    groupWriter.WriteColumn(secretQuestionIdColumn);
                    groupWriter.WriteColumn(actionFlagColumn);
                    groupWriter.WriteColumn(modifiedByUserIdColumn);
                    groupWriter.WriteColumn(createdByUserIdColumn);
                    groupWriter.WriteColumn(modifiedDTimColumn);
                    groupWriter.WriteColumn(createdDTimColumn);
                    groupWriter.WriteColumn(lifeCycleStatusIdColumn);
                    groupWriter.WriteColumn(lCIDColumn);
                    groupWriter.WriteColumn(contactInfoIdColumn);
                    groupWriter.WriteColumn(addressIdColumn);
                    groupWriter.WriteColumn(customerIdColumn);
                    groupWriter.WriteColumn(pUIDColumn);
                    groupWriter.WriteColumn(emailColumn);
                    groupWriter.WriteColumn(jobTitleColumn);
                    groupWriter.WriteColumn(lastNameColumn);
                    groupWriter.WriteColumn(middleInitialColumn);
                    groupWriter.WriteColumn(firstNameColumn);
                    groupWriter.WriteColumn(userNameColumn);
                    groupWriter.WriteColumn(pubProcessIdColumn);
                    groupWriter.WriteColumn(publisherIdColumn);

                }
            }

            this.stats.NumRecords = userId.Count;
            Reset(rowsInNextBatch);
            appendFile = true;
        }

        public void Reset(int rowsInBatch)
        {
            if (rowsInBatch == 0) 
            {
                // Minor perf optimization: rowsInBatch=0 means we are done writing, so no need to recreate objects
                return;
            }
            //Set all the lists to a default state
            userId = new List<int?>(rowsInBatch);
            archiveDTim = new List<DateTimeOffset?>(rowsInBatch);
            fraudStatusId = new List<short?>(rowsInBatch);
            tradeScreeningStatusId = new List<short?>(rowsInBatch);
            eventTime = new List<DateTimeOffset>(rowsInBatch);
            deltaPartitionId = new List<DateTimeOffset>(rowsInBatch);
            partitionId = new List<int>(rowsInBatch);
            batchId = new List<short>(rowsInBatch);
            personId = new List<long?>(rowsInBatch);
            timestamp = new List<byte[]>(rowsInBatch);
            rowId = new List<long?>(rowsInBatch);
            code = new List<string>(rowsInBatch);
            usercontactMedia = new List<short?>(rowsInBatch);
            secretAnswer = new List<string>(rowsInBatch);
            secretQuestionId = new List<short?>(rowsInBatch);
            actionFlag = new List<short?>(rowsInBatch);
            modifiedByUserId = new List<int?>(rowsInBatch);
            createdByUserId = new List<int?>(rowsInBatch);
            modifiedDTim = new List<DateTimeOffset?>(rowsInBatch);
            createdDTim = new List<DateTimeOffset?>(rowsInBatch);
            lifeCycleStatusId = new List<short?>(rowsInBatch);
            lCID = new List<int?>(rowsInBatch);
            contactInfoId = new List<int?>(rowsInBatch);
            addressId = new List<int?>(rowsInBatch);
            customerId = new List<int?>(rowsInBatch);
            pUID = new List<string>(rowsInBatch);
            email = new List<string>(rowsInBatch);
            jobTitle = new List<string>(rowsInBatch);
            lastName = new List<string>(rowsInBatch);
            middleInitial = new List<string>(rowsInBatch);
            firstName = new List<string>(rowsInBatch);
            userName = new List<string>(rowsInBatch);
            pubProcessId = new List<long>(rowsInBatch);
            publisherId = new List<short>(rowsInBatch);

        }
    }
}