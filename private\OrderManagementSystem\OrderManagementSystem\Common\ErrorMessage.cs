﻿namespace OrderManagementSystem.Common
{
    public static class ErrorMessage
    {
        public const string InternalError = "InternalError";
        public const string NotFound = "NotFound";
        public const string Unavailable = "Unavailable";
        public const string Unauthorized = "Unauthorized";
        public const string NotSupported = "NotSupported";

        public const string InvalidOperation = "InvalidOperation";
        public const string InvalidId = "InvalidId";
        public const string InvalidKey = "InvalidKey";
        public const string InvalidKeyFormat = "InvalidKeyFormat";
        public const string InvalidValue = "InvalidValue";
        public const string InvalidValueFormat = "InvalidValueFormat";
        public const string InvalidValueType = "InvalidValueType";
        public const string IllegalCharactersInValue = "IllegalCharactersInValue";
        public const string KeyHasNoValues = "KeyHasNoValues";
        public const string TooManyValuesForKey = "TooManyValuesForKey";
        public const string InvalidKeyValueForTargeting = "InvalidKeyValueForTargeting";
        public const string MultipleKVPGroupTypesNotAllowed = "MultipleKVPGroupTypesNotAllowed";
        public const string InvalidRange = "InvalidRange";
        public const string InvalidStatus = "InvalidStatus";
        public const string InvalidFileExtension = "InvalidFileExtension";

        public const string ValueRequired = "ValueRequired";
        public const string ValueTooLong = "ValueTooLong";
        public const string TooFewValues = "TooFewValues";
        public const string TooManyValues = "TooManyValues";
        public const string ReadOnlyValue = "ReadOnlyValue";
        public const string NotValidEmailAddress = "NotValidEmailAddress";
        public const string MaxLengthError = "MaxLengthError";

        public const string NoSupportedTargeting = "NoSupportedTargeting";
        public const string UnsupportedTargetType = "UnsupportedTargetType";
        public const string InvalidSegmentForTargeting = "InvalidSegmentForTargeting";
        public const string InvalidLocationForTargeting = "InvalidLocationForTargeting";
        public const string OnlyOneDailyFrequencyTargetSupported = "OnlyOneDailyFrequencyTargetSupported";
        public const string OnlyOneHourlyFrequencyTargetSupported = "OnlyOneHourlyFrequencyTargetSupported";
        public const string OnlyOneWeeklyFrequencyTargetSupported = "OnlyOneWeeklyFrequencyTargetSupported";
        public const string OnlyOneMonthlyFrequencyTargetSupported = "OnlyOneMonthlyFrequencyTargetSupported";
        public const string OnlyOneLifetimeFrequencyTargetSupported = "OnlyOneLifetimeFrequencyTargetSupported";
        public const string OnlyOneRecencyTargetSupported = "OnlyOneRecencyTargetSupported";
        public const string MultipleAudienceTargetsOfSameTimeNotSupported = "MultipleAudienceTargetsOfSameTimeNotSupported";

        public const string NotInParentRange = "NotInParentRange";

        public const string DeleteOperationFailed = "DeleteOperationFailed";

        public const string NoBillToMemberIdAvailable = "NoBillToMemberIdAvailable";
        public const string MediaPlanStateNotEligibleForCommit = "MediaPlanStateNotEligibleForCommit";
        public const string LineStateNotEligibleForCommit = "LineStateNotEligibleForCommit";
        public const string LineSpendExceedsMediaPlanSpend = "LineSpendExceedsMediaPlanSpend";
        public const string UpdatesDuringCommitNotAllowed = "UpdatesDuringCommitNotAllowed";
        public const string CommitOperationAlreadyRunning = "CommitOperationAlreadyRunning";

        public const string PublisherApprovalRequired = "PublisherApprovalRequired";
        public const string PublisherApprovalNotRequired = "PublisherApprovalNotRequired";
        public const string PublisherApprovalAlreadyExists = "PublisherApprovalAlreadyExists";

        public const string LinkedProductTimeZoneMismatch = "LinkedProductTimeZoneMismatch";

        public const string InvalidProduct = "InvalidProduct";
        public const string OperationNotAllowed = "OperationNotAllowed";

        public const string EndDateBeyondProductMaxBookingDate = "EndDateBeyondProductMaxBookingDate";

        public const string ImportFileNotInCorrectBlobStorageAccount = "ImportFileNotInCorrectBlobStorageAccount";
        public const string ImportFileNoSASToken = "ImportFileNoSASToken";
        public const string ParentMediaPlanDoesNotExist = "ParentMediaPlanDoesNotExist";
        public const string ParentLineDoesNotExist = "ParentLineDoesNotExist";
        public const string DuplicateNegativeIdUsedInImport = "DuplicateNegativeIdUsedInImport";

        //Monitoring errors
        public const string MonetizeCallFailure = "MonetizeCallFailure";

        public const string LineMakeGoodBonusProductRoadblockCpdNotNull = "Cpd Should be Null for LineTypes MakeGood/Bonus and ProductType Roadblock";
        public const string LineMakeGoodBonusProductRotationalCpmSpendNotNull = "LineMakeGoodBonusProductRotationalCpmSpendNotNull";
        public const string LineMakeGoodBonusProductRotationalCpmSpendNotNullImpressionsNull = "Cpm Spend Should be Null, Impressions Not Null for LineTypes MakeGood/bonus and ProductType Rotational";
        public const string LineMakeGoodBonusProductSOVCpmSpendNotNull = "LineMakeGoodBonusProductSOVCpmSpendNotNull";
        // extend as needed

        public const string EvenlyPacingNotSupported = "EvenlyPacingNotSupported";

        public const string CannotMixLegacyIdsWithLocations = "CannotMixLegacyAndNewLocation";
        public const string LocationTargetingDaoRequired = "LocationTargetingDaoRequired";
        public const string LocationTargetNoValidData = "LocationTargetNoValidData";
        public const string LocationTargetInvalidData = "LocationTargetInvalidData";
        public const string TooManyTargetedCities = "TooManyTargetedCities";
        public const string CannotMixInclusionAndExclusionAtSameLevel = "CannotMixInclusionAndExclusionAtSameLevel";
        public const string ChildLocationsOfIncludedTargetCannotBeIncluded= "ChildLocationsOfIncludedTargetCannotBeIncluded";
        public const string ChildLocationsOfExcludedTargetCannotBeTargeted = "ChildLocationsOfExcludedTargetCannotBeTargeted";

        public const string SeasonalityAdjustmentValueOutofRange = "SeasonalityAdjustmentValueOutofRange";
        public const string SeasonalityMonthKeyValueOutofRange = "SeasonalityMonthKeyValueOutofRange";
        public const string SeasonalityStartDateMustBeLessThanOrEqualToEndDate = "SeasonalityStartDateMustBeLessThanOrEqualToEndDate";
        public const string SeasonalityNeitherStartDateEndDateNorMonthKeyAreProvided = "SeasonalityNeitherStartDateEndDateNorMonthKeyAreProvided";

        public const string OnlyOneDMATargetAllowed = "OnlyOneDMATargetAllowed";
        public const string CannotIncludeAndExcludeDMATargets = "CannotIncludeAndExcludeDMATargets";
        public const string DmaTargetIdsRequired = "DmaTargetIdsRequired";

        public const string SOVPercentImpressionsOutOfRange = "SOVPercentImpressionsOutOfRange";
        public const string LineNameIsRequired = "LineNameIsRequired";
        public const string LineDescriptionTooLong = "LineDescriptionTooLong";
        public const string LineNameContainsForbiddenSymbols = "LineNameContainsForbiddenSymbols";
        public const string FailedToEnrichCountryInfo = "Failed to enrich country info";

        //Import errors
        public const string NoExactMatchFound = "NoExactMatchFound";
        public const string NamesNotSupportedForCity = "NamesNotSupportedForCity";
        public const string TooManyKeyValueTargetsPerKey = "TooManyKeyValueTargetsPerKey";
        public const string KeyHasOverlappingIncludeExcludeValues = "KeyHasOverlappingIncludeExcludeValues";
    }
}
