apiVersion: "clickhouse.altinity.com/v1"

kind: "ClickHouseInstallation"

metadata:
  name: "adgroup8"
  namespace: clickhouse

spec:
  defaults:
    templates:
      dataVolumeClaimTemplate: managed-premium-volume
      podTemplate: clickhouse-pod
      serviceTemplate: chi-service-template
      shardServiceTemplate: shard-service-template
      replicaServiceTemplate: replica-service-template

  # Optional, allows tuning reconciling cycle for ClickhouseInstallation  from clickhouse-operator side
  reconciling:
    # DISCUSSED TO BE DEPRECATED
    # Syntax sugar
    # Overrides all three 'reconcile.host.wait.{exclude, queries, include}' values from the operator's config
    # Possible values:
    #  - wait - should wait to exclude host, complete queries and include host back into the cluster
    #  - nowait - should NOT wait to exclude host, complete queries and include host back into the cluster
    policy: "nowait"
    configMapPropagationTimeout: 90

  configuration:
    zookeeper:
      nodes:
      - host: clickhouse-keeper8.xchkeeper

    users:
      admin/password_sha256_hex: 37a8eec1ce19687d132fe29051dca629d164e2c4958ba141d5f4133a33f0688f

      #remoteSyncAccount/password_sha256_hex: 56b8344c18700d2d9257a117b397055797191caf852051e12a5e10a176002400
      #remoteSyncAccount/networks/host_regexp: "(chi-{chi}-[^.]+\\d+-\\d+|clickhouse\\-{chi})\\.{namespace}\\.svc\\.cluster\\.local$"
      #remoteSyncAccount/networks/ip:
      #  - **********/24
      #  - *********/24

    #<!-- <dhParamsFile>/mnt/azure/share/certs/key.pem</dhParamsFile> -->
    files:
      config.d/server-setting.xml: |
        <yandex>
            <https_port>8443</https_port>
            <tcp_port_secure>9440</tcp_port_secure>
            <listen_host>0.0.0.0</listen_host>
            <max_concurrent_queries>2000</max_concurrent_queries>
            <max_connections>8192</max_connections>
            <http_connections_soft_limit>500</http_connections_soft_limit>
            <keep_alive_timeout>30</keep_alive_timeout>
        </yandex>
      config.d/openSSL-setting.xml: |
        <yandex>
            <openSSL>
                <server replace="replace">
                    <certificateFile>/etc/clickhouse-server/certs/clickhousecert.crt</certificateFile>
                    <privateKeyFile>/etc/clickhouse-server/certs/clickhousecert.key</privateKeyFile>
                    <verificationMode>none</verificationMode>
                    <loadDefaultCAFile>true</loadDefaultCAFile>
                    <cacheSessions>true</cacheSessions>
                    <disableProtocols>sslv2,sslv3,tlsv1,tlsv1_1</disableProtocols>
                    <cipherList>ALL:!ADH:!LOW:!EXP:!MD5:!3DES:@STRENGTH</cipherList>
                    <preferServerCiphers>true</preferServerCiphers>
                </server>
            </openSSL>
        </yandex>
      config.d/Logger-setting.xml: |
        <yandex>
          <logger>
            <level>trace</level>
            <log>/var/lib/clickhouse/log/clickhouse-server/clickhouse-server.log</log>
            <errorlog>/var/lib/clickhouse/log/clickhouse-server/clickhouse-server.err.log</errorlog>
            <size>1024M</size>
            <count>96</count>
            <console>false</console>
          </logger>
        </yandex>
      config.d/macros.xml: |
        <yandex>
          <distributed_ddl>
            <path>/clickhouse/adgroup8/task_queue/ddl</path>
          </distributed_ddl>
        </yandex>
      config.d/interserver_http_host.xml: |
        <yandex>
          <include_from>/tmp/clickhouse-server/config.d/interserver_http_host.xml</include_from>
          <interserver_http_host incl="interserver_http_host" />
          <interserver_http_post>9009</interserver_http_post>
          <listen_host>::</listen_host>
          <tcp_port>9000</tcp_port>
          <interserver_http_credentials>
            <user>remoteSyncAccount</user>
            <password>__INTERSERVER_PASSWORD_PLACEHOLDER__</password>
          </interserver_http_credentials>
        </yandex>
      config.d/backup_disk.xml: |
        <clickhouse>
            <storage_configuration>
                <disks>
                    <backups>
                        <type>local</type>
                        <path>/backups/</path>
                    </backups>
                </disks>
            </storage_configuration>
            <backups>
                <allowed_disk>backups</allowed_disk>
                <allowed_path>/backups/</allowed_path>
            </backups>
        </clickhouse>
      config.d/remote_servers.xml: |
        <yandex>
          <include_from>/tmp/clickhouse-server/config.d/remote_servers.xml</include_from>
          <remote_servers incl="remote_servers" replace="replace" />
        </yandex>
      config.d/use-keeper.xml: |
        <clickhouse>
          <zookeeper>
              <!-- where are the ZK nodes -->
              <node index="1">
                <host>clickhouse-keeper8-0.clickhouse-keepers8.xchkeeper.svc.cluster.local</host>
              <port>2181</port>
              </node>
              <node index="2">
                <host>clickhouse-keeper8-1.clickhouse-keepers8.xchkeeper.svc.cluster.local</host>
                <port>2181</port>
              </node>
              <node index="3">
                <host>clickhouse-keeper8-2.clickhouse-keepers8.xchkeeper.svc.cluster.local</host>
                <port>2181</port>
              </node>
              <node index="4">
                <host>clickhouse-keeper8-2.clickhouse-keepers8.xchkeeperbcp.svc.cluster.local</host>
                <port>2181</port>
              </node>
              <node index="5">
                <host>clickhouse-keeper8-1.clickhouse-keepers8.xchkeeperbcp.svc.cluster.local</host>
                <port>2181</port>
              </node>
              <node index="6">
                <host>clickhouse-keeper8-2.clickhouse-keepers8.xchkeeperbcp.svc.cluster.local</host>
                <port>2181</port>
              </node>
          </zookeeper>
        </clickhouse>
      config.d/keeper-map.xml: |
        <clickhouse>
          <keeper_map_path_prefix>/keeper_map_tables</keeper_map_path_prefix>
        </clickhouse>
      config.d/01-clickhouse-06-processors_profile_log.xml: |
        <yandex>
            <processors_profile_log replace="1">
                <database>system</database>
                <table>processors_profile_log</table>
                <engine>Engine = MergeTree PARTITION BY event_date ORDER BY event_time TTL event_date + interval 30 day</engine>
                <flush_interval_milliseconds>7500</flush_interval_milliseconds>
            </processors_profile_log>
        </yandex>
      config.d/01-clickhouse-07-asynchronous_metric_log.xml: |
        <yandex>
            <asynchronous_metric_log replace="1">
                <database>system</database>
                <table>asynchronous_metric_log</table>
                <engine>Engine = MergeTree PARTITION BY event_date ORDER BY event_time TTL event_date + interval 30 day</engine>
                <flush_interval_milliseconds>7500</flush_interval_milliseconds>
            </asynchronous_metric_log>
        </yandex>
      users.d/clickhouse-user-settings.xml: |
       <yandex>
            <profiles>
                <default>
                    <prefer_column_name_to_alias>0</prefer_column_name_to_alias>
                    <allow_experimental_join_condition>1</allow_experimental_join_condition>
                    <max_query_size>67108864</max_query_size>
                    <log_queries>1</log_queries>
                    <join_use_nulls>1</join_use_nulls>
                    <max_threads>8</max_threads>
                    <max_ast_elements>200000</max_ast_elements>
                    <max_ast_depth>2000</max_ast_depth>
                </default>
            </profiles>
       </yandex>
    clusters:
      - name: advbi
        layout:
          shardsCount: 16
          replicasCount: 3

  templates:
    volumeClaimTemplates:
      - name: managed-premium-volume
        spec:
          storageClassName: managed-premium
          accessModes:
            - ReadWriteOnce
          resources:
            requests:
              storage: 3.5Ti
    podTemplates:
      - name: clickhouse-pod
        metadata:
          labels:
            azure.workload.identity/use: "true"
            app: chi-clickhouse-server
        spec:
          serviceAccountName: aks-advbi-clickhouse-prod
          nodeSelector:
            "agentpool": biagskue32az
          volumes:
            - name: adv-bi-azurefile-volumne
              persistentVolumeClaim:
                 claimName: adv-bi-azurefile-pvc
            - name: adv-bi-cdrshare1-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-cdrshare1-pvc
            - name: adv-bi-cdrshare2-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-cdrshare2-pvc
            - name: adv-bi-cdrshare3-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-cdrshare3-pvc
            - name: adv-bi-cdrshare4-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-cdrshare4-pvc
            - name: adv-bi-cdrshare5-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-cdrshare5-pvc
            - name: adv-bi-cdrshare6-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-cdrshare6-pvc
            - name: adv-bi-cdrshare7-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-cdrshare7-pvc
            - name: adv-bi-cdrshare8-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-cdrshare8-pvc
            - name: adv-bi-fastbishare1-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-fastbishare1-pvc
            - name: adv-bi-fastbishare2-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-fastbishare2-pvc
            - name: adv-bi-fastbishare3-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-fastbishare3-pvc
            - name: adv-bi-fastbishare4-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-fastbishare4-pvc
            - name: adv-bi-fastbishare5-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-fastbishare5-pvc
            - name: adv-bi-fastbishare6-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-fastbishare6-pvc
            - name: adv-bi-fastbishare7-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-fastbishare7-pvc
            - name: adv-bi-fastbishare8-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-fastbishare8-pvc
            - name: adv-bi-batchconvshare1-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-batchconvshare1-pvc
            - name: adv-bi-batchconvshare2-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-batchconvshare2-pvc
            - name: adv-bi-bigbatchshare1-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-bigbatchshare1-pvc
            - name: adv-bi-bigbatchshare2-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-bigbatchshare2-pvc
            - name: adv-bi-fastbiconvshare1-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-fastbiconvshare1-pvc
            - name: adv-bi-fastbiconvshare2-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-fastbiconvshare2-pvc
            - name: adv-bi-qsbteshare1-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-qsbteshare1-pvc
            - name: adv-bi-qsbteshare2-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-qsbteshare2-pvc
            - name: adv-bi-sovshare1-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-sovshare1-pvc
            - name: adv-bi-sovshare2-volume
              persistentVolumeClaim:
                  claimName: adv-bi-prod-sovshare2-pvc
            - name: clickhouse-cert-volume
              csi:
                driver: secrets-store.csi.k8s.io
                readOnly: true
                volumeAttributes:
                  secretProviderClass: azure-kv-clickhouse-cert
          containers:
            - name: clickhouse-container
              image: campaignplatform.azurecr.io/dspbi/clickhouse-server:24.5
              volumeMounts:
                - name: adv-bi-azurefile-volumne
                  mountPath: /mnt/azure/share
                - name: adv-bi-cdrshare1-volume
                  mountPath: /mnt/cdrshare1
                - name: adv-bi-cdrshare2-volume
                  mountPath: /mnt/cdrshare2
                - name: adv-bi-cdrshare3-volume
                  mountPath: /mnt/cdrshare3
                - name: adv-bi-cdrshare4-volume
                  mountPath: /mnt/cdrshare4
                - name: adv-bi-cdrshare5-volume
                  mountPath: /mnt/cdrshare5
                - name: adv-bi-cdrshare6-volume
                  mountPath: /mnt/cdrshare6
                - name: adv-bi-cdrshare7-volume
                  mountPath: /mnt/cdrshare7
                - name: adv-bi-cdrshare8-volume
                  mountPath: /mnt/cdrshare8
                - name: adv-bi-fastbishare1-volume
                  mountPath: /mnt/fastbishare1
                - name: adv-bi-fastbishare2-volume
                  mountPath: /mnt/fastbishare2
                - name: adv-bi-fastbishare3-volume
                  mountPath: /mnt/fastbishare3
                - name: adv-bi-fastbishare4-volume
                  mountPath: /mnt/fastbishare4
                - name: adv-bi-fastbishare5-volume
                  mountPath: /mnt/fastbishare5
                - name: adv-bi-fastbishare6-volume
                  mountPath: /mnt/fastbishare6
                - name: adv-bi-fastbishare7-volume
                  mountPath: /mnt/fastbishare7
                - name: adv-bi-fastbishare8-volume
                  mountPath: /mnt/fastbishare8
                - name: adv-bi-batchconvshare1-volume
                  mountPath: /mnt/batchconvshare1
                - name: adv-bi-batchconvshare2-volume
                  mountPath: /mnt/batchconvshare2
                - name: adv-bi-bigbatchshare1-volume
                  mountPath: /mnt/bigbatchshare1
                - name: adv-bi-bigbatchshare2-volume
                  mountPath: /mnt/bigbatchshare2
                - name: adv-bi-fastbiconvshare1-volume
                  mountPath: /mnt/fastbiconvshare1
                - name: adv-bi-fastbiconvshare2-volume
                  mountPath: /mnt/fastbiconvshare2
                - name: adv-bi-qsbteshare1-volume
                  mountPath: /mnt/qsbteshare1
                - name: adv-bi-qsbteshare2-volume
                  mountPath: /mnt/qsbteshare2
                - name: adv-bi-sovshare1-volume
                  mountPath: /mnt/sovshare1
                - name: adv-bi-sovshare2-volume
                  mountPath: /mnt/sovshare2
                - name: clickhouse-cert-volume
                  mountPath: /etc/clickhouse-server/certs
                  readOnly: true
              resources:
                requests:
                  cpu: 24
                  memory: 100Gi
                limits:
                  cpu: 32
                  memory: 200Gi
              startupProbe:
                failureThreshold: 400
                httpGet:
                  path: /ping
                  port: http
                  scheme: HTTP
                initialDelaySeconds: 120
                periodSeconds: 3
                successThreshold: 1
                timeoutSeconds: 2
              readinessProbe:
                failureThreshold: 401
                httpGet:
                  path: /ping
                  port: http
                  scheme: HTTP
                initialDelaySeconds: 1
                periodSeconds: 3
                successThreshold: 1
                timeoutSeconds: 2
              livenessProbe:
                failureThreshold: 400
                httpGet:
                  path: /ping
                  port: http
                  scheme: HTTP
                initialDelaySeconds: 10
                periodSeconds: 3
                successThreshold: 1
                timeoutSeconds: 2
              env:
                - name: SHARD_COUNT
                  value: "16"
                - name: REPLICA_COUNT
                  value: "3"
                - name: BASE_HOSTNAME_PROD
                  value: chi-adgroup8-advbi
                - name: BASE_HOSTNAME_BCP
                  value: chi-adgroup8-advbi
                - name: HOSTNAME_SUFFIX_PROD
                  value: clickhouse.svc.cluster.local
                - name: HOSTNAME_SUFFIX_BCP
                  value: clickhousebcp.svc.cluster.local
                - name: REMOTE_SYNC_PASSWORD
                  value: "__ENV_REMOTE_SYNC_PASSWORD_PLACEHOLDER__"
                - name: CLUSER_NAME
                  value: advbi
                - name: BCP_KUBE_DNS
                  value: "**********"
              command:
                - bash
                - -x
                - -c
                - |
                  echo "nameserver *********" > /etc/resolv.conf &&
                  apt-get update && apt-get install -y dnsmasq  &&
                  echo "server=/${HOSTNAME_SUFFIX_BCP}/${BCP_KUBE_DNS}" >> /etc/dnsmasq.conf  &&
                  echo "server=/xchkeeperbcp.svc.cluster.local/${BCP_KUBE_DNS}" >> /etc/dnsmasq.conf &&
                  echo "server=*********" >> /etc/dnsmasq.conf &&
                  echo "nameserver 127.0.0.1" > /etc/resolv.conf &&
                  /etc/init.d/dnsmasq restart &&
                  HOST=`hostname -s` &&
                  FQDN=`hostname --fqdn` &&

                  #generate interserver_http_host
                  mkdir -p /tmp/clickhouse-server/config.d/ &&
                  {
                    echo "<yandex>"
                    echo "  <interserver_http_host>${FQDN}</interserver_http_host>"
                    echo "  <interserver_http_post>9009</interserver_http_post>"
                    echo "</yandex>"
                  } > /tmp/clickhouse-server/config.d/interserver_http_host.xml &&
                  cat /tmp/clickhouse-server/config.d/interserver_http_host.xml &&

                  #generate remote_servers
                  {
                    echo "<yandex>"
                    echo "  <remote_servers>"
                    #shards
                    echo "    <${CLUSER_NAME}>"
                    for ((i=0; i<$SHARD_COUNT; i++ )); do
                    echo "      <shard>"
                    echo "        <internal_replication>true</internal_replication>"
                    for ((j=0; j<$REPLICA_COUNT; j++ )); do
                    echo "        <replica>"
                    echo "          <host>${BASE_HOSTNAME_PROD}-${i}-${j}-0.${BASE_HOSTNAME_PROD}-${i}-${j}.${HOSTNAME_SUFFIX_PROD}</host>"
                    echo "          <port>9000</port>"
                    echo "          <user>remoteSyncAccount</user>"
                    echo "          <password>${REMOTE_SYNC_PASSWORD}</password>"
                    echo "          <secure>0</secure>"
                    echo "        </replica>"
                    done
                    for ((j=0; j<$REPLICA_COUNT; j++ )); do
                    echo "        <replica>"
                    echo "          <host>${BASE_HOSTNAME_BCP}-${i}-${j}-0.${BASE_HOSTNAME_BCP}-${i}-${j}.${HOSTNAME_SUFFIX_BCP}</host>"
                    echo "          <port>9000</port>"
                    echo "          <user>remoteSyncAccount</user>"
                    echo "          <password>${REMOTE_SYNC_PASSWORD}</password>"
                    echo "          <secure>0</secure>"
                    echo "        </replica>"
                    done
                    echo "      </shard>"
                    done
                    echo "    </${CLUSER_NAME}>"

                    #all-replicated
                    echo "    <all-replicated>"
                    echo "      <shard>"
                    echo "        <internal_replication>true</internal_replication>"
                    for ((i=0; i<$SHARD_COUNT; i++ )); do
                    for ((j=0; j<$REPLICA_COUNT; j++ )); do
                    echo "        <replica>"
                    echo "          <host>${BASE_HOSTNAME_PROD}-${i}-${j}-0.${BASE_HOSTNAME_PROD}-${i}-${j}.${HOSTNAME_SUFFIX_PROD}</host>"
                    echo "          <port>9000</port>"
                    echo "          <user>remoteSyncAccount</user>"
                    echo "          <password>${REMOTE_SYNC_PASSWORD}</password>"
                    echo "          <secure>0</secure>"
                    echo "        </replica>"
                    done
                    for ((j=0; j<$REPLICA_COUNT; j++ )); do
                    echo "        <replica>"
                    echo "          <host>${BASE_HOSTNAME_BCP}-${i}-${j}-0.${BASE_HOSTNAME_BCP}-${i}-${j}.${HOSTNAME_SUFFIX_BCP}</host>"
                    echo "          <port>9000</port>"
                    echo "          <user>remoteSyncAccount</user>"
                    echo "          <password>${REMOTE_SYNC_PASSWORD}</password>"
                    echo "          <secure>0</secure>"
                    echo "        </replica>"
                    done
                    done
                    echo "      </shard>"
                    echo "    </all-replicated>"

                    #all-sharded
                    echo "    <all-sharded>"
                    for ((i=0; i<$SHARD_COUNT; i++ )); do
                    for ((j=0; j<$REPLICA_COUNT; j++ )); do
                    echo "      <shard>"
                    echo "        <internal_replication>false</internal_replication>"
                    echo "        <replica>"
                    echo "          <host>${BASE_HOSTNAME_PROD}-${i}-${j}-0.${BASE_HOSTNAME_PROD}-${i}-${j}.${HOSTNAME_SUFFIX_PROD}</host>"
                    echo "          <port>9000</port>"
                    echo "          <user>remoteSyncAccount</user>"
                    echo "          <password>${REMOTE_SYNC_PASSWORD}</password>"
                    echo "          <secure>0</secure>"
                    echo "        </replica>"
                    echo "      </shard>"
                    done
                    for ((j=0; j<$REPLICA_COUNT; j++ )); do
                    echo "      <shard>"
                    echo "        <internal_replication>false</internal_replication>"
                    echo "        <replica>"
                    echo "          <host>${BASE_HOSTNAME_BCP}-${i}-${j}-0.${BASE_HOSTNAME_BCP}-${i}-${j}.${HOSTNAME_SUFFIX_BCP}</host>"
                    echo "          <port>9000</port>"
                    echo "          <user>remoteSyncAccount</user>"
                    echo "          <password>${REMOTE_SYNC_PASSWORD}</password>"
                    echo "          <secure>0</secure>"
                    echo "        </replica>"
                    echo "      </shard>"
                    done
                    done
                    echo "    </all-sharded>"

                    echo "  </remote_servers>"
                    echo "</yandex>"
                  } > /tmp/clickhouse-server/config.d/remote_servers.xml &&
                  cat /tmp/clickhouse-server/config.d/remote_servers.xml &&
                  /usr/bin/clickhouse-server --config-file=/etc/clickhouse-server/config.xml

            - name: clickhouse-backup
              image: campaignplatform.azurecr.io/dspbi/clickhouse-backup:latest
              imagePullPolicy: Always
              args: ["server"]
              env:
                 - name: LOG_LEVEL
                   value: "info"
                 - name: ALLOW_EMPTY_BACKUPS
                   value: "true"
                 - name: API_LISTEN
                   value: "0.0.0.0:7171"
                 - name: BACKUPS_TO_KEEP_LOCAL
                   value: "-1"
                 - name: BACKUPS_TO_KEEP_REMOTE
                   value: "35"
                 # INSERT INTO system.backup_actions to execute backup
                 - name: API_CREATE_INTEGRATION_TABLES
                   value: "true"
                 - name: REMOTE_STORAGE
                   value: "azblob"
                 - name: AZBLOB_ACCOUNT_NAME
                   value: "advbichdatastorageprod"
                 - name: AZBLOB_CONTAINER
                   value: "backup"
                 - name: AZBLOB_PATH
                   value: clickhouse/backup/{replica}
                 - name: AZBLOB_ACCOUNT_KEY
                   valueFrom:
                      secretKeyRef:
                        name: clickhouse-secret
                        key: account_key
              ports:
                 - name: backup-rest
                   containerPort: 7171
    serviceTemplates:
      - name: chi-service-template
        metadata:
          annotations:
            service.beta.kubernetes.io/azure-load-balancer-internal: "false"
        spec:
          ports:
            - name: https
              port: 8443
          type: LoadBalancer
      - name: shard-service-template
        metadata:
          annotations:
            service.beta.kubernetes.io/azure-load-balancer-internal: "false"
        spec:
          ports:
            - name: https
              port: 8443
          type: LoadBalancer
      - name: replica-service-template
        #metadata:
        #  annotations:
        #    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
        #    service.beta.kubernetes.io/azure-load-balancer-internal-subnet: "aks-advi-adgroup-subnet"
        #    service.beta.kubernetes.io/azure-load-balancer-ipv4: 10.232.{shardIndex}.1{replicaIndex}
        spec:
          ports:
            - name: https
              port: 8443
            - name: http
              port: 8123
            - name: tcp
              port: 9440
            - name: tcp2
              port: 9009
            - name: tcp3
              port: 9000
          #type: LoadBalancer
          type: ClusterIP
          clusterIP: None