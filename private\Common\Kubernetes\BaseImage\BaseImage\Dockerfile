FROM campaignplatform.azurecr.io/xmlconfigflattenercore:latest AS xml-tool
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS dotnet-tools
WORKDIR /app

RUN dotnet tool install -g dotnet-dump \
    && dotnet tool install -g dotnet-trace \
    && dotnet tool install -g dotnet-counters \
    && dotnet tool install -g dotnet-certificate-tool
    
# Build runtime image
FROM mcr.microsoft.com/dotnet/aspnet:9.0

WORKDIR /base

# To install additional libs
# For media processing:
# - libmediainfo-dev
# - libgdiplus
# For networking / netstat
# - net-tool
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    libmediainfo-dev \
    libgdiplus \
    dnsutils \
    curl \
    zip \
    procps \
    bc \ 
    net-tools

RUN curl -sL https://aka.ms/InstallAzureCLIDeb | bash
# Make sure to copy the tool (and other stuff) to runtime
COPY --from=xml-tool /app ./xmlconfigflattenercore
COPY --from=dotnet-tools /root/.dotnet/tools /root/.dotnet/tools/
ENV PATH="${PATH}:/root/.dotnet/tools"

COPY ./*.sh ./
RUN chmod +x ./*.sh

# Copy .NET 8 runtime to support code that hasn't upgraded yet
COPY --from=mcr.microsoft.com/dotnet/aspnet:8.0 /usr/share/dotnet /usr/share/dotnet

# Hack OpenSSL setting per issue # patching .net8 openssl configuration per issue comment in https://github.com/dotnet/runtime/issues/98797#issuecomment-1961125933
COPY ./openssl_addition.conf /tmp/openssl_addition.conf
RUN cat /tmp/openssl_addition.conf >> /etc/ssl/openssl.cnf

# Change TimeZone to PST, to match EAP
RUN ln -fs /usr/share/zoneinfo/America/Los_Angeles /etc/localtime && dpkg-reconfigure -f noninteractive tzdata

# Copy certs, important to change to .crt, otherwise update-ca-certificates will not pick it up
COPY ./*.crt /usr/local/share/ca-certificates/
RUN update-ca-certificates

# Set environment variables for collection diagnostics
ENV COMPlus_PerfMapEnabled 1
ENV DOTNET_EnableWriteXorExecute=0