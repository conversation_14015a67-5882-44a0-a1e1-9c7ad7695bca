﻿using Kusto.Cloud.Platform.Utils;
using OrderManagementSystem.Common;
using OrderManagementSystem.Common.BusinessLogicHelpers;
using OrderManagementSystem.Configuration;
using OrderManagementSystem.DataAccessObjects;
using OrderManagementSystem.Entities;
using OrderManagementSystem.Entities.Internal;
using OrderManagementSystem.Monetize;
using OrderManagementSystem.Requests;
using System.Net;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace OrderManagementSystem.Validators
{
    public static class LineValidationHelper
    {
        public static readonly string[] ALLOWED_FIELDS =
        [
            "Name",
            "ID",
            "Status",
            "Type",
            "StartDate",
            "EndDate",
            "Budget",
            "EstimatedImpressions"
        ];
        private static readonly Regex KeyRegex = new(@"^[A-Za-z0-9_\- ]+$", RegexOptions.Compiled);

        private static readonly Regex ValueRegex = new(@"^[A-Za-z0-9_\- ,]+$", RegexOptions.Compiled);

        private class FilterInput
        {
            public string Field { get; init; }
            public string Operator { get; init; }
            public object Value { get; set; }

            public FilterInput(string field, string @operator, object value)
            {
                Field = field;
                Operator = @operator;
                Value = value;
            }
        }
        public static async Task<Result> ValidateFields(string source, ILogger logger, Line line, Customer customer, MediaPlan mediaPlan, Product product, ISegmentDao segmentDao, ICountryDao countryDao, ILocationDataAccessObject locationDao, IKeyValueDataAccessObject keyValueDao)
        {
            var result = new Result();

            var MakeGoodBonusLineTypes = new List<LineType>
            {
                LineType.MakeGood,
                LineType.Bonus,
                // Add more types here if needed in the future
            };

            result.Merge(ValidateName(source, line, logger));
            result.Merge(ValidateDescription(source, line, logger));
            result.Merge(ValidateType(source, product, line, logger));
            result.Merge(ValidateMakeGoodBonus(source, line, product, logger));

            if (line.Status == LineStatus.Draft)
            {
                //For Draft Lines we have bare minimum validations
                return result;
            }

            result.Merge(ValidateStatus(source, line, logger));
            result.Merge(ValidateLineFields(source, line, logger));
            result.Merge(ValidateStartAndEndDate(source, line, customer, mediaPlan, product, logger));
            result.Merge(ValidateCpm(source, line, product, MakeGoodBonusLineTypes, logger));
            result.Merge(ValidateCpd(source, line, product, MakeGoodBonusLineTypes, logger));
            result.Merge(ValidateRotational(source, line, product, logger));
            result.Merge(ValidateSOV(source, line, product, logger));
            // TODO: validate calendar slots (if line has applicable type)

            result.Merge(await ValidateTargeting(source, line.Targets, product, segmentDao, countryDao, locationDao, keyValueDao, logger));

            return result;
        }

        private static Result ValidateName(string source, Line line, ILogger logger) => ValidationHelper.ValidateRequiredString(source, $"{nameof(line.Name)}", line.Name, ValidatorConstants.LINE_NAME_MAX_LENGTH, logger);
        private static Result ValidateDescription(string source, Line line, ILogger logger) => ValidationHelper.ValidateOptionalString(source, $"{nameof(line.Description)}", line.Description, ValidatorConstants.LINE_DESCRIPTION_MAX_LENGTH, logger);
        private static Result ValidateStatus(string source, Line line, ILogger logger)
        {
            var result = new Result();
            if (line.Status != default)
            {
                // Log and add error for non-default status
                result.AddError(new Error(source: source, message: ErrorMessage.ReadOnlyValue, property: [nameof(line.Status)], type: ErrorType.UserError),
                    logger, "Line status is not default. LineId: {LineId}, Status: {Status}", line.Id, line.Status);
            }
            return result;
        }
        private static Result ValidateType(string source, Product product, Line line, ILogger logger)
        {
            var result = new Result();
            if (line.Type != LineType.MakeGood && line.Type != LineType.Bonus)
            {
                switch (line.Type)
                {
                    case LineType.Roadblock:
                        if (product.Type != ProductType.Roadblock)
                        {
                            // Log and add error for invalid type combination
                            result.AddError(new Error(source: source, message: ErrorMessage.InvalidValue, property: [nameof(line.Type), nameof(product.Type)], type: ErrorType.UserError),
                                logger, "Invalid type combination. LineId: {LineId}, LineType: {LineType}, ProductType: {ProductType}", line.Id, line.Type, product.Type);
                        }
                        break;
                    case LineType.Rotational:
                        if (product.Type != ProductType.Rotational)
                        {
                            result.AddError(new Error(source: source, message: ErrorMessage.InvalidValue, property: [nameof(line.Type), nameof(product.Type)], type: ErrorType.UserError),
                                logger, "Invalid type combination. LineId: {LineId}, LineType: {LineType}, ProductType: {ProductType}", line.Id, line.Type, product.Type);
                        }
                        break;
                    case LineType.Bulk:
                        if (product.Type != ProductType.Bulk)
                        {
                            result.AddError(new Error(source: source, message: ErrorMessage.InvalidValue, property: [nameof(line.Type), nameof(product.Type)], type: ErrorType.UserError),
                                logger, "Invalid type combination. LineId: {LineId}, LineType: {LineType}, ProductType: {ProductType}", line.Id, line.Type, product.Type);
                        }
                        break;
                    case LineType.SOV:
                        if (product.Type != ProductType.SOV)
                        {
                            result.AddError(new Error(source: source, message: ErrorMessage.InvalidValue, property: [nameof(line.Type), nameof(product.Type)], type: ErrorType.UserError),
                                logger, "Invalid type combination. LineId: {LineId}, LineType: {LineType}, ProductType: {ProductType}", line.Id, line.Type, product.Type);
                        }
                        break;
                    case LineType._:
                        break;
                    default:
                        result.AddError(new Error(source: source, message: ErrorMessage.InvalidValue, property: [nameof(line.Type)], type: ErrorType.UserError),
                            logger, "Invalid line type. LineId: {LineId}, LineType: {LineType}", line.Id, line.Type);
                        break;
                }
            }
            return result;
        }
        private static Result ValidateMakeGoodBonus(string source, Line line, Product product, ILogger logger)
        {
            var result = new Result();
            if (line.Type == LineType.MakeGood || line.Type == LineType.Bonus)
            {
                if (product.Type == ProductType.Roadblock)
                {
                    if (line.Cpd.HasValue)
                    {
                        result.AddError(new Error(source, ErrorMessage.LineMakeGoodBonusProductRoadblockCpdNotNull, [nameof(line.Cpd)], type: ErrorType.UserError),
                            logger, $"MakeGood/Bonus line with Roadblock product should not have CPD. LineId: {line.Id}");
                    }
                }
                else if (product.Type == ProductType.Rotational || product.Type == ProductType.SOV)
                {
                    var targetSpend = line.TargetSpend.HasValue ? line.TargetSpend.Value : 0;
                    var cpm = line.Cpm.HasValue ? line.Cpm.Value : 0;
                    if (line.Status == LineStatus.Draft &&
                        (targetSpend != 0 || cpm != 0))
                    {
                        //In draft state, it is okay if Line doesn't have target impressions set yet, but CPM and Spend need to be null
                        var message = product.Type == ProductType.Rotational
                            ? ErrorMessage.LineMakeGoodBonusProductRotationalCpmSpendNotNull
                            : ErrorMessage.LineMakeGoodBonusProductSOVCpmSpendNotNull;
                        result.AddError(new Error(source, message, [nameof(line.TargetSpend), nameof(line.Cpm)], type: ErrorType.UserError),
                            logger, $"MakeGood/Bonus line with Rotational/SOV product should not have CPM/Spend. LineId: {line.Id}");
                    }
                    else if (product.Type == ProductType.Rotational && line.Status != LineStatus.Draft &&
                        (targetSpend != 0 || cpm != 0 || line.TargetImpressions == null || line.TargetImpressions == 0))
                    {
                        logger.LogUserError($"MakeGood/Bonus line with Rotational product has invalid CPM/Spend/Impressions. LineId: {line.Id}");
                        result.AddError(new Error(source, ErrorMessage.LineMakeGoodBonusProductRotationalCpmSpendNotNullImpressionsNull, [nameof(line.TargetImpressions), nameof(line.TargetSpend), nameof(line.Cpm)], type: ErrorType.UserError),
                            logger, $"MakeGood/Bonus line with Rotational product has invalid CPM/Spend/Impressions. LineId: {line.Id}");
                    }
                }
                else
                {
                    result.AddError(new Error(source, ErrorMessage.InvalidProduct, [nameof(product.Type)], type: ErrorType.UserError),
                        logger, $"MakeGood/Bonus line with invalid product type. LineId: {line.Id}, ProductType: {product.Type}");
                }

            }
            return result;
        }
        private static Result ValidateStartAndEndDate(string source, Line line, Customer customer, MediaPlan mediaPlan, Product product, ILogger logger)
        {
            var result = new Result();

            // Null is only valid for Draft, and Draft lines have already exited validation
            if (line.StartDateTime == null)
            {
                result.AddError(new Error(source: source, message: ErrorMessage.InvalidValue, property: [nameof(line.StartDateTime)], type: ErrorType.UserError),
                    logger, "Line start date is null. LineId: {LineId}", line.Id);
            }
            if (line.EndDateTime == null)
            {
                result.AddError(new Error(source: source, message: ErrorMessage.InvalidValue, property: [nameof(line.EndDateTime)], type: ErrorType.UserError),
                    logger, "Line end date is null. LineId: {LineId}", line.Id);
            }
            if (result.Failed) { return result; }

            //Validate Line's date range is within Media Plan's date range when translating on time zone
            if (line.StartDateTime > line.EndDateTime)
            {
                result.AddError(new Error(source: source, message: ErrorMessage.InvalidRange, property: [nameof(line.StartDateTime),nameof(line.EndDateTime)], type: ErrorType.UserError),
                    logger, "Line start date is after end date. LineId: {LineId}, Start: {StartDate}, End: {EndDate}", line.Id, line.StartDateTime, line.EndDateTime);
            }

            result.Merge(ValidateLineAndMediaPlanDates(source, line, customer, mediaPlan, product, logger));
            return result;
        }
        private static Result ValidateCpm(string source, Line line, Product product, List<LineType> MakeGoodBonusLineTypes, ILogger logger)
        {
            var result = new Result();

            // CPM can NOT be set for RoadBlock line
            if (!MakeGoodBonusLineTypes.Contains(line.Type))
            {
                if (product.Type == ProductType.Roadblock && line.Cpm.HasValue)
                {
                    result.AddError(new Error(source: source, message: ErrorMessage.InvalidValue, property: [nameof(line.Cpm)], type: ErrorType.UserError),
                        logger, "CPM should not be set for Roadblock line. LineId: {LineId}", line.Id);
                }
                else if (product.Type != ProductType.Roadblock)
                {
                    // CPM is required for non RoadBlock line
                    if (!line.Cpm.HasValue)
                    {
                        result.AddError(new Error(source: source, message: ErrorMessage.ValueRequired, property: [nameof(line.Cpm)], type: ErrorType.UserError),
                            logger, "CPM is required for non-Roadblock line. LineId: {LineId}", line.Id);
                    }
                    else if (line.Cpm < 0)
                    {
                        //TODO: once make-good Line type is added, update validation to block $0 CPM
                        result.AddError(new Error(source: source, message: ErrorMessage.InvalidValue, property: [nameof(line.Cpm)], type: ErrorType.UserError),
                            logger, "CPM is negative. LineId: {LineId}, CPM: {CPM}", line.Id, line.Cpm);
                    }
                }
            }

            return result;
        }
        private static Result ValidateCpd(string source, Line line, Product product, List<LineType> MakeGoodBonusLineTypes, ILogger logger)
        {
            var result = new Result();

            // CPD only apply to RoadBlock line
            if (!MakeGoodBonusLineTypes.Contains(line.Type))
            {
                if (product.Type != ProductType.Roadblock && line.Cpd.HasValue)
                {
                    result.AddError(new Error(source: source, message: ErrorMessage.InvalidValue, property: [nameof(line.Cpd)], type: ErrorType.UserError),
                        logger, "CPD should not be set for non-Roadblock line. LineId: {LineId}", line.Id);
                }
                else if (product.Type == ProductType.Roadblock)
                {
                    // CPD is required for RoadBlock line
                    if (!line.Cpd.HasValue)
                    {
                        result.AddError(new Error(source: source, message: ErrorMessage.ValueRequired, property: [nameof(line.Cpd)], type: ErrorType.UserError),
                            logger, "CPD is required for Roadblock line. LineId: {LineId}", line.Id);
                    }
                    else if (line.Cpd < 0)
                    {
                        //TODO: once make-good Line type is added, update validation to block $0 CPD
                        result.AddError(new Error(source: source, message: ErrorMessage.InvalidValue, property: [nameof(line.Cpd)], type: ErrorType.UserError),
                            logger, "CPD is negative. LineId: {LineId}, CPD: {CPD}", line.Id, line.Cpd);
                    }
                }
            }

            return result;
        }

        private static Result ValidateRotational(string source, Line line, Product product, ILogger logger)
        {
            var result = new Result();
            switch (product.Type)
            {
                case ProductType.Rotational:
                    if (line.TargetImpressions is null && line.TargetSpend is null)
                    {
                        result.AddError(new Error(source, ErrorMessage.TooFewValues, [nameof(line.TargetImpressions),nameof(line.TargetSpend)], type: ErrorType.UserError),
                            logger, "Rotational line missing both TargetImpressions and TargetSpend. LineId: {LineId}", line.Id);
                        break;
                    }
                    if (line.TargetImpressions is not null && line.TargetSpend is not null)
                    {
                        result.AddError(new Error(source, ErrorMessage.TooManyValues, [nameof(line.TargetImpressions),nameof(line.TargetSpend)], type: ErrorType.UserError),
                            logger, "Rotational line has both TargetImpressions and TargetSpend set. LineId: {LineId}", line.Id);
                        break;
                    }
                    break;
                default:
                    if (line.TargetImpressions.HasValue)
                    {
                        logger.LogWarning($"Non-rotational line has TargetImpressions set. LineId: {line.Id}");
                        // will be ignored downstream
                        result.AddWarning(new Warning(source, ErrorMessage.InvalidValue, [nameof(line.TargetImpressions)]));
                    }
                    if (line.TargetSpend.HasValue)
                    {
                        logger.LogWarning($"Non-rotational line has TargetSpend set. LineId: {line.Id}");
                        // will be ignored downstream
                        result.AddWarning(new Warning(source, ErrorMessage.InvalidValue, [nameof(line.TargetSpend)]));
                    }
                    break;
            }
            return result;
        }

        private static Result ValidateSOV(string source, Line line, Product product, ILogger logger)
        {
            var result = new Result();

            if(product.Type != ProductType.SOV)
            {
                return result;
            }

            if (!line.PercentImpressions.HasValue)
            {
                result.AddError(new Error(source: source, message: ErrorMessage.ValueRequired, property: [nameof(line.PercentImpressions)], type: ErrorType.UserError),
                    logger, "SOV line missing PercentImpressions. LineId: {LineId}", line.Id);
            }
            else if (line.PercentImpressions < 0 || line.PercentImpressions > 100)
            {
                result.AddError(new Error(
                    source: source,
                    message: ErrorMessage.SOVPercentImpressionsOutOfRange,
                    property: new[] { nameof(line.PercentImpressions) },
                    type: ErrorType.UserError
                ), logger, "SOV line PercentImpressions out of range. LineId: {LineId}, PercentImpressions: {PercentImpressions}", line.Id, line.PercentImpressions);
            }
            if (line.TargetImpressions.HasValue)
            {
                result.AddError(new Error(source, ErrorMessage.InvalidValue, [nameof(line.TargetImpressions)], type: ErrorType.UserError),
                    logger, "SOV line has TargetImpressions set. LineId: {LineId}", line.Id);
            }
            if (line.TargetSpend.HasValue)
            {
                result.AddError(new Error(source, ErrorMessage.InvalidValue, [nameof(line.TargetSpend)], type: ErrorType.UserError),
                    logger, "SOV line has TargetSpend set. LineId: {LineId}", line.Id);
            }

            return result;
        }

        public static Result ValidateLineAndMediaPlanDates(string source, Line line, Customer customer, MediaPlan mediaPlan, Product product, ILogger logger)
        {
            var result = new Result();
            var lineStartDate = DateTime.SpecifyKind(line.StartDateTime!.Value, DateTimeKind.Unspecified);
            var lineEndDate = DateTime.SpecifyKind(line.EndDateTime!.Value, DateTimeKind.Unspecified);

            var mediaPlanTimeZone = XandrTimeZoneHelper.GetTimeZoneInfo(customer.Timezone.Id);
            var lineTimeZone = XandrTimeZoneHelper.GetTimeZoneInfo(product.TimeZoneId);

            // Check Line EndDate against Product MaxBookingDate before TZ conversion
            if (product.MaxBookingDate != null && lineEndDate >= product.MaxBookingDate?.AddDays(1))
            {
                result.AddError(new Error(source: source, message: ErrorMessage.EndDateBeyondProductMaxBookingDate, property: [nameof(line.EndDateTime)], type: ErrorType.UserError),
                    logger, "lineEndDate is not valid. LineId: {LineId}, ProductId: {ProductId}, MediaPlanId: {MediaPlanId}, CustomerId: {CustomerId}, lineEndDate: {LineEndDate}, product.MaxBookingDate: {MaxBookingDate}",
                    line.Id, product.Id, mediaPlan.Id, customer.Id, lineEndDate, product.MaxBookingDate);
            }

            if (mediaPlanTimeZone != null && lineTimeZone != null)
            {
                //Convert FROM Line TZ TO Media Plan TZ
                lineStartDate = TimeZoneInfo.ConvertTime(lineStartDate, lineTimeZone, mediaPlanTimeZone);
                lineEndDate = TimeZoneInfo.ConvertTime(lineEndDate, lineTimeZone, mediaPlanTimeZone);
                logger.LogInformation($"LineValidationHelper.ValidateLineAndMediaPlanDates: Different time zones between media plan and line, so conversion required. MediaPlan.TimeZone: {mediaPlanTimeZone.DisplayName}, MediaPlan.BaseOffset: {mediaPlanTimeZone.BaseUtcOffset}, Line.TimeZone: {lineTimeZone.DisplayName}, Line.BaseOffset: {lineTimeZone.BaseUtcOffset}, MediaPlan.StartDate: {mediaPlan.StartDate.ToString("yyyy-MM-dd HH:mm:ss")}, MediaPlanEndDate: {mediaPlan.EndDate.ToString("yyyy-MM-dd HH:mm:ss")}, Line.StartDate: {lineStartDate.ToString("yyyy-MM-dd HH:mm:ss")}, Line.EndDate: {lineEndDate.ToString("yyyy-MM-dd HH:mm:ss")}");
            }

            if (lineStartDate.Date < mediaPlan.StartDate.Date)
            {
                result.AddError(new Error(source: source, message: ErrorMessage.NotInParentRange, property: [nameof(line.StartDateTime)], type: ErrorType.UserError),
                    logger, "Line start date is before media plan start date. LineId: {LineId}, ProductId: {ProductId}, MediaPlanId: {MediaPlanId}, CustomerId: {CustomerId}, lineStartDate: {LineStartDate}, mediaPlan.StartDate: {MediaPlanStartDate}",
                    line.Id, product.Id, mediaPlan.Id, customer.Id, lineStartDate.Date, mediaPlan.StartDate.Date);
            }
            if (lineEndDate.Date > mediaPlan.EndDate.Date)
            {
                result.AddError(new Error(source: source, message: ErrorMessage.NotInParentRange, property: [nameof(line.EndDateTime)], type: ErrorType.UserError),
                    logger, "Line end date is after media plan end date. LineId: {LineId}, ProductId: {ProductId}, MediaPlanId: {MediaPlanId}, CustomerId: {CustomerId}, lineEndDate: {LineEndDate}, mediaPlan.EndDate: {MediaPlanEndDate}",
                    line.Id, product.Id, mediaPlan.Id, customer.Id, lineEndDate.Date, mediaPlan.EndDate.Date);
            }

            return result;
        }

        public static async Task<Result> ValidateTargeting(string source, Target[]? targets, Product product, ISegmentDao segmentDao, ICountryDao countryDao, ILocationDataAccessObject locationDao, IKeyValueDataAccessObject keyValueDao, ILogger logger)
        {
            var result = new Result();
            if (targets != null && targets.Length > 0)
            {
                if (product.SupportedTargets == null || product.SupportedTargets.Length == 0)
                {
                    result.AddError(new Error(source: source, message: ErrorMessage.NoSupportedTargeting, property: [nameof(targets)], type: ErrorType.UserError),
                        logger, "No supported targeting. ProductId: {ProductId}", product.Id);
                    return result;
                }

                var supportedTargetTypes = product.SupportedTargets!
                                       .Select(st => st.TargetTypeId)
                                       .ToHashSet();

                if (DynamicConfigWrapper.EnableDMATargeting)
                {
                    var dmaTargets = targets.OfType<DMATarget>().ToArray();
                    if (dmaTargets.Length > 1)
                    {
                        result.AddError(new Error(
                            source,
                            ErrorMessage.OnlyOneDMATargetAllowed,
                            property: [nameof(TargetType.DMA)],
                            type: ErrorType.UserError
                        ), logger, "Only one DMA target allowed. ProductId: {ProductId}", product.Id);
                    }

                    var dmaIncludeCount = dmaTargets.Count(dt => !dt.IsExcluded);
                    var dmaExcludeCount = dmaTargets.Count(dt => dt.IsExcluded);

                    if (dmaIncludeCount > 0 && dmaExcludeCount > 0)
                    {
                        result.AddError(new Error(
                            source,
                            ErrorMessage.CannotIncludeAndExcludeDMATargets,
                            property: [nameof(TargetType.DMA)],
                            type: ErrorType.UserError
                        ), logger, "Cannot include and exclude DMA targets. ProductId: {ProductId}", product.Id);
                    }

                    if (dmaIncludeCount + dmaExcludeCount > 1)
                    {
                        result.AddError(new Error(
                            source,
                            ErrorMessage.OnlyOneDMATargetAllowed,
                            property: new[] { nameof(TargetType.DMA) },
                            type: ErrorType.UserError
                        ), logger, "Only one DMA target allowed (include+exclude). ProductId: {ProductId}", product.Id);
                    }

                    foreach (var dmaTarget in dmaTargets)
                    {
                        if (dmaTarget.Ids == null || dmaTarget.Ids.Length == 0)
                        {
                            result.AddError(new Error(
                                source,
                                ErrorMessage.DmaTargetIdsRequired,
                                property: [nameof(dmaTarget.Ids)],
                                type: ErrorType.UserError
                            ), logger, "DMA target IDs required. ProductId: {ProductId}", product.Id);
                        }

                        var conflictingDmaTargets = dmaTargets
                            .Where(dt => dt.IsExcluded == dmaTarget.IsExcluded
                                         && dt.Ids != null
                                         && dmaTarget.Ids != null
                                         && dt.Ids.Intersect(dmaTarget.Ids).Any())
                            .ToList();

                        if (conflictingDmaTargets.Count > 1)
                        {
                            result.AddError(new Error(
                                source,
                                ErrorMessage.CannotIncludeAndExcludeDMATargets,
                                property: [nameof(dmaTarget.Ids)],
                                type: ErrorType.UserError
                            ), logger, "Conflicting DMA targets. ProductId: {ProductId}", product.Id);
                        }
                    }
                }

                //Booleans are for FrequencyAndRecency validation. Outside loop to ensure we catch if multiple FAR targets are supplied.
                bool hasDailyFreq = false;
                bool hasHourlyFreq = false;
                bool hasWeeklyFreq = false;
                bool hasMonthlyFreq = false;
                bool hasLifetimeFreq = false;
                bool hasRecency = false;

                //Below variables are for Audience validation
                var includeAudienceTargetingType = AudienceTargetingType._;
                var excludeAudienceTargetingType = AudienceTargetingType._;

                foreach (var target in targets)
                {

                    var tt = Target.GetTargetType(target);
                    if (!supportedTargetTypes.Contains(tt))
                    {
                        result.AddError(new Error(
                            source: source,
                            message: ErrorMessage.UnsupportedTargetType,
                            property: new[] { nameof(targets) },
                            type: ErrorType.UserError
                        ), logger, "Unsupported target type {TargetType} for ProductId {ProductId}", tt, product.Id);
                        return result;
                    }

                    if (target is AudienceTarget audTarget)
                    {
                        if (audTarget.AudienceTargetingType == AudienceTargetingType._)
                        {
                            result.AddError(new Error(source: source, message: ErrorMessage.InvalidValue, property: [nameof(audTarget.AudienceTargetingType)], type: ErrorType.UserError),
                                logger, "Invalid audience targeting type. AudienceTargetingType: {AudienceTargetingType}, ProductId: {ProductId}", audTarget.AudienceTargetingType, product.Id);
                            continue;
                        }

                        if ((audTarget.IsExcluded && excludeAudienceTargetingType != AudienceTargetingType._)
                            || (!audTarget.IsExcluded && includeAudienceTargetingType != AudienceTargetingType._))
                        {
                            result.AddError(new Error(source: source, message: ErrorMessage.MultipleAudienceTargetsOfSameTimeNotSupported, property: [nameof(audTarget)], type: ErrorType.UserError),
                                logger, "Multiple audience targets of same type not supported. ProductId: {ProductId}", product.Id);
                            continue;
                        }

                        if (audTarget.IsExcluded) { excludeAudienceTargetingType = audTarget.AudienceTargetingType; }
                        else { includeAudienceTargetingType = audTarget.AudienceTargetingType; }

                        //Need to do segment validation, ensuring that the requested segments for targeting exist
                        var request = new SegmentGetAllRequest(logger, product.PublisherId);
                        var getSegmentResult = await segmentDao.Get(request);
                        result.Merge(getSegmentResult);

                        if (getSegmentResult.Succeeded)
                        {
                            var segments = getSegmentResult.Entity;
                            if (segments == null || segments.Length == 0)
                            {
                                result.AddError(new Error(source: source, message: ErrorMessage.InvalidSegmentForTargeting, property: [nameof(targets)], type: ErrorType.UserError),
                                    logger, "No segments found for targeting. ProductId: {ProductId}", product.Id);
                            }
                            else
                            {
                                var intersect = segments.Select(_ => _.Id).Intersect(audTarget.Ids);
                                if (intersect.Count() != audTarget.Ids.Length)
                                {
                                    result.AddError(new Error(source: source, message: ErrorMessage.InvalidSegmentForTargeting, property: [nameof(targets)], type: ErrorType.UserError),
                                        logger, "Invalid segment IDs for targeting. ProductId: {ProductId}, SegmentIds: {SegmentIds}", product.Id, string.Join(", ", audTarget.Ids));
                                }
                            }
                        }
                    }
                    else if (target is LocationTarget locationTarget)
                    {
                        bool isNewLocationFlow = locationTarget.Locations != null;

                        if (isNewLocationFlow && locationTarget.Ids != null && locationTarget.Ids.Length > 0)
                        {
                            logger.LogWarning("Cannot mix legacy Ids with Locations in LocationTarget. Ignoring Ids and using Locations only.");
                        }
                        // Validate new style
                        else if (isNewLocationFlow)
                        {
                            result.Merge(await ValidateLocationTargetHierarchy(source, logger, locationTarget, locationDao));
                        }
                        // Validate old style
                        else if (locationTarget.Ids != null && locationTarget.Ids.Length > 0)
                        {
                            var request = new CountryGetAllRequest(logger);
                            var getCountryResult = await countryDao.Get(request);
                            result.Merge(getCountryResult);

                            if (getCountryResult.Succeeded)
                            {
                                var countries = getCountryResult.Entity;
                                if (countries == null || countries.Length == 0)
                                {
                                    result.AddError(new Error(
                                        source,
                                        ErrorMessage.InvalidLocationForTargeting,
                                        property: [nameof(targets)],
                                        type: ErrorType.UserError
                                    ), logger, "No countries found for targeting. ProductId: {ProductId}", product.Id);
                                }
                                else
                                {
                                    var intersect = countries.Select(_ => _.Id).Intersect(locationTarget.Ids);
                                    if (intersect.Count() != locationTarget.Ids.Length)
                                    {
                                        result.AddError(new Error(
                                            source,
                                            ErrorMessage.InvalidLocationForTargeting,
                                            property: [nameof(targets)],
                                            type: ErrorType.UserError
                                        ), logger, "Invalid country IDs for targeting. ProductId: {ProductId}, CountryIds: {CountryIds}", product.Id, string.Join(", ", locationTarget.Ids));
                                    }
                                }
                            }
                        }
                        else
                        {
                            result.AddError(new Error(
                                source,
                                ErrorMessage.LocationTargetNoValidData,
                                property: [nameof(locationTarget)],
                                type: ErrorType.UserError
                            ), logger, "LocationTarget must have either Ids or Locations set. ProductId: {ProductId}", product.Id);
                        }
                    }
                    else if (target is FrequencyAndRecencyTarget frequencyAndRecencyTarget)
                    {
                        //Need to validate that we only have 1 of each "sub-type" of this target type
                        foreach (var far in frequencyAndRecencyTarget.FrequencyAndRecencys)
                        {
                            if (far.Unit.HasValue)
                            {
                                switch (far.Unit)
                                {
                                    case FrequencyUnit.Hour:
                                        if (hasHourlyFreq)
                                        {
                                            result.AddError(new Error(source: source, message: ErrorMessage.OnlyOneHourlyFrequencyTargetSupported, property: [nameof(targets)], type: ErrorType.UserError),
                                                logger, "Only one Hourly Frequency target supported. ProductId: {ProductId}", product.Id);
                                        }
                                        hasHourlyFreq = true;
                                        break;
                                    case FrequencyUnit.Day:
                                        if (hasDailyFreq)
                                        {
                                            result.AddError(new Error(source: source, message: ErrorMessage.OnlyOneDailyFrequencyTargetSupported, property: [nameof(targets)], type: ErrorType.UserError),
                                                logger, "Only one Daily Frequency target supported. ProductId: {ProductId}", product.Id);
                                        }
                                        hasDailyFreq = true;
                                        break;
                                    case FrequencyUnit.Week:
                                        if (hasWeeklyFreq)
                                        {
                                            result.AddError(new Error(source: source, message: ErrorMessage.OnlyOneWeeklyFrequencyTargetSupported, property: [nameof(targets)], type: ErrorType.UserError),
                                                logger, "Only one Weekly Frequency target supported. ProductId: {ProductId}", product.Id);
                                        }
                                        hasWeeklyFreq = true;
                                        break;
                                    case FrequencyUnit.Month:
                                        if (hasMonthlyFreq)
                                        {
                                            result.AddError(new Error(source: source, message: ErrorMessage.OnlyOneMonthlyFrequencyTargetSupported, property: [nameof(targets)], type: ErrorType.UserError),
                                                logger, "Only one Monthly Frequency target supported. ProductId: {ProductId}", product.Id);
                                        }
                                        hasMonthlyFreq = true;
                                        break;
                                    case FrequencyUnit.Lifetime:
                                        if (hasLifetimeFreq)
                                        {
                                            result.AddError(new Error(source: source, message: ErrorMessage.OnlyOneLifetimeFrequencyTargetSupported, property: [nameof(targets)], type: ErrorType.UserError),
                                                logger, "Only one Lifetime Frequency target supported. ProductId: {ProductId}", product.Id);
                                        }
                                        hasLifetimeFreq = true;
                                        break;
                                    case FrequencyUnit._:
                                        continue;
                                    default:
                                        result.AddError(new Error(source: source, message: ErrorMessage.InvalidValue, property: [nameof(far.Unit)], type: ErrorType.UserError),
                                            logger, "Invalid FrequencyUnit. Unit: {Unit}, ProductId: {ProductId}", far.Unit, product.Id);
                                        break;
                                }
                                if (far.Number == null)
                                {
                                    result.AddError(new Error(source: source, message: ErrorMessage.ValueRequired, property: [nameof(far.Number)], type: ErrorType.UserError),
                                        logger, "Frequency number is required. Unit: {Unit}, ProductId: {ProductId}", far.Unit, product.Id);
                                }
                                if (far.Number < 0 || far.Number > 255)
                                {
                                    result.AddError(new Error(source: source, message: ErrorMessage.InvalidValue, property: [nameof(far.Number)], type: ErrorType.UserError),
                                        logger, "Frequency number out of range. Unit: {Unit}, Number: {Number}, ProductId: {ProductId}", far.Unit, far.Number, product.Id);
                                }
                            }
                            if (far.MinutesPerImpression.HasValue)
                            {
                                if (hasRecency)
                                {
                                    result.AddError(new Error(source: source, message: ErrorMessage.OnlyOneRecencyTargetSupported, property: [nameof(targets)], type: ErrorType.UserError),
                                        logger, "Only one Recency target supported. ProductId: {ProductId}", product.Id);
                                    continue;
                                }
                                hasRecency = true;
                            }
                        }
                    }
                }
                if (DynamicConfigWrapper.EnableKVPTargeting && targets.OfType<KeyValueTarget>().Any())
                {

                    var rawKvTargets = targets.OfType<KeyValueTarget>().ToList();
                    foreach (var kv in rawKvTargets)
                    {
                        if (kv.Entries == null || kv.Entries.Length == 0)
                        {
                            result.AddError(new Error(
                                source: source,
                                message: ErrorMessage.ValueRequired,
                                property: new[] { nameof(KeyValueTarget.Entries) },
                                type: ErrorType.UserError
                            ), logger, "KeyValueTarget must have at least one entry");
                            return result;
                        }
                    }

                    var kvTargets = targets
                            .OfType<KeyValueTarget>()
                            .Where(kv => kv.Entries?.Length > 0)
                            .ToList();

                    var keyGroups = new Dictionary<string, (HashSet<string> includes, HashSet<string> excludes)>(StringComparer.OrdinalIgnoreCase);

                    foreach (var kv in kvTargets)
                    {
                        foreach (var entry in kv.Entries)
                        {
                            var key = entry.Key?.Trim();
                            if (string.IsNullOrWhiteSpace(key))
                            {
                                logger.LogUserError($"Key is null or empty in KeyValueEntry. Source: {source}, ProductId: {product.Id}");
                                result.AddError(new Error(source, ErrorMessage.InvalidKeyFormat, property: ["<null>"], type: ErrorType.UserError));
                                continue;
                            }

                            foreach (var part in entry.Values.Select(v => v?.Trim()).Where(v => !string.IsNullOrWhiteSpace(v)))
                            {
                                var safePart = part!;

                                if (safePart.Length > 100 || !ValueRegex.IsMatch(safePart))
                                {
                                    logger.LogUserError($"Invalid value format in KeyValueTarget. Source: {source}, Key: {key}, Value: {safePart}");
                                    result.AddError(new Error(source, ErrorMessage.InvalidValueFormat, property: [key, safePart], type: ErrorType.UserError));
                                    continue;
                                }

                                if (!keyGroups.TryGetValue(key, out var group))
                                {
                                    group = (
                                        new HashSet<string>(StringComparer.OrdinalIgnoreCase),
                                        new HashSet<string>(StringComparer.OrdinalIgnoreCase)
                                    );
                                    keyGroups[key] = group;
                                }

                                if (entry.IsExcluded)
                                    group.excludes.Add(safePart);
                                else
                                    group.includes.Add(safePart);
                            }
                        }
                    }

                    foreach (var (key, (includes, excludes)) in keyGroups)
                    {
                        if (string.IsNullOrWhiteSpace(key) || key.Length > 50 || !KeyRegex.IsMatch(key))
                        {
                            result.AddError(new Error(source, ErrorMessage.InvalidKeyFormat, property: [key], type: ErrorType.UserError),
                                logger, "Invalid key format in KeyValueTarget. Source: {Source}, Key: {Key}", source, key);
                            continue;
                        }

                        var keyResult = await keyValueDao.GetKey(key, logger);
                        result.Merge(keyResult);
                        if (keyResult.Failed || keyResult.Entity == null)
                        {
                            result.AddError(new Error(source, ErrorMessage.InvalidKey, property: [key], type: ErrorType.UserError),
                                logger, "Key not found in KeyValueTarget. Source: {Source}, Key: {Key}", source, key);
                            continue;
                        }

                        var expectedType = keyResult.Entity.Type;

                        if (expectedType == ValueTypes.StringArray || expectedType == ValueTypes.NumericArray)
                        {
                            logger.LogUserError(
                              $"Key '{key}' is defined as '{expectedType}', which is not allowed. Only String and Numeric target types are supported.");
                            result.AddError(new Error(
                                source,
                                ErrorMessage.InvalidValueType,
                                new[] { key, expectedType.ToString() },
                                ErrorType.UserError
                            ));
                            continue;
                        }

                        foreach (var supplied in includes.Union(excludes))
                        {
                            if ((expectedType == ValueTypes.Numeric)
                                && !decimal.TryParse(supplied, out _))
                            {
                                logger.LogUserError($"Value-type mismatch for key '{key}': expected numeric, got '{supplied}'");
                                result.AddError(new Error(
                                    source,
                                    ErrorMessage.InvalidValueType,
                                    new[] { key, supplied },
                                    ErrorType.UserError
                                ));
                            }
                        }


                        if (includes.Overlaps(excludes))
                        {
                            result.AddError(new Error(source, ErrorMessage.KeyHasOverlappingIncludeExcludeValues, property: [key], type: ErrorType.UserError),
                                logger, "Key has overlapping include/exclude values. Source: {Source}, Key: {Key}", source, key);
                        }

                        var allValues = new HashSet<string>(includes, StringComparer.OrdinalIgnoreCase);
                        allValues.UnionWith(excludes);
                        if (allValues.Count == 0)
                        {
                            result.AddError(new Error(source, ErrorMessage.KeyHasNoValues, property: [key], type: ErrorType.UserError),
                                logger, "Key has no values in KeyValueTarget. Source: {Source}, Key: {Key}", source, key);
                            continue;
                        }

                        var valuesCsv = string.Join(",", allValues);
                        if (valuesCsv.Length > 4000 || allValues.Count > 100)
                        {
                            logger.LogUserError($"Too many values for key in KeyValueTarget. Source: {source}, Key: {key}, ValuesCount: {allValues.Count}");
                            result.AddError(new Error(source, ErrorMessage.TooManyValuesForKey, property: [key]));
                        }

                        var targetingKeyId = keyResult.Entity.Id;
                        var request = new KeyValueGetAllValuesRequest(logger, targetingKeyId);

                        var valuesResult = await keyValueDao.GetAllValues(request, logger);
                        result.Merge(valuesResult);

                        var returnedValues = new HashSet<string>(
                            valuesResult.Entity?.Select(v => v.Name.ToString() ?? string.Empty)
                                ?? Enumerable.Empty<string>(),
                            StringComparer.OrdinalIgnoreCase
                        );

                        foreach (var val in allValues)
                        {
                            if (!returnedValues.Contains(val))
                            {
                                result.AddError(new Error(source, ErrorMessage.InvalidValue, property: [key, val], type: ErrorType.UserError),
                                    logger, "Invalid value for key in KeyValueTarget. Source: {Source}, Key: {Key}, Value: {Value}", source, key, val);
                            }
                        }
                    }
                }
            }
            return result;
        }

        public static async Task<Result> ValidateLocationTargetHierarchy(
        string source,
        ILogger logger,
        LocationTarget locationTarget,
        ILocationDataAccessObject locationDao)
        {
            var result = new Result();

            var request = new LocationGetAllRequest(logger);

            //locationTarget.Locations confirmed not null in caller
            var locationMap = locationTarget.Locations!.ToList();

            List<TargetType> supportedTargetTypes = new List<TargetType>()
            {
                TargetType.Country,
                TargetType.Region,
                TargetType.City
            };

            if (locationMap.Where(location => !supportedTargetTypes.Contains(location.Type)).Any())
            {
                result.AddError(new Error(source, ErrorMessage.LocationTargetInvalidData, [nameof(locationTarget.Locations)], type: ErrorType.UserError)
                    , logger, "At least one location in LocationTarget.Locations array has invalid Target Type. Check caller to identify problematic entity(s).");
            }

            var targetedCityIds = locationMap
                .Where(l => l.Type == TargetType.City)
                .Select(l => (int)l.Id)
                .Distinct()
                .ToList() ?? new List<int>();

            // If the number of targeted cities exceeds the maximum allowed, exit immediately.
            if (targetedCityIds.Count > DynamicConfigWrapper.MaxTargetedCities)
            {
                result.AddError(new Error(
                    source,
                    ErrorMessage.TooManyTargetedCities,
                    property: [nameof(locationTarget.Locations)],
                    type: ErrorType.UserError
                ), logger, $"Too many targeted cities. Source: {source}, Count: {targetedCityIds.Count}, Max:  {DynamicConfigWrapper.MaxTargetedCities}");
                return result;
            }

            var countriesTask = locationDao.GetAllCountries(request);
            var regionsTask = locationDao.GetAllRegions(request);

            XandrCity[] cities = Array.Empty<XandrCity>();
            if (targetedCityIds.Count == 1)
            {
                var cityResult = await locationDao.GetCityById(targetedCityIds[0], logger);
                if (cityResult.Succeeded && cityResult.Entity != null)
                    cities = new[] { cityResult.Entity };
            }
            else if (targetedCityIds.Count > 1)
            {
                var citiesResult = await locationDao.GetCitiesByIdsAsync(targetedCityIds, logger);
                if (citiesResult.Succeeded && citiesResult.Entity != null)
                    cities = citiesResult.Entity;
            }

            await Task.WhenAll(countriesTask, regionsTask);

            if (countriesTask.Result.Failed || regionsTask.Result.Failed)
            {
                logger.LogError("Failed to retrieve location hierarchy. Source: {source}", source);
                result.AddError(new Error(source, "Failed to retrieve location hierarchy."));
                return result;
            }

            var countries = countriesTask.Result.Entity!;
            var regions = regionsTask.Result.Entity!;
            var included = locationMap.Where(x => x.IsExcluded == false).ToList();
            var excluded = locationMap.Where(x => x.IsExcluded == true).ToList();

            // 1. Rule: no mixed Include/Exclude at same TargetType level
            LocationValidationHelper.ValidateNoMixedInclusionExclusion(source, included, excluded, result, request.Logger);

            // 2. Rule: no child can be INCLUDED if parent is INCLUDED
            foreach (var parent in included)
            {
                if (LocationValidationHelper.HasIncludedChild(parent.Type, parent.Id, regions, cities, included))
                {
                    result.AddError(new Error(source, ErrorMessage.ChildLocationsOfIncludedTargetCannotBeIncluded, property: [nameof(locationTarget.Locations)], type: ErrorType.UserError),
                        logger, "Targeted {TargetType} {TargetId} cannot have child targets also targeted. Source: {Source}", parent.Type, parent.Id, source);
                }
            }

            // 3. Rule: no child can be INCLUDED if parent is EXCLUDED
            foreach (var parent in excluded)
            {
                if (LocationValidationHelper.HasAnyChild(parent.Type, parent.Id, regions, cities, locationMap))
                {
                    result.AddError(new Error(source, ErrorMessage.ChildLocationsOfExcludedTargetCannotBeTargeted, property: [nameof(locationTarget.Locations)], type: ErrorType.UserError),
                        logger, "Targeted child of excluded {TargetType} {TargetId} is not allowed. Source: {Source}", parent.Type, parent.Id, source);
                }
            }

            return result;
        }

        public static bool DoesLineRequireForecastRefresh(Line line, Product product, Forecast? previousForecast, PublisherApproval? approval)
        {
            if (product.Type == ProductType.Roadblock) { return false; }

            var targetImpressions = LineBusinessLogicHelper.CalculateRotationalImpressions(line.TargetImpressions, line.TargetSpend, line.Cpm);

            ScoreCard? previousScoreCard = null;
            if (previousForecast != null)
            {
                var previousScoreCardOutput = previousForecast.ToScoreCardOutput(targetImpressions);

                //ScoreCardOutput object will set "remaining" = Math.Max(0, available - booked) so we don't show users a negative. We want the negative if it exists here
                previousScoreCard = previousScoreCardOutput?.ToScoreCard();
            }

            return approval == null || !LineBusinessLogicHelper.IsYieldAnalyticsApprovalNeeded(previousScoreCard, product.Type);
        }

        public static bool YieldAnalyticsFinalCheck(Line line, Product product, Forecast? forecast, PublisherApproval? approval)
        {
            if (product.Type == ProductType.Roadblock) { return false; }

            var targetImpressions = LineBusinessLogicHelper.CalculateRotationalImpressions(line.TargetImpressions, line.TargetSpend, line.Cpm);

            ScoreCard? scoreCard = null;
            if (forecast != null)
            {
                var scoreCardOutput = forecast.ToScoreCardOutput(targetImpressions);

                //ScoreCardOutput object will set "remaining" = Math.Max(0, available - booked) so we don't show users a negative. We want the negative if it exists here
                scoreCard = scoreCardOutput?.ToScoreCard();
            }

            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")?.ToUpperInvariant() ?? ServiceHelper.TEST_ENVIRONMENT.ToUpperInvariant();

            return approval != null || !(environment != ServiceHelper.TEST_ENVIRONMENT.ToUpperInvariant() && LineBusinessLogicHelper.IsYieldAnalyticsApprovalNeeded(scoreCard, product.Type));
        }

        private static bool TryConvertToArray(object? input, out object[] filterValues)
        {
            try
            {
                filterValues = JsonSerializer.Deserialize<object[]>(JsonSerializer.Serialize(input)) ?? Array.Empty<object>();
                return filterValues.Length > 0;
            }
            catch (JsonException)
            {
                filterValues = Array.Empty<object>();
                return false;
            }
        }

        public static Result ValidateFilter(String filter, ILogger logger)
        {
            var result = new Result();
            try
            {
                FilterInput[] filterInputs = JsonSerializer.Deserialize<FilterInput[]>(filter, new JsonSerializerOptions { PropertyNameCaseInsensitive = true })!;
                foreach (var filterInput in filterInputs)
                {
                    if (!ValidatorConstants.ALLOWED_OPERATORS.Contains(filterInput.Operator))
                    {
                        result.AddError(new Error(source: $"{filter}", message: ErrorMessage.InvalidOperation, property: [nameof(filterInput.Operator)], type: ErrorType.UserError),
                            logger, "Invalid operator in filter: {Operator}", filterInput.Operator);
                    }
                    switch (filterInput.Field)
                    {
                        case "Status":
                            if (filterInput.Operator != "in" && filterInput.Operator != "eq")
                            {
                                result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidOperation, property: [nameof(filterInput.Operator)], type: ErrorType.UserError),
                                    logger, "Invalid operator for Status field: {Operator}", filterInput.Operator);
                            }

                            if (TryConvertToArray(filterInput.Value, out Object[] statusArray))
                            {
                                if (filterInput.Operator == "eq")
                                {
                                    result.AddError(new Error(source: $"filterInput", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Invalid value for Status field with 'eq' operator: {Value}", filterInput.Value);
                                }
                                foreach (var entry in statusArray)
                                {
                                    if (!Enum.TryParse(entry.ToString(), out Entities.LineStatus status))
                                    {
                                        result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                            logger, "Invalid value for Status field: {Entry}", entry);
                                        break;
                                    }
                                }
                            }
                            else
                            {
                                if (filterInput.Operator == "in" || !Enum.TryParse(filterInput.Value.ToString(), out Entities.LineStatus status))
                                {
                                    result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Invalid value for Status field: {Value}", filterInput.Value);
                                }
                            }
                            break;

                        case "Type":
                            if (filterInput.Operator != "in" && filterInput.Operator != "eq")
                            {
                                result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidOperation, property: [nameof(filterInput.Operator)], type: ErrorType.UserError),
                                    logger, "Invalid operator for Type field: {Operator}", filterInput.Operator);
                            }

                            if (TryConvertToArray(filterInput.Value, out Object[] typeArray))
                            {
                                if (filterInput.Operator == "eq")
                                {
                                    result.AddError(new Error(source: $"filterInput", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Invalid value for Type field with 'eq' operator: {Value}", filterInput.Value);
                                }
                                foreach (var entry in typeArray)
                                {
                                    if (!Enum.TryParse(entry.ToString(), out Entities.LineType status))
                                    {
                                        result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                            logger, "Invalid value for Type field: {Entry}", entry);
                                        break;
                                    }
                                }
                            }
                            else
                            {
                                if (filterInput.Operator == "in" || !Enum.TryParse(filterInput.Value.ToString(), out Entities.LineType status))
                                {
                                    result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Invalid value for Type field: {Value}", filterInput.Value);
                                }
                            }
                            break;

                        case "ID":
                            if (filterInput.Operator != "contains" && filterInput.Operator != "eq")
                            {
                                result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidOperation, property: [nameof(filterInput.Operator)], type: ErrorType.UserError),
                                    logger, "Invalid operator for ID field: {Operator}", filterInput.Operator);
                            }
                            break;

                        case "StartDate":
                        case "EndDate":
                            if (filterInput.Operator == "in" || filterInput.Operator == "contains")
                            {
                                result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidOperation, property: [nameof(filterInput.Operator)], type: ErrorType.UserError),
                                    logger, "Invalid operator for Date field: {Operator}", filterInput.Operator);
                            }
                            if (TryConvertToArray(filterInput.Value, out Object[] array1))
                            {
                                if (filterInput.Operator != "between")
                                {
                                    result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Invalid value for Date field with 'between' operator: {Value}", filterInput.Value);
                                }
                                else
                                {
                                    var dateArray = new DateTime[array1.Length];
                                    int date_index = 0;
                                    for (; date_index < array1.Length; date_index++)
                                    {
                                        if (DateTime.TryParse(array1[date_index].ToString(), out DateTime date))
                                        {
                                            dateArray[date_index] = date;
                                        }
                                        else
                                        {
                                            result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                                logger, "Invalid value for Date field: {Value}", array1[date_index]);
                                            break;
                                        }
                                    }
                                    if (dateArray.Length == 1)
                                    {
                                        result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.TooFewValues, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                            logger, "Too few values for Date field: {Value}", filterInput.Value);
                                    }
                                    if (dateArray.Length == 2 && date_index == 2 && dateArray[0] > dateArray[1])
                                    {
                                        result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                            logger, "Invalid date range for Date field: {Value}", filterInput.Value);
                                    }
                                    if (dateArray.Length > 2)
                                    {
                                        result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.TooManyValues, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                            logger, "Too many values for Date field: {Value}", filterInput.Value);
                                    }
                                }
                            }
                            else
                            {
                                if (filterInput.Operator == "between")
                                {
                                    result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Invalid value for Date field with 'between' operator: {Value}", filterInput.Value);
                                }
                                if (!DateTime.TryParse(filterInput.Value.ToString(), out DateTime dateTime))
                                {
                                    result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Invalid value for Date field: {Value}", filterInput.Value);
                                }
                            }
                            break;

                        case "Name":
                            filterInput.Value = WebUtility.HtmlEncode(filterInput.Value.ToString() ?? String.Empty);
                            if (filterInput.Operator != "contains" && filterInput.Operator != "eq")
                            {
                                result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidOperation, property: [nameof(filterInput.Operator)], type: ErrorType.UserError),
                                    logger, "Invalid operator for Name field: {Operator}", filterInput.Operator);
                            }
                            if (filterInput.Value is not String || String.IsNullOrEmpty(filterInput.Value.ToString()))
                            {
                                result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                    logger, "Invalid value for Name field: {Value}", filterInput.Value);
                            }
                            break;

                        case "Budget":
                            if (filterInput.Operator == "in" || filterInput.Operator == "contains")
                            {
                                result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidOperation, property: [nameof(filterInput.Operator)], type: ErrorType.UserError),
                                    logger, "Invalid operator for Budget field: {Operator}", filterInput.Operator);
                            }
                            if (TryConvertToArray(filterInput.Value, out Object[] array2))
                            {
                                if (filterInput.Operator != "between")
                                {
                                    result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Invalid value for Budget field with 'between' operator: {Value}", filterInput.Value);
                                }

                                var numArray = new decimal[array2.Length];
                                int num_index = 0;
                                for (; num_index < array2.Length; num_index++)
                                {
                                    if (decimal.TryParse(array2[num_index].ToString(), out decimal num))
                                    {
                                        numArray[num_index] = num;
                                    }
                                    else
                                    {
                                        result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                            logger, "Invalid value for Budget field: {Value}", array2[num_index]);
                                        break;
                                    }
                                }
                                if (numArray.Length == 1)
                                {
                                    result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.TooFewValues, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Too few values for Budget field: {Value}.", filterInput.Value);
                                }
                                if (numArray.Length == 2 && num_index == 2 && numArray[0] > numArray[1])
                                {
                                    result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Invalid budget range for Budget field: {Value}", filterInput.Value);
                                }
                                if (numArray.Length > 2)
                                {
                                    result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.TooManyValues, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Too many values for Budget field: {Value}", filterInput.Value);
                                }
                            }
                            else
                            {
                                if (filterInput.Operator == "between" || !decimal.TryParse(filterInput.Value.ToString(), out decimal num))
                                {
                                    result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Invalid value for Budget field: {Value}", filterInput.Value);
                                }
                            }
                            break;

                        case "EstimatedImpressions":
                            if (filterInput.Operator == "in" || filterInput.Operator == "contains")
                            {
                                result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidOperation, property: [nameof(filterInput.Operator)], type: ErrorType.UserError),
                                    logger, "Invalid operator for EstimatedImpressions field: {Operator}", filterInput.Operator);
                            }
                            if (TryConvertToArray(filterInput.Value, out Object[] array3))
                            {
                                if (filterInput.Operator != "between")
                                {
                                    result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Invalid value for EstimatedImpressions field with 'between' operator: {Value}", filterInput.Value);
                                }

                                var numArray = new long[array3.Length];
                                int num_index = 0;
                                for (; num_index < array3.Length; num_index++)
                                {
                                    if (long.TryParse(array3[num_index].ToString(), out long num))
                                    {
                                        numArray[num_index] = num;
                                    }
                                    else
                                    {
                                        result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                            logger, "Invalid value for EstimatedImpressions field: {Value}", array3[num_index]);
                                        break;
                                    }
                                }
                                if (numArray.Length == 1)
                                {
                                    result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.TooFewValues, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Too few values for EstimatedImpressions field: {Value}", filterInput.Value);
                                }
                                if (numArray.Length == 2 && num_index == 2 && numArray[0] > numArray[1])
                                {
                                    result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Invalid estimated impressions range for EstimatedImpressions field: {Value}", filterInput.Value);
                                }
                                if (numArray.Length > 2)
                                {
                                    result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.TooManyValues, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Too many values for EstimatedImpressions field: {Value}", filterInput.Value);
                                }
                            }
                            else
                            {
                                if (filterInput.Operator == "between" || !long.TryParse(filterInput.Value.ToString(), out long num))
                                {
                                    result.AddError(new Error(source: $"{filterInput}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Value)], type: ErrorType.UserError),
                                        logger, "Invalid value for EstimatedImpressions field: {Value}", filterInput.Value);
                                }
                            }
                            break;
                        default:
                            result.AddError(new Error(source: $"{filter}", message: ErrorMessage.InvalidValue, property: [nameof(filterInput.Field)], type: ErrorType.UserError),
                                logger, "Invalid field in filter: {Field}", filterInput.Field);
                            break;
                    }
                }
            }
            catch (Exception)
            {
                result.AddError(new Error(source: $"{filter}", message: ErrorMessage.InvalidValue, property: [nameof(filter)], type: ErrorType.UserError),
                    logger, "Invalid filter value: {Filter}", filter);
            }
            return result;
        }

        private static Result ValidateLineFields(string source, Line line, ILogger logger)
        {
            var result = new Result();

            if (string.IsNullOrWhiteSpace(line.Name) || line.Name.Trim().Length == 0)
            {
                result.AddError(new Error(source: $"{nameof(line.Name)}", message: ErrorMessage.LineNameIsRequired, type: ErrorType.UserError),
                    logger, "Line name is required. LineId: {LineId}", line.Id);
            }
            if (ValidatorConstants.ForbiddenSymbolRegex.IsMatch(line.Name))
            {
                result.AddError(new Error(source: $"{nameof(line.Name)}", message: ErrorMessage.LineNameContainsForbiddenSymbols, type: ErrorType.UserError),
                    logger, "Line name contains forbidden symbols. LineId: {LineId}, Name: {Name}", line.Id, line.Name);
            }

            return result;
        }
    }
}
