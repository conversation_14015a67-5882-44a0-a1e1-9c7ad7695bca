﻿namespace Microsoft.Advertising.Advertiser.Api.V2
{
    using CampaignMiddleTierTest.Framework;
    using CampaignMiddleTierTest.Framework.Clickhouse;
    using CampaignMiddleTierTest.Framework.MiddletierTestObjects;
    using CampaignMiddleTierTest.Framework.Utilities;
    using CampaignTest.ApiFunctionalTests.Collections;
    using CampaignTest.Framework;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.Aggregator;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Messages;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.Advertising.Advertiser.Api.V2.TestHelper;
    using Microsoft.Data.SqlClient;
    using Microsoft.Test.Validation;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;
    using System;
    using System.Collections.Generic;
    using System.Configuration;
    using System.Data;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Text;

    [TestClass]
    [DeploymentItem("Microsoft.Data.SqlClient.SNI.x86.dll")]
    [DeploymentItem("Microsoft.Data.SqlClient.SNI.x64.dll")]
    public class GetCampaignsGridData : CampaignTestBase
    {
        public const string PerformanceMetrics = "PerformanceMetrics";
        public const string PeriodPerformanceMetrics = "PeriodPerformanceMetrics";
        public const string PerformanceMetricsChange = "PerformanceMetricsChange";
        public const string PerformanceMetricsChangePercentage = "PerformanceMetricsChangePercentage";

        public const string PhonePerformanceMetrics = "PhonePerformanceMetrics";
        public const string PhonePeriodPerformanceMetrics = "PhonePeriodPerformanceMetrics";
        public const string PhonePerformanceMetricsChange = "PhonePerformanceMetricsChange";
        public const string PhonePerformanceMetricsChangePercentage = "PhonePerformanceMetricsChangePercentage";

        public const string SeasonalityAdjustment = "0";
        public const string DataExclusion = "1";

        private static readonly float acceptableFloatingDelta = 0.02f;

        public static readonly List<long> InMarketAudienceIds = new List<long>();
        private static long PredictiveTargetingAudienceId = 815753130;

        private CustomerInfo cInfo = DefaultCustomer;
        private CustomerInfo ViewThroughConversionCustomerInfo = ViewThroughConversionPilotCustomer;
        private TestCampaignCollection campaignCollection;
        private TestCampaignCollection testCampaignCollection;

        private static BiDateRange today = new BiDateRange() { CustomDateRangeStart = DateTime.Today.Date, CustomDateRangeEnd = DateTime.Today.Date, Type = DateRangeType.CustomRange };
        private static BiDateRange yesterday = new BiDateRange() { CustomDateRangeStart = DateTime.Today.AddDays(-1).Date, CustomDateRangeEnd = DateTime.Today.AddDays(-1).Date, Type = DateRangeType.CustomRange };
        private static BiDateRange thisWeek = new BiDateRange() { CustomDateRangeStart = DateTime.Today.AddDays(-7).Date, CustomDateRangeEnd = DateTime.Today.Date, Type = DateRangeType.CustomRange };

        private static readonly TimeSpan timeAdjustmentForMovingAverage = TimeSpan.FromDays(28); // 4 weeks

        private static readonly string AppleStoreAppId = "979729863"; // https://apps.apple.com/us/app/microsoft-advertising/id979729863?platform=iphone

        private static Dictionary<SimplifiedBudgetLimitType, string> mtToODataBudgetType = new Dictionary<SimplifiedBudgetLimitType, string>
        {
            { SimplifiedBudgetLimitType.LifetimeBudgetStandard, "LifetimeBudgetStandard" },
            { SimplifiedBudgetLimitType.MonthlyBudgetSpendUntilDepleted, "Monthly" },
            { SimplifiedBudgetLimitType.DailyBudgetStandard, "DailyStandard" },
            { SimplifiedBudgetLimitType.DailyBudgetAccelerated, "DailyStandard" }
        };

        public static string[] AllSelectNonBiProperties = new string[]
        {
            "Id",
            "Name",
            "Status",
            "DeliveryStatus",
            "DeliveryStatusDetails",
            "QualityScore",
            "UpdatedOnLastImport",
            "HasAuctionInsight",
            "TrackingUrlTemplate",
            "UrlCustomParameters",
            "BiddingScheme",
            "Languages",
            "IsBidLandscapeAvailable",
            "OptimizationScore",
        };

        public static string[] AllSelectProperties = AllSelectNonBiProperties.Concat(new string[]
        {
            PerformanceMetrics,
            PeriodPerformanceMetrics,
            PerformanceMetricsChange,
            PerformanceMetricsChangePercentage,
            PhonePerformanceMetrics,
            PhonePeriodPerformanceMetrics,
            PhonePerformanceMetricsChange,
            PhonePerformanceMetricsChangePercentage,
        }).ToArray();

        public readonly static List<string> AllRequestableMetricsForInlineChart = new List<string>
        {
            "Clicks",
            "AverageCPC",
            "CTR",
            "Impressions",
            "Spend",
            "TopImpressionRate",
            "AbsoluteTopImpressionRate",
            "AdvertiserReportedRevenue",
            "RevenueOnAdSpend",
            "AllConversionAdvertiserReportedRevenue",
            "AllConversionRevenueOnAdSpend",
            "VideoViews",
            "ViewThroughRate",
            "AverageCPV",
            "TotalWatchTimeInMS",
            "AverageWatchTimePerImpression",
            "AverageWatchTimePerVideoView",
            "VideoViewsAt25Percent",
            "VideoViewsAt50Percent",
            "VideoViewsAt75Percent",
            "CompletedVideoViews",
            "VideoCompletionRate",
            "ViewThroughConversionsRevenue",
            "ViewThroughConversionsReturnOnAdSpend",
            "ConversionsCredit",
            "AllConversionsCredit",
            "ViewThroughConversionsCredit",
            //"PartialConversionCPA",
            "PartialConversionRate",
            "AllPartialConversionCPA",
            "AllPartialConversionRate",
            "Downloads",
            "CostPerDownload",
            "PostClickDownloadRate",
            "FirstLaunches",
            "CostPerFirstLaunch",
            "PostClickFirstLaunchRate",
            "Purchases",
            "PostInstallPurchaseRate",
            "CostPerPurchase",
            "Subscriptions",
            "PostInstallSubscriptionRate",
            "CostPerSubscription",
            "AuctionWonPercent",
            "AuctionLostToBudgetPercent",
            "AuctionLostToRankPercent",
            "ClickSharePercent",
        };

        private CustomerInfo personalizedOffersCustomer;
        private ShoppingStore _store;
        private long providerId = 100;

        private CustomerInfo personalizedOffersCustomerV3;
        private ShoppingStore _storeV3;
        private long providerIdV3 = 100;

        private CustomerInfo personalizedOffersCustomerCPS;
        private ShoppingStore _storeCPS;
        private long providerIdCPS = 100;

        private CustomerInfo sponsoredPromotionsForBrandsCustomer;
        private CustomerStore retailerStore;
        private CustomerLinkedStores customerLinkedStores;

        [TestInitialize]
        public void Initialize()
        {
            if (skipInit)
            {
                return;
            }
            base.InitializeTest();

            if (TestContext.TestName == "GetCampaignsGridData_MonthlyBudget" || TestContext.TestName == "GetCampaignsGridData_BidStrategyLimitedStatus_IfTrainingStatus")
            {
                cInfo = DefaultCustomer;
            }
            else
            {
                cInfo = CustomerInfo.CreateNewDefaultCustomerInfo();
            }

            TestCampaignCollection.Clean(cInfo);
            CreateCampaignsForCustomer(cInfo, 10);

            var inMarketAudiences = TestAudienceCriterion.GetInMarketAudiences(cInfo: cInfo);
            inMarketAudiences.ForEach(a => InMarketAudienceIds.Add(a.Id.Value));

            personalizedOffersCustomer = CustomerInfo.CreateStandardAdvertiserWithPilot(Features.PersonalizedOffers, Features.LocalInventoryAds);
            if (ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                _store = new ShoppingStore(personalizedOffersCustomer);
                providerId = _store.ProviderId;
            }

            personalizedOffersCustomerV3 = CustomerInfo.CreateStandardAdvertiserWithPilot(Features.PersonalizedOffersV3, Features.InStoreTransaction);
            if (ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                _storeV3 = new ShoppingStore(personalizedOffersCustomerV3);
                providerIdV3 = _storeV3.ProviderId;
            }

            personalizedOffersCustomerCPS = CustomerInfo.CreateStandardAdvertiserWithPilot(Features.PersonalizedOffersCPS, Features.InStoreTransaction);
            if (ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                _storeCPS = new ShoppingStore(personalizedOffersCustomerCPS);
                providerIdCPS = _storeCPS.ProviderId;
            }

            sponsoredPromotionsForBrandsCustomer = CustomerInfo.CreateStandardAdvertiserWithPilot(Features.SponsoredProductAdsV2, Features.SponsoredPromotionsForBrands, Features.ShoppableAds);
            if (ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                var retailerCustomer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
                    CustomerFactory.TargetCountry.US,
                    CustomerFactory.TargetLanguage.English,
                    1,
                    false,
                    false,
                    Features.PersonalizedOffers
                );

                retailerStore = new CustomerStore(retailerCustomer);
            }
            customerLinkedStores = new CustomerLinkedStores(sponsoredPromotionsForBrandsCustomer, retailerStore);

            CreateGoal(personalizedOffersCustomerV3);
            CreateGoal(personalizedOffersCustomerCPS);
        }

        [TestCleanup]
        public void Cleanup()
        {
            if (skipInit)
            {
                return;
            }
            this.retailerStore?.Dispose();
            this.customerLinkedStores?.Dispose();
            this._store?.Dispose();
            this._storeV3?.Dispose();
            this._storeCPS?.Dispose();
        }

        private void CreateGoal(CustomerInfo customerInfo)
        {
            var uetTagCollection = new UETTagCollection(1);

            uetTagCollection.Add_Success(customerInfo);
            Assert.IsTrue(uetTagCollection.Tags[0].Id.Value > 0);

            ConversionGoalCollection goalsCollection = new ConversionGoalCollection(1, BingAds.CampaignManagement.ConversionGoalType.Event, uetTagCollection.Tags[0].Id);
            goalsCollection.Goals[0].Revenue = new BingAds.CampaignManagement.ConversionGoalRevenue()
            {
                Type = BingAds.CampaignManagement.ConversionGoalRevenueType.FixedValue,
                Value = 2,
                CurrencyCode = "USD"
            };

            goalsCollection.Goals[0].Status = BingAds.CampaignManagement.ConversionGoalStatus.Active;

            goalsCollection.Add_Success(customerInfo);

            List<object> resultGoals = goalsCollection.Get_Success(goalsCollection.GetGoalIds(), customerInfo);
            Assert.IsNotNull(resultGoals);
            Assert.AreEqual(1, resultGoals.Count);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_BVT(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            // Account scope, no selected columns
            var select = AllSelectProperties;
            var result = CallGetCampaignsGridData(this.cInfo);
            var biData = PopulateRandomBiData(DateTime.Today);

            var expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, null);

            // Account scope, with selected columns
            result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));

            expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, biData);

            //TODO Totals row validation
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        public void GetCampaignsGridData_ConversionDelay(int[] pilotFeatures)
        {
            DatabaseHelper.AddPilotFeatureToAccount(this.cInfo.AccountIds[0], new[] { AccountPilotFeatures.ConversionDelayMetrics });
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            // Account scope, no selected columns
            var select = new string[] { "Id", "PerformanceMetrics/Clicks", "PerformanceMetrics/ConversionDelayZeroDay", "PerformanceMetrics/ConversionDelayNinety", "PerformanceMetrics/ConversionDelayNinetyNine", PeriodPerformanceMetrics, PerformanceMetricsChange, PerformanceMetricsChangePercentage };
            var biDatas = new Dictionary<long, List<BiData>>();
            for (int i = 0; i < 7; i++)
            {
                Dictionary<long, BiData> biData = new Dictionary<long, BiData>();

                switch (i)
                {
                    case 0:
                    case 1:
                    case 2:
                        biData = PopulateRandomBiData(DateTime.Today.Date, useConversionCredit: true, conversionLag: 0, GoalType: SegmentationGoalType.Duration, mediumId: (int)Medium.Search);
                        break;
                    case 3:
                    case 4:
                        biData = PopulateRandomBiData(DateTime.Today.Date.AddDays(-2), useConversionCredit: true, conversionLag: 1, GoalType: SegmentationGoalType.PageVisit, mediumId: (int)Medium.Search);
                        break;
                    case 5:
                        biData = PopulateRandomBiData(DateTime.Today.Date.AddDays(-2), useConversionCredit: true, conversionLag: 2, GoalType: SegmentationGoalType.PageVisit, mediumId: (int)Medium.Native);
                        break;
                    case 6:
                        biData = PopulateRandomBiData(DateTime.Today.Date, useConversionCredit: true, conversionLag: 3, GoalType: SegmentationGoalType.Duration, mediumId: (int)Medium.Native);
                        break;
                }

                if (biData != null)
                {
                    foreach (var kvp in biData)
                    {
                        if (!biDatas.ContainsKey(kvp.Key))
                        {
                            biDatas.Add(kvp.Key, new List<BiData>() { kvp.Value });
                        }
                        else
                        {
                            biDatas[kvp.Key].Add(kvp.Value);
                        }
                    }
                }
            }

            var segmentations = new List<string>() { SegmentationType.GoalType.ToString() };

            var comparisonDateRange = new BiDateRange { Type = DateRangeType.CustomRange, CustomDateRangeStart = DateTime.Today.AddDays(-1), CustomDateRangeEnd = DateTime.Today };

            // Account scope, with selected columns
            var result = CallGetCampaignsGridData(this.cInfo, thisWeek, comparisonDateRange, select: this.GetSelect(select), segmentationTypes: segmentations);

            var expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyConversionDelayGridData(result, expectedResults.ToArray(), select, biDatas);

            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                Assert.IsNotNull(actualRow[PerformanceMetricsChangePercentage]["ConversionDelayZeroDay"]);
                Assert.IsNotNull(actualRow[PerformanceMetricsChangePercentage]["ConversionDelayNinety"]);
                Assert.IsNotNull(actualRow[PerformanceMetricsChangePercentage]["ConversionDelayNinetyNine"]);
            }

            //TODO Totals row validation
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        public void GetCampaignsGridData_ConversionDelay_ReturnNullWhenNotSet(int[] pilotFeatures)
        {
            var cInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
                CustomerFactory.TargetCountry.US,
                CustomerFactory.TargetLanguage.English,
                2,
                true,
                Features.ConversionDelayMetrics);
            var campaignCollection = new TestCampaignCollection(3);
            campaignCollection.Campaigns.First().Data.TrackingTemplate = "http://www.trackingTemplate.com/url={lpurl}";
            campaignCollection.Campaigns.First().Data.FinalUrlSuffix = "abc.com";
            var response = campaignCollection.Add(cInfo, cInfo.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(response);
            
            var select = new string[] { "Id", "PerformanceMetrics/ConversionDelayZeroDay", "PerformanceMetrics/ConversionDelayNinety", "PerformanceMetrics/ConversionDelayNinetyNine" };
            
            var result = CallGetCampaignsGridData(cInfo, thisWeek, select: this.GetSelect(select));
            
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                Assert.IsNull((string)actualRow.PerformanceMetrics.ConversionDelayZeroDay);
                Assert.IsNull((string)actualRow.PerformanceMetrics.ConversionDelayNinety);
                Assert.IsNull((string)actualRow.PerformanceMetrics.ConversionDelayNinetyNine);
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_ConversionDelay_GoalSegmentation()
        {
            var cInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
                CustomerFactory.TargetCountry.US,
                CustomerFactory.TargetLanguage.English,
                2,
                true,
                Features.ConversionDelayMetrics);

            var campaignCollection = new TestCampaignCollection(3);
            campaignCollection.Campaigns.First().Data.TrackingTemplate = "http://www.trackingTemplate.com/url={lpurl}";
            campaignCollection.Campaigns.First().Data.FinalUrlSuffix = "abc.com";

            var response = campaignCollection.Add(cInfo, cInfo.AccountIds[0]);

            ResponseValidator.ValidateBasicSuccess(response);

            // Account scope, no selected columns
            var select = new string[] { "Id", "PerformanceMetrics/Conversions", "PerformanceMetrics/ConversionDelayZeroDay", "PerformanceMetrics/ConversionDelayNinety", "PerformanceMetrics/ConversionDelayNinetyNine" };
            var biDatas = new Dictionary<long, List<BiData>>();
            foreach (var c in campaignCollection.Campaigns)
            {
                Dictionary<long, BiData> biData = new Dictionary<long, BiData>();
                for (int i = 0; i < 7; i++)
                {
                    switch (i)
                    {
                        case 0:
                            biData = PopulateRandomBiData(DateTime.Today.Date, customerInfo: cInfo, campaignCollection: campaignCollection, useConversionCredit: true, conversionLag: i % 3, GoalType: SegmentationGoalType.OfflineConversion);
                            break;
                        case 1:
                            biData = PopulateRandomBiData(DateTime.Today.Date, customerInfo: cInfo, campaignCollection: campaignCollection, useConversionCredit: true, conversionLag: i % 3, GoalType: SegmentationGoalType.OfflineConversion);
                            break;
                        case 2:
                            biData = PopulateRandomBiData(DateTime.Today.Date, customerInfo: cInfo, campaignCollection: campaignCollection, useConversionCredit: true, conversionLag: i % 3, GoalType: SegmentationGoalType.ProductConversion);
                            break;
                        case 3:
                            biData = PopulateRandomBiData(DateTime.Today.Date, customerInfo: cInfo, campaignCollection: campaignCollection, useConversionCredit: true, conversionLag: i % 3, GoalType: SegmentationGoalType.OfflineConversion);
                            break;
                        case 4:
                            biData = PopulateRandomBiData(DateTime.Today.Date, customerInfo: cInfo, campaignCollection: campaignCollection, useConversionCredit: true, conversionLag: i % 3, GoalType: SegmentationGoalType.OfflineConversion);
                            break;
                        case 5:
                            biData = PopulateRandomBiData(DateTime.Today.Date, customerInfo: cInfo, campaignCollection: campaignCollection, useConversionCredit: true, conversionLag: i % 3, GoalType: SegmentationGoalType.ProductConversion);
                            break;
                        case 6:
                            biData = PopulateRandomBiData(DateTime.Today.Date, customerInfo: cInfo, campaignCollection: campaignCollection, useConversionCredit: true, conversionLag: i % 3, GoalType: SegmentationGoalType.ProductConversion);
                            break;
                    }

                    if (biData != null)
                    {
                        foreach (var kvp in biData)
                        {
                            if (!biDatas.ContainsKey(kvp.Key))
                            {
                                biDatas.Add(kvp.Key, new List<BiData>() { kvp.Value });
                            }
                            else
                            {
                                biDatas[kvp.Key].Add(kvp.Value);
                            }
                        }
                    }
                }
            }

            var result = CallGetCampaignsGridData(cInfo, thisWeek, select: this.GetSelect(select), segmentationTypes: new List<string>() { SegmentationType.GoalType.ToString() });

            var resultDict = new Dictionary<long, List<Dictionary<string, Dictionary<string, double>>>>();

            for (int i = 0 ; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                for (int j = 0; j < actualRow.PerformanceMetrics.SegmentedData.Count; j++)
                {
                    var segmentationKey = (string)actualRow.PerformanceMetrics.SegmentedData[j].Key[0].Value;
                    var data = actualRow.PerformanceMetrics.SegmentedData[j].Data;
                    var dict1 = new Dictionary<string, double>
                    {
                        { "ConversionDelayZeroDay", (double)data["ConversionDelayZeroDay"] },
                        { "ConversionDelayNinety", (double)data["ConversionDelayNinety"] },
                        { "ConversionDelayNinetyNine", (double)data["ConversionDelayNinetyNine"] }
                    };

                    var dict2 = new Dictionary<string, Dictionary<string, double>>
                    {
                        {segmentationKey, dict1 }
                    };
                    if (resultDict.ContainsKey((long)actualRow.Id))
                    {
                        resultDict[(long)actualRow.Id].Add(dict2);
                    }
                    else
                    {
                        resultDict.Add((long)actualRow.Id, new List<Dictionary<string, Dictionary<string, double>>> { dict2 });
                    }
                }
            }

            foreach (var kvp in resultDict)
            {
                foreach(var k in kvp.Value)
                {
                    foreach (var val in k)
                    {
                        var goalType = val.Key;
                        var conversionLagZeroDay = biDatas[kvp.Key].Where(x => x.ConversionLag == 0 && ((SegmentationGoalType)x.GoalTypeId).ToString() == goalType).Sum(x => x.ConversionsCredit);
                        var totalConversions = (double)biDatas[kvp.Key].Where(x => ((SegmentationGoalType)x.GoalTypeId).ToString() == goalType).Sum(x => x.ConversionsCredit);

                        var sum = 0.0;
                        var conversionDelayNinety = 0;
                        var conversionDelayNinetyNine = 0;
                        foreach (var bi in biDatas[kvp.Key].Where(x => ((SegmentationGoalType)x.GoalTypeId).ToString() == goalType).OrderBy(x => x.ConversionLag))
                        {
                            sum += (float)bi.ConversionsCredit;
                            if (sum < totalConversions * 0.9)
                            {
                                conversionDelayNinety = bi.ConversionLag;
                            }
                            if (sum < totalConversions * 0.99)
                            {
                                conversionDelayNinetyNine = bi.ConversionLag;
                            }
                        }

                        Assert.AreEqual(
                            Math.Round((double)conversionLagZeroDay / (double)totalConversions, 4),
                            Math.Round((double)val.Value["ConversionDelayZeroDay"], 4)
                        );
                        Assert.AreEqual(conversionDelayNinety, (int)val.Value["ConversionDelayNinety"]);
                        Assert.AreEqual(conversionDelayNinetyNine, (int)val.Value["ConversionDelayNinetyNine"]);
                    }
                }
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        public void GetCampaignsGridData_ConversionDelay_SortingAndFiltering(int[] pilotFeatures)
        {
            DatabaseHelper.AddPilotFeatureToAccount(this.cInfo.AccountIds[0], new[] { AccountPilotFeatures.ConversionDelayMetrics });
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            // Account scope, no selected columns

            var orderBy = "PerformanceMetrics/ConversionDelayZeroDay desc";
            string filter = $"PerformanceMetrics/ConversionDelayZeroDay gt 100";

            var select = new string[] { "Id", "PerformanceMetrics/ConversionDelayZeroDay", "PerformanceMetrics/ConversionDelayNinety", "PerformanceMetrics/ConversionDelayNinetyNine" };

            var biDatas = new Dictionary<long, List<BiData>>();
            for (int i = 0; i < 7; i++)
            {
                Dictionary<long, BiData> biData = new Dictionary<long, BiData>();

                switch (i)
                {
                    case 0:
                    case 1:
                    case 2:
                        biData = PopulateRandomBiData(DateTime.Today.Date, useConversionCredit: true, conversionLag: 0);
                        break;
                    case 3:
                    case 4:
                        biData = PopulateRandomBiData(DateTime.Today.Date, useConversionCredit: true, conversionLag: 1);
                        break;
                    case 5:
                        biData = PopulateRandomBiData(DateTime.Today.Date, useConversionCredit: true, conversionLag: 2);
                        break;
                    case 6:
                        biData = PopulateRandomBiData(DateTime.Today.Date, useConversionCredit: true, conversionLag: 3);
                        break;
                }

                if (biData != null)
                {
                    foreach (var kvp in biData)
                    {
                        if (!biDatas.ContainsKey(kvp.Key))
                        {
                            biDatas.Add(kvp.Key, new List<BiData>() { kvp.Value });
                        }
                        else
                        {
                            biDatas[kvp.Key].Add(kvp.Value);
                        }
                    }
                }
            }

            // Account scope, with selected columns
            var result = CallGetCampaignsGridData(this.cInfo, thisWeek, select: this.GetSelect(select), orderBy: orderBy);

            for(int i = 1; i < result.value.Count; i++)
            {
                if(result.value[i].PerformanceMetrics.ConversionDelayZeroDay > result.value[i - 1].PerformanceMetrics.ConversionDelayZeroDay)
                {
                    Assert.Fail("Sorting failed.");
                }
            }

            result = CallGetCampaignsGridData(this.cInfo, thisWeek, select: this.GetSelect(select), queryFilter: filter);

            for (int i = 0; i < result.value.Count; i++)
            {
                if ((double)result.value[i].PerformanceMetrics.ConversionDelayZeroDay <= 100.0)
                {
                    Assert.Fail("Filtering failed.");
                }
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        public void GetCampaignsGridData_AppCamapignRelevantColumn_BVT(int[] pilotFeatures)
        {
            if (CustomerInfo.DefaultCustomerPilot.GetValueOrDefault(0) == Features.DatamartClickhouseMigrationPhase2)
            {
                Console.WriteLine($"Skipping tests because DefaultCustomerPilot is {Features.DatamartClickhouseMigrationPhase2}");
                return;
            }

            pilotFeatures = pilotFeatures.Concat(new int[] { Features.IsAccountEnableForAppCampaign }).ToArray();
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }

            if (ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                retailerStore = new CustomerStore(cInfo, 5104030, true);
                customerLinkedStores = new CustomerLinkedStores(cInfo, retailerStore);
            }

            // Account scope, no selected columns
            var select = AllSelectProperties;
            var result = CallGetCampaignsGridData(this.cInfo);
            var biData = PopulateRandomBiData(DateTime.Today, isAppCampaign: true, advertisingChannelTypeId: (int)CampaignFactory.CampaignType.App);

            var expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, null);

            result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));

            expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, biData);

            VerifyColumnSortAndFilter<double>(biData, "Downloads");
            VerifyColumnSortAndFilter<double>(biData, "PostClickDownloadRate");
            VerifyColumnSortAndFilter<double>(biData, "CostPerDownload");
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_BidStrategyLimitedStatus_BVT(int[] pilotFeatures)
        {
            pilotFeatures = pilotFeatures.Concat(new int[] { 1255 }).ToArray();
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var select = new string[] { "Id", "CampaignDeliveryStatusDetails", "DeliveryStatus", "DeliveryStatusDetails" };
            var result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var campaignDeliveryStatusDetails = actualRow.CampaignDeliveryStatusDetails;
                var deliveryStatusDetails = actualRow.DeliveryStatusDetails;
                var deliveryStatus = actualRow.DeliveryStatus;
                Assert.IsNotNull(campaignDeliveryStatusDetails);
                Assert.IsNotNull(deliveryStatusDetails);

                Assert.IsTrue(deliveryStatusDetails.Count == campaignDeliveryStatusDetails.Count);
                Assert.IsTrue(deliveryStatus == deliveryStatusDetails[0] && deliveryStatus == campaignDeliveryStatusDetails[0].Status && deliveryStatus == "Eligible");

                //Validate that the status in both DeliveryStatusDetails and CampaignDeliveryStatusDetails is same for pilot customer.
                for (int k = 0; k < deliveryStatusDetails.Count; k++)
                {
                    Assert.AreEqual(deliveryStatusDetails[k], campaignDeliveryStatusDetails[k].Status);
                }
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_BidStrategyLimitedStatus(int[] pilotFeatures)
        {
            var customer = CustomerInfo.CreatePremiumMSAdvertiser(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 2, true, Features.EnableAutobiddingLimitedStatus);
            TestCampaignCollection.Clean(customer);
            ReportDownloadE2ETests.CreateConversionGoal(customer);
            var campaignCollection = new TestCampaignCollection(10);

            campaignCollection.Campaigns.ForEach(c => c.Data.BiddingScheme = new TargetCpaBiddingScheme()
            {
                Type = BiddingStrategyType.TargetCpa,
                TargetCpa = 1,
            });

            var response = campaignCollection.Add(customer, customer.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(response);

            SetupBidStrategyLimitedStatus(customer, campaignCollection);
            var select = new string[] { "Id", "CampaignDeliveryStatusDetails", "DeliveryStatus", "DeliveryStatusDetails" };
            var result = CallGetCampaignsGridData(customer, select: this.GetSelect(select));
            VerifyBidStrategyLimitedStatus(result);

        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_BidStrategyLimitedStatus_AutobiddingConsolidation(int[] pilotFeatures)
        {
            var customer = CustomerInfo.CreatePremiumMSAdvertiser(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 2, true, Features.EnableAutobiddingLimitedStatus, Features.AutobiddingConsolidationEnabled, Features.AutobiddingConsolidationFillGapsEnabled);
            TestCampaignCollection.Clean(customer);
            ReportDownloadE2ETests.CreateConversionGoal(customer);
            var campaignCollection = new TestCampaignCollection(10);

            campaignCollection.Campaigns.ForEach(c => {
                c.Data.BiddingScheme = new TargetCpaBiddingScheme()
                {
                    Type = BiddingStrategyType.TargetCpa,
                    TargetCpa = 1,
                };
                c.Data.CampaignType = CampaignType.Default;
            });

            var response = campaignCollection.Add(customer, customer.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(response);

            SetupBidStrategyLimitedStatus(customer, campaignCollection);
            var select = new string[] { "Id", "CampaignDeliveryStatusDetails", "DeliveryStatus", "DeliveryStatusDetails" };
            var result = CallGetCampaignsGridData(customer, select: this.GetSelect(select));
            VerifyBidStrategyLimitedStatus(result);

        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_BidStrategyLimitedStatus_0_RecommendedValue(int[] pilotFeatures)
        {
            pilotFeatures = pilotFeatures.Concat(new int[] { 1255 }).ToArray();
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }

            SetupBidStrategyLimitedStatus(this.cInfo, this.campaignCollection, recommendedValue: 0.0);
            var select = new string[] { "Id", "CampaignDeliveryStatusDetails", "DeliveryStatus", "DeliveryStatusDetails" };
            var result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));
            Assert.IsNotNull(result);
            var limitedStatus = new List<string> { "AutoBiddingLimitedInventoryLimit", "AutoBiddingLimitedMaxCPCRestriction", "AutoBiddingLimitedBudgetRestriction", "AutoBiddingLimitedUnAttainableTarget", "AutoBiddingPortfolioBidStrategyTrainingStatus" };
            for (int i = 0; i < this.campaignCollection.Ids.Count(); i++)
            {
                var actualRow = result.value[i];
                var campaignDeliveryStatusDetails = actualRow.CampaignDeliveryStatusDetails;
                var deliveryStatusDetails = actualRow.DeliveryStatusDetails;
                var deliveryStatus = actualRow.DeliveryStatus;
                Assert.IsNotNull(campaignDeliveryStatusDetails);
                Assert.IsNotNull(deliveryStatusDetails);

                Assert.IsTrue(deliveryStatusDetails.Count == campaignDeliveryStatusDetails.Count);
                Assert.IsTrue(deliveryStatus == deliveryStatusDetails[0] && deliveryStatus == campaignDeliveryStatusDetails[0].Status && deliveryStatus == "Eligible");

                //Validate that the status in both DeliveryStatusDetails and CampaignDeliveryStatusDetails is same for pilot customer.
                for (int k = 0; k < deliveryStatusDetails.Count; k++)
                {
                    Assert.AreEqual(deliveryStatusDetails[k], campaignDeliveryStatusDetails[k].Status);
                }

                for (int k = 0; k < deliveryStatusDetails.Count; k++)
                {
                    Assert.IsFalse(limitedStatus.Contains(deliveryStatusDetails[k].Value));
                }
            }

        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_BidStrategyLimitedStatus_CustomerNotInPilot(int[] pilotFeatures)
        {
            TestCampaignCollection.Clean(cInfo);
            CreateCampaignsForCustomer(cInfo, 10);
            var select = new string[] { "Id", "CampaignDeliveryStatusDetails", "DeliveryStatus", "DeliveryStatusDetails" };
            var result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));
            Assert.IsNotNull(result);
            for (int i = 0; i < this.campaignCollection.Ids.Count(); i++)
            {
                var actualRow = result.value[i];
                var campaignDeliveryStatusDetails = actualRow.CampaignDeliveryStatusDetails;
                var deliveryStatusDetails = actualRow.DeliveryStatusDetails;
                var deliveryStatus = actualRow.DeliveryStatus;
                Assert.IsNotNull(campaignDeliveryStatusDetails);
                Assert.IsNotNull(deliveryStatusDetails);

                Assert.IsTrue(deliveryStatusDetails.Count == campaignDeliveryStatusDetails.Count);
                Assert.IsTrue(deliveryStatus == deliveryStatusDetails[0] && deliveryStatus == campaignDeliveryStatusDetails[0].Status && deliveryStatus == "Eligible");

                //Validate that the status in both DeliveryStatusDetails and CampaignDeliveryStatusDetails is same for pilot customer.
                for (int k = 0; k < deliveryStatusDetails.Count; k++)
                {
                    Assert.AreEqual(deliveryStatusDetails[k], campaignDeliveryStatusDetails[k].Status);
                }

                var limitedStatus = new List<string> { "AutoBiddingLimitedInventoryLimit", "AutoBiddingLimitedMaxCPCRestriction", "AutoBiddingLimitedBudgetRestriction", "AutoBiddingLimitedUnAttainableTarget", "AutoBiddingPortfolioBidStrategyTrainingStatus" };
                for (int k = 0; k < deliveryStatusDetails.Count; k++)
                {
                    Assert.IsFalse(limitedStatus.Contains(deliveryStatusDetails[k].Value));
                }
            }

        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_BidStrategyLimitedStatus_IfTrainingStatus()
        {
            TestCampaignCollection.Clean(cInfo);
            CreateCampaignsForCustomer(cInfo, 10);

            this.campaignCollection.Campaigns.ForEach(c => c.Data.BiddingScheme = new MaxClicksBiddingScheme()
            {
                Type = BiddingStrategyType.MaxClicks,
            });
            this.campaignCollection.Update();

            SetupBidStrategyLimitedStatus(this.cInfo, this.campaignCollection, inTrainingStatus: 1);
            var select = new string[] { "Id", "CampaignDeliveryStatusDetails", "DeliveryStatus", "DeliveryStatusDetails" };
            var result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));
            for (int i = 0; i < 5; i++)
            {
                var actualRow = result.value[i];
                var campaignDeliveryStatusDetails = actualRow.CampaignDeliveryStatusDetails;
                var deliveryStatusDetails = actualRow.DeliveryStatusDetails;
                var deliveryStatus = actualRow.DeliveryStatus;
                Assert.IsNotNull(campaignDeliveryStatusDetails);
                Assert.IsNotNull(deliveryStatusDetails);

                Assert.IsTrue(deliveryStatusDetails.Count == campaignDeliveryStatusDetails.Count);
                Assert.IsTrue(deliveryStatus == deliveryStatusDetails[0] && deliveryStatus == campaignDeliveryStatusDetails[0].Status && deliveryStatus == "AutoBiddingLearning");

                //Validate that the status in both DeliveryStatusDetails and CampaignDeliveryStatusDetails is same for pilot customer.
                for (int k = 0; k < deliveryStatusDetails.Count; k++)
                {
                    Assert.AreEqual(deliveryStatusDetails[k], campaignDeliveryStatusDetails[k].Status);
                }
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_BidStrategyLimitedStatus_BothCampaignsAndPortfolio(int[] pilotFeatures)
        {
            TestCampaignCollection.Clean(this.cInfo);
            CreateCampaignsForCustomer(cInfo, 10);
            pilotFeatures = pilotFeatures.Concat(new int[] { 1255 }).ToArray();
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }

            testCampaignCollection = new TestCampaignCollection(5);
            testCampaignCollection.Campaigns.First().Data.TrackingTemplate = "http://www.trackingTemplate.com/url={lpurl}";
            testCampaignCollection.Campaigns.First().Data.FinalUrlSuffix = null;

            var response = testCampaignCollection.Add(this.cInfo, this.cInfo.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(response);

            var pbsGridData = new GetPortfolioBidStrategiesGridData();
            var portfolioIds = pbsGridData.SetupPortfolioBidStrategyData(this.cInfo, testCampaignCollection);

            var queryString = $"UPDATE dbo.PortfolioBidStrategy \n" +
                        $"SET BidStrategyLimitedStatus = 1, RecommendedValue = 0.0 \n" +
                        $"WHERE BidStrategyId in ({String.Join(",", portfolioIds)})";
            var command = new SqlCommand(queryString);
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.cInfo.CustomerId, command, out _);
            SetupBidStrategyLimitedStatus(this.cInfo, testCampaignCollection);
            SetupBidStrategyLimitedStatus(this.cInfo, this.campaignCollection);

            var select = new string[] { "Id", "CampaignDeliveryStatusDetails", "DeliveryStatus", "DeliveryStatusDetails" };
            var result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));
            VerifyBidStrategyLimitedStatus(result, expectPortfolioTrainingStatus: true);

        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_BidStrategyLimitedStatus_NoLearningStatus_ForPortfolioCampaigns_NoPilot(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }

            var pbsGridData = new GetPortfolioBidStrategiesGridData();
            var portfolioIds = pbsGridData.SetupPortfolioBidStrategyData(this.cInfo, campaignCollection);

            var queryString = $"UPDATE dbo.PortfolioBidStrategy \n" +
                        $"SET BidStrategyLimitedStatus = 1, RecommendedValue = 0.25 \n" +
                        $"WHERE BidStrategyId in ({String.Join(",", portfolioIds)})";
            var command = new SqlCommand(queryString);
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(this.cInfo.CustomerId, command, out _);

            var select = new string[] { "Id", "CampaignDeliveryStatusDetails", "DeliveryStatus", "DeliveryStatusDetails" };
            var result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var campaignDeliveryStatusDetails = actualRow.CampaignDeliveryStatusDetails;
                var deliveryStatusDetails = actualRow.DeliveryStatusDetails;

                for (int k = 0; k < deliveryStatusDetails.Count; k++)
                {
                    Assert.AreEqual(deliveryStatusDetails[k], campaignDeliveryStatusDetails[k].Status);
                    Assert.IsFalse(campaignDeliveryStatusDetails[k].Status == "AutoBiddingLearning");
                }
            }

        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_BidStrategyLimitedStatus_CampaignUsesPortfolioTrainingStatus(int[] pilotFeatures)
        {
            var cInfo = DefaultCustomer;
            CreateCampaignsForCustomer(cInfo, 10);
            pilotFeatures = pilotFeatures.Concat(new int[] { 1255 }).ToArray();
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(cInfo.CustomerId, true, pilotFeatures);
            }

            testCampaignCollection = new TestCampaignCollection(5);
            testCampaignCollection.Campaigns.First().Data.TrackingTemplate = "http://www.trackingTemplate.com/url={lpurl}";
            testCampaignCollection.Campaigns.First().Data.FinalUrlSuffix = null;

            var response = testCampaignCollection.Add(cInfo, cInfo.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(response);

            var pbsGridData = new GetPortfolioBidStrategiesGridData();
            var portfolioIds = pbsGridData.SetupPortfolioBidStrategyData(cInfo, testCampaignCollection);

            var queryString = $"UPDATE dbo.PortfolioBidStrategy \n" +
                        $"SET BidStrategyLimitedStatus = 5 \n" +
                        $"WHERE BidStrategyId in ({String.Join(",", portfolioIds)})";
            var command = new SqlCommand(queryString);
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(cInfo.CustomerId, command, out _);

            SetupBidStrategyLimitedStatus(cInfo, testCampaignCollection, inTrainingStatus: 0);
            var select = new string[] { "Id", "CampaignDeliveryStatusDetails", "DeliveryStatus", "DeliveryStatusDetails" };
            var result = CallGetCampaignsGridData(cInfo, select: this.GetSelect(select));
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var campaignDeliveryStatusDetails = actualRow.CampaignDeliveryStatusDetails;
                var deliveryStatusDetails = actualRow.DeliveryStatusDetails;
                var deliveryStatus = actualRow.DeliveryStatus;
                Assert.IsNotNull(campaignDeliveryStatusDetails);
                Assert.IsNotNull(deliveryStatusDetails);

                Assert.IsTrue(deliveryStatusDetails.Count == campaignDeliveryStatusDetails.Count);

                // Individual campaign Training status should not be in the list
                if (testCampaignCollection.Ids.Contains((long)actualRow.Id))
                {
                    for (int k = 0; k < campaignDeliveryStatusDetails.Count; k++)
                    {
                        Assert.IsTrue(campaignDeliveryStatusDetails[k].Status.Value != "AutoBiddingLearning" && campaignDeliveryStatusDetails[k].Status.Value == "AutoBiddingPortfolioBidStrategyTrainingStatus");
                    }
                }

                //Validate that the status in both DeliveryStatusDetails and CampaignDeliveryStatusDetails is same for pilot customer.
                for (int k = 0; k < deliveryStatusDetails.Count; k++)
                {
                    Assert.AreEqual(deliveryStatusDetails[k], campaignDeliveryStatusDetails[k].Status);
                }
            }

        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_BidStrategyLimitedStatus_CostPerSale()
        {
            var select = new[] { "Name", "CampaignCashback", "BiddingScheme", "DeliveryStatusDetails" };
            int customerId = this.personalizedOffersCustomerCPS.CustomerId;
            int accountId = this.personalizedOffersCustomerCPS.AccountIds.First();
            string orderBy = "Name asc";

            var url = ApiVersion.BaseUrl + $"/Customers({customerId})/Accounts({accountId})/Default.BulkUpsert";
            var insertResult = PersonalizedOffersHelper.GenerateCashbackDataCPS(personalizedOffersCustomerCPS, url, providerIdCPS);

            // 101: Non-retail domain
            this.SetupBidStrategyLimitStatusByCampaignId(customerId, accountId, insertResult[2][2].ToObject<long>(), 0, 0, 101);

            string filter = "DisplayBiddingStrategyType eq Enum.BiddingStrategyType'CostPerSale'";
            var result = CallGetCampaignsGridData(personalizedOffersCustomerCPS, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.IsTrue(result.value[0].DeliveryStatusDetails.ToObject<List<string>>().Contains("CPSNonRetailDomain"));
            Assert.IsFalse(result.value[1].DeliveryStatusDetails.ToObject<List<string>>().Contains("CPSNonRetailDomain"));
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_BidStrategyLimitedStatus_CostPerSale_SponsoredPromotionForBrands()
        {
            var select = new[] { "Name", "CampaignCashback", "BiddingScheme", "DeliveryStatusDetails" };
            int customerId = this.sponsoredPromotionsForBrandsCustomer.CustomerId;
            int accountId = this.sponsoredPromotionsForBrandsCustomer.AccountIds.First();
            string orderBy = "Name asc";

            var url = ApiVersion.BaseUrl + $"/Customers({customerId})/Accounts({accountId})/Default.BulkUpsert";
            var insertResult = PersonalizedOffersHelper.GenerateCashbackDataSponsoredPromotionsForBrands(sponsoredPromotionsForBrandsCustomer, url, customerLinkedStores.GlobalStoreId);

            // 101: Non-retail domain
            this.SetupBidStrategyLimitStatusByCampaignId(customerId, accountId, insertResult[2][2].ToObject<long>(), 0, 0, 101);
            this.SetupLegacyCostPerSaleBidStrategyEligibleStatusByCampaignId(customerId, accountId, insertResult[3][2].ToObject<long>());

            string filter = "DisplayBiddingStrategyType eq Enum.BiddingStrategyType'CostPerSale'";
            var result = CallGetCampaignsGridData(sponsoredPromotionsForBrandsCustomer, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.IsFalse(result.value[0].DeliveryStatusDetails.ToObject<List<string>>().Contains("CPSNonRetailDomain"));
            // SPB campaigns should not subject to in review status
            Assert.IsFalse(result.value[0].DeliveryStatusDetails.ToObject<List<string>>().Contains("CampaignInReview"));
            // We might not have mocked product offers in SI so the delivery status won't be eligible
            // Check no in-review status instead
            Assert.IsFalse(result.value[1].DeliveryStatusDetails.ToObject<List<string>>().Contains("CampaignInReview"));
        }

        private void VerifyBidStrategyLimitedStatus(dynamic result, int inTrainingStatus = 0, bool inPilot = true, bool expectPortfolioTrainingStatus = false)
        {
            var limitedStatus = new List<string> { "AutoBiddingLimitedInventoryLimit", "AutoBiddingLimitedMaxCPCRestriction", "AutoBiddingLimitedBudgetRestriction", "AutoBiddingLimitedUnAttainableTarget", "AutoBiddingPortfolioBidStrategyTrainingStatus" };
            if (expectPortfolioTrainingStatus)
            {
                for (int i = 0; i < result.value.Count; i++)
                {
                    var actualRow = result.value[i];
                    var deliveryStatus = actualRow.DeliveryStatus;
                    var campaignDeliveryStatusDetails = actualRow.CampaignDeliveryStatusDetails;
                    var deliveryStatusDetails = actualRow.DeliveryStatusDetails;
                    Assert.AreEqual(deliveryStatusDetails.Count, campaignDeliveryStatusDetails.Count);

                    if (inPilot)
                    {
                        for (int k = 0; k < deliveryStatusDetails.Count; k++)
                        {
                            Assert.AreEqual(deliveryStatusDetails[k], campaignDeliveryStatusDetails[k].Status);
                            if (campaignDeliveryStatusDetails[k].Entity != null)
                            {
                                Assert.AreEqual((double)campaignDeliveryStatusDetails[k].RecommendedValue, 0.25);
                                if (campaignDeliveryStatusDetails[k].Entity == EntityType.PortfolioBidStrategy)
                                {
                                    Assert.IsTrue(this.testCampaignCollection.Ids.Contains((long)actualRow.Id));
                                }
                                if (campaignDeliveryStatusDetails[k].Entity == EntityType.Campaign)
                                {
                                    Assert.IsTrue(this.campaignCollection.Ids.Contains((long)actualRow.Id));
                                }
                            }
                        }
                    }
                    else
                    {
                        //Validate that no limited status is there in delivery status details if customer is not in pilot.
                        Assert.IsTrue(deliveryStatus == deliveryStatusDetails[0]);
                        for (int k = 0; k < deliveryStatusDetails.Count; k++)
                        {
                            Assert.IsFalse(limitedStatus.Contains(deliveryStatusDetails[k].Value));
                        }
                    }
                }
            }
            else
            {
                for (int i = 0; i < 5; i++)
                {
                    var actualRow = result.value[i];
                    var campaignDeliveryStatusDetails = ((JArray)actualRow.CampaignDeliveryStatusDetails).ToArray<dynamic>();
                    var deliveryStatusDetails = ((JArray)actualRow.DeliveryStatusDetails).ToArray<dynamic>();
                    var deliveryStatus = actualRow.DeliveryStatus;
                    Assert.IsNotNull(campaignDeliveryStatusDetails);
                    Assert.IsNotNull(deliveryStatusDetails);
                    if (inPilot)
                    {
                        Assert.IsTrue(deliveryStatusDetails.Length == campaignDeliveryStatusDetails.Length);
                        Assert.IsTrue(deliveryStatus == deliveryStatusDetails[0] && deliveryStatus == campaignDeliveryStatusDetails[0]["Status"].ToString());

                        //Validate that the status in both DeliveryStatusDetails and CampaignDeliveryStatusDetails is same for pilot customer.
                        for (int k = 0; k < deliveryStatusDetails.Length; k++)
                        {
                            Assert.AreEqual(deliveryStatusDetails[k], campaignDeliveryStatusDetails[k].Status);
                        }

                        switch (i)
                        {
                            case 1:
                                {
                                    var status = campaignDeliveryStatusDetails.SingleOrDefault(status => status.Status == "AutoBiddingLimitedInventoryLimit");
                                    Assert.IsNotNull(status, "Status doesn't match");
                                    break;
                                }
                            case 2:
                                {
                                    var status = campaignDeliveryStatusDetails.SingleOrDefault(status => status.Status == "AutoBiddingLimitedMaxCPCRestriction");
                                    Assert.IsNotNull(status, "Status doesn't match");
                                    Assert.AreEqual(0.25, (double)status.RecommendedValue, "RecommendedValue doesn't match");
                                    break;
                                }
                            case 3:
                                {
                                    var status = campaignDeliveryStatusDetails.SingleOrDefault(status => status.Status == "AutoBiddingLimitedBudgetRestriction");
                                    Assert.IsNotNull(status, "Status doesn't match");
                                    Assert.AreEqual(0.25, (double)status.RecommendedValue, "RecommendedValue doesn't match");
                                    break;
                                }
                            case 4:
                                {
                                    var status = campaignDeliveryStatusDetails.SingleOrDefault(status => status.Status == "AutoBiddingLimitedUnAttainableTarget");
                                    Assert.IsNotNull(status, "Status doesn't match");
                                    Assert.AreEqual(0.25, (double)status.RecommendedValue, "RecommendedValue doesn't match");
                                    break;
                                }
                        }
                    }
                    else
                    {
                        //Validate that no limited status is there in delivery status details if customer is not in pilot.
                        Assert.IsTrue(deliveryStatus == deliveryStatusDetails[0]);
                        for (int k = 0; k < deliveryStatusDetails.Length; k++)
                        {
                            Assert.IsFalse(limitedStatus.Contains(deliveryStatusDetails[k]));
                        }
                    }
                }
            }


        }

        private void SetupBidStrategyLimitedStatus(CustomerInfo customer, TestCampaignCollection campaigns, int inTrainingStatus = 0, double recommendedValue = 0.25)
        {
            for (int i = 0; i < 5; i++)
            {
                SetupBidStrategyLimitStatusByCampaignId(customer.CustomerId, customer.AccountIds[0], campaigns.Ids[i], inTrainingStatus, recommendedValue, i);
            }
        }

        private void SetupBidStrategyLimitStatusByCampaignId(int customerId, int accounId, long campaignId, int inTrainingStatus = 0, double recommendedValue = 0.25, int bidStrategyLimitedStatus = 0)
        {
            var queryString = $"IF NOT EXISTS (SELECT * \n" +
                        $"FROM dbo.CampaignDeliveryStatus CDS \n" +
                        $"WHERE CampaignId = {campaignId}) \n" +
                        $"INSERT INTO dbo.CampaignDeliveryStatus (AccountId, CampaignId, InTrainingStatus, ModifiedByUserId, CreatedDTim, ModifiedDTim, BidStrategyLimitedStatus, RecommendedValue) \n" +
                        $"VALUES ({accounId}, {campaignId}, {inTrainingStatus}, 0, getdate(), getdate(), {bidStrategyLimitedStatus}, {recommendedValue}) \n" +
                        $"ELSE \n" +
                        $"UPDATE CDS \n" +
                        $"SET CDS.InTrainingStatus = {inTrainingStatus}, CDS.BidStrategyLimitedStatus = {bidStrategyLimitedStatus}, CDS.RecommendedValue = {recommendedValue} \n" +
                        $"FROM dbo.CampaignDeliveryStatus CDS \n" +
                        $"WHERE CampaignId = {campaignId}";
            var command = new SqlCommand(queryString);
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(customerId, command, out _);
        }

        private void SetupLegacyCostPerSaleBidStrategyEligibleStatusByCampaignId(int customerId, int accountId, long campaignId)
        {
            var queryString = $"UPDATE dbo.Campaign \n" +
                $"SET CampaignFeatureBitMask = CampaignFeatureBitMask | 32 \n" +
                $"FROM dbo.Campaign \n" +
                $"WHERE CampaignId = {campaignId}";
            var command = new SqlCommand(queryString);
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteCommand(customerId, command, out _);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_DependantVideoMetrics_NonNull(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var biData = PopulateRandomBiData(DateTime.Today);

            var select = new string[] { "PerformanceMetrics/ViewThroughRate" };
            var result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                Assert.IsNotNull((double?)actualRow.PerformanceMetrics.ViewThroughRate, "ViewThroughRate is null");
            }

            select = new string[] { "PerformanceMetrics/AverageCPV" };
            result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                Assert.IsNotNull((double?)actualRow.PerformanceMetrics.AverageCPV, "AverageCPV is null");
            }

            select = new string[] { "PerformanceMetrics/AverageWatchTimePerImpression" };
            result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                Assert.IsNotNull((double?)actualRow.PerformanceMetrics.AverageWatchTimePerImpression, "AverageWatchTimePerImpression is null");
            }

            select = new string[] { "PerformanceMetrics/AverageWatchTimePerVideoView" };
            result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                Assert.IsNotNull((double?)actualRow.PerformanceMetrics.AverageWatchTimePerVideoView, "AverageWatchTimePerVideoView is null");
            }

            select = new string[] { "PerformanceMetrics/VideoCompletionRate" };
            result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                Assert.IsNotNull((double?)actualRow.PerformanceMetrics.VideoCompletionRate, "VideoCompletionRate is null");
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_Filter_TargetColumns()
        {
            TestCampaignCollection.Clean(cInfo);
            ReportDownloadE2ETests.CreateConversionGoal(cInfo);

            var testcampaignCollection = new TestCampaignCollection(3);

            testcampaignCollection.Campaigns[0].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testcampaignCollection.Campaigns[0].Data.DailyTargetBudgetAmount = 10;
            testcampaignCollection.Campaigns[0].Data.Status = CampaignStatus.Active;
            testcampaignCollection.Campaigns[0].Data.Name = "TargetCpaBiddingSchemeName";
            testcampaignCollection.Campaigns[0].Data.BiddingScheme = new TargetCpaBiddingScheme()
            {
                MaxCpc = new Bid() { Amount = 3.1 },
                TargetCpa = 4.1,
            };

            testcampaignCollection.Campaigns[1].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testcampaignCollection.Campaigns[1].Data.DailyTargetBudgetAmount = 10;
            testcampaignCollection.Campaigns[1].Data.Status = CampaignStatus.Active;
            testcampaignCollection.Campaigns[1].Data.Name = "TargetRoasBiddingSchemeName";
            testcampaignCollection.Campaigns[1].Data.BiddingScheme = new TargetRoasBiddingScheme()
            {
                MaxCpc = new Bid() { Amount = 3.2 },
                TargetRoas = .42,
            };

            testcampaignCollection.Campaigns[2].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testcampaignCollection.Campaigns[2].Data.DailyTargetBudgetAmount = 10;
            testcampaignCollection.Campaigns[2].Data.Status = CampaignStatus.Active;
            testcampaignCollection.Campaigns[2].Data.Name = "TargetImpressionShareBiddingSchemeName";
            testcampaignCollection.Campaigns[2].Data.BiddingScheme = new TargetImpressionShareBiddingScheme()
            {
                MaxCpc = new Bid() { Amount = 3.3 },
                TargetImpressionShare = 4.3,
                TargetAdPosition = TargetAdPosition.FirstPage
            };
            ResponseValidator.ValidateBasicSuccess(testcampaignCollection.Add(cInfo));

            var select = AllSelectNonBiProperties.Concat(new string[] { "DisplayBiddingStrategyTargetImpressionShare" }).ToArray();
            string filter = $"DisplayBiddingStrategyTargetImpressionShare eq 4.3";
            var result = CallGetCampaignsGridData(cInfo, queryFilter: filter, select: this.GetSelect(select));

            var expectedResults = testcampaignCollection.Campaigns.Where(x => x.Data.BiddingScheme is TargetImpressionShareBiddingScheme);

            Assert.IsNotNull(result?.value, "Value is null.");
            Assert.AreEqual(expectedResults.Count(), result.value.Count, "Row count mismatch");
            Assert.AreEqual(result.value[0].BiddingScheme.TargetImpressionShare.Value, 4.3);

        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_Filter_TargetColumns_Autobidding_Consolidation_Pilot_Enabled()
        {
            cInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, Features.AutobiddingConsolidationEnabled, Features.AutobiddingConsolidationFillGapsEnabled);

            TestCampaignCollection.Clean(cInfo);
            ReportDownloadE2ETests.CreateConversionGoal(cInfo);

            var testCampaignCollection = new TestCampaignCollection(3);

            testCampaignCollection.Campaigns[0].Data.CampaignType = CampaignType.Default;
            testCampaignCollection.Campaigns[0].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testCampaignCollection.Campaigns[0].Data.DailyTargetBudgetAmount = 10;
            testCampaignCollection.Campaigns[0].Data.Status = CampaignStatus.Active;
            testCampaignCollection.Campaigns[0].Data.Name = "TargetCpaBiddingSchemeName";
            testCampaignCollection.Campaigns[0].Data.BiddingScheme = new TargetCpaBiddingScheme()
            {
                MaxCpc = new Bid() { Amount = 3.1 },
                TargetCpa = 4.1,
            };

            testCampaignCollection.Campaigns[1].Data.CampaignType = CampaignType.Default;
            testCampaignCollection.Campaigns[1].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testCampaignCollection.Campaigns[1].Data.DailyTargetBudgetAmount = 10;
            testCampaignCollection.Campaigns[1].Data.Status = CampaignStatus.Active;
            testCampaignCollection.Campaigns[1].Data.Name = "TargetRoasBiddingSchemeName";
            testCampaignCollection.Campaigns[1].Data.BiddingScheme = new TargetRoasBiddingScheme()
            {
                MaxCpc = new Bid() { Amount = 3.2 },
                TargetRoas = .42,
            };

            testCampaignCollection.Campaigns[2].Data.CampaignType = CampaignType.Default;
            testCampaignCollection.Campaigns[2].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testCampaignCollection.Campaigns[2].Data.DailyTargetBudgetAmount = 10;
            testCampaignCollection.Campaigns[2].Data.Status = CampaignStatus.Active;
            testCampaignCollection.Campaigns[2].Data.Name = "TargetImpressionShareBiddingSchemeName";
            testCampaignCollection.Campaigns[2].Data.BiddingScheme = new TargetImpressionShareBiddingScheme()
            {
                MaxCpc = new Bid() { Amount = 3.3 },
                TargetImpressionShare = 4.3,
                TargetAdPosition = TargetAdPosition.FirstPage
            };
            ResponseValidator.ValidateBasicSuccess(testCampaignCollection.Add(cInfo));

            var select = AllSelectNonBiProperties.Concat(new string[] { "DisplayBiddingStrategyTargetImpressionShare" }).ToArray();
            string filter = $"DisplayBiddingStrategyTargetImpressionShare eq 4.3";
            var result = CallGetCampaignsGridData(cInfo, queryFilter: filter, select: this.GetSelect(select));

            var expectedResults = testCampaignCollection.Campaigns.Where(x => x.Data.BiddingScheme is TargetImpressionShareBiddingScheme);

            Assert.IsNotNull(result?.value, "Value is null.");
            Assert.AreEqual(expectedResults.Count(), result.value.Count, "Row count mismatch");
            Assert.AreEqual(result.value[0].BiddingScheme.TargetImpressionShare.Value, 4.3);

        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_Sort_TargetColumns()
        {
            TestCampaignCollection.Clean(cInfo);
            ReportDownloadE2ETests.CreateConversionGoal(cInfo);

            var testcampaignCollection = new TestCampaignCollection(3);

            testcampaignCollection.Campaigns[0].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testcampaignCollection.Campaigns[0].Data.DailyTargetBudgetAmount = 10;
            testcampaignCollection.Campaigns[0].Data.Status = CampaignStatus.Active;
            testcampaignCollection.Campaigns[0].Data.Name = "TargetCpaBiddingSchemeName1";
            testcampaignCollection.Campaigns[0].Data.BiddingScheme = new TargetCpaBiddingScheme()
            {
                MaxCpc = new Bid() { Amount = 3.1 },
                TargetCpa = 4.3,
            };

            testcampaignCollection.Campaigns[1].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testcampaignCollection.Campaigns[1].Data.DailyTargetBudgetAmount = 10;
            testcampaignCollection.Campaigns[1].Data.Status = CampaignStatus.Active;
            testcampaignCollection.Campaigns[1].Data.Name = "TargetCpaBiddingSchemeName2";
            testcampaignCollection.Campaigns[1].Data.BiddingScheme = new TargetCpaBiddingScheme()
            {
                MaxCpc = new Bid() { Amount = 3.2 },
                TargetCpa = 4.2,
            };

            testcampaignCollection.Campaigns[2].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testcampaignCollection.Campaigns[2].Data.DailyTargetBudgetAmount = 10;
            testcampaignCollection.Campaigns[2].Data.Status = CampaignStatus.Active;
            testcampaignCollection.Campaigns[2].Data.Name = "TargetCpaBiddingSchemeName13";
            testcampaignCollection.Campaigns[2].Data.BiddingScheme = new TargetCpaBiddingScheme()
            {
                MaxCpc = new Bid() { Amount = 3.3 },
                TargetCpa = 4.1
            };

            ResponseValidator.ValidateBasicSuccess(testcampaignCollection.Add(cInfo));

            var select = AllSelectNonBiProperties.Concat(new string[] { "DisplayBiddingStrategyTargetCpa" }).ToArray();
            string orderBy = $"DisplayBiddingStrategyTargetCpa asc";
            var result = CallGetCampaignsGridData(cInfo, orderBy: orderBy, select: this.GetSelect(select));

            var expectedResults = new double[] { 4.1, 4.2, 4.3 };

            Assert.IsNotNull(result?.value, "Value is null.");
            Assert.AreEqual(expectedResults.Count(), result.value.Count, "Row count mismatch");
            double oldTargetCpa = -1.0d;
            for (int i = 0; i < result.value.Count; i++)
            {
                var c = result.value[i];
                var curTargetCpaVal = c.BiddingScheme.TargetCpa.Value;
                if (curTargetCpaVal != null)
                {
                    Assert.IsTrue(oldTargetCpa < curTargetCpaVal);
                    oldTargetCpa = curTargetCpaVal;
                }
            }

        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_Sort_TargetColumns_Autobidding_Consolidation_PilotEnabled()
        {
            cInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, Features.ViewThroughConversionFlagId, Features.EnableNativeAdsFlagId, Features.AutobiddingConsolidationFillGapsEnabled, Features.AutobiddingConsolidationEnabled);

            TestCampaignCollection.Clean(cInfo);
            ReportDownloadE2ETests.CreateConversionGoal(cInfo);

            var testcampaignCollection = new TestCampaignCollection(3);

            testcampaignCollection.Campaigns[0].Data.CampaignType = CampaignType.Default;
            testcampaignCollection.Campaigns[0].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testcampaignCollection.Campaigns[0].Data.DailyTargetBudgetAmount = 10;
            testcampaignCollection.Campaigns[0].Data.Status = CampaignStatus.Active;
            testcampaignCollection.Campaigns[0].Data.Name = "TargetCpaBiddingSchemeName1";
            testcampaignCollection.Campaigns[0].Data.BiddingScheme = new TargetCpaBiddingScheme()
            {
                MaxCpc = new Bid() { Amount = 3.1 },
                TargetCpa = 4.3,
            };

            testcampaignCollection.Campaigns[1].Data.CampaignType = CampaignType.Default;
            testcampaignCollection.Campaigns[1].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testcampaignCollection.Campaigns[1].Data.DailyTargetBudgetAmount = 10;
            testcampaignCollection.Campaigns[1].Data.Status = CampaignStatus.Active;
            testcampaignCollection.Campaigns[1].Data.Name = "TargetCpaBiddingSchemeName2";
            testcampaignCollection.Campaigns[1].Data.BiddingScheme = new TargetCpaBiddingScheme()
            {
                MaxCpc = new Bid() { Amount = 3.2 },
                TargetCpa = 4.2,
            };

            testcampaignCollection.Campaigns[2].Data.CampaignType = CampaignType.Default;
            testcampaignCollection.Campaigns[2].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testcampaignCollection.Campaigns[2].Data.DailyTargetBudgetAmount = 10;
            testcampaignCollection.Campaigns[2].Data.Status = CampaignStatus.Active;
            testcampaignCollection.Campaigns[2].Data.Name = "TargetCpaBiddingSchemeName13";
            testcampaignCollection.Campaigns[2].Data.BiddingScheme = new TargetCpaBiddingScheme()
            {
                MaxCpc = new Bid() { Amount = 3.3 },
                TargetCpa = 4.1
            };

            ResponseValidator.ValidateBasicSuccess(testcampaignCollection.Add(cInfo));

            var select = AllSelectNonBiProperties.Concat(new string[] { "DisplayBiddingStrategyTargetCpa" }).ToArray();
            string orderBy = $"DisplayBiddingStrategyTargetCpa asc";
            var result = CallGetCampaignsGridData(cInfo, orderBy: orderBy, select: this.GetSelect(select));

            var expectedResults = new double[] { 4.1, 4.2, 4.3 };

            Assert.IsNotNull(result?.value, "Value is null.");
            Assert.AreEqual(expectedResults.Count(), result.value.Count, "Row count mismatch");
            double oldTargetCpa = -1.0d;
            for (int i = 0; i < result.value.Count; i++)
            {
                var c = result.value[i];
                var curTargetCpaVal = c.BiddingScheme.TargetCpa.Value;
                if (curTargetCpaVal != null)
                {
                    Assert.IsTrue(oldTargetCpa < curTargetCpaVal);
                    oldTargetCpa = curTargetCpaVal;
                }
            }

        }

        [TestMethod]
        [Priority(2), TestCategory(CampaignTest.Framework.TestCategoryNames.BVT)]
        [Owner(TestOwners.ODataTests)]
        public void CampaignPerformanceTimeSeries_DynamicColumns_WithFilter_BVT()
        {
            cInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, Features.ViewThroughConversionFlagId, Features.EnableNativeAdsFlagId);
            short numberOfDays = 3;

            DateTime startDate = DateTime.Now.AddDays(-14).Date;
            DateTime endDate = startDate.AddDays(numberOfDays - 1).Date;

            TestCampaignCollection.Clean(cInfo);
            campaignCollection = new TestCampaignCollection(3);
            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(cInfo));

            List<BiData> biDataList = new List<BiData>();

            long campaignId = this.campaignCollection.Campaigns.First().Data.Id;
            string filter = $"Id eq {campaignId}";

            if (mockReporting)
            {
                biDataList = SetDailyMockBiData(startDate, endDate, true);
            }

            IEnumerable<dynamic> result = CallGetPerformanceTimeSeries(startDate, endDate, timeGrain: "Day", metrics: "Clicks,Impressions,SalesCount,AverageCPS,Installs,CostPerInstall,RevenuePerInstall", filter: filter);

            Assert.AreEqual(7, result.Count(), "Metrics Count Mismatch.");

            if (mockReporting)
            {
                VerifyData_InlineChart(
                    biDataList, result, numberOfDays, new string[] { "Clicks", "Impressions", "SalesCount", "AverageCPS", "Installs", "CostPerInstall", "RevenuePerInstall" }, filterByCampaignId: campaignId);
            }
        }

        [TestMethod]
        [Priority(2), TestCategory(CampaignTest.Framework.TestCategoryNames.BVT)]
        [Owner(TestOwners.ODataTests)]
        public void CampaignPerformanceTimeSeries_AppCampaign_WithFilter_BVT()
        {
            if (CustomerInfo.DefaultCustomerPilot.GetValueOrDefault(0) == Features.DatamartClickhouseMigrationPhase2)
            {
                Console.WriteLine($"Skipping tests because DefaultCustomerPilot is {Features.DatamartClickhouseMigrationPhase2}");
                return;
            }

            cInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, Features.ViewThroughConversionFlagId, Features.EnableNativeAdsFlagId, Features.IsAccountEnableForAppCampaign);

            if (ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                retailerStore = new CustomerStore(cInfo, 5104030, true);
                customerLinkedStores = new CustomerLinkedStores(cInfo, retailerStore);
            }

            short numberOfDays = 3;

            DateTime startDate = DateTime.Now.AddDays(-14).Date;
            DateTime endDate = startDate.AddDays(numberOfDays - 1).Date;

            TestCampaignCollection.Clean(cInfo);
            campaignCollection = new TestCampaignCollection(3, CampaignFactory.CampaignType.App);

            foreach (var campaign in campaignCollection.Campaigns)
            {
                campaign.Data.Languages = new Language[] { Language.English };
                campaign.Data.BiddingScheme = new ManualCpaBiddingScheme() { ManualCPI = 30 };
                campaign.Data.CampaignSubType = CampaignSubType.AppInstall;
                campaign.Data.CampaignSettings = new CampaignSettings[] { new AppSetting { AppStore = AppStore.MicrosoftAppStore, AppId = "9MSPC6MP8FM4" } };
            }

            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(cInfo));

            List<BiData> biDataList = new List<BiData>();

            long campaignId = this.campaignCollection.Campaigns.First().Data.Id;
            string filter = $"Id eq {campaignId}";

            if (mockReporting)
            {
                biDataList = SetDailyMockBiData(startDate, endDate, true, isAppCampaign: true);
            }

            IEnumerable<dynamic> result = CallGetPerformanceTimeSeries(startDate, endDate, timeGrain: "Day", metrics: "Downloads,PostClickDownloadRate,CostPerDownload,FirstLaunches,PostClickFirstLaunchRate,CostPerFirstLaunch," +
                "Purchases,PostInstallPurchaseRate,CostPerPurchase,Subscriptions,PostInstallSubscriptionRate,CostPerSubscription", filter: filter);

            Assert.AreEqual(12, result.Count(), "Metrics Count Mismatch.");

            if (mockReporting)
            {
                VerifyData_InlineChart(
                    biDataList, result, numberOfDays, new string[] { "Downloads", "PostClickDownloadRate","CostPerDownload","FirstLaunches","PostClickFirstLaunchRate","CostPerFirstLaunch",
                        "Purchases","PostInstallPurchaseRate","CostPerPurchase","Subscriptions","PostInstallSubscriptionRate","CostPerSubscription"}, filterByCampaignId: campaignId);
            }
        }

        [TestMethod]
        [Priority(2), TestCategory(CampaignTest.Framework.TestCategoryNames.BVT)]
        [Owner(TestOwners.ODataTests)]
        public void CampaignPerformanceTimeSeries_DynamicColumns_WithOutFilter_BVT()
        {
            cInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, Features.ViewThroughConversionFlagId, Features.EnableNativeAdsFlagId);

            short numberOfDays = 3;

            DateTime startDate = DateTime.Now.AddDays(-14).Date;
            DateTime endDate = startDate.AddDays(numberOfDays - 1).Date;

            TestCampaignCollection.Clean(cInfo);
            campaignCollection = new TestCampaignCollection(3);
            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(cInfo));
            List<BiData> biDataList = new List<BiData>();

            if (mockReporting)
            {
                biDataList = SetDailyMockBiData(startDate, endDate, true);
            }

            IEnumerable<dynamic> result = CallGetPerformanceTimeSeries(startDate, endDate, timeGrain: "Day", metrics: "Clicks,Impressions");

            Assert.AreEqual(2, result.Count(), "Metrics Count Mismatch.");

            if (mockReporting)
            {
                VerifyData_InlineChart(
                    biDataList, result, numberOfDays, new string[] { "Clicks", "Impressions" });
            }
        }

        [TestMethod]
        [Priority(2), TestCategory(CampaignTest.Framework.TestCategoryNames.BVT)]
        [Owner(TestOwners.ODataTests)]
        public void CampaignPerformanceTimeSeries_DynamicColumns_AllMetrics_BVT()
        {
            cInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, Features.ViewThroughConversionFlagId, Features.EnableNativeAdsFlagId);

            short numberOfDays = 3;

            DateTime startDate = DateTime.Now.AddDays(-14).Date;
            DateTime endDate = startDate.AddDays(numberOfDays - 1).Date;

            TestCampaignCollection.Clean(cInfo);
            campaignCollection = new TestCampaignCollection(3);
            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(cInfo));
            List<BiData> biDataList = new List<BiData>();

            if (mockReporting)
            {
                biDataList = SetDailyMockBiData(startDate, endDate, true,mockSov:true);
            }

            IEnumerable<dynamic> result = CallGetPerformanceTimeSeries(startDate, endDate, timeGrain: "Day", metrics: AllRequestableMetricsForInlineChart.ToDelimitedString(","));

            Assert.AreEqual(AllRequestableMetricsForInlineChart.Count(), result.Count(), "Metrics Count Mismatch.");

            if (mockReporting)
            {
                // the name of the metrics returned from call is different for these two, mapping them here for validation
                var retRequestableMetrics = AllRequestableMetricsForInlineChart.Select(x => x).ToList();
                retRequestableMetrics[retRequestableMetrics.IndexOf("CTR")] = "ClickThruRate";
                retRequestableMetrics[retRequestableMetrics.IndexOf("Spend")] = "Spent";

                VerifyData_InlineChart(biDataList, result, numberOfDays, retRequestableMetrics.ToArray());
            }
        }

        [TestMethod]
        [Priority(2), TestCategory(CampaignTest.Framework.TestCategoryNames.BVT)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridDataWithPerformanceTimeSeries_WithOutFilter_MovingAverage_BVT()
        {
            var customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
                CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1,
                false, Features.ViewThroughConversionFlagId, Features.EnableNativeAdsFlagId);

            short numberOfDays = 3;

            DateTime startDate = DateTime.Now.AddDays(-14).Date;
            DateTime endDate = startDate.AddDays(numberOfDays - 1).Date;

            var dateRange = new BiDateRange { Type = DateRangeType.CustomRange, CustomDateRangeStart = startDate, CustomDateRangeEnd = endDate };

            TestCampaignCollection.Clean(customerInfo);
            campaignCollection = new TestCampaignCollection(3);
            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(customerInfo));
            List<BiData> biDataList = new List<BiData>();

            if (mockReporting)
            {
                biDataList = SetDailyMockBiData(startDate - timeAdjustmentForMovingAverage, endDate, true, customerInfo: customerInfo);
            }

            var segmentations = new List<string>() { SegmentationType.AudienceType.ToString() };

            var select = AllSelectProperties;
            var result = CallGetCampaignsGridData(customerInfo, segmentationTypes: segmentations, dateRange: dateRange, select: this.GetSelect(select),

                providePerformanceTimeSeriesParameters: true, performanceTimesSeriesMetrics: "Clicks,Impressions", movingAverage: true);

            var performanceTimeSeriesResult = JsonConvert.DeserializeObject<dynamic>((string)result["@ns.performancetimeseries"]);
            Assert.IsNotNull(performanceTimeSeriesResult);

            if (mockReporting)
            {
                var expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
                var aggregatedBiDataByCampaignId = GetAggregatedBiDataByCampaignId(biDataList.Where(b => b.Date >= startDate).ToList());
                VerifyGridData(result, expectedResults.ToArray(), select, biDataByCampaignId: aggregatedBiDataByCampaignId, useConversionCredit: true);


                VerifyData_InlineChart(
                    biDataList, performanceTimeSeriesResult, numberOfDays, new string[] { "Clicks", "Impressions" }, validateMovingAverage: true);
            }
        }

        [TestMethod]
        [Priority(2), TestCategory(CampaignTest.Framework.TestCategoryNames.BVT)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridDataWithPerformanceTimeSeries_WithOutFilter_BVT()
        {
            var customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
                CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1,
                false, Features.ViewThroughConversionFlagId, Features.EnableNativeAdsFlagId);

            short numberOfDays = 3;

            DateTime startDate = DateTime.Now.AddDays(-14).Date;
            DateTime endDate = startDate.AddDays(numberOfDays - 1).Date;

            var dateRange = new BiDateRange { Type = DateRangeType.CustomRange, CustomDateRangeStart = startDate, CustomDateRangeEnd = endDate };

            TestCampaignCollection.Clean(customerInfo);
            campaignCollection = new TestCampaignCollection(3);
            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(customerInfo));
            List<BiData> biDataList = new List<BiData>();

            if (mockReporting)
            {
                biDataList = SetDailyMockBiData(startDate, endDate, true, customerInfo: customerInfo);
            }

            var select = AllSelectProperties;
            var result = CallGetCampaignsGridData(customerInfo, dateRange: dateRange, select: this.GetSelect(select),
                providePerformanceTimeSeriesParameters: true, performanceTimesSeriesMetrics: "Clicks,Impressions");

            var performanceTimeSeriesResult = JsonConvert.DeserializeObject<dynamic>((string)result["@ns.performancetimeseries"]);
            Assert.IsNotNull(performanceTimeSeriesResult);

            if (mockReporting)
            {
                var expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
                var aggregatedBiDataByCampaignId = GetAggregatedBiDataByCampaignId(biDataList);
                VerifyGridData(result, expectedResults.ToArray(), select, biDataByCampaignId: aggregatedBiDataByCampaignId, useConversionCredit: true);

                VerifyData_InlineChart(
                    biDataList, performanceTimeSeriesResult, numberOfDays, new string[] { "Clicks", "Impressions" });
            }

            result = CallGetCampaignsGridData(customerInfo, dateRange: dateRange, select: this.GetSelect(select),
                providePerformanceTimeSeriesParameters: true, performanceTimesSeriesMetrics: "Spend,CTR");

            performanceTimeSeriesResult = JsonConvert.DeserializeObject<dynamic>((string)result["@ns.performancetimeseries"]);
            Assert.IsNotNull(performanceTimeSeriesResult);

            if (mockReporting)
            {
                var expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
                var aggregatedBiDataByCampaignId = GetAggregatedBiDataByCampaignId(biDataList);
                VerifyGridData(result, expectedResults.ToArray(), select, biDataByCampaignId: aggregatedBiDataByCampaignId, useConversionCredit: true);

                VerifyData_InlineChart(
                    biDataList, performanceTimeSeriesResult, numberOfDays, new string[] { "Spent", "ClickThruRate" });
            }
        }

        [TestMethod]
        [Priority(2), TestCategory(CampaignTest.Framework.TestCategoryNames.BVT)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridDataWithPerformanceTimeSeries_WithFilter_MovingAverage_BVT()
        {
            var customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
                CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1,
                false, Features.ViewThroughConversionFlagId, Features.EnableNativeAdsFlagId);

            short numberOfDays = 3;

            DateTime startDate = DateTime.Now.AddDays(-14).Date;
            DateTime endDate = startDate.AddDays(numberOfDays - 1).Date;

            var dateRange = new BiDateRange { Type = DateRangeType.CustomRange, CustomDateRangeStart = startDate, CustomDateRangeEnd = endDate };

            TestCampaignCollection.Clean(customerInfo);
            campaignCollection = new TestCampaignCollection(3);
            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(customerInfo));
            List<BiData> biDataList = new List<BiData>();

            long campaignId = this.campaignCollection.Campaigns.First().Data.Id;
            string filter = $"Id eq {campaignId}";

            if (mockReporting)
            {
                biDataList = SetDailyMockBiData(startDate - timeAdjustmentForMovingAverage, endDate, true, customerInfo: customerInfo);
            }

            var select = AllSelectProperties;
            var result = CallGetCampaignsGridData(customerInfo, dateRange: dateRange, queryFilter: filter, select: this.GetSelect(select),
                providePerformanceTimeSeriesParameters: true, performanceTimesSeriesMetrics: "Clicks,Impressions", movingAverage: true);

            var performanceTimeSeriesResult = JsonConvert.DeserializeObject<dynamic>((string)result["@ns.performancetimeseries"]);
            Assert.IsNotNull(performanceTimeSeriesResult);

            if (mockReporting)
            {
                var expectedResults = campaignCollection.Campaigns.Where(x => x.Data.Id == campaignId);
                var aggregatedBiDataByCampaignId = GetAggregatedBiDataByCampaignId(biDataList.Where(b => b.Date >= startDate).ToList(), filterByCampaignId: campaignId);
                VerifyGridData(result, expectedResults.ToArray(), select, biDataByCampaignId: aggregatedBiDataByCampaignId, useConversionCredit: true);

                VerifyData_InlineChart(
                    biDataList, performanceTimeSeriesResult, numberOfDays, new string[] { "Clicks", "Impressions" }, filterByCampaignId: campaignId, validateMovingAverage: true);
            }
        }

        [TestMethod]
        [Priority(2), TestCategory(CampaignTest.Framework.TestCategoryNames.BVT)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridDataWithPerformanceTimeSeries_WithFilter_BVT()
        {
            var customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
                CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1,
                false, Features.ViewThroughConversionFlagId, Features.EnableNativeAdsFlagId);

            short numberOfDays = 3;

            DateTime startDate = DateTime.Now.AddDays(-14).Date;
            DateTime endDate = startDate.AddDays(numberOfDays - 1).Date;

            var dateRange = new BiDateRange { Type = DateRangeType.CustomRange, CustomDateRangeStart = startDate, CustomDateRangeEnd = endDate };

            TestCampaignCollection.Clean(customerInfo);
            campaignCollection = new TestCampaignCollection(3);
            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(customerInfo));
            List<BiData> biDataList = new List<BiData>();

            long campaignId = this.campaignCollection.Campaigns.First().Data.Id;
            string filter = $"Id eq {campaignId}";

            if (mockReporting)
            {
                biDataList = SetDailyMockBiData(startDate, endDate, true, customerInfo: customerInfo);
            }

            var segmentations = new List<string>() { SegmentationType.AudienceType.ToString() };

            var select = AllSelectProperties;
            var result = CallGetCampaignsGridData(customerInfo, segmentationTypes: segmentations, dateRange: dateRange, queryFilter: filter, select: this.GetSelect(select),
                providePerformanceTimeSeriesParameters: true, performanceTimesSeriesMetrics: "Clicks,Impressions");

            var performanceTimeSeriesResult = JsonConvert.DeserializeObject<dynamic>((string)result["@ns.performancetimeseries"]);
            Assert.IsNotNull(performanceTimeSeriesResult);

            if (mockReporting)
            {
                var expectedResults = campaignCollection.Campaigns.Where(x => x.Data.Id == campaignId);
                var aggregatedBiDataByCampaignId = GetAggregatedBiDataByCampaignId(biDataList, filterByCampaignId: campaignId);
                VerifyGridData(result, expectedResults.ToArray(), select, biDataByCampaignId: aggregatedBiDataByCampaignId, useConversionCredit: true);

                VerifyData_InlineChart(
                    biDataList, performanceTimeSeriesResult, numberOfDays, new string[] { "Clicks", "Impressions" }, filterByCampaignId: campaignId);
            }

            result = CallGetCampaignsGridData(customerInfo, segmentationTypes: segmentations, dateRange: dateRange, queryFilter: filter, select: this.GetSelect(select),
                providePerformanceTimeSeriesParameters: true, performanceTimesSeriesMetrics: "Spend,CTR");

            performanceTimeSeriesResult = JsonConvert.DeserializeObject<dynamic>((string)result["@ns.performancetimeseries"]);
            Assert.IsNotNull(performanceTimeSeriesResult);

            if (mockReporting)
            {
                var expectedResults = campaignCollection.Campaigns.Where(x => x.Data.Id == campaignId);
                var aggregatedBiDataByCampaignId = GetAggregatedBiDataByCampaignId(biDataList, filterByCampaignId: campaignId);
                VerifyGridData(result, expectedResults.ToArray(), select, biDataByCampaignId: aggregatedBiDataByCampaignId, useConversionCredit: true);

                VerifyData_InlineChart(
                    biDataList, performanceTimeSeriesResult, numberOfDays, new string[] { "Spent", "ClickThruRate" }, filterByCampaignId: campaignId);
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_ConversionCredit(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            // Account scope, no selected columns
            var select = AllSelectProperties;
            var result = CallGetCampaignsGridData(this.cInfo);
            var biData = PopulateRandomBiData(DateTime.Today, useConversionCredit: true, viewThroughEnabled: true);

            var expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, null);

            // Account scope, with selected columns
            result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));

            expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, biData, useConversionCredit: true);

        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_ConversionCredit_Decimal(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            // Account scope, no selected columns
            var select = AllSelectProperties;
            select = select.Append("PerformanceMetrics/ViewThroughConversions").ToArray();
            var result = CallGetCampaignsGridData(this.cInfo);
            var biData = PopulateRandomBiData(DateTime.Today, useConversionCredit: true, viewThroughEnabled: true);

            var expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, null);

            // Account scope, with selected columns
            result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));

            expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, biData, useConversionCredit: true);

        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [TestCategory(TestCategoryNames.CustomParamFinalUrlSuffix)]
        public void GetCampaignsGridData_FinalUrlSuffix()
        {
            var finalUrlSuffixCInfo = CustomerInfo.CreateStandardAdvertiserWithPilot(Features.FinalUrlSuffixPhase1FlagId);
            this.CreateCampaignsForCustomer(finalUrlSuffixCInfo, 1, "x=y");
            // Account scope, no selected columns
            var uurlSelect = AllSelectProperties.Append("FinalUrlSuffix").ToArray();
            var result = CallGetCampaignsGridData(finalUrlSuffixCInfo);

            var expectedResults = campaignCollection.Campaigns.ToArray();
            VerifyGridData(result, expectedResults, uurlSelect, null);

            // Account scope, with selected columns
            result = CallGetCampaignsGridData(finalUrlSuffixCInfo, select: this.GetSelect(uurlSelect));

            expectedResults = campaignCollection.Campaigns.ToArray();
            VerifyGridData(result, expectedResults, uurlSelect, null);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        public void GetCampaignsGridData_ViewThroughConversions()
        {
            var viewThroughConversionCustomerInfo = ViewThroughConversionCustomerInfo;
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                viewThroughConversionCustomerInfo = CustomerInfo.CreateNewViewThroughConversionCustomer();
            }
            TestCampaignCollection.Clean(viewThroughConversionCustomerInfo);
            this.CreateCampaignsForCustomer(viewThroughConversionCustomerInfo, 5);

            // Account scope, no selected columns
            var select = AllSelectProperties;
            var result = CallGetCampaignsGridData(viewThroughConversionCustomerInfo);
            var biData = PopulateRandomBiData(DateTime.Today, customerInfo: viewThroughConversionCustomerInfo);

            var expectedResults = campaignCollection.Campaigns.ToArray();
            VerifyGridData(result, expectedResults, select, null);

            // Account scope, with selected columns
            result = CallGetCampaignsGridData(viewThroughConversionCustomerInfo, select: this.GetSelect(select));

            expectedResults = campaignCollection.Campaigns.ToArray();
            VerifyGridData(result, expectedResults, select, biData);

            // Perf sort
            string orderBy = "PerformanceMetrics/ViewThroughConversions asc";
            result = CallGetCampaignsGridData(viewThroughConversionCustomerInfo, orderBy: orderBy, select: this.GetSelect(select));

            expectedResults = biData != null
                ? campaignCollection.Campaigns.OrderBy(x => biData[x.Data.Id].ViewThroughConversions.GetValueOrDefault())
                    .ThenBy(x => x.Data.Id).ToArray()
                : campaignCollection.Campaigns.OrderBy(x => x.Data.Id).ToArray();
            VerifyGridData(result, expectedResults, select, biData);

            // Perf filter
            string filter = $"PerformanceMetrics/ViewThroughConversions gt 20";
            result = CallGetCampaignsGridData(viewThroughConversionCustomerInfo, queryFilter: filter, select: this.GetSelect(select));

            expectedResults = biData != null
                ? campaignCollection.Campaigns.Where(x => biData[x.Data.Id].ViewThroughConversions > 20).OrderBy(x => x.Data.Id).ToArray()
                : Enumerable.Empty<TestCampaign>().ToArray();
            VerifyGridData(result, expectedResults, select, biData);
        }

        [TestMethod]
        [Priority(2), TestCategory(CampaignTest.Framework.TestCategoryNames.BVT)]
        [Owner(TestOwners.ODataTests)]
        public void CampaignPerformanceTimeSeries_PartialConversion_BVT()
        {
            cInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, Features.ViewThroughConversionFlagId, Features.EnableNativeAdsFlagId);

            short numberOfDays = 3;

            DateTime startDate = DateTime.Now.AddDays(-14).Date;
            DateTime endDate = startDate.AddDays(numberOfDays - 1).Date;

            TestCampaignCollection.Clean(cInfo);
            campaignCollection = new TestCampaignCollection(3);
            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(cInfo));
            List<BiData> biDataList = new List<BiData>();

            if (mockReporting)
            {
                biDataList = SetDailyMockBiData(startDate, endDate, true);
            }

            IEnumerable<dynamic> result = CallGetPerformanceTimeSeries(startDate, endDate, timeGrain: "Day", metrics: "All");

            Assert.AreNotEqual(0, result.Count(), "Found zero metrics returned.");

            if (mockReporting)
            {
                VerifyData_InlineChart(biDataList, result, numberOfDays);
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        [DataRow(new int[] { })]
        public void GetCampaignsGridData_NoPartialConversions(int[] pilotFeatures)
        {
            List<int> featureList = new List<int>() { Features.ViewThroughConversionFlagId, Features.EnableNativeAdsFlagId };
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                featureList.AddRange(pilotFeatures);
            }
            var PartialConversionPilotCustomer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, featureList.ToArray());
            this.CreateCampaignsForCustomer(PartialConversionPilotCustomer, 5);
            // Account scope, no selected columns
            var biData = PopulateRandomBiData(DateTime.Today, customerInfo: PartialConversionPilotCustomer, useConversionCredit: true, viewThroughEnabled: true);

            string[] select = new string[] { };
            var expectedResults = campaignCollection.Campaigns.ToArray();
            var result = CallGetCampaignsGridData(PartialConversionPilotCustomer);
            VerifyGridData(result, expectedResults, select, biData, useConversionCredit: true, partialConversionEnabled: true);

            select = AllSelectNonBiProperties.Concat(new string[]
                    {
                        "PerformanceMetrics/Clicks",
                        "PerformanceMetrics/Impressions"
                    }).ToArray();
            result = CallGetCampaignsGridData(PartialConversionPilotCustomer, select: this.GetSelect(select));
            VerifyGridData(result, expectedResults, select, biData, useConversionCredit: true, partialConversionEnabled: true);

            select = AllSelectNonBiProperties.Concat(new string[]
                    {
                        "PerformanceMetrics/ConversionsCredit",
                        "PerformanceMetrics/PartialConversionCPA",
                        "PerformanceMetrics/PartialConversionRate",
                        "PerformanceMetrics/AllConversionsCredit",
                        "PerformanceMetrics/AllPartialConversionCPA",
                        "PerformanceMetrics/AllPartialConversionRate",
                    }).ToArray();
            result = CallGetCampaignsGridData(PartialConversionPilotCustomer, select: this.GetSelect(select));
            VerifyGridData(result, expectedResults, select, biData, useConversionCredit: true, partialConversionEnabled: true);

            select = AllSelectNonBiProperties.Concat(new string[]
                    {
                        "PerformanceMetrics/ViewThroughConversionsCredit",
                        "PerformanceMetrics/ViewThroughPartialConversionsCPA",
                        "PerformanceMetrics/ViewThroughPartialConversionsRate",
                    }).ToArray();
            result = CallGetCampaignsGridData(PartialConversionPilotCustomer, select: this.GetSelect(select));
            VerifyGridData(result, expectedResults, select, biData, useConversionCredit: true, partialConversionEnabled: true);

            select = AllSelectNonBiProperties.Concat(new string[] { PerformanceMetrics }).ToArray();
            result = CallGetCampaignsGridData(PartialConversionPilotCustomer, select: this.GetSelect(select));
            VerifyGridData(result, expectedResults, select, biData, useConversionCredit: true, partialConversionEnabled: true);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        [DataRow(new int[] { })]

        public void GetCampaignsGridData_PartialConversions(int[] pilotFeatures)
        {
            List<int> featureList = new List<int>() { Features.ViewThroughConversionFlagId, Features.EnableNativeAdsFlagId };
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                featureList.AddRange(pilotFeatures);
            }
            var PartialConversionPilotCustomer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, featureList.ToArray());
            this.CreateCampaignsForCustomer(PartialConversionPilotCustomer, 5);
            // Account scope, no selected columns
            string[] select = AllSelectNonBiProperties.Concat(new string[]
                {
                "PerformanceMetrics/ConversionsCredit",
                "PerformanceMetrics/PartialConversionCPA",
                "PerformanceMetrics/PartialConversionRate",
                "PerformanceMetrics/AllConversionsCredit",
                "PerformanceMetrics/AllPartialConversionCPA",
                "PerformanceMetrics/AllPartialConversionRate",
                "PerformanceMetrics/ViewThroughConversionsCredit",
                "PerformanceMetrics/ViewThroughPartialConversionsCPA",
                "PerformanceMetrics/ViewThroughPartialConversionsRate",
                }).ToArray();
            var result = CallGetCampaignsGridData(PartialConversionPilotCustomer);
            var biData = PopulateRandomBiData(DateTime.Today, customerInfo: PartialConversionPilotCustomer, useConversionCredit: true, viewThroughEnabled: true);

            var expectedResults = campaignCollection.Campaigns.ToArray();
            VerifyGridData(result, expectedResults, select, null);

            // Account scope, with selected columns
            result = CallGetCampaignsGridData(PartialConversionPilotCustomer, select: this.GetSelect(select));

            expectedResults = campaignCollection.Campaigns.ToArray();
            VerifyGridData(result, expectedResults, select, biData, useConversionCredit: true, partialConversionEnabled: true);

            if (biData != null)
            {
                VerifyTotalPerformanceForPartialConversion(result, expectedResults, biData);
            }

            // Perf sort
            string orderBy = "PerformanceMetrics/ConversionsCredit asc";
            result = CallGetCampaignsGridData(PartialConversionPilotCustomer, orderBy: orderBy, select: this.GetSelect(select));

            expectedResults = biData != null
                ? campaignCollection.Campaigns.OrderBy(x => biData[x.Data.Id].ConversionsCredit.GetValueOrDefault())
                    .ThenBy(x => x.Data.Id).ToArray()
                : campaignCollection.Campaigns.OrderBy(x => x.Data.Id).ToArray();
            VerifyGridData(result, expectedResults, select, biData, useConversionCredit: true, partialConversionEnabled: true);

            // Perf filter
            string filter = $"PerformanceMetrics/ConversionsCredit gt 80.5";
            result = CallGetCampaignsGridData(PartialConversionPilotCustomer, queryFilter: filter, select: this.GetSelect(select));

            expectedResults = biData != null
                ? campaignCollection.Campaigns.Where(x => biData[x.Data.Id].ConversionsCredit > 80.5).OrderBy(x => x.Data.Id).ToArray()
                : Enumerable.Empty<TestCampaign>().ToArray();
            VerifyGridData(result, expectedResults, select, biData, useConversionCredit: true, partialConversionEnabled: true);
        }

        [Ignore]
        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        [DataRow(new int[] { })]

        public void GetCampaignsGridData_PartialConversions_NotInViewThroughPilot(int[] pilotFeatures)
        {
            List<int> featureList = new List<int>() { Features.ViewThroughConversionFlagId, Features.AdultAds };
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                featureList.AddRange(pilotFeatures);
            }
            // customer is in partialConversion pilot and not in viewthroughConversion pilot.
            var PartialConversionPilotCustomer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.India, CustomerFactory.TargetLanguage.English, 1, false, featureList.ToArray());
            this.CreateCampaignsForCustomer(PartialConversionPilotCustomer, 5);
            // Account scope, no selected columns
            string[] select = AllSelectNonBiProperties.Concat(new string[]
                {
                "PerformanceMetrics/ConversionsCredit",
                "PerformanceMetrics/PartialConversionCPA",
                "PerformanceMetrics/PartialConversionRate",
                "PerformanceMetrics/AllConversionsCredit",
                "PerformanceMetrics/AllPartialConversionCPA",
                "PerformanceMetrics/AllPartialConversionRate"
                }).ToArray();
            var result = CallGetCampaignsGridData(PartialConversionPilotCustomer);
            var biData = PopulateRandomBiData(DateTime.Today, customerInfo: PartialConversionPilotCustomer, useConversionCredit: true, viewThroughEnabled: false);

            var expectedResults = campaignCollection.Campaigns.ToArray();
            VerifyGridData(result, expectedResults, select, null);

            // Account scope, with selected columns
            result = CallGetCampaignsGridData(PartialConversionPilotCustomer, select: this.GetSelect(select));

            expectedResults = campaignCollection.Campaigns.ToArray();
            VerifyGridData(result, expectedResults, select, biData, useConversionCredit: true, partialConversionEnabled: true);
            if (biData != null)
            {
                VerifyTotalPerformanceForPartialConversion(result, expectedResults, biData);
            }

            // Perf sort
            string orderBy = "PerformanceMetrics/ConversionsCredit asc";
            result = CallGetCampaignsGridData(PartialConversionPilotCustomer, orderBy: orderBy, select: this.GetSelect(select));

            expectedResults = biData != null
                ? campaignCollection.Campaigns.OrderBy(x => biData[x.Data.Id].ConversionsCredit.GetValueOrDefault())
                    .ThenBy(x => x.Data.Id).ToArray()
                : campaignCollection.Campaigns.OrderBy(x => x.Data.Id).ToArray();
            VerifyGridData(result, expectedResults, select, biData, useConversionCredit: true, partialConversionEnabled: true);

            // Perf filter
            string filter = $"PerformanceMetrics/ConversionsCredit gt 80.5";
            result = CallGetCampaignsGridData(PartialConversionPilotCustomer, queryFilter: filter, select: this.GetSelect(select));

            expectedResults = biData != null
                ? campaignCollection.Campaigns.Where(x => biData[x.Data.Id].ConversionsCredit > 80.5).OrderBy(x => x.Data.Id).ToArray()
                : Enumerable.Empty<TestCampaign>().ToArray();
            VerifyGridData(result, expectedResults, select, biData, useConversionCredit: true, partialConversionEnabled: true);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.AudienceAutoBidding)]
        [DataRow(new int[] { })]

        public void GetCampaignsGridData_ViewThroughConversionsRevenue(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            TestCampaignCollection.Clean(AutoBiddingForAudienceNetworkCustomer);
            this.CreateCampaignsForCustomer(AutoBiddingForAudienceNetworkCustomer, 5);
            // Account scope, no selected columns
            var select = AllSelectProperties;
            var result = CallGetCampaignsGridData(AutoBiddingForAudienceNetworkCustomer);
            var biData = PopulateRandomBiData(DateTime.Today, customerInfo: AutoBiddingForAudienceNetworkCustomer);

            var expectedResults = campaignCollection.Campaigns.ToArray();
            VerifyGridData(result, expectedResults, select, null);

            // Account scope, with selected columns
            result = CallGetCampaignsGridData(AutoBiddingForAudienceNetworkCustomer, select: this.GetSelect(select));

            expectedResults = campaignCollection.Campaigns.ToArray();
            VerifyGridData(result, expectedResults, select, biData);

            // Perf sort Revenue
            string orderBy = "PerformanceMetrics/ViewThroughConversionsRevenue asc";
            result = CallGetCampaignsGridData(AutoBiddingForAudienceNetworkCustomer, orderBy: orderBy, select: this.GetSelect(select));
            GridDataValidateHelper.VerifyGridDataOrder(result, "ViewThroughConversionsRevenue", true);

            // Perf filter Revenue
            string filter = $"PerformanceMetrics/ViewThroughConversionsRevenue gt 100.0";
            result = CallGetCampaignsGridData(AutoBiddingForAudienceNetworkCustomer, queryFilter: filter, select: this.GetSelect(select));
            GridDataValidateHelper.VerifyGridDataFilter(result, "ViewThroughConversionsRevenue", 100, "gt");

            // Perf sort CPA
            orderBy = "PerformanceMetrics/ViewThroughConversionsCPA asc";
            result = CallGetCampaignsGridData(AutoBiddingForAudienceNetworkCustomer, orderBy: orderBy, select: this.GetSelect(select));
            GridDataValidateHelper.VerifyGridDataOrder(result, "ViewThroughConversionsCPA");

            // Perf filter Revenue
            filter = $"PerformanceMetrics/ViewThroughConversionsCPA gt 5.0";
            result = CallGetCampaignsGridData(AutoBiddingForAudienceNetworkCustomer, queryFilter: filter, select: this.GetSelect(select));
            GridDataValidateHelper.VerifyGridDataFilter(result, "ViewThroughConversionsCPA", 5.0, "gt");

            // Perf sort ReturnOnAdSpend
            orderBy = "PerformanceMetrics/ViewThroughConversionsReturnOnAdSpend asc";
            result = CallGetCampaignsGridData(AutoBiddingForAudienceNetworkCustomer, orderBy: orderBy, select: this.GetSelect(select));
            GridDataValidateHelper.VerifyGridDataOrder(result, "ViewThroughConversionsReturnOnAdSpend");

            // Perf filter ReturnOnAdSpend
            filter = $"PerformanceMetrics/ViewThroughConversionsReturnOnAdSpend gt 5.0";
            result = CallGetCampaignsGridData(AutoBiddingForAudienceNetworkCustomer, queryFilter: filter, select: this.GetSelect(select));
            GridDataValidateHelper.VerifyGridDataFilter(result, "ViewThroughConversionsReturnOnAdSpend", 5.0, "gt");

            // Perf sort Rate
            orderBy = "PerformanceMetrics/ViewThroughConversionsRate asc";
            result = CallGetCampaignsGridData(AutoBiddingForAudienceNetworkCustomer, orderBy: orderBy, select: this.GetSelect(select));
            GridDataValidateHelper.VerifyGridDataOrder(result, "ViewThroughConversionsRate");

            // Perf filter Rate
            filter = $"PerformanceMetrics/ViewThroughConversionsRate gt 2.0";
            result = CallGetCampaignsGridData(AutoBiddingForAudienceNetworkCustomer, queryFilter: filter, select: this.GetSelect(select));
            GridDataValidateHelper.VerifyGridDataFilter(result, "ViewThroughConversionsRate", 2.0, "gt");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Conversions)]
        public void GetCampaignsGridData_CVRL()
        {
            List<int> featureList = new List<int>() { Features.ConversionValueRules };
            var cvrlCustomer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, featureList.ToArray());
            this.CreateCampaignsForCustomer(cvrlCustomer, 5);
            // Account scope, no selected columns

            var segmentations = new List<string>() { SegmentationType.AudienceType.ToString() };
            string[] select = AllSelectNonBiProperties.Concat(new string[]
                {
                    "PerformanceMetrics/AdvertiserReportedRevenueAdjustment",
                    "PerformanceMetrics/AllConversionAdvertiserReportedRevenueAdjustment",
                    "PerformanceMetrics/ViewThroughConversionsRevenueAdjustment"
                }).ToArray();

            var result = CallGetCampaignsGridData(cvrlCustomer, segmentationTypes: segmentations);

            var biData = PopulateRandomBiData(DateTime.Today, customerInfo: cvrlCustomer, useConversionCredit: true, viewThroughEnabled: true, enableCvrl: true);

            var expectedResults = campaignCollection.Campaigns.ToArray();
            VerifyGridData(result, expectedResults, select, null, useCvrl: true);

            // Account scope, with selected columns
            result = CallGetCampaignsGridData(cvrlCustomer, segmentationTypes: segmentations, select: this.GetSelect(select));

            expectedResults = campaignCollection.Campaigns.ToArray();
            VerifyGridData(result, expectedResults, select, biData, useConversionCredit: true, partialConversionEnabled: true, useCvrl: true);

            // Perf sort Revenue
            string orderBy = "PerformanceMetrics/AdvertiserReportedRevenueAdjustment asc";
            result = CallGetCampaignsGridData(cvrlCustomer, segmentationTypes: segmentations, orderBy: orderBy, select: this.GetSelect(select));
            GridDataValidateHelper.VerifyGridDataOrder(result, "AdvertiserReportedRevenueAdjustment", true);

            // Perf filter Revenue
            string filter = $"PerformanceMetrics/AdvertiserReportedRevenueAdjustment gt 100.0";
            result = CallGetCampaignsGridData(cvrlCustomer, segmentationTypes: segmentations, queryFilter: filter, select: this.GetSelect(select));
            GridDataValidateHelper.VerifyGridDataFilter(result, "AdvertiserReportedRevenueAdjustment", 100, "gt");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_NewCustomer()
        {
            List<int> featureList = new List<int>() { Features.PmaxNewCustomerAcquisition };
            var customer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, featureList.ToArray());
            this.CreateCampaignsForCustomer(customer, 5);
            // Account scope, no selected columns

            var segmentations = new List<string>() { SegmentationType.AudienceType.ToString() };
            string[] select = AllSelectNonBiProperties.Concat(new string[]
                {
                    "PerformanceMetrics/NewCustomerConversions",
                    "PerformanceMetrics/NewCustomerConversionRate",
                    "PerformanceMetrics/NewCustomerRevenue",
                    "PerformanceMetrics/NewCustomerCPA",
                    "PerformanceMetrics/NewCustomerRevenueOnAdSpend",
                    "PerformanceMetrics/UnknownCustomerConversions",
                    "PerformanceMetrics/UnknownCustomerRevenue",
                    "PerformanceMetrics/NewCustomerCount"
                }).ToArray();

            var biData = PopulateRandomBiData(DateTime.Today, customerInfo: customer, useConversionCredit: true, enableNewCustomer: true);

            var result = CallGetCampaignsGridData(customer, segmentationTypes: segmentations);

            var expectedResults = campaignCollection.Campaigns.ToArray();
            VerifyGridData(result, expectedResults, select, null, useCvrl: true);

            // Account scope, with selected columns
            result = CallGetCampaignsGridData(customer, select: this.GetSelect(select));

            expectedResults = campaignCollection.Campaigns.ToArray();
            VerifyGridData(result, expectedResults, select, biData, useConversionCredit: true);

            // Perf sort Revenue
            string orderBy = "PerformanceMetrics/NewCustomerRevenue asc";
            result = CallGetCampaignsGridData(customer, segmentationTypes: segmentations, orderBy: orderBy, select: this.GetSelect(select));
            GridDataValidateHelper.VerifyGridDataOrder(result, "NewCustomerRevenue", true);

            // Perf filter Revenue
            string filter = $"PerformanceMetrics/NewCustomerRevenue gt 100.0";
            result = CallGetCampaignsGridData(customer, segmentationTypes: segmentations, queryFilter: filter, select: this.GetSelect(select));
            GridDataValidateHelper.VerifyGridDataFilter(result, "NewCustomerRevenue", 100, "gt");
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.AIM)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_PerformanceMetrics_AudienceSOV_WithSortAndFilter_WithAndWithoutAudienceSegmentation(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var select = new[]
            {
                "Id",
                "PerformanceMetrics/AudienceAuctionWonPercent",
                "PerformanceMetrics/AudienceTopISLostToBudgetPercent",
                "PerformanceMetrics/AudienceTopISLostToRankPercent",
                "PerformanceMetrics/RelativeCTR",
            };

            var Today = DateTime.Today.Date;

            var todayBiData = PopulateRandomBiData(Today, mediumId: 10);

            if (todayBiData != null)
            {
                var sortedExpectedResults = todayBiData.OrderBy(s => s.Value.AudienceAuctionWonPercent).ToArray();
                var middleIndedx = Convert.ToInt32(sortedExpectedResults.Length / 2);
                double? filterValue = sortedExpectedResults[middleIndedx].Value.AudienceAuctionWonPercent;
                var expectedResults = sortedExpectedResults.
                    Where(s => s.Value.AudienceAuctionWonPercent > filterValue).ToArray();

                string orderBy = "PerformanceMetrics/AudienceAuctionWonPercent asc";

                string filter = $"PerformanceMetrics/AudienceAuctionWonPercent gt {filterValue.ToString()}";

                foreach (var segmentationTypes in new List<List<string>>() { new List<string>() { "AudienceName" } })
                {
                    var result = CallGetCampaignsGridData(this.cInfo, today, segmentationTypes: segmentationTypes, orderBy: orderBy, queryFilter: filter, select: this.GetSelect(select));
                    var expectToalBiData = calculateBiDataTotalValue(todayBiData, true, 2);

                    Assert.AreEqual((double?)result["@ns.unfiltered.totals"].PerformanceMetrics.AudienceAuctionWonPercent, expectToalBiData.AudienceAuctionWonPercent, "@ns.unfiltered.totals AudienceAuctionWonPercent doesn't match");
                    Assert.AreEqual((double?)result["@ns.unfiltered.totals"].PerformanceMetrics.AudienceTopISLostToBudgetPercent, expectToalBiData.AudienceTopISLostToBudgetPercent, "@ns.unfiltered.totals AudienceTopISLostToBudgetPercent doesn't match");
                    Assert.AreEqual((double?)result["@ns.unfiltered.totals"].PerformanceMetrics.AudienceTopISLostToRankPercent, expectToalBiData.AudienceTopISLostToRankPercent, "@ns.unfiltered.totals AudienceTopISLostToRankPercent doesn't match");
                    Assert.AreEqual((double?)result["@ns.unfiltered.totals"].PerformanceMetrics.RelativeCTR, expectToalBiData.RelativeCTR, "@ns.unfiltered.totals RelativeCTR doesn't match");

                    for (int i = 0; i < result.value.Count; i++)
                    {
                        var actualRow = result.value[i];
                        var expectedRow = expectedResults[i].Value;
                        Assert.AreEqual(expectedRow.AudienceAuctionWonPercent, (double?)actualRow.PerformanceMetrics.AudienceAuctionWonPercent, "AudienceAuctionWonPercent doesn't match");
                        Assert.AreEqual(expectedRow.AudienceTopISLostToBudgetPercent, (double?)actualRow.PerformanceMetrics.AudienceTopISLostToBudgetPercent, "AudienceTopISLostToBudgetPercent doesn't match");
                        Assert.AreEqual(expectedRow.AudienceTopISLostToRankPercent, (double?)actualRow.PerformanceMetrics.AudienceTopISLostToRankPercent, "AudienceTopISLostToRankPercent doesn't match");
                        Assert.AreEqual(expectedRow.RelativeCTR, (double?)actualRow.PerformanceMetrics.RelativeCTR, "RelativeCTR doesn't match");
                    }

                    if (segmentationTypes != null)
                    {
                        Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((JArray)a[PerformanceMetrics]["SegmentedData"][0]["Key"]).Count == 1));
                        Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((string)a[PerformanceMetrics]["SegmentedData"][0]["Key"][0]["StringValue"]).Equals("Hobbies and Leisure/Pets and Animals")));
                    }
                }
            }
            else
            {
                string orderBy = "PerformanceMetrics/AudienceAuctionWonPercent asc";

                string filter = $"PerformanceMetrics/AudienceAuctionWonPercent gt 0";

                CallGetCampaignsGridData(this.cInfo, today, orderBy: orderBy, queryFilter: filter, select: this.GetSelect(select));
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.AIM)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_PerformanceMetrics_RelativeCTR(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            // Selecting RelativeCTR by itself, it's one of the special cases for dynamic that needed fix.
            // BI sproc is now fix but having this test to ensure it works

            var select = new[]
            {
                "Id",
                "PerformanceMetrics/RelativeCTR",
            };

            var Today = DateTime.Today.Date;

            var todayBiData = PopulateRandomBiData(Today, mediumId: 10);

            if (todayBiData != null)
            {
                var sortedExpectedResults = todayBiData.OrderBy(s => s.Value.RelativeCTR).ToArray();
                var middleIndedx = Convert.ToInt32(sortedExpectedResults.Length / 2);
                double? filterValue = sortedExpectedResults[middleIndedx].Value.RelativeCTR;
                var expectedResults = sortedExpectedResults.
                    Where(s => s.Value.RelativeCTR > filterValue).ToArray();

                string orderBy = "PerformanceMetrics/RelativeCTR asc";

                string filter = $"PerformanceMetrics/RelativeCTR gt {filterValue.ToString()}";

                var result = CallGetCampaignsGridData(this.cInfo, today, orderBy: orderBy, queryFilter: filter, select: this.GetSelect(select));
                var expectToalBiData = calculateBiDataTotalValue(todayBiData, true, 2);

                Assert.AreEqual((double?)result["@ns.unfiltered.totals"].PerformanceMetrics.RelativeCTR, expectToalBiData.RelativeCTR, "@ns.unfiltered.totals RelativeCTR doesn't match");

                for (int i = 0; i < result.value.Count; i++)
                {
                    var actualRow = result.value[i];
                    var expectedRow = expectedResults[i].Value;
                    Assert.AreEqual(expectedRow.RelativeCTR, (double?)actualRow.PerformanceMetrics.RelativeCTR, "RelativeCTR doesn't match");
                }
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.AIM)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_PerformanceMetrics_RelativeCTR_WithSortAndFilter(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var select = new[]
            {
                "Id",
                "PerformanceMetrics/AudienceAuctionWonPercent",
                "PerformanceMetrics/AudienceTopISLostToBudgetPercent",
                "PerformanceMetrics/AudienceTopISLostToRankPercent",
                "PerformanceMetrics/RelativeCTR",
            };

            var Today = DateTime.Today.Date;

            var todayBiData = PopulateRandomBiData(Today, mediumId: 10);

            if (todayBiData != null)
            {
                var sortedExpectedResults = todayBiData.OrderBy(s => s.Value.RelativeCTR).ToArray();
                var middleIndedx = Convert.ToInt32(sortedExpectedResults.Length / 2);
                double? filterValue = sortedExpectedResults[middleIndedx].Value.RelativeCTR;
                var expectedResults = sortedExpectedResults.
                    Where(s => (s.Value.RelativeCTR.HasValue) && (s.Value.RelativeCTR > filterValue)).ToArray();

                string orderBy = "PerformanceMetrics/RelativeCTR asc";

                string filter = $"PerformanceMetrics/RelativeCTR gt {filterValue}";

                var result = CallGetCampaignsGridData(this.cInfo, today, orderBy: orderBy, queryFilter: filter, select: this.GetSelect(select));
                var expectToalBiData = calculateBiDataTotalValue(todayBiData, true, 2);

                Assert.AreEqual((double?)result["@ns.unfiltered.totals"].PerformanceMetrics.RelativeCTR, expectToalBiData.RelativeCTR, "@ns.unfiltered.totals RelativeCTR doesn't match");

                for (int i = 0; i < result.value.Count; i++)
                {
                    var actualRow = result.value[i];
                    var expectedRow = expectedResults[i].Value;
                    Assert.AreEqual(expectedRow.RelativeCTR, (double?)actualRow.PerformanceMetrics.RelativeCTR, "RelativeCTR doesn't match");
                }
            }
            else
            {
                string orderBy = "PerformanceMetrics/AudienceAuctionWonPercent asc";

                string filter = $"PerformanceMetrics/AudienceAuctionWonPercent gt 0";

                CallGetCampaignsGridData(this.cInfo, today, orderBy: orderBy, queryFilter: filter, select: this.GetSelect(select));
            }

        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.AIM)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_PerformanceMetrics_RelativeCTR_WithNullValue_WithSortAndFilter(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var select = new[]
            {
                "Id",
                "PerformanceMetrics/AudienceAuctionWonPercent",
                "PerformanceMetrics/AudienceTopISLostToBudgetPercent",
                "PerformanceMetrics/AudienceTopISLostToRankPercent",
                "PerformanceMetrics/RelativeCTR",
            };

            var Today = DateTime.Today.Date;

            var todayBiData = PopulateRandomBiData(Today, populateRelativeCTR: false);

            if (todayBiData != null)
            {
                double? filterValue = 0;

                string orderBy = "PerformanceMetrics/RelativeCTR asc";

                string filter = $"PerformanceMetrics/RelativeCTR gt {filterValue}";

                var result = CallGetCampaignsGridData(this.cInfo, today, orderBy: orderBy, queryFilter: filter, select: this.GetSelect(select));
                Assert.AreEqual(result.value.Count, 0);
            }
            else
            {
                string orderBy = "PerformanceMetrics/AudienceAuctionWonPercent asc";

                string filter = $"PerformanceMetrics/AudienceAuctionWonPercent gt 0";

                CallGetCampaignsGridData(this.cInfo, today, orderBy: orderBy, queryFilter: filter, select: this.GetSelect(select));
            }

        }

        [TestMethod]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_PerformanceMetrics_CPS(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var select = new[]
            {
                "Id",
                "PerformanceMetrics/SalesCount",
                "PerformanceMetrics/AverageCPS",
                PeriodPerformanceMetrics,
                PerformanceMetricsChange,
                PerformanceMetricsChangePercentage
            };

            var todayBiData = PopulateRandomBiData(DateTime.Today.Date);
            var yesterdayBiData = PopulateRandomBiData(DateTime.Today.Date.AddDays(-1), clear: false);

            var allExpectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id).ToArray();

            var result = CallGetCampaignsGridData(this.cInfo, today, yesterday, select: this.GetSelect(select));
            VerifyGridData(result, allExpectedResults, select, todayBiData, yesterdayBiData);

            VerifyColumnSortAndFilter<long>(todayBiData, "SalesCount");
            VerifyColumnSortAndFilter<double>(todayBiData, "AverageCPS", expectNullTotal: true);
        }

        [TestMethod]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_PerformanceMetrics_Installs(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var select = new[]
            {
                "Id",
                "PerformanceMetrics/Installs",
                "PerformanceMetrics/CostPerInstall",
                "PerformanceMetrics/RevenuePerInstall",
                PeriodPerformanceMetrics,
                PerformanceMetricsChange,
                PerformanceMetricsChangePercentage
            };

            var todayBiData = PopulateRandomBiData(DateTime.Today.Date);
            var yesterdayBiData = PopulateRandomBiData(DateTime.Today.Date.AddDays(-1), clear: false);

            var allExpectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id).ToArray();

            var result = CallGetCampaignsGridData(this.cInfo, today, yesterday, select: this.GetSelect(select));
            VerifyGridData(result, allExpectedResults, select, todayBiData, yesterdayBiData);

            VerifyColumnSortAndFilter<long>(todayBiData, "Installs");
            VerifyColumnSortAndFilter<double>(todayBiData, "CostPerInstall");
            VerifyColumnSortAndFilter<double>(todayBiData, "RevenuePerInstall");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_AdCustomizerAttributes(int[] pilotFeatures)
        {
            CustomerInfo customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, true, pilotFeatures);
            TestCampaignCollection.Clean(customerInfo);

            var campaignCollection = new TestCampaignCollection(2);
            campaignCollection.Campaigns[0] = TestSetting.Factories.TestCampaginFactory.Produce(CampaignFactory.CampaignType.Default);
            campaignCollection.Campaigns[0].Data.Languages = new Language[] { Language.All };
            campaignCollection.Campaigns[1] = TestSetting.Factories.TestCampaginFactory.Produce(CampaignFactory.CampaignType.Default);
            campaignCollection.Campaigns[1].Data.Languages = new Language[] { Language.All };

            var addResponse = campaignCollection.Add(customerInfo, customerInfo.AccountIds[0]);
            EntityValidator.ValidateBasicSuccess(addResponse);
            Dictionary<long, BiData> biData = this.PopulateRandomBiData(DateTime.Today, campaignCollection: campaignCollection);

            var allExpectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id).ToArray();
            var basicSelect = new string[] { "Id" };

            var campaignId = campaignCollection.Campaigns[1].Data.Id;
            RSAAdCustomizerAttributeTestHelper.CallCreateAdCustomizerAttributes(customerInfo, ApiVersion.BaseUrl);
            RSAAdCustomizerAttributeTestHelper.CallCreateAdCustomizerAttributeValues(customerInfo, ApiVersion.BaseUrl, campaignId);

            string lastWriteTime = DateTime.UtcNow.ToString("O");

            dynamic result = CallGetCampaignsGridData(customerInfo, select: GetSelect(basicSelect), expand: "RSAAdCustomizerAttributes", lastWriteTime: lastWriteTime);
            VerifyGridData(result, allExpectedResults, basicSelect, biData, campaignIdForRSAAdCustomizerAttribute: campaignId);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_AdCustomizerAttributes_EditorialStatus(int[] pilotFeatures)
        {
            CustomerInfo customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, true, pilotFeatures);
            TestCampaignCollection.Clean(customerInfo);

            var campaignCollection = new TestCampaignCollection(2);
            campaignCollection.Campaigns[0] = TestSetting.Factories.TestCampaginFactory.Produce(CampaignFactory.CampaignType.Default);
            campaignCollection.Campaigns[0].Data.Languages = new Language[] { Language.All };
            campaignCollection.Campaigns[1] = TestSetting.Factories.TestCampaginFactory.Produce(CampaignFactory.CampaignType.Default);
            campaignCollection.Campaigns[1].Data.Languages = new Language[] { Language.All };

            var addResponse = campaignCollection.Add(customerInfo, customerInfo.AccountIds[0]);
            EntityValidator.ValidateBasicSuccess(addResponse);
            Dictionary<long, BiData> biData = this.PopulateRandomBiData(DateTime.Today, campaignCollection: campaignCollection);

            var allExpectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id).ToArray();
            var basicSelect = new string[] { "Id" };

            var campaignId = campaignCollection.Campaigns[1].Data.Id;
            RSAAdCustomizerAttributeTestHelper.CallCreateAdCustomizerAttributes(customerInfo, ApiVersion.BaseUrl);
            RSAAdCustomizerAttributeTestHelper.CallCreateAdCustomizerAttributeValues(customerInfo, ApiVersion.BaseUrl, campaignId);

            var accountId = customerInfo.AccountIds[0];
            DatabaseHelper.UpdateAdCustomizerAccountEditorialStatus(customerInfo.CustomerId, accountId);

            string lastWriteTime = DateTime.UtcNow.ToString("O");

            var result = CallGetCampaignsGridData(customerInfo, select: GetSelect(basicSelect), expand: "RSAAdCustomizerAttributes", lastWriteTime: lastWriteTime);
            Assert.AreEqual("ApprovedLimited", result.value[0]["RSAAdCustomizerAttributes"][0].EditorialStatus.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_AdCustomizerAttributes_NoDefaultAccountValue(int[] pilotFeatures)
        {
            CustomerInfo customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, true, pilotFeatures);
            TestCampaignCollection.Clean(customerInfo);

            var campaignCollection = new TestCampaignCollection(2);
            campaignCollection.Campaigns[0] = TestSetting.Factories.TestCampaginFactory.Produce(CampaignFactory.CampaignType.Default);
            campaignCollection.Campaigns[0].Data.Languages = new Language[] { Language.All };
            campaignCollection.Campaigns[1] = TestSetting.Factories.TestCampaginFactory.Produce(CampaignFactory.CampaignType.Default);
            campaignCollection.Campaigns[1].Data.Languages = new Language[] { Language.All };

            var addResponse = campaignCollection.Add(customerInfo, customerInfo.AccountIds[0]);
            EntityValidator.ValidateBasicSuccess(addResponse);
            Dictionary<long, BiData> biData = this.PopulateRandomBiData(DateTime.Today, campaignCollection: campaignCollection);

            var allExpectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id).ToArray();
            var basicSelect = new string[] { "Id" };

            var campaignId = campaignCollection.Campaigns[1].Data.Id;
            var payload = new
            {
                Attributes = new[]
                {
                    new
                    {
                        odatatype = "#Model.AdCustomizerAttribute",
                        Name = "Name",
                        Datatype = "String",
                    },
                    new
                    {
                        odatatype = "#Model.AdCustomizerAttribute",
                        Name = "Count",
                        Datatype = "Number",
                    },
                    new
                    {
                        odatatype = "#Model.AdCustomizerAttribute",
                        Name = "Money",
                        Datatype = "Price",
                    },
                    new
                    {
                        odatatype = "#Model.AdCustomizerAttribute",
                        Name = "Percent",
                        Datatype = "Percent",
                    }
                }
            };

            RSAAdCustomizerAttributeTestHelper.CallCreateAdCustomizerAttributesCustomized(customerInfo, ApiVersion.BaseUrl, payload);
            RSAAdCustomizerAttributeTestHelper.CallCreateAdCustomizerAttributeValues(customerInfo, ApiVersion.BaseUrl, campaignId);

            string lastWriteTime = DateTime.UtcNow.ToString("O");

            dynamic result = CallGetCampaignsGridData(customerInfo, select: GetSelect(basicSelect), expand: "RSAAdCustomizerAttributes", lastWriteTime: lastWriteTime);
            VerifyGridData(result, allExpectedResults, basicSelect, biData);
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var actualValue = actualRow["RSAAdCustomizerAttributes"];
                if (actualRow.Id == campaignId)
                {
                    RSAAdCustomizerAttributeTestHelper.CheckAdCustomizerAttributes1(actualValue);
                }
                else
                {
                    RSAAdCustomizerAttributeTestHelper.CheckAdCustomizerAttributes5(actualValue);
                }
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_AdCustomizerAttributes_CampaignType(int[] pilotFeatures)
        {
            CustomerInfo customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, true, pilotFeatures);
            TestCampaignCollection.Clean(customerInfo);
            var basicSelect = new string[] { "Id" };

            var campaignCollection = new TestCampaignCollection(2);
            campaignCollection.Campaigns[0] = TestSetting.Factories.TestCampaginFactory.Produce(CampaignFactory.CampaignType.Audience);
            campaignCollection.Campaigns[0].Data.Languages = new Language[] { Language.All };

            campaignCollection.Campaigns[1] = TestSetting.Factories.TestCampaginFactory.Produce(CampaignFactory.CampaignType.Default);
            campaignCollection.Campaigns[1].Data.Languages = new Language[] { Language.All };

            var addResponse = campaignCollection.Add(customerInfo, customerInfo.AccountIds[0]);
            EntityValidator.ValidateBasicSuccess(addResponse);
            Dictionary<long, BiData> biData = this.PopulateRandomBiData(DateTime.Today, campaignCollection: campaignCollection);
            var allExpectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id).ToArray();
            var campaignId = campaignCollection.Campaigns[1].Data.Id;

            RSAAdCustomizerAttributeTestHelper.CallCreateAdCustomizerAttributes(customerInfo, ApiVersion.BaseUrl);

            dynamic result = CallGetCampaignsGridData(customerInfo, select: GetSelect(basicSelect), expand: "RSAAdCustomizerAttributes");
            VerifyGridData(result, allExpectedResults, basicSelect, biData);

            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var actualValue = actualRow["RSAAdCustomizerAttributes"];
                if (actualRow.Id == campaignId)
                {
                    RSAAdCustomizerAttributeTestHelper.CheckAdCustomizerAttributes2(actualValue);
                }
                else
                {
                    JArray value = actualValue as JArray;
                    Assert.AreEqual(0, value.Count);
                }
            }

            //filter test
            result = CallGetCampaignsGridData(customerInfo, queryFilter: $"CampaignType eq Enum.CampaignType'Audience'", select: GetSelect(basicSelect), expand: "RSAAdCustomizerAttributes");
            Assert.AreEqual(1, result.value.Count, "Row count mismatch");

            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var actualValue = actualRow["RSAAdCustomizerAttributes"];
                JArray value = actualValue as JArray;
                Assert.AreEqual(0, value.Count);
            }

            result = CallGetCampaignsGridData(customerInfo, queryFilter: $"CampaignType eq Enum.CampaignType'Default'", select: GetSelect(basicSelect), expand: "RSAAdCustomizerAttributes");
            Assert.AreEqual(1, result.value.Count, "Row count mismatch");

            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var actualValue = actualRow["RSAAdCustomizerAttributes"];
                RSAAdCustomizerAttributeTestHelper.CheckAdCustomizerAttributes2(actualValue);
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_Componentized(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }

            var allExpectedResults = this.campaignCollection.Campaigns.OrderBy(x => x.Data.Id).ToArray();
            var basicSelect = new string[] { "Id" };

            Dictionary<long, BiData> biData = this.PopulateRandomBiData(DateTime.Today);
            string lastWriteTime = DateTime.UtcNow.ToString("O");

            // QualityScore
            var qsSelect = basicSelect.Append("QualityScore/OverallQualityScore").ToArray();
            dynamic result = CallGetCampaignsGridData(this.cInfo, select: GetSelect(qsSelect), lastWriteTime: lastWriteTime);
            VerifyGridData(result, allExpectedResults, qsSelect, biData);

            // HasAuctionInsight
            result = CallGetCampaignsGridData(this.cInfo, select: GetSelect(basicSelect), queryFilter: "HasAuctionInsight eq false", lastWriteTime: lastWriteTime);
            VerifyGridData(result, allExpectedResults, basicSelect, biData);

            // IsLastImported
            result = CallGetCampaignsGridData(this.cInfo, select: GetSelect(basicSelect), queryFilter: "UpdatedOnLastImport eq false", lastWriteTime: lastWriteTime);
            VerifyGridData(result, allExpectedResults, basicSelect, biData);

            // PerformanceMetrics
            var perfSelect = basicSelect.Append("PerformanceMetrics/Clicks").ToArray();
            result = CallGetCampaignsGridData(this.cInfo, select: GetSelect(perfSelect), lastWriteTime: lastWriteTime);
            VerifyGridData(result, allExpectedResults, perfSelect, biData);

            // PhonePerformanceMetrics
            perfSelect = basicSelect.Append("PhonePerformanceMetrics/ClickCalls").ToArray();
            result = CallGetCampaignsGridData(this.cInfo, select: GetSelect(perfSelect), lastWriteTime: lastWriteTime);
            VerifyGridData(result, allExpectedResults, perfSelect, biData);

            // SoV
            var sovSelect = basicSelect.Append("PerformanceMetrics/AuctionWonPercent").ToArray();
            result = CallGetCampaignsGridData(this.cInfo, select: GetSelect(sovSelect), lastWriteTime: lastWriteTime);
            VerifyGridData(result, allExpectedResults, sovSelect, biData);

            // UpgradedUrl
            var uurlSelect = basicSelect.Append("TrackingUrlTemplate").ToArray();
            result = CallGetCampaignsGridData(this.cInfo, select: GetSelect(uurlSelect), lastWriteTime: lastWriteTime);
            VerifyGridData(result, allExpectedResults, uurlSelect, biData);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_ExpandFilterSortBudget()
        {
            // Test sort, expand without sub-select
            var result = CallGetCampaignsGridData(this.cInfo, expand: "Budget", orderBy: "Budget/Amount asc");
            var expectedCampaigns = campaignCollection.Campaigns
                .OrderBy(x => x.Data.DailyTargetBudgetAmount)
                .ThenBy(x => x.Data.Id)
                .ToArray();

            for (int i = 0; i < result.value.Count; i++)
            {
                var campaign = expectedCampaigns[i];
                Assert.AreEqual(campaign.Data.BudgetId.GetValueOrDefault().ToString(), result.value[i].Budget.Id.ToString(), "Budget/Id mismatch");
                Assert.AreEqual(campaign.Data.BudgetName ?? string.Empty, result.value[i].Budget.Name.ToString(), "Budget/Name mismatch");
                Assert.AreEqual(mtToODataBudgetType[campaign.Data.SimplifiedBudgetType], result.value[i].Budget.Type.ToString(), "Budget/Type mismatch");
                Assert.AreEqual(campaign.Data.DailyTargetBudgetAmount.ToString(), result.value[i].Budget.Amount.ToString(), "Budget/Amount mismatch");
            }

            // Test filter, expand with sub-select
            double filterValue = expectedCampaigns[3].Data.DailyTargetBudgetAmount.Value;
            expectedCampaigns = campaignCollection.Campaigns
                .Where(x => x.Data.DailyTargetBudgetAmount > filterValue)
                .OrderBy(x => x.Data.Id)
                .ToArray();

            result = CallGetCampaignsGridData(this.cInfo, expand: "Budget($select=Amount,Type)", queryFilter: $"Budget/Amount gt {filterValue}");
            for (int i = 0; i < result.value.Count; i++)
            {
                var campaign = expectedCampaigns[i];
                Assert.AreEqual(mtToODataBudgetType[campaign.Data.SimplifiedBudgetType], result.value[i].Budget.Type.ToString(), "Budget/Type mismatch");
                Assert.AreEqual(campaign.Data.DailyTargetBudgetAmount.ToString(), result.value[i].Budget.Amount.ToString(), "Budget/Amount mismatch");
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_MonthlyBudget()
        {
            // Test sort, expand without sub-select
            var result = CallGetCampaignsGridData(this.cInfo, expand: "Budget", orderBy: "Budget/Amount asc");
            campaignCollection.Reload();
            var expectedCampaigns = campaignCollection.Campaigns
               .OrderBy(x => x.Data.DailyTargetBudgetAmount)
               .ThenBy(x => x.Data.Id)
               .ToArray();

            for (int i = 0; i < result.value.Count; i++)
            {
                var campaign = expectedCampaigns[i];
                Assert.AreEqual(campaign.Data.MonthlyBudgetAmount.ToString(), result.value[i].Budget.MonthlyAmount.ToString(), "Budget/MonthlyAmount mismatch");
            }
        }

        [TestMethod, Priority(1)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_ExpandFilterSortBudgetNew_GridDataFilter()
        {
            // Test sort, expand without sub-select
            var result = CallGetCampaignsGridData(this.cInfo, expand: "Budget", orderBy: "Budget/Amount asc");
            var expectedCampaigns = campaignCollection.Campaigns
                .OrderBy(x => x.Data.DailyTargetBudgetAmount)
                .ThenBy(x => x.Data.Id)
                .ToArray();

            for (int i = 0; i < result.value.Count; i++)
            {
                var campaign = expectedCampaigns[i];
                Assert.AreEqual(campaign.Data.BudgetId.GetValueOrDefault().ToString(), result.value[i].Budget.Id.ToString(), "Budget/Id mismatch");
                Assert.AreEqual(campaign.Data.BudgetName ?? string.Empty, result.value[i].Budget.Name.ToString(), "Budget/Name mismatch");
                Assert.AreEqual(mtToODataBudgetType[campaign.Data.SimplifiedBudgetType], result.value[i].Budget.Type.ToString(), "Budget/Type mismatch");
                Assert.AreEqual(campaign.Data.DailyTargetBudgetAmount.ToString(), result.value[i].Budget.Amount.ToString(), "Budget/Amount mismatch");
            }

            // Test filter, expand with sub-select
            double filterValue = expectedCampaigns[3].Data.DailyTargetBudgetAmount.Value;

            expectedCampaigns = campaignCollection.Campaigns
                .Where(x => x.Data.DailyTargetBudgetAmount > filterValue)
                .OrderBy(x => x.Data.Id)
                .ToArray();

            result = CallGetCampaignsGridData(
                this.cInfo,
                expand: "Budget($select=Amount,Type)",
                gridDataFilter: new dynamic[]
                {
                    new
                    {
                        PropertyToFilter = "Budget.Amount",
                        FilterOperation = FilterOperation.Greater,
                        FilterOnValues = new object[] { filterValue },
                        CustomColumnId = (string) null,
                    }
                });

            for (int i = 0; i < result.value.Count; i++)
            {
                var campaign = expectedCampaigns[i];
                Assert.AreEqual(mtToODataBudgetType[campaign.Data.SimplifiedBudgetType], result.value[i].Budget.Type.ToString(), "Budget/Type mismatch");
                Assert.AreEqual(campaign.Data.DailyTargetBudgetAmount.ToString(), result.value[i].Budget.Amount.ToString(), "Budget/Amount mismatch");
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_ComparisonPeriod()
        {
            var biData = PopulateRandomBiData(DateTime.Today);
            var comparisonBiData = PopulateRandomBiData(DateTime.Today.AddDays(-1), clear: false);

            var select = new[] { "Id", PerformanceMetrics, PeriodPerformanceMetrics, PerformanceMetricsChange, PerformanceMetricsChangePercentage };
            var result = CallGetCampaignsGridData(this.cInfo, today, yesterday, select: this.GetSelect(select));

            var expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, biData, comparisonBiData);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_PeriodSegmentationByAudienceType(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var biData = PopulateRandomBiData(DateTime.Today);
            var comparisonBiData = PopulateRandomBiData(DateTime.Today.AddDays(-1), clear: false);

            Dictionary<long, Dictionary<string, BiData>> segBiDataByCampaingnId = null;
            var select = new[] { "Id", "PerformanceMetrics/Clicks", "PerformanceMetrics/Impressions", "PerformanceMetrics/AllConversions", "PerformanceMetrics/AllConversionAdvertiserReportedRevenue", PeriodPerformanceMetrics, PerformanceMetricsChange, PerformanceMetricsChangePercentage };

            var segmentations = new List<string>() { SegmentationType.AudienceType.ToString() };
            var result = CallGetCampaignsGridData(this.cInfo, today, yesterday, segmentations, select: this.GetSelect(select));
            if (mockReporting)
            {
                segBiDataByCampaingnId = new Dictionary<long, Dictionary<string, BiData>>();
                comparisonBiData.ForEach(kvp => segBiDataByCampaingnId.Add(kvp.Key, new Dictionary<string, BiData>()));
                comparisonBiData.ForEach(kvp => segBiDataByCampaingnId[kvp.Key].Add("InMarket", kvp.Value));
            }
            var expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, biData, comparisonBiData, comparisonSegmentedBiDataByCampaignId: segBiDataByCampaingnId);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        public void GetCampaignsGridData_SegmentationByAudienceType_Impression(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var biData = PopulateRandomBiData(DateTime.Today.Date, targetTypeId: 51);

            var select = new[] { "PerformanceMetrics/Clicks", "PerformanceMetrics/Impressions" };

            var segmentations = new List<string>() { SegmentationType.AudienceType.ToString() };
            var result = CallGetCampaignsGridData(this.cInfo, today, null, segmentations, select: this.GetSelect(select));
            if (mockReporting)
            {
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((JArray)a[PerformanceMetrics]["SegmentedData"]).Count == 1));
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((JArray)a[PerformanceMetrics]["SegmentedData"][0]["Key"]).Count == 1));
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((string)a[PerformanceMetrics]["SegmentedData"][0]["Key"][0]["SegmentationType"]).Equals("AudienceType")));
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((string)a[PerformanceMetrics]["SegmentedData"][0]["Key"][0]["StringValue"]).Equals("ImpressionBasedRemarketingList")));
            }
            var expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, biData);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        public void GetCampaignsGridData_SegmentationByAudienceType_CustomSegment(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var biData = PopulateRandomBiData(DateTime.Today.Date, targetTypeId: 50);

            var select = new[] { "PerformanceMetrics/Clicks", "PerformanceMetrics/Impressions" };

            var segmentations = new List<string>() { SegmentationType.AudienceType.ToString() };
            var result = CallGetCampaignsGridData(this.cInfo, today, null, segmentations, select: this.GetSelect(select));
            if (mockReporting)
            {
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((JArray)a[PerformanceMetrics]["SegmentedData"]).Count == 1));
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((JArray)a[PerformanceMetrics]["SegmentedData"][0]["Key"]).Count == 1));
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((string)a[PerformanceMetrics]["SegmentedData"][0]["Key"][0]["SegmentationType"]).Equals("AudienceType")));
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((string)a[PerformanceMetrics]["SegmentedData"][0]["Key"][0]["StringValue"]).Equals("CustomSegment")));
            }
            var expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, biData);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_ComparisonPeriod_AbsoluteChange(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var biData = PopulateRandomBiData(DateTime.Today);
            var comparisonBiData = PopulateRandomBiData(DateTime.Today.AddDays(-1), clear: false);

            // Sort
            string orderBy = "PoPPerformanceMetricsAbsoluteChange/Clicks asc";
            var select = new[] { "Id", "PerformanceMetrics/Clicks", "PerformanceMetrics/Impressions", "PerformanceMetrics/AllConversions", "PerformanceMetrics/AllConversionAdvertiserReportedRevenue", PeriodPerformanceMetrics, PerformanceMetricsChange, PerformanceMetricsChangePercentage };
            var result = CallGetCampaignsGridData(this.cInfo, today, yesterday, select: this.GetSelect(select), orderBy: orderBy);

            IOrderedEnumerable<TestCampaign> expectedResultsSort = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            if (mockReporting)
            {
                expectedResultsSort = campaignCollection.Campaigns.OrderBy(x =>
                {
                    long id = x.Data.Id;
                    return Math.Abs(biData[id].Clicks.Value - comparisonBiData[id].Clicks.Value);
                })
                .ThenBy(x => x.Data.Id);
            }

            VerifyGridData(result, expectedResultsSort.ToArray(), select, biData, comparisonBiData);

            // Filter
            string filter = "PoPPerformanceMetricsAbsoluteChange/Clicks gt 50";
            result = CallGetCampaignsGridData(this.cInfo, today, yesterday, select: this.GetSelect(select), queryFilter: filter);

            IEnumerable<TestCampaign> expectedResultsFilter = Enumerable.Empty<TestCampaign>();
            if (mockReporting)
            {
                expectedResultsFilter = campaignCollection.Campaigns.Where(x =>
                {
                    long id = x.Data.Id;
                    return Math.Abs(biData[id].Clicks.Value - comparisonBiData[id].Clicks.Value) > 50;
                })
                .OrderBy(x => x.Data.Id);
            }

            VerifyGridData(result, expectedResultsFilter.ToArray(), select, biData, comparisonBiData);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_ComparisonPeriod_AbsoluteChangeNew_GridDataFilter(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var biData = PopulateRandomBiData(DateTime.Today);
            var comparisonBiData = PopulateRandomBiData(DateTime.Today.AddDays(-1), clear: false);

            // Sort
            string orderBy = "PoPPerformanceMetricsAbsoluteChange/Clicks asc";
            var select = new[] { "PerformanceMetrics/Clicks", PeriodPerformanceMetrics, PerformanceMetricsChange, PerformanceMetricsChangePercentage };
            var result = CallGetCampaignsGridData(this.cInfo, today, yesterday, select: this.GetSelect(select), orderBy: orderBy);

            IOrderedEnumerable<TestCampaign> expectedResultsSort = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            if (mockReporting)
            {
                expectedResultsSort = campaignCollection.Campaigns.OrderBy(x =>
                {
                    long id = x.Data.Id;
                    return Math.Abs(biData[id].Clicks.Value - comparisonBiData[id].Clicks.Value);
                })
                .ThenBy(x => x.Data.Id);
            }

            VerifyGridData(result, expectedResultsSort.ToArray(), select, biData, comparisonBiData);

            // Filter
            result = CallGetCampaignsGridData(
                this.cInfo,
                today,
                yesterday,
                select: this.GetSelect(select),
                gridDataFilter:
                new dynamic[]
                {
                    new
                    {
                        PropertyToFilter = "PerformanceMetricsAbsoluteChange/Clicks",
                        FilterOperation = FilterOperation.Greater,
                        FilterOnValues = new object[] { 50 },
                        CustomColumnId = (string) null,
                    }
                });

            IEnumerable<TestCampaign> expectedResultsFilter = Enumerable.Empty<TestCampaign>();
            if (mockReporting)
            {
                expectedResultsFilter = campaignCollection.Campaigns.Where(x =>
                {
                    long id = x.Data.Id;
                    return Math.Abs(biData[id].Clicks.Value - comparisonBiData[id].Clicks.Value) > 50;
                })
                .OrderBy(x => x.Data.Id);
            }

            VerifyGridData(result, expectedResultsFilter.ToArray(), select, biData, comparisonBiData);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_Segmentation(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var select = new[] { PerformanceMetrics, PhonePerformanceMetrics, "PerformanceMetrics/TopImpressionRate", "PerformanceMetrics/AbsoluteTopImpressionRate" };
            var segmentations = new List<string>() { SegmentationType.DayOfTheWeek.ToString() };

            var today = DateTime.Today.Date;
            var yesterday = DateTime.Today.AddDays(-1).Date;

            Dictionary<long, Dictionary<string, BiData>> segBiDataByAdGroupId = null;
            var todayBiData = PopulateRandomBiData(today);
            var yesterdayBiData = PopulateRandomBiData(yesterday, false);

            if (todayBiData != null)
            {
                segBiDataByAdGroupId = new Dictionary<long, Dictionary<string, BiData>>();
                todayBiData.ForEach(kvp => segBiDataByAdGroupId.Add(kvp.Key, new Dictionary<string, BiData>()));
                todayBiData.ForEach(kvp => segBiDataByAdGroupId[kvp.Key].Add(today.DayOfWeek.ToString(), kvp.Value));
                yesterdayBiData.ForEach(kvp => segBiDataByAdGroupId[kvp.Key].Add(yesterday.DayOfWeek.ToString(), kvp.Value));
            }

            var result = CallGetCampaignsGridData(this.cInfo, thisWeek, null, segmentations, select: this.GetSelect(select));

            var expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, segmentedBiDataByCampaignId: segBiDataByAdGroupId);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.AIM)]
        [Ignore("GA Predictive Targeting V2")]
        public void GetCampaignsGridData_SegmentationByAudienceName_PredictiveTargeting_NonPilotingAccount()
        {
            var select = new[] { PerformanceMetrics };
            var segmentations = new List<string>() { SegmentationType.AudienceName.ToString() };

            var perfDataDict = PopulateRandomBiData(DateTime.Today.Date, enablePredictiveTargetingForOne: true);

            var result = CallGetCampaignsGridData(this.cInfo, thisWeek, null, segmentations, select: this.GetSelect(select));

            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                // if this account has PredictiveTargeting biData but not in pilot
                // we will covert this BiData into Default (User not in Audience Segemnt)
                var expectedBIData = ((JArray)result.value).Where(a => ((JArray)a[PerformanceMetrics]["SegmentedData"]).IsNullOrEmpty());
                Assert.AreEqual(expectedBIData.Count(), 1);
            }

            // add mutiple bidata for one segment
            perfDataDict = PopulateRandomBiData(DateTime.Today.Date, clear: false, enablePredictiveTargetingForOne: false);

            result = CallGetCampaignsGridData(this.cInfo, thisWeek, null, segmentations, select: this.GetSelect(select));

            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                // if SegmentedData List has two biData, the first one (Predictive Targeting) will be set to dafault.
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((JArray)a[PerformanceMetrics]["SegmentedData"][0]["Key"]).Count == 1));
                Assert.AreEqual("Hobbies and Leisure/Pets and Animals", result.value[0][PerformanceMetrics]["SegmentedData"][0]["Key"][0]["StringValue"].ToString());
                Assert.AreEqual("Users not in audience segments", result.value[0][PerformanceMetrics]["SegmentedData"][1]["Key"][0]["StringValue"].ToString());
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.AIM)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_SegmentationByAudienceName_PredictiveTargeting_PilotingAccount(int[] pilotFeatures)
        {
            var select = new[] { PerformanceMetrics };
            var segmentations = new List<string>() { SegmentationType.AudienceName.ToString() };

            // for piloting account
            CustomerInfo cInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, true, pilotFeatures);

            TestCampaignCollection testCampaignCollection = new TestCampaignCollection(3);

            var response = testCampaignCollection.Add(cInfo, cInfo.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(response);

            var perfDataDict = PopulateRandomBiData(DateTime.Today.Date, customerInfo: cInfo, campaignCollection: testCampaignCollection, enablePredictiveTargetingForOne: true);

            var result = CallGetCampaignsGridData(cInfo, thisWeek, null, segmentations, select: this.GetSelect(select));

            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((JArray)a[PerformanceMetrics]["SegmentedData"][0]["Key"]).Count == 1));
                Assert.AreEqual(1, ((JArray)result.value).Count(a => ((string)a[PerformanceMetrics]["SegmentedData"][0]["Key"][0]["StringValue"]).Equals("Predictive targeting")));
                VerifySegmentedBiData_PredictiveTargeting(perfDataDict.First().Value, result.value[0][PerformanceMetrics]["SegmentedData"][0]["Data"]);
            }

            // add mutiple bidata for one segment
            var perfDataDict2 = PopulateRandomBiData(DateTime.Today.Date, clear: false, customerInfo: cInfo, campaignCollection: testCampaignCollection, enablePredictiveTargetingForOne: true);

            // adgroup level
            result = CallGetCampaignsGridData(cInfo, thisWeek, null, segmentations, select: this.GetSelect(select));

            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((JArray)a[PerformanceMetrics]["SegmentedData"][0]["Key"]).Count == 1));
                Assert.AreEqual(1, ((JArray)result.value).Count(a => ((string)a[PerformanceMetrics]["SegmentedData"][0]["Key"][0]["StringValue"]).Equals("Predictive targeting")));
                BiData addedBiData = perfDataDict2.First().Value + perfDataDict.First().Value;
                addedBiData.CalculateDependeningFields();
                VerifySegmentedBiData_PredictiveTargeting(addedBiData, result.value[0][PerformanceMetrics]["SegmentedData"][0]["Data"]);
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.AIM)]
        [Ignore("GA Predictive Targeting V2")]
        public void GetCampaignsGridData_SegmentationByAudienceCategory_PredictiveTargeting_NonPilotingAccount()
        {
            var select = new[] { PerformanceMetrics };
            var segmentations = new List<string>() { SegmentationType.AudienceType.ToString() };

            // non-piloting account (only with V1 pilot)
            var perfDataDict = PopulateRandomBiData(DateTime.Today.Date, enablePredictiveTargetingForOne: true);

            var result = CallGetCampaignsGridData(this.cInfo, thisWeek, null, segmentations, select: this.GetSelect(select));

            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                // if this account has PredictiveTargeting biData but not in pilot
                // we will covert this BiData into Default (User not in Audience Segemnt)
                var expectedBIData = ((JArray)result.value).Where(a => ((JArray)a[PerformanceMetrics]["SegmentedData"]).IsNullOrEmpty());
                Assert.AreEqual(expectedBIData.Count(), 1);
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.AIM)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_SegmentationByAudienceCategory_PredictiveTargeting_PilotingAccount(int[] pilotFeatures)
        {
            var select = new[] { PerformanceMetrics };
            var segmentations = new List<string>() { SegmentationType.AudienceType.ToString() };

            // for piloting account
            CustomerInfo cInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, true, pilotFeatures);

            TestCampaignCollection testCampaignCollection = new TestCampaignCollection(3);

            var response = testCampaignCollection.Add(cInfo, cInfo.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(response);

            var perfDataDict = PopulateRandomBiData(DateTime.Today.Date, customerInfo: cInfo, campaignCollection: testCampaignCollection, enablePredictiveTargetingForOne: true);

            var result = CallGetCampaignsGridData(cInfo, thisWeek, null, segmentations, select: this.GetSelect(select));

            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((JArray)a[PerformanceMetrics]["SegmentedData"][0]["Key"]).Count == 1));
                Assert.AreEqual(1, ((JArray)result.value).Count(a => ((string)a[PerformanceMetrics]["SegmentedData"][0]["Key"][0]["StringValue"]).Equals("PredictiveTargeting")));
                VerifySegmentedBiData_PredictiveTargeting(perfDataDict.First().Value, result.value[0][PerformanceMetrics]["SegmentedData"][0]["Data"]);
            }

            // add mutiple bidata for one segment
            var perfDataDict2 = PopulateRandomBiData(DateTime.Today.Date, clear: false, customerInfo: cInfo, campaignCollection: testCampaignCollection, enablePredictiveTargetingForOne: true);

            // adgroup level
            result = CallGetCampaignsGridData(cInfo, thisWeek, null, segmentations, select: this.GetSelect(select));

            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                Assert.AreEqual("AudienceType", result.value[0][PerformanceMetrics]["SegmentedData"][0]["Key"][0]["SegmentationType"].ToString());
                Assert.AreEqual("PredictiveTargeting", result.value[0][PerformanceMetrics]["SegmentedData"][0]["Key"][0]["StringValue"].ToString());
                BiData addedBiData = perfDataDict2.First().Value + perfDataDict.First().Value;
                addedBiData.CalculateDependeningFields();
                VerifySegmentedBiData_PredictiveTargeting(addedBiData, result.value[0][PerformanceMetrics]["SegmentedData"][0]["Data"]);
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_AdScenarioTypeSegmentation()
        {
            Dictionary<long, BiData> biDataById = null;
            Dictionary<long, Dictionary<string, BiData>> segBiDataById = null;
            Dictionary<string, BiData> totalBiData = null;
            Dictionary<string, Dictionary<string, BiData>> totalSegBiData = null;

            var today = DateTime.Today.Date;

            var expectedCampaigns = campaignCollection.Campaigns.OrderBy(x => x.Data.Id).ToArray();

            if (mockReporting)
            {
                AdScenarioTypeSegHelper.CreateBiDataWithAdScenarioTypeSeg(
                    out biDataById,
                    out segBiDataById,
                    out totalBiData,
                    out totalSegBiData,
                    cInfo,
                    (entityIndex, biData) =>
                    {
                        var bi = new BiData(biData);
                        bi.CampaignId = expectedCampaigns[entityIndex].Data.Id;
                        bi.Id = bi.CampaignId;
                        bi.Date = today;
                        return bi;
                    },
                    BiDatabaseHelper.SetCampaignMockBiData);

                foreach (var campaign in expectedCampaigns)
                {
                    if (!biDataById.ContainsKey(campaign.Data.Id))
                    {
                        biDataById[campaign.Data.Id] = new BiData() { Clicks = 0, Impressions = 0, ClickThruRate = 0 };
                        segBiDataById[campaign.Data.Id] = new Dictionary<string, BiData>();
                    }
                }
            }

            var select = new[] { PerformanceMetrics };
            var segmentations = new List<string>() { SegmentationType.AdScenario.ToString() };

            var result = CallGetCampaignsGridData(this.cInfo, thisWeek, null, segmentations, select: this.GetSelect(select));

            VerifyGridData(result, expectedCampaigns, select, biDataByCampaignId: biDataById, segmentedBiDataByCampaignId: segBiDataById);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_SegmentationByAudienceName(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var select = new[] { PerformanceMetrics };
            var segmentations = new List<string>() { SegmentationType.AudienceName.ToString() };

            PopulateRandomBiData(DateTime.Today.Date);
            var result = CallGetCampaignsGridData(this.cInfo, thisWeek, null, segmentations, select: this.GetSelect(select), lcidString: "1036");

            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((JArray)a[PerformanceMetrics]["SegmentedData"][0]["Key"]).Count == 1));
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((string)a[PerformanceMetrics]["SegmentedData"][0]["Key"][0]["StringValue"]).StartsWith("fr-FR_")));
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_Pmax_Segmentation(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var select = new[] { PerformanceMetrics };
            var segmentations = new List<string>() { SegmentationType.TopVsOther.ToString(), SegmentationType.Network.ToString() };
            var testCampaignCollection = new TestCampaignCollection(0);
            testCampaignCollection.Campaigns.Add(this.campaignCollection.Campaigns[0]);
            PopulateRandomBiData(DateTime.Today.Date, advertisingChannelTypeId: 9, campaignCollection: testCampaignCollection);

            foreach (var segmentation in segmentations)
            {
                var result = CallGetCampaignsGridData(this.cInfo, thisWeek, null, new List<string>() { segmentation }, select: this.GetSelect(select), lcidString: "1033");

                if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
                {
                    Assert.AreEqual((string)result["@ns.pmax.non.deleted.totals"].PerformanceMetrics.SegmentedData[0].Key[0].StringValue, "CrossNetwork", $"Segment StringValue for segmentation type {segmentation}");
                    Assert.AreEqual((double?)result["@ns.pmax.non.deleted.totals"].PerformanceMetrics.CTR, (double?)result["@ns.unfiltered.totals"].PerformanceMetrics.CTR, $"CTR doesn't match for segmentation type {segmentation}");
                }
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.LinkedInCampaigns)]
        [DataRow(new int[] { })]
        public void GetCampaignsGridData_LinkedIn_Segmentation(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var select = new[] { PerformanceMetrics };
            var segmentations = new List<string>() { SegmentationType.Network.ToString() };
            var testCampaignCollection = new TestCampaignCollection(0);
            testCampaignCollection.Campaigns.Add(this.campaignCollection.Campaigns[0]);
            PopulateRandomBiData(DateTime.Today.Date, mediumId: 12, campaignCollection: testCampaignCollection);

            foreach (var segmentation in segmentations)
            {
                var result = CallGetCampaignsGridData(this.cInfo, thisWeek, null, new List<string>() { segmentation }, select: this.GetSelect(select), lcidString: "1033");

                if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
                {
                    Assert.AreEqual((double?)result["@ns.linkedin.non.deleted.totals"].PerformanceMetrics.CTR, (double?)result["@ns.unfiltered.totals"].PerformanceMetrics.CTR, $"CTR doesn't match for segmentation type {segmentation}");
                }
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_Pmax_NoGoal_Success(int[] pilotFeatures)
        {
            List<int> featureList = new List<int>() { Features.PerformanceMaxCampaigns, Features.InStoreTransaction, AccountPilotFeatures.PMaxLite };
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                featureList.AddRange(pilotFeatures);
            }
            // create a new customer
            var customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, featureList.ToArray());
            var select = AllSelectNonBiProperties.Concat(new string[]
                    {
                        "CampaignSettings",
                        "PerformanceMetrics/Clicks",
                        "PerformanceMetrics/Impressions"
                    }).ToArray();

            var testCampaignCollection = new TestCampaignCollection(1, CampaignFactory.CampaignType.PerformanceMax, CampaignFactory.SimplifiedBudgetType.DailyBudgetStandard);
            testCampaignCollection.Campaigns[0].Data.Languages = new Language[] { Language.English };
            testCampaignCollection.Campaigns.First().Data.TrackingTemplate = "http://www.trackingTemplate.com/url={lpurl}";

            var response = testCampaignCollection.Add(customerInfo, customerInfo.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(response);

            PopulateRandomBiData(DateTime.Today.Date, advertisingChannelTypeId: 9, campaignCollection: testCampaignCollection, customerInfo: customerInfo);
            var result = CallGetCampaignsGridData(customerInfo, select: this.GetSelect(select));
            Console.WriteLine($"Campaign Settings: {JsonConvert.SerializeObject(result.value[0]["CampaignSettings"])}");
            Assert.IsNull((string)result.value[0]["CampaignSettings"][0]["OriginalCampaignType"]);
            Assert.AreEqual(result.value[0]["BiddingScheme"].Type.ToString(), "MaxConversions");

        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_SegmentationByGoalType(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var select = new[] { PerformanceMetrics };
            var segmentations = new List<string>() { SegmentationType.GoalType.ToString() };

            SegmentationGoalType goalType = SegmentationGoalType.Smart;
            PopulateRandomBiData(DateTime.Today.Date, GoalType: goalType);
            var result = CallGetCampaignsGridData(this.cInfo, thisWeek, null, segmentations, select: this.GetSelect(select), lcidString: "1036");

            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((JArray)a[PerformanceMetrics]["SegmentedData"][0]["Key"]).Count == 1));
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((string)a[PerformanceMetrics]["SegmentedData"][0]["Key"][0]["SegmentationType"]).Equals("GoalType")));
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((string)a[PerformanceMetrics]["SegmentedData"][0]["Key"][0]["StringValue"]).Equals(goalType.ToString())));
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_SegmentationByGoalType_PartialConversion(int[] pilotFeatures)
        {
            List<int> featureList = new List<int>() {};
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                featureList.AddRange(pilotFeatures);
            }
            var PartialConversionPilotCustomer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, featureList.ToArray());

            string[] select = AllSelectNonBiProperties.Concat(new string[]
            {
                "PerformanceMetrics/Clicks",
                "PerformanceMetrics/ConversionsCredit",
                "PerformanceMetrics/PartialConversionCPA",
                "PerformanceMetrics/PartialConversionRate",
                "PerformanceMetrics/AllConversionsCredit",
                "PerformanceMetrics/AllPartialConversionCPA",
                "PerformanceMetrics/AllPartialConversionRate",
                "PerformanceMetrics/ViewThroughConversionsCredit",
                "PerformanceMetrics/ViewThroughPartialConversionsCPA",
                "PerformanceMetrics/ViewThroughPartialConversionsRate",
            }).ToArray();
            var segmentations = new List<string>() { SegmentationType.GoalType.ToString() };
            CreateCampaignsForCustomer(PartialConversionPilotCustomer, 3);

            SegmentationGoalType goalType = SegmentationGoalType.Smart;
            Dictionary<long, BiData> SmartGoalBiDataList = PopulateRandomBiData(DateTime.Today.Date, customerInfo: PartialConversionPilotCustomer, GoalType: goalType, useConversionCredit: true, viewThroughEnabled: true);
            goalType = SegmentationGoalType.DestinationURL;
            Dictionary<long, BiData> DestinationBiDataList = PopulateRandomBiData(DateTime.Today.Date, customerInfo: PartialConversionPilotCustomer, GoalType: goalType, clear: false, useConversionCredit: true, viewThroughEnabled: true);
            var result = CallGetCampaignsGridData(PartialConversionPilotCustomer, thisWeek, null, segmentations, select: this.GetSelect(select), lcidString: "1036");

            if (mockReporting)
            {
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((JArray)a[PerformanceMetrics]["SegmentedData"][0]["Key"]).Count == 1));
                Assert.AreEqual(result.value.Count, ((JArray)result.value).Count(a => ((string)a[PerformanceMetrics]["SegmentedData"][0]["Key"][0]["SegmentationType"]).Equals("GoalType")));

                VerifySegmentData_PartialConversion(result, SmartGoalBiDataList, DestinationBiDataList);
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_InstoreVisit_ClicksHitThreshold()
        {
            var select = new[] { PerformanceMetrics };
            var segmentations = new List<string>() { SegmentationType.GoalType.ToString() };
            SegmentationGoalType goalType = SegmentationGoalType.StoreVisits;

            // mock today data
            IList<TestCampaign> myCampaigns = new List<TestCampaign>() { this.campaignCollection.Campaigns[0] };
            var biData = myCampaigns.Select(a =>
            {
                var data = new BiData();
                data.AccountId = this.cInfo.AccountIds[0];
                data.CampaignId = a.Data.Id;
                data.Date = DateTime.Today.Date;
                data.GoalTypeId = (byte)goalType;
                data.Clicks = 200;
                data.Conversions = 10;
                data.AllConversions = 15;
                return data;
            });
            var dict = biData.ToDictionary(b => b.CampaignId.Value, b => b);
            BiDatabaseHelper.SetCampaignMockBiData(this.cInfo, dict.Values);

            // check today data
            var result = CallGetCampaignsGridData(this.cInfo, today, null, segmentations, select: this.GetSelect(select));
            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                //Check the single account. Take the first one.
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Clicks, 200);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Conversions, 10);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].AllConversions, 15);

                //segmentation
                Assert.AreEqual((string)result.value[0][PerformanceMetrics]["SegmentedData"][0]["Key"][0]["StringValue"], "StoreVisits");
                Assert.AreEqual((int)result.value[0][PerformanceMetrics]["SegmentedData"][0]["Data"].Conversions, 10);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics]["SegmentedData"][0]["Data"].AllConversions, 15);
            }

            // check this week data
            result = CallGetCampaignsGridData(this.cInfo, thisWeek, null, segmentations, select: this.GetSelect(select));
            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                //Check the single account. Take the first one.
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Clicks, 200);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Conversions, 10);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].AllConversions, 15);

                //segmentation
                Assert.AreEqual((string)result.value[0][PerformanceMetrics]["SegmentedData"][0]["Key"][0]["StringValue"], "StoreVisits");
                Assert.AreEqual((int)result.value[0][PerformanceMetrics]["SegmentedData"][0]["Data"].Conversions, 10);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics]["SegmentedData"][0]["Data"].AllConversions, 15);
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_InstoreVisit_ClicksHitThreshold_UseConversionCredits()
        {
            var select = new[] { PerformanceMetrics };
            var segmentations = new List<string>() { SegmentationType.GoalType.ToString() };
            SegmentationGoalType goalType = SegmentationGoalType.StoreVisits;

            // mock today data
            IList<TestCampaign> myCampaigns = new List<TestCampaign>() { this.campaignCollection.Campaigns[0] };
            Random rand = new Random();
            float conversionCreditsExpected = (float)(rand.Next(50) + rand.NextDouble());
            float allConversionCreditsExpected = conversionCreditsExpected + (float)(rand.Next(50) + rand.NextDouble());
            var biData = myCampaigns.Select(a =>
            {
                var data = new BiData();
                data.AccountId = this.cInfo.AccountIds[0];
                data.CampaignId = a.Data.Id;
                data.Date = DateTime.Today.Date;
                data.GoalTypeId = (byte)goalType;
                data.Clicks = 200;
                data.Conversions = (long)conversionCreditsExpected;
                data.AllConversions = (long)allConversionCreditsExpected;
                data.ConversionsCredit = conversionCreditsExpected;
                data.AllConversionsCredit = allConversionCreditsExpected;
                data.TotalConversionsCredit = allConversionCreditsExpected;
                data.FullClickConversionsCredit = allConversionCreditsExpected;
                return data;
            });
            var dict = biData.ToDictionary(b => b.CampaignId.Value, b => b);
            BiDatabaseHelper.SetCampaignMockBiData(this.cInfo, dict.Values, useConversionCredit: true);

            // check today data
            var result = CallGetCampaignsGridData(this.cInfo, today, null, segmentations, select: this.GetSelect(select));
            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                //Check the single account. Take the first one.
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Clicks, 200);
                Assert.AreEqual(result.value[0][PerformanceMetrics].Conversions.ToString(), BiDatabaseHelper.RoundingFloatVal(conversionCreditsExpected));
                Assert.AreEqual(result.value[0][PerformanceMetrics].AllConversions.ToString(), BiDatabaseHelper.RoundingFloatVal(allConversionCreditsExpected));

                //segmentation
                Assert.AreEqual((string)result.value[0][PerformanceMetrics]["SegmentedData"][0]["Key"][0]["StringValue"], "StoreVisits");
                Assert.AreEqual(result.value[0][PerformanceMetrics]["SegmentedData"][0]["Data"].Conversions.ToString(), BiDatabaseHelper.RoundingFloatVal(conversionCreditsExpected));
                Assert.AreEqual(result.value[0][PerformanceMetrics]["SegmentedData"][0]["Data"].AllConversions.ToString(), BiDatabaseHelper.RoundingFloatVal(allConversionCreditsExpected));
            }

            // check this week data
            result = CallGetCampaignsGridData(this.cInfo, thisWeek, null, segmentations, select: this.GetSelect(select));
            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                //Check the single account. Take the first one.
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Clicks, 200);
                Assert.AreEqual((string)result.value[0][PerformanceMetrics].Conversions, BiDatabaseHelper.RoundingFloatVal(conversionCreditsExpected));
                Assert.AreEqual((string)result.value[0][PerformanceMetrics].AllConversions, BiDatabaseHelper.RoundingFloatVal(allConversionCreditsExpected));

                //segmentation
                Assert.AreEqual((string)result.value[0][PerformanceMetrics]["SegmentedData"][0]["Key"][0]["StringValue"], "StoreVisits");
                Assert.AreEqual((string)result.value[0][PerformanceMetrics]["SegmentedData"][0]["Data"].Conversions, BiDatabaseHelper.RoundingFloatVal(conversionCreditsExpected));
                Assert.AreEqual((string)result.value[0][PerformanceMetrics]["SegmentedData"][0]["Data"].AllConversions, BiDatabaseHelper.RoundingFloatVal(allConversionCreditsExpected));
            }
        }


        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_InstoreVisit_ClicksNotHitThreshold()
        {
            var select = new[] { PerformanceMetrics };
            var segmentations = new List<string>() { SegmentationType.GoalType.ToString() };
            SegmentationGoalType goalType = SegmentationGoalType.StoreVisits;

            // mock today data
            IList<TestCampaign> myCampaigns = new List<TestCampaign>() { this.campaignCollection.Campaigns[0] };
            var biData = myCampaigns.Select(a =>
            {
                var data = new BiData();
                data.AccountId = this.cInfo.AccountIds[0];
                data.CampaignId = a.Data.Id;
                data.Date = DateTime.Today.Date;
                data.GoalTypeId = (byte)goalType;
                data.Clicks = 19;
                data.Conversions = 10;
                data.AllConversions = 15;
                return data;
            });
            var dict = biData.ToDictionary(b => b.CampaignId.Value, b => b);
            BiDatabaseHelper.SetCampaignMockBiData(this.cInfo, dict.Values);

            // check today data
            var result = CallGetCampaignsGridData(this.cInfo, today, null, segmentations, select: this.GetSelect(select));
            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                //Check the single account. Take the first one.
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Clicks, 19);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Conversions, 0);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].AllConversions, 0);

                //segmentation
                Assert.AreEqual((int)result.value[0][PerformanceMetrics]["SegmentedData"].Count, 0);
            }

            // check this week data
            result = CallGetCampaignsGridData(this.cInfo, thisWeek, null, segmentations, select: this.GetSelect(select));
            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                //Check the single account. Take the first one.
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Clicks, 19);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Conversions, 0);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].AllConversions, 0);

                //segmentation
                Assert.AreEqual((int)result.value[0][PerformanceMetrics]["SegmentedData"].Count, 0);
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_InstoreVisit_ClicksSummaryHitThreshold()
        {
            var select = new[] { PerformanceMetrics };
            var segmentations = new List<string>() { SegmentationType.GoalType.ToString() };
            SegmentationGoalType goalType = SegmentationGoalType.StoreVisits;

            // mock yesterday data
            IList<TestCampaign> myCampaigns = new List<TestCampaign>() { this.campaignCollection.Campaigns[0] };
            var biData = myCampaigns.Select(a =>
            {
                var data = new BiData();
                data.AccountId = this.cInfo.AccountIds[0];
                data.CampaignId = a.Data.Id;
                data.Date = DateTime.Today.AddDays(-1).Date;
                data.GoalTypeId = (byte)goalType;
                data.Clicks = 19;
                data.Conversions = 10;
                data.AllConversions = 15;
                return data;
            });
            var dict = biData.ToDictionary(b => b.CampaignId.Value, b => b);
            BiDatabaseHelper.SetCampaignMockBiData(this.cInfo, dict.Values);

            // mock today data
            biData = myCampaigns.Select(a =>
            {
                var data = new BiData();
                data.AccountId = this.cInfo.AccountIds[0];
                data.CampaignId = a.Data.Id;
                data.Date = DateTime.Today.Date;
                data.GoalTypeId = (byte)goalType;
                data.Clicks = 199;
                data.Conversions = 10;
                data.AllConversions = 15;
                return data;
            });
            dict = biData.ToDictionary(b => b.CampaignId.Value, b => b);
            BiDatabaseHelper.SetCampaignMockBiData(this.cInfo, dict.Values, false);

            // check yesterday data
            var result = CallGetCampaignsGridData(this.cInfo, yesterday, null, segmentations, select: this.GetSelect(select));
            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                //Check the single account. Take the first one.
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Clicks, 19);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Conversions, 0);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].AllConversions, 0);

                //segmentation
                Assert.AreEqual((int)result.value[0][PerformanceMetrics]["SegmentedData"].Count, 0);
            }

            // check today data
            result = CallGetCampaignsGridData(this.cInfo, today, null, segmentations, select: this.GetSelect(select));
            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                //Check the single account. Take the first one.
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Clicks, 199);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Conversions, 0);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].AllConversions, 0);

                //segmentation
                Assert.AreEqual((int)result.value[0][PerformanceMetrics]["SegmentedData"].Count, 0);
            }

            // check this week data
            result = CallGetCampaignsGridData(this.cInfo, thisWeek, null, segmentations, select: this.GetSelect(select));
            if (!ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                //Check the single account. Take the first one.
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Clicks, 218);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].Conversions, 20);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics].AllConversions, 30);

                //segmentation
                Assert.AreEqual((string)result.value[0][PerformanceMetrics]["SegmentedData"][0]["Key"][0]["StringValue"], "StoreVisits");
                Assert.AreEqual((int)result.value[0][PerformanceMetrics]["SegmentedData"][0]["Data"].Conversions, 20);
                Assert.AreEqual((int)result.value[0][PerformanceMetrics]["SegmentedData"][0]["Data"].AllConversions, 30);
            }
        }


        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_Sorting()
        {
            var select = new[] { "Name", "Id", "PerformanceMetrics/Impressions", "PerformanceMetrics/AllConversionAdvertiserReportedRevenue" };
            var biData = PopulateRandomBiData(DateTime.Today);

            string orderBy = "Name desc";
            var result = CallGetCampaignsGridData(this.cInfo, orderBy: orderBy, select: this.GetSelect(select));

            var expectedResults = campaignCollection.Campaigns.OrderByDescending(x => x.Data.Name);
            VerifyGridData(result, expectedResults.ToArray(), select, biData);

            // Perf sort
            orderBy = "PerformanceMetrics/Impressions asc";
            result = CallGetCampaignsGridData(this.cInfo, orderBy: orderBy, select: this.GetSelect(select));

            expectedResults = biData != null
                ? campaignCollection.Campaigns.OrderBy(x => biData[x.Data.Id].Impressions).ThenBy(x => x.Data.Id)
                : campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, biData);

            select = AllSelectProperties;

            // Perf sort
            orderBy = "PerformanceMetrics/AllConversionAdvertiserReportedRevenue asc";
            result = CallGetCampaignsGridData(this.cInfo, orderBy: orderBy, select: this.GetSelect(select));

            expectedResults = biData != null
                ? campaignCollection.Campaigns.OrderBy(x => biData[x.Data.Id].AllConversionAdvertiserReportedRevenue.GetValueOrDefault())
                    .ThenBy(x => x.Data.Id)
                : campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, biData);

            // Optimization score
            orderBy = "OptimizationScore asc";
            result = CallGetCampaignsGridData(this.cInfo, orderBy: orderBy, select: this.GetSelect(select));

            expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), select, biData);
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_Sort_DisplayBiddingStrategyManualCPI()
        {
            CustomerInfo appCampaignCustomer = CustomerInfo.CreateStandardAdvertiserWithPilot(Features.IsAccountEnableForAppCampaign);
            TestCampaignCollection.Clean(appCampaignCustomer);

            ReportDownloadE2ETests.CreateConversionGoal(appCampaignCustomer);
            long providerId = 0;
            long linkedStoreId = 101; //Static ID for Microsoft Store in CI
            if (ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI"))
            {
                retailerStore = new CustomerStore(appCampaignCustomer, 5104030, true);
                customerLinkedStores = new CustomerLinkedStores(appCampaignCustomer, retailerStore);
                linkedStoreId = customerLinkedStores.CoOpStoreIds[0];
            }

            var testcampaignCollection = new TestCampaignCollection(3);
            testcampaignCollection.Campaigns[0].Data.CampaignType = CampaignType.App;
            testcampaignCollection.Campaigns[1].Data.CampaignType = CampaignType.App;
            testcampaignCollection.Campaigns[2].Data.CampaignType = CampaignType.App;

            testcampaignCollection.Campaigns[0].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testcampaignCollection.Campaigns[0].Data.DailyTargetBudgetAmount = 10;
            testcampaignCollection.Campaigns[0].Data.Status = CampaignStatus.Active;
            testcampaignCollection.Campaigns[0].Data.TrackingTemplate = "http://www.trackingtool.com/tracking?url=" + "{" + "lpurl" + "}" + "&type=1";
            testcampaignCollection.Campaigns[0].Data.Name = "DisplayBiddingStrategyName1";
            testcampaignCollection.Campaigns[0].Data.BiddingScheme = new ManualCpaBiddingScheme()
            {
                ManualCPI = 20.0
            };
            testcampaignCollection.Campaigns[0].Data.CampaignSettings = new CampaignSettings[] { new ShoppingSettings { Priority = CampaignPriority.High, SalesCountry = "US", ProviderId = linkedStoreId } };

            testcampaignCollection.Campaigns[1].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testcampaignCollection.Campaigns[1].Data.DailyTargetBudgetAmount = 10;
            testcampaignCollection.Campaigns[1].Data.Status = CampaignStatus.Active;
            testcampaignCollection.Campaigns[1].Data.TrackingTemplate = "http://www.trackingtool.com/tracking?url=" + "{" + "lpurl" + "}" + "&type=1";
            testcampaignCollection.Campaigns[1].Data.Name = "DisplayBiddingStrategyName2";
            testcampaignCollection.Campaigns[1].Data.BiddingScheme = new ManualCpaBiddingScheme()
            {
                ManualCPI = 40.0
            };
            testcampaignCollection.Campaigns[1].Data.CampaignSettings = new CampaignSettings[] { new ShoppingSettings { Priority = CampaignPriority.High, SalesCountry = "US", ProviderId = linkedStoreId } };

            testcampaignCollection.Campaigns[2].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testcampaignCollection.Campaigns[2].Data.DailyTargetBudgetAmount = 10;
            testcampaignCollection.Campaigns[2].Data.Status = CampaignStatus.Active;
            testcampaignCollection.Campaigns[2].Data.TrackingTemplate = "http://www.trackingtool.com/tracking?url=" + "{" + "lpurl" + "}" + "&type=1";
            testcampaignCollection.Campaigns[2].Data.Name = "DisplayBiddingStrategyName13";
            testcampaignCollection.Campaigns[2].Data.BiddingScheme = new ManualCpaBiddingScheme()
            {
                ManualCPI = 30.0
            };
            testcampaignCollection.Campaigns[2].Data.CampaignSettings = new CampaignSettings[] { new ShoppingSettings { Priority = CampaignPriority.High, SalesCountry = "US", ProviderId = linkedStoreId } };


            ResponseValidator.ValidateBasicSuccess(testcampaignCollection.Add(appCampaignCustomer));

            var select = AllSelectNonBiProperties.Concat(new string[] { "DisplayBiddingStrategyManualCpi" }).ToArray();
            string orderBy = $"DisplayBiddingStrategyManualCpi asc";
            var result = CallGetCampaignsGridData(appCampaignCustomer, orderBy: orderBy, select: this.GetSelect(select));

            var expectedResults = new double[] { 20.0, 30.0, 40.0 };

            Assert.IsNotNull(result?.value, "Value is null.");
            Assert.AreEqual(expectedResults.Count(), result.value.Count, "Row count mismatch");
            double oldTargetCpa = -1.0d;
            for (int i = 0; i < result.value.Count; i++)
            {
                var c = result.value[i];
                var curManualCpiVal = c.BiddingScheme.ManualCPI.Value;
                if (curManualCpiVal != null)
                {
                    Assert.IsTrue(oldTargetCpa < curManualCpiVal);
                    oldTargetCpa = curManualCpiVal;
                }
            }

        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_Sort_Filter_DisplayBiddingStrategyManualCPC()
        {
            //Enable legacyWindowsAppCampaigns feature and UniversalAppCampaigns feature
            CustomerInfo appCampaignCustomer = CustomerInfo.CreateStandardAdvertiserWithPilot(Features.UnifiedAppCampaign, Features.IsAccountEnableForAppCampaign);

            TestCampaignCollection.Clean(appCampaignCustomer);
            ReportDownloadE2ETests.CreateConversionGoal(appCampaignCustomer);

            // data mock
            var testcampaignCollection = new TestCampaignCollection(3);
            testcampaignCollection.Campaigns[0].Data.CampaignType = CampaignType.App;
            testcampaignCollection.Campaigns[1].Data.CampaignType = CampaignType.App;
            testcampaignCollection.Campaigns[2].Data.CampaignType = CampaignType.App;
            testcampaignCollection.Campaigns[0].Data.CampaignSubType = CampaignSubType.AppInstall;
            testcampaignCollection.Campaigns[1].Data.CampaignSubType = CampaignSubType.AppInstall;
            testcampaignCollection.Campaigns[2].Data.CampaignSubType = CampaignSubType.AppInstall;

            testcampaignCollection.Campaigns[0].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testcampaignCollection.Campaigns[0].Data.DailyTargetBudgetAmount = 10;
            testcampaignCollection.Campaigns[0].Data.Status = CampaignStatus.Active;
            testcampaignCollection.Campaigns[0].Data.TrackingTemplate = "http://www.trackingtool.com/tracking?url=" + "{" + "lpurl" + "}" + "&type=1";
            testcampaignCollection.Campaigns[0].Data.Name = "DisplayBiddingStrategyName1";
            testcampaignCollection.Campaigns[0].Data.BiddingScheme = new ManualBiddingScheme()
            {
                ManualCPC = 20.0
            };
            testcampaignCollection.Campaigns[0].Data.CampaignSettings = new CampaignSettings[] { new AppSetting { AppStore = AppStore.AppleAppStore, AppId = AppleStoreAppId } };

            testcampaignCollection.Campaigns[1].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testcampaignCollection.Campaigns[1].Data.DailyTargetBudgetAmount = 10;
            testcampaignCollection.Campaigns[1].Data.Status = CampaignStatus.Active;
            testcampaignCollection.Campaigns[1].Data.TrackingTemplate = "http://www.trackingtool.com/tracking?url=" + "{" + "lpurl" + "}" + "&type=1";
            testcampaignCollection.Campaigns[1].Data.Name = "DisplayBiddingStrategyName2";
            testcampaignCollection.Campaigns[1].Data.BiddingScheme = new ManualBiddingScheme()
            {
                ManualCPC = 40.0
            };
            testcampaignCollection.Campaigns[1].Data.CampaignSettings = new CampaignSettings[] { new AppSetting { AppStore = AppStore.AppleAppStore, AppId = AppleStoreAppId } };

            testcampaignCollection.Campaigns[2].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            testcampaignCollection.Campaigns[2].Data.DailyTargetBudgetAmount = 10;
            testcampaignCollection.Campaigns[2].Data.Status = CampaignStatus.Active;
            testcampaignCollection.Campaigns[2].Data.TrackingTemplate = "http://www.trackingtool.com/tracking?url=" + "{" + "lpurl" + "}" + "&type=1";
            testcampaignCollection.Campaigns[2].Data.Name = "DisplayBiddingStrategyName13";
            testcampaignCollection.Campaigns[2].Data.BiddingScheme = new ManualBiddingScheme()
            {
                ManualCPC = 30.0
            };
            testcampaignCollection.Campaigns[2].Data.CampaignSettings = new CampaignSettings[] { new AppSetting { AppStore = AppStore.AppleAppStore, AppId = AppleStoreAppId } };


            ResponseValidator.ValidateBasicSuccess(testcampaignCollection.Add(appCampaignCustomer));

            var select = AllSelectNonBiProperties.Concat(new string[] { "DisplayBiddingStrategyManualCpc" }).ToArray();

            // sort
            string orderBy = $"DisplayBiddingStrategyManualCpc asc";
            var result = CallGetCampaignsGridData(appCampaignCustomer, orderBy: orderBy, select: this.GetSelect(select));

            var expectedResults = new double[] { 20.0, 30.0, 40.0 };

            Assert.IsNotNull(result?.value, "Value is null.");
            Assert.AreEqual(expectedResults.Count(), result.value.Count, "Row count mismatch");
            Assert.AreEqual(expectedResults[0], result.value[0].BiddingScheme.ManualCPC.Value, "ManualCPC value mismatch");
            Assert.AreEqual(expectedResults[1], result.value[1].BiddingScheme.ManualCPC.Value, "ManualCPC value mismatch");
            Assert.AreEqual(expectedResults[2], result.value[2].BiddingScheme.ManualCPC.Value, "ManualCPC value mismatch");

            // filter
            string filter = $"DisplayBiddingStrategyManualCpc gt 30.0";
            result = CallGetCampaignsGridData(appCampaignCustomer, queryFilter: filter, select: this.GetSelect(select));

            Assert.IsNotNull(result?.value, "Value is null.");
            Assert.AreEqual(1, result.value.Count, "Row count mismatch");
            Assert.AreEqual(40.0, result.value[0].BiddingScheme.ManualCPC.Value, "ManualCPC value mismatch");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_Paging()
        {
            var select = new[] { "Name", "Id" };

            // Page 1
            var result = CallGetCampaignsGridData(this.cInfo, skip: 0, top: 5, select: this.GetSelect(select));

            var expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id).Skip(0).Take(5);
            VerifyGridData(result, expectedResults.ToArray(), select);

            // Page 2
            result = CallGetCampaignsGridData(this.cInfo, skip: 5, top: 5, select: this.GetSelect(select));

            expectedResults = campaignCollection.Campaigns.OrderBy(x => x.Data.Id).Skip(5).Take(5);
            VerifyGridData(result, expectedResults.ToArray(), select);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_Filtering(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var select = AllSelectProperties;
            string name = campaignCollection.Campaigns[3].Data.Name;
            var biData = PopulateRandomBiData(DateTime.Today);

            string filter = $"Name eq '{name}'";
            var result = CallGetCampaignsGridData(this.cInfo, queryFilter: filter, select: this.GetSelect(select));

            var expectedResults = campaignCollection.Campaigns.Where(x => x.Data.Name.Equals(name));
            VerifyGridData(result, expectedResults.ToArray(), select, biData);

            // Perf filter
            filter = $"PerformanceMetrics/Clicks gt 50";
            result = CallGetCampaignsGridData(this.cInfo, queryFilter: filter, select: this.GetSelect(select));

            expectedResults = biData != null
                ? campaignCollection.Campaigns.Where(x => biData[x.Data.Id].Clicks > 50).OrderBy(x => x.Data.Id)
                : Enumerable.Empty<TestCampaign>();
            VerifyGridData(result, expectedResults.ToArray(), select, biData);

            // Perf filter
            filter = $"PerformanceMetrics/AllConversions gt 5";
            result = CallGetCampaignsGridData(this.cInfo, queryFilter: filter, select: this.GetSelect(select));

            expectedResults = biData != null
                ? campaignCollection.Campaigns.Where(x => biData[x.Data.Id].AllConversions > 5).OrderBy(x => x.Data.Id)
                : Enumerable.Empty<TestCampaign>();
            VerifyGridData(result, expectedResults.ToArray(), select, biData);

            // Optimization score
            filter = $"OptimizationScore eq 0";
            result = CallGetCampaignsGridData(this.cInfo, queryFilter: filter, select: this.GetSelect(select));

            expectedResults = Enumerable.Empty<TestCampaign>();
            VerifyGridData(result, expectedResults.ToArray(), select, biData);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_Filter_BroadMatchOnlyCampaigns(int[] pilotFeatures)
        {
            CustomerInfo localCustomerInfo = CustomerInfo.CreateStandardAdvertiserWithPilot(Features.BroadOnlyCampaign);
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(localCustomerInfo.CustomerId, true, pilotFeatures);
            }

            var campaignCollection = new TestCampaignCollection(2);
            campaignCollection.Campaigns[0] = TestSetting.Factories.TestCampaginFactory.Produce(CampaignFactory.CampaignType.Default);
            campaignCollection.Campaigns[0].Data.IsBroadMatchOnlyCampaign = true;

            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(localCustomerInfo));

            var select = new[] { "Name", "Id" };

            string filter = $"IsBroadMatchOnlyCampaign eq true";
            var result = CallGetCampaignsGridData(localCustomerInfo, queryFilter: filter, select: this.GetSelect(select));

            var expectedResults = campaignCollection.Campaigns.Where(c => c.Data.IsBroadMatchOnlyCampaign);
            VerifyGridData(result, expectedResults.ToArray(), select);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_Filtering_GridDataFilter(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var select = new[] { "Name", "Id", PerformanceMetrics, PhonePerformanceMetrics };
            string name = campaignCollection.Campaigns[3].Data.Name;
            var biData = PopulateRandomBiData(DateTime.Today);

            var result = CallGetCampaignsGridData(
                this.cInfo,
                gridDataFilter: new dynamic[]
                {
                    new
                    {
                        PropertyToFilter = "Name",
                        FilterOperation = FilterOperation.Equal,
                        FilterOnValues = new object[] { name },
                        PeriodComparisonFilterOptions = PeriodComparisonOptions.AbsoluteChange,
                        CustomColumnId = (string)null,
                    }
                },
                select: this.GetSelect(select));

            var expectedResults = campaignCollection.Campaigns.Where(x => x.Data.Name.Equals(name));
            VerifyGridData(result, expectedResults.ToArray(), select, biData);

            // Perf filter
            result = CallGetCampaignsGridData(
                this.cInfo,
                gridDataFilter: new dynamic[]
                {
                    new
                    {
                        PropertyToFilter = "PerformanceMetrics/Clicks",
                        FilterOperation = FilterOperation.Greater,
                        FilterOnValues = new object[] { 50 },
                        PeriodComparisonFilterOptions = PeriodComparisonOptions.AbsoluteChange,
                        CustomColumnId = (string) null,
                    }
                },
                select: this.GetSelect(select));

            expectedResults = biData != null
                ? campaignCollection.Campaigns.Where(x => biData[x.Data.Id].Clicks > 50).OrderBy(x => x.Data.Id)
                : Enumerable.Empty<TestCampaign>();
            VerifyGridData(result, expectedResults.ToArray(), select, biData);

            // Perf filter
            result = CallGetCampaignsGridData(
                this.cInfo,
                gridDataFilter: new dynamic[]
                {
                    new
                    {
                        PropertyToFilter = "PerformanceMetrics/AllConversions",
                        FilterOperation = FilterOperation.Greater,
                        FilterOnValues = new object[] { 5 },
                        PeriodComparisonFilterOptions = PeriodComparisonOptions.AbsoluteChange,
                        CustomColumnId = (string) null,
                    }
                },
                select: this.GetSelect(select));

            expectedResults = biData != null
                ? campaignCollection.Campaigns.Where(x => biData[x.Data.Id].AllConversions > 5).OrderBy(x => x.Data.Id)
                : Enumerable.Empty<TestCampaign>();
            VerifyGridData(result, expectedResults.ToArray(), select, biData);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_Filtering_GridDataFilter_PassManyInFilterValues(int[] pilotFeatures)
        {
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }
            var select = new[] { "Name", "Id", PerformanceMetrics, PhonePerformanceMetrics };
            string name = campaignCollection.Campaigns[3].Data.Name;
            var biData = PopulateRandomBiData(DateTime.Today);

            var result = CallGetCampaignsGridData(
                this.cInfo,
                gridDataFilter: new dynamic[]
                {
                  new
                  {
                      PropertyToFilter = "Name",
                      FilterOperation = FilterOperation.In,
                      FilterOnValues = new object[] {name}.Union(Enumerable.Range(0, 1000).Select(x => $"n{x}")).ToList(),
                      PeriodComparisonFilterOptions = PeriodComparisonOptions.AbsoluteChange,
                      CustomColumnId = (string)null,
                  }
                },
                select: this.GetSelect(select)
            );

            var expectedResults = campaignCollection.Campaigns.Where(x => x.Data.Name.Equals(name));
            VerifyGridData(result, expectedResults.ToArray(), select, biData);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_GridDataFilterAndQueryOptionsFilterPresent_ExpectError()
        {
            var select = AllSelectProperties;
            string name = campaignCollection.Campaigns[3].Data.Name;

            CallGetCampaignsGridData(
                this.cInfo,
                gridDataFilter: new dynamic[]
                {
                    new
                    {
                        PropertyToFilter = "Name",
                        FilterOperation = FilterOperation.Equal,
                        FilterOnValues = new object[] { name },
                        PeriodComparisonFilterOptions = PeriodComparisonOptions.AbsoluteChange,
                        CustomColumnId = (string) null,
                    }
                },
                queryFilter: $"Name eq '{name}'",
                select: this.GetSelect(select),
                expectedStatusCode: HttpStatusCode.BadRequest);
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.AggregatorService)]
        public void GetCampaignsGridData_LastWriteTime()
        {
            dynamic result = CallGetCampaignsGridData(cInfo, lastWriteTime: "Max");

            DateTime firstCacheCreationTime;
            string objectCacheCreationTime = GridDataResultHelper.GetObjectCacheCreationTimeFromResult(result, out firstCacheCreationTime);

            // Check to ensure the returned time can be round-tripped
            result = CallGetCampaignsGridData(cInfo, lastWriteTime: objectCacheCreationTime);

            DateTime secondCacheCreationTime;
            GridDataResultHelper.GetObjectCacheCreationTimeFromResult(result, out secondCacheCreationTime);

            // The second returned time should be equal or greater than the first. Ideally they should be the same indicating a cache hit,
            // however this is not guarenteed.
            if (secondCacheCreationTime < firstCacheCreationTime)
            {
                Assert.Fail($"Second returned ObjectCacheCreationTime should be equal to or larger than the first ObjectCacheCreationTime. " +
                            $"First: {firstCacheCreationTime.ToString("O")}" +
                            $"Second: {secondCacheCreationTime.ToString("O")}");
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_Experiments()
        {
            CustomerInfo localCustomerInfo = CustomerInfo.CreateStandardAdvertiserWithPilot(Features.Experiment);

            var customerHierarchy = new CampaignsCollectionHelper(
                localCustomerInfo,
                2,
                (num, campaign) =>
                {
                    campaign.Data.Name = $"Campaign {num}";
                    campaign.Data.DailyTargetBudgetAmount = num + 1;
                    campaign.Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
                },
                experimentsPerCampaign: new[] { 1, 1 });

            var select = new string[] { "Id", "ExperimentId", "BaseCampaignName" };

            string filter = $"ExperimentId gt 0";
            string orderBy = "ExperimentId asc";
            string expand = "AssociatedExperiment";
            var result = CallGetCampaignsGridData(localCustomerInfo, orderBy: orderBy, queryFilter: filter, select: this.GetSelect(select), expand: expand);

            GridDataResultHelper.GetExpectedResult getExpectedResult = delegate (int i, string column) { return GetEntityValue(column, customerHierarchy.ExperimentCollection.Campaigns[i]); };

            GridDataResultHelper.VerifyGridData(result, customerHierarchy.ExperimentCollection.Campaigns.Count, getExpectedResult, select);

            foreach (var campaign in result.value)
            {
                Assert.IsNotNull(campaign.AssociatedExperiment);
            }

            result = CallGetCampaignsGridData(localCustomerInfo, select: this.GetSelect(select), expand: expand);

            foreach (var campaign in result.value)
            {
                Assert.IsNotNull(campaign.AssociatedExperiment);
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_BadSelect()
        {
            var select = new string[] { "BadSelect" };

            var result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select), expectedStatusCode: HttpStatusCode.BadRequest);

            Assert.AreEqual(1, result.value.Count, "Error count mismatch");
            Assert.AreEqual("InvalidField", result.value[0].Code.ToString(), "Error code mismatch");
            Assert.AreEqual("$select", result.value[0].Property.ToString(), "Error property mismatch");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetCampaignsGridData_BadExpand()
        {
            var expand = new string[] { "Keywords" };

            var result = CallGetCampaignsGridData(this.cInfo, expand: this.GetSelect(expand), expectedStatusCode: HttpStatusCode.BadRequest);

            Assert.AreEqual(1, result.value.Count, "Error count mismatch");
            Assert.AreEqual("InvalidField", result.value[0].Code.ToString(), "Error code mismatch");
            Assert.AreEqual("$select", result.value[0].Property.ToString(), "Error property mismatch");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.SmartCampaign)]
        public void GetCampaignsGridData_SmartCampaign()
        {
            var customerInfo = SmartPilotCustomer;
            TestCampaignCollection.Clean(customerInfo);

            long businessId = BusinessHelper.CreateBusiness(customerInfo, "Shoes", "http://test.shoes.com");
            int campaignCount = 2;
            var campaignCollection = new TestCampaignCollection(campaignCount);
            campaignCollection.Campaigns[1].Data.CampaignType = CampaignType.Smart;
            campaignCollection.Campaigns[1].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            campaignCollection.Campaigns[1].Data.Languages = new[] { Language.English };
            campaignCollection.Campaigns[1].Data.CampaignSettings = new[]
            {
                new SmartSettings { Goal = CampaignGoal.MoreWebsiteActions, BusinessId = businessId, Step = 1 }
            };
            var campaignAddResponse = campaignCollection.Add(customerInfo, customerInfo.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(campaignAddResponse);

            var result = CallGetCampaignsGridData(customerInfo);

            Assert.AreEqual(campaignCount, result.value.Count);

            for (int i = 0; i < campaignCount; i++)
            {
                if (result.value[i].Id.ToString() == campaignCollection.Campaigns[1].Data.Id.ToString())
                {
                    Assert.AreEqual(DeliveryStatus.CampaignInProgress.ToString(), result.value[i].DeliveryStatus.ToString());
                    Assert.AreEqual(CampaignType.Smart.ToString(), result.value[i].CampaignType.ToString());
                    Assert.AreEqual(CampaignStatus.InProgress.ToString(), result.value[i].Status.ToString());
                    Assert.AreEqual(mtToODataBudgetType[SimplifiedBudgetLimitType.DailyBudgetStandard], result.value[i].Budget.Type.ToString());
                    Assert.AreEqual(Language.English.ToString(), result.value[i].Languages[0].ToString());
                    Assert.AreEqual("MoreWebsiteActions", result.value[i].CampaignSettings[0].Goal.ToString());
                    Assert.AreEqual(businessId.ToString(), result.value[i].CampaignSettings[0].BusinessId.ToString());
                    Assert.AreEqual("1", result.value[i].CampaignSettings[0].Step.ToString());
                }
                else
                {
                    Assert.AreEqual(DeliveryStatus.Eligible.ToString(), result.value[i].DeliveryStatus.ToString());
                }
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.SmartCampaign)]
        public void GetCampaignsGridData_SmartCampaign_SelectCampaignSettings()
        {
            var customerInfo = SmartPilotCustomer;
            TestCampaignCollection.Clean(customerInfo);

            long businessId = BusinessHelper.CreateBusiness(customerInfo, "Shoes", "http://test.shoes.com");
            var campaignCollection = new TestCampaignCollection(1);
            campaignCollection.Campaigns[0].Data.CampaignType = CampaignType.Smart;
            campaignCollection.Campaigns[0].Data.SimplifiedBudgetType = SimplifiedBudgetLimitType.DailyBudgetStandard;
            campaignCollection.Campaigns[0].Data.Languages = new[] { Language.English };
            campaignCollection.Campaigns[0].Data.CampaignSettings = new[]
            {
                new SmartSettings { Goal = CampaignGoal.MoreWebsiteActions, BusinessId = businessId, Step = 1 }
            };
            var campaignAddResponse = campaignCollection.Add(customerInfo, customerInfo.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(campaignAddResponse);

            var result = CallGetCampaignsGridData(customerInfo, select: "Id,Name,Languages,Budget,Status,DeliveryStatus,CampaignType,CampaignSettings");

            Assert.AreEqual(1, result.value.Count);

            Assert.AreEqual(campaignCollection.Campaigns[0].Data.Id.ToString(), result.value[0].Id.ToString());
            Assert.AreEqual(campaignCollection.Campaigns[0].Data.Name.ToString(), result.value[0].Name.ToString());
            Assert.AreEqual(DeliveryStatus.CampaignInProgress.ToString(), result.value[0].DeliveryStatus.ToString());
            Assert.AreEqual(CampaignType.Smart.ToString(), result.value[0].CampaignType.ToString());
            Assert.AreEqual(CampaignStatus.InProgress.ToString(), result.value[0].Status.ToString());
            Assert.AreEqual(mtToODataBudgetType[SimplifiedBudgetLimitType.DailyBudgetStandard], result.value[0].Budget.Type.ToString());
            Assert.AreEqual(Language.English.ToString(), result.value[0].Languages[0].ToString());
            Assert.AreEqual("MoreWebsiteActions", result.value[0].CampaignSettings[0].Goal.ToString());
            Assert.AreEqual(businessId.ToString(), result.value[0].CampaignSettings[0].BusinessId.ToString());
            Assert.AreEqual("1", result.value[0].CampaignSettings[0].Step.ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.AutoBidding)]
        public void GetCampaignsGridData_BidStrategyName_SortFilter()
        {
            var select = new[] { "BidStrategyName" };
            var customerInfo1 = CustomerInfo.CreateStandardAdvertiserWithPilot(Features.PortfolioBidStrategy);

            var url = ApiVersion.BaseUrl + $"/Customers({customerInfo1.CustomerId})/Accounts({customerInfo1.AccountIds.First()})/Default.BulkUpsert";
            var portfolioBidStrategyName = nameof(GetCampaignsGridData_BidStrategyName_SortFilter) + "1" + Guid.NewGuid();
            var portfolioBidStrategyName2 = nameof(GetCampaignsGridData_BidStrategyName_SortFilter) + "2" + Guid.NewGuid();
            var portfolioBidStrategyName3 = nameof(GetCampaignsGridData_BidStrategyName_SortFilter) + "3" + Guid.NewGuid();
            PortfolioBidStrategyHelpers.AssociateNewPortfolioBidStrategyAndNewCampaign(portfolioBidStrategyName, customerInfo1, url, Guid.NewGuid().ToString());
            PortfolioBidStrategyHelpers.AssociateNewPortfolioBidStrategyAndNewCampaign(portfolioBidStrategyName2, customerInfo1, url, Guid.NewGuid().ToString());

            String orderBy = "BidStrategyName desc";
            var result = CallGetCampaignsGridData(customerInfo1, orderBy: orderBy, select: this.GetSelect(select));

            Assert.AreEqual(2, result.value.Count);
            Assert.AreEqual(portfolioBidStrategyName2, result.value[0].BidStrategyName.ToString());
            Assert.AreEqual(portfolioBidStrategyName, result.value[1].BidStrategyName.ToString());

            string filter = $"BidStrategyName eq '{portfolioBidStrategyName}'";
            var filterResult3 = CallGetCampaignsGridData(customerInfo1, queryFilter: filter, select: "BidStrategyName");

            Assert.AreEqual(portfolioBidStrategyName, filterResult3.value[0].BidStrategyName.ToString());

            string filter2 = $"BidStrategyName eq '{portfolioBidStrategyName3}'";
            var filterResult2 = CallGetCampaignsGridData(customerInfo1, queryFilter: filter2, select: "BidStrategyName");
            Assert.AreEqual(0, filterResult2.value.Count);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.CampaignSettings)]
        [Ignore]
        public void GetCampaignsGridData_CashbackSettings_SortFilter()
        {
            var select = new[] { "Name", "CampaignCashback" };

            var url = ApiVersion.BaseUrl + $"/Customers({personalizedOffersCustomer.CustomerId})/Accounts({personalizedOffersCustomer.AccountIds.First()})/Default.BulkUpsert";
            PersonalizedOffersHelper.GenerateCashbackData(personalizedOffersCustomer, url, providerId);

            // Sort based on Cashback Percent
            var orderBy = "CampaignCashback/Percent asc";
            var result = CallGetCampaignsGridData(personalizedOffersCustomer, select: this.GetSelect(select), orderBy: orderBy);
            Assert.AreEqual(4, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 4", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 3", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 1", result.value[3].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[0].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("25", result.value[1].CampaignCashback.Percent.ToString(), "Campaign order does not match");
            Assert.AreEqual("45", result.value[2].CampaignCashback.Percent.ToString(), "Campaign order does not match");
            Assert.AreEqual("50", result.value[3].CampaignCashback.Percent.ToString(), "Campaign order does not match");

            //Sort based on Cashback Budget
            orderBy = "CampaignCashback/MonthlyBudget desc";
            result = CallGetCampaignsGridData(personalizedOffersCustomer, select: this.GetSelect(select), orderBy: orderBy);
            Assert.AreEqual(4, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 2", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 1", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 3", result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 4", result.value[3].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("7500", result.value[0].CampaignCashback.MonthlyBudget.ToString(), "Campaign order does not match");
            Assert.AreEqual("5000", result.value[1].CampaignCashback.MonthlyBudget.ToString(), "Campaign order does not match");
            Assert.AreEqual("2500", result.value[2].CampaignCashback.MonthlyBudget.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[3].CampaignCashback.ToString(), "Campaign order does not match");

            //Sort based on Cashback Scope
            orderBy = "CampaignCashback/Scope asc";
            result = CallGetCampaignsGridData(personalizedOffersCustomer, select: this.GetSelect(select), orderBy: orderBy);
            Assert.AreEqual(4, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 4", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 3", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 1", result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[3].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[0].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("None", result.value[1].CampaignCashback.Scope.ToString(), "Campaign order does not match");
            Assert.AreEqual("Store", result.value[2].CampaignCashback.Scope.ToString(), "Campaign order does not match");
            Assert.AreEqual("Product", result.value[3].CampaignCashback.Scope.ToString(), "Campaign order does not match");

            orderBy = "Name asc";

            // FIlter based on Cashback Percent
            var filter = "CampaignCashback/Percent gt 35";
            result = CallGetCampaignsGridData(personalizedOffersCustomer, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(2, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 1", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("50", result.value[0].CampaignCashback.Percent.ToString(), "Campaign order does not match");
            Assert.AreEqual("45", result.value[1].CampaignCashback.Percent.ToString(), "Campaign order does not match");

            // FIlter based on Cashback Budget
            filter = "CampaignCashback/MonthlyBudget lt 7000";
            result = CallGetCampaignsGridData(personalizedOffersCustomer, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(3, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 1", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 3", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 4", result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("5000", result.value[0].CampaignCashback.MonthlyBudget.ToString(), "Campaign order does not match");
            Assert.AreEqual("2500", result.value[1].CampaignCashback.MonthlyBudget.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[2].CampaignCashback.ToString(), "Campaign order does not match");

            // FIlter based on Cashback Scope
            filter = "CampaignCashback/Scope eq Enum.CashbackScope'Store'";
            result = CallGetCampaignsGridData(personalizedOffersCustomer, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(1, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 1", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Store", result.value[0].CampaignCashback.Scope.ToString(), "Campaign order does not match");

            // FIlter based on Cashback Scope null
            filter = "CampaignCashback/Scope eq null";
            result = CallGetCampaignsGridData(personalizedOffersCustomer, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(1, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 4", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[0].CampaignCashback.ToString(), "Campaign order does not match");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.CampaignSettings)]
        public void GetCampaignsGridData_CashbackSettingsV3_SortFilter()
        {
            var select = new[] { "Name", "CampaignCashback" };

            var url = ApiVersion.BaseUrl + $"/Customers({personalizedOffersCustomerV3.CustomerId})/Accounts({personalizedOffersCustomerV3.AccountIds.First()})/Default.BulkUpsert";
            var insertResult = PersonalizedOffersHelper.GenerateCashbackDataV3(personalizedOffersCustomerV3, url, providerIdV3);

            //Sort based on CouponsEnabled
            var orderBy = "CampaignCashback/CouponsEnabled asc";
            var result = CallGetCampaignsGridData(personalizedOffersCustomerV3, select: this.GetSelect(select), orderBy: orderBy);
            Assert.AreEqual(5, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 4", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 5", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 1", result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[3].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 3", result.value[4].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[0].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[1].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("Disabled", result.value[2].CampaignCashback.CouponsEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("Disabled", result.value[3].CampaignCashback.CouponsEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("Enabled", result.value[4].CampaignCashback.CouponsEnabled.ToString(), "Campaign order does not match");

            //Sort based on PersonalizedOffersEnabled
            orderBy = "CampaignCashback/PersonalizedOffersEnabled desc";
            result = CallGetCampaignsGridData(personalizedOffersCustomerV3, select: this.GetSelect(select), orderBy: orderBy);
            Assert.AreEqual(5, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 3", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 1", result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 5", result.value[3].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 4", result.value[4].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("True", result.value[0].CampaignCashback.PersonalizedOffersEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("True", result.value[1].CampaignCashback.PersonalizedOffersEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("True", result.value[2].CampaignCashback.PersonalizedOffersEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[3].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[4].CampaignCashback.ToString(), "Campaign order does not match");

            // Sort based on Cashback Percent
            orderBy = "CampaignCashback/Percent asc";
            result = CallGetCampaignsGridData(personalizedOffersCustomerV3, select: this.GetSelect(select), orderBy: orderBy);
            Assert.AreEqual(5, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 4", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 5", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 3", result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[3].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 1", result.value[4].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[0].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[1].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("25", result.value[2].CampaignCashback.Percent.ToString(), "Campaign order does not match");
            Assert.AreEqual("45", result.value[3].CampaignCashback.Percent.ToString(), "Campaign order does not match");
            Assert.AreEqual("50", result.value[4].CampaignCashback.Percent.ToString(), "Campaign order does not match");

            //Sort based on Cashback Scope
            orderBy = "CampaignCashback/Scope asc";
            result = CallGetCampaignsGridData(personalizedOffersCustomerV3, select: this.GetSelect(select), orderBy: orderBy);
            Assert.AreEqual(5, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 4", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 5", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 3", result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 1", result.value[3].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[4].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[0].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[1].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("None", result.value[2].CampaignCashback.Scope.ToString(), "Campaign order does not match");
            Assert.AreEqual("Store", result.value[3].CampaignCashback.Scope.ToString(), "Campaign order does not match");
            Assert.AreEqual("Product", result.value[4].CampaignCashback.Scope.ToString(), "Campaign order does not match");

            orderBy = "Name asc";

            // FIlter based on CouponsEnabled
            var filter = "CampaignCashback/CouponsEnabled eq Enum.CouponsEnabled'Enabled'";
            result = CallGetCampaignsGridData(personalizedOffersCustomerV3, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(1, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 3", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Enabled", result.value[0].CampaignCashback.CouponsEnabled.ToString(), "Campaign order does not match");

            // FIlter based on CouponsEnabled
            filter = "CampaignCashback/CouponsEnabled eq Enum.CouponsEnabled'Disabled'";
            result = CallGetCampaignsGridData(personalizedOffersCustomerV3, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(2, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 1", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Disabled", result.value[0].CampaignCashback.CouponsEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("Disabled", result.value[1].CampaignCashback.CouponsEnabled.ToString(), "Campaign order does not match");

            // FIlter based on PersonalizedOffersEnabled
            filter = "CampaignCashback/PersonalizedOffersEnabled eq true";
            result = CallGetCampaignsGridData(personalizedOffersCustomerV3, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(3, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 1", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 3", result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("True", result.value[0].CampaignCashback.PersonalizedOffersEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("True", result.value[1].CampaignCashback.PersonalizedOffersEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("True", result.value[2].CampaignCashback.PersonalizedOffersEnabled.ToString(), "Campaign order does not match");

            // FIlter based on PersonalizedOffersEnabled
            filter = "CampaignCashback/PersonalizedOffersEnabled eq false";
            result = CallGetCampaignsGridData(personalizedOffersCustomerV3, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(0, result.value.Count, "Campaign Count does not match");

            // FIlter based on Cashback Percent
            filter = "CampaignCashback/Percent gt 35";
            result = CallGetCampaignsGridData(personalizedOffersCustomerV3, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(2, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 1", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("50", result.value[0].CampaignCashback.Percent.ToString(), "Campaign order does not match");
            Assert.AreEqual("45", result.value[1].CampaignCashback.Percent.ToString(), "Campaign order does not match");

            // FIlter based on Cashback Scope
            filter = "CampaignCashback/Scope eq Enum.CashbackScope'Store'";
            result = CallGetCampaignsGridData(personalizedOffersCustomerV3, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(1, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 1", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Store", result.value[0].CampaignCashback.Scope.ToString(), "Campaign order does not match");

            // FIlter based on Cashback Scope null
            filter = "CampaignCashback/Scope eq null";
            result = CallGetCampaignsGridData(personalizedOffersCustomerV3, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(2, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 4", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 5", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[0].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[1].CampaignCashback.ToString(), "Campaign order does not match");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.CampaignSettings)]
        public void GetCampaignsGridData_CashbackSettings_CPS_SortFilter()
        {
            var select = new[] { "Name", "CampaignCashback", "BiddingScheme" };

            var url = ApiVersion.BaseUrl + $"/Customers({personalizedOffersCustomerCPS.CustomerId})/Accounts({personalizedOffersCustomerCPS.AccountIds.First()})/Default.BulkUpsert";
            var insertResult = PersonalizedOffersHelper.GenerateCashbackDataCPS(personalizedOffersCustomerCPS, url, providerIdCPS);

            //Sort based on CouponsEnabled
            var orderBy = "CampaignCashback/CouponsEnabled asc";
            var result = CallGetCampaignsGridData(personalizedOffersCustomerCPS, select: this.GetSelect(select), orderBy: orderBy);
            Assert.AreEqual(5, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 4", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 5", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 1", result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[3].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 3", result.value[4].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[0].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[1].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("Disabled", result.value[2].CampaignCashback.CouponsEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("Disabled", result.value[3].CampaignCashback.CouponsEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("Enabled", result.value[4].CampaignCashback.CouponsEnabled.ToString(), "Campaign order does not match");

            //Sort based on PersonalizedOffersEnabled
            orderBy = "CampaignCashback/PersonalizedOffersEnabled desc";
            result = CallGetCampaignsGridData(personalizedOffersCustomerCPS, select: this.GetSelect(select), orderBy: orderBy);
            Assert.AreEqual(5, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 3", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 1", result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 5", result.value[3].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 4", result.value[4].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("True", result.value[0].CampaignCashback.PersonalizedOffersEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("True", result.value[1].CampaignCashback.PersonalizedOffersEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("True", result.value[2].CampaignCashback.PersonalizedOffersEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[3].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[4].CampaignCashback.ToString(), "Campaign order does not match");

            // Sort based on Cashback Percent
            orderBy = "CampaignCashback/Percent asc";
            result = CallGetCampaignsGridData(personalizedOffersCustomerCPS, select: this.GetSelect(select), orderBy: orderBy);
            Assert.AreEqual(5, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 4", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 5", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 3", result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[3].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 1", result.value[4].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[0].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[1].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("25", result.value[2].CampaignCashback.Percent.ToString(), "Campaign order does not match");
            Assert.AreEqual("45", result.value[3].CampaignCashback.Percent.ToString(), "Campaign order does not match");
            Assert.AreEqual("50", result.value[4].CampaignCashback.Percent.ToString(), "Campaign order does not match");

            //Sort based on Cashback Scope
            orderBy = "CampaignCashback/Scope asc";
            result = CallGetCampaignsGridData(personalizedOffersCustomerCPS, select: this.GetSelect(select), orderBy: orderBy);
            Assert.AreEqual(5, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 4", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 5", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 3", result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 1", result.value[3].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[4].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[0].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[1].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("None", result.value[2].CampaignCashback.Scope.ToString(), "Campaign order does not match");
            Assert.AreEqual("Store", result.value[3].CampaignCashback.Scope.ToString(), "Campaign order does not match");
            Assert.AreEqual("Product", result.value[4].CampaignCashback.Scope.ToString(), "Campaign order does not match");

            //Sort based on BiddingScheme Scope
            orderBy = "DisplayBiddingStrategyType desc";
            result = CallGetCampaignsGridData(personalizedOffersCustomerCPS, select: this.GetSelect(select), orderBy: orderBy);
            Assert.AreEqual(5, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 3", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 1", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 5", result.value[3].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 4", result.value[4].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("CostPerSale", result.value[0].BiddingScheme.Type.ToString(), "Campaign order does not match");
            Assert.AreEqual("15", result.value[0].BiddingScheme.TargetCostPerSale.ToString(), "Campaign order does not match");
            Assert.AreEqual("CostPerSale", result.value[1].BiddingScheme.Type.ToString(), "Campaign order does not match");
            Assert.AreEqual("10", result.value[1].BiddingScheme.TargetCostPerSale.ToString(), "Campaign order does not match");
            Assert.AreEqual("MaxConversionValue", result.value[2].BiddingScheme.Type.ToString(), "Campaign order does not match");
            Assert.AreEqual("EnhancedCpc", result.value[3].BiddingScheme.Type.ToString(), "Campaign order does not match");
            Assert.AreEqual("EnhancedCpc", result.value[4].BiddingScheme.Type.ToString(), "Campaign order does not match");


            orderBy = "Name asc";

            // FIlter based on CouponsEnabled
            var filter = "CampaignCashback/CouponsEnabled eq Enum.CouponsEnabled'Enabled'";
            result = CallGetCampaignsGridData(personalizedOffersCustomerCPS, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(1, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 3", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Enabled", result.value[0].CampaignCashback.CouponsEnabled.ToString(), "Campaign order does not match");

            // FIlter based on CouponsEnabled
            filter = "CampaignCashback/CouponsEnabled eq Enum.CouponsEnabled'Disabled'";
            result = CallGetCampaignsGridData(personalizedOffersCustomerCPS, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(2, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 1", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Disabled", result.value[0].CampaignCashback.CouponsEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("Disabled", result.value[1].CampaignCashback.CouponsEnabled.ToString(), "Campaign order does not match");

            // FIlter based on PersonalizedOffersEnabled
            filter = "CampaignCashback/PersonalizedOffersEnabled eq true";
            result = CallGetCampaignsGridData(personalizedOffersCustomerCPS, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(3, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 1", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 3", result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("True", result.value[0].CampaignCashback.PersonalizedOffersEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("True", result.value[1].CampaignCashback.PersonalizedOffersEnabled.ToString(), "Campaign order does not match");
            Assert.AreEqual("True", result.value[2].CampaignCashback.PersonalizedOffersEnabled.ToString(), "Campaign order does not match");

            // FIlter based on PersonalizedOffersEnabled
            filter = "CampaignCashback/PersonalizedOffersEnabled eq false";
            result = CallGetCampaignsGridData(personalizedOffersCustomerCPS, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(0, result.value.Count, "Campaign Count does not match");

            // FIlter based on Cashback Percent
            filter = "CampaignCashback/Percent gt 35";
            result = CallGetCampaignsGridData(personalizedOffersCustomerCPS, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(2, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 1", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 2", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("50", result.value[0].CampaignCashback.Percent.ToString(), "Campaign order does not match");
            Assert.AreEqual("45", result.value[1].CampaignCashback.Percent.ToString(), "Campaign order does not match");

            // FIlter based on Cashback Scope
            filter = "CampaignCashback/Scope eq Enum.CashbackScope'Store'";
            result = CallGetCampaignsGridData(personalizedOffersCustomerCPS, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(1, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 1", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Store", result.value[0].CampaignCashback.Scope.ToString(), "Campaign order does not match");

            // FIlter based on Cashback Scope null
            filter = "CampaignCashback/Scope eq null";
            result = CallGetCampaignsGridData(personalizedOffersCustomerCPS, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(2, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 4", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 5", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[0].CampaignCashback.ToString(), "Campaign order does not match");
            Assert.AreEqual("", result.value[1].CampaignCashback.ToString(), "Campaign order does not match");


            // FIlter based on Cashback Scope
            filter = "DisplayBiddingStrategyType eq Enum.BiddingStrategyType'CostPerSale'";
            result = CallGetCampaignsGridData(personalizedOffersCustomerCPS, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(2, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual("Campaign 1", result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("Campaign 3", result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual("CostPerSale", result.value[0].BiddingScheme.Type.ToString(), "Campaign order does not match");
            Assert.AreEqual("CostPerSale", result.value[1].BiddingScheme.Type.ToString(), "Campaign order does not match");
        }

        [TestMethod, Priority(2)]
        public void GetCampaignsGridData_DSAMixedModeCampaign_CampaignSettings()
        {
            CreateAccountWithDSAMixedModeCampaign(out CustomerInfo dsaCustomerInfo, out TestCampaignCollection dsaCampaignCollection);

            string[] selectedColumns = new[] { "Id", "Name", "CampaignSettings" };
            var result = CallGetCampaignsGridData(dsaCustomerInfo, select: GetSelect(selectedColumns));

            var expectedResults = dsaCampaignCollection.Campaigns.OrderBy(c => c.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), selectedColumns);

            Campaign expectedCampaign = dsaCampaignCollection.Campaigns.Single().Data;
            DynamicSearchAdsSetting expectedSettings = (DynamicSearchAdsSetting)expectedCampaign.CampaignSettings.Single(s => s is DynamicSearchAdsSetting);

            Assert.AreEqual(1, result.value.Count, "Only expected one Campaign");
            dynamic actualCampaign = result.value[0];

            Assert.AreEqual(1, actualCampaign["CampaignSettings"].Count, "Only expected one setting");
            dynamic actualSetting = actualCampaign["CampaignSettings"][0];
            Assert.IsNotNull(actualSetting, "CampaignSettings");

            Assert.AreEqual("#Model.DynamicSearchAdsSetting", actualSetting["@odata.type"].ToString(), "@odata.type");
            Assert.AreEqual(expectedSettings.DomainName, actualSetting["DomainName"].ToString(), "DomainName");
            Assert.AreEqual(expectedSettings.Language, actualSetting["Language"].ToString(), "Language");
            Assert.AreEqual($"{expectedSettings.Source}", actualSetting["Source"].ToString(), "Source");
        }

        // NOTE: Any test method that relies on connecting to AdInsightMT must have "AdInsightIntegration" in its name
        [TestMethod, Priority(2)]
        public void AdInsightIntegration_GetCampaignsGridData_AvailableInsightTypeIds()
        {
            // Mock AdInsightODataClient is only available in CI
            if (!mockReporting)
            {
                return;
            }

            // This can be moved to the initialization method as soon as other tests require
            var expectedFluctuations = AddFluctuations(campaignCollection.Campaigns);
            var expectedAccountFluctuations = AddAccountFluctuation();

            // FluctuationType.Conversion will be filtered by online check logic - requiring to mock corresponding Opportunity data
            expectedAccountFluctuations.Remove(FluctuationType.Conversion);

            var dateRange = new BiDateRange
            {
                Type = DateRangeType.CustomRange,
                CustomDateRangeStart = DateTime.Today.AddDays(-2),
                CustomDateRangeEnd = DateTime.Today.AddDays(1)
            };

            string[] selectedColumns = new[] { "Id", "Name", "AvailableInsightTypeIds" };
            var result = CallGetCampaignsGridData(cInfo, select: GetSelect(selectedColumns), dateRange: dateRange);

            var expectedResults = campaignCollection.Campaigns.OrderBy(c => c.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), selectedColumns);
            Assert.AreEqual(GetFluctuationString(expectedAccountFluctuations), result["@ns.unfiltered.totals"]["AvailableInsightTypeIds"].ToString());

            result = CallGetCampaignsGridData(cInfo, select: GetSelect(selectedColumns), dateRange: dateRange, queryFilter: "AvailableInsightTypeIds/any(d:d eq Enum.FluctuationType'Impression' or d eq Enum.FluctuationType'Click')");

            expectedResults = campaignCollection.Campaigns.Where(c => c.Data.Id % 2 == 0 || c.Data.Id % 3 == 0).OrderBy(c => c.Data.Id);
            VerifyGridData(result, expectedResults.ToArray(), selectedColumns);
            Assert.AreEqual(GetFluctuationString(expectedAccountFluctuations), result["@ns.unfiltered.totals"]["AvailableInsightTypeIds"].ToString());
        }

        // NOTE: Any test method that relies on connecting to AdInsightMT must have "AdInsightIntegration" in its name
        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void AdInsightIntegration_GetCampaignsGridData_OptimizationScore_SortFilter()
        {
            // Mock AdInsightODataClient is only available in CI
            if (!mockReporting)
            {
                return;
            }

            CustomerInfo customerInfo = CustomerInfo.CreateStandardAdvertiser();

            double?[] optimizationScoreMock = new double?[] { 0.123, 0.2, null, 0.1, null, 0.933 };
            int campaignCount = optimizationScoreMock.Length;
            var campaignCollection = new TestCampaignCollection(campaignCount);
            for (var i = 0; i < campaignCount; i++)
            {
                campaignCollection.Campaigns[i].Data.Name = $"Campaign {i} [OptimizationScoreHook:{optimizationScoreMock[i]}]";
            }

            campaignCollection.Campaigns[3].Data.Status = CampaignStatus.UserPaused;
            var campaignAddResponse = campaignCollection.Add(customerInfo, customerInfo.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(campaignAddResponse);

            var select = new[] { "Id,Name,OptimizationScore" };
            var orderBy = "OptimizationScore desc";
            var result = CallGetCampaignsGridData(customerInfo, orderBy: orderBy, select: this.GetSelect(select));
            Assert.AreEqual(campaignCount, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual(campaignCollection.Campaigns[5].Data.Name, result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual(campaignCollection.Campaigns[1].Data.Name, result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual(campaignCollection.Campaigns[0].Data.Name, result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual(campaignCollection.Campaigns[4].Data.Name, result.value[3].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual(campaignCollection.Campaigns[3].Data.Name, result.value[4].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual(campaignCollection.Campaigns[2].Data.Name, result.value[5].Name.ToString(), "Campaign order does not match");

            Assert.AreEqual(optimizationScoreMock[5].ToString(), result.value[0].OptimizationScore.ToString(), "Campaign optimization score does not match");
            Assert.AreEqual(optimizationScoreMock[1].ToString(), result.value[1].OptimizationScore.ToString(), "Campaign optimization score does not match");
            Assert.AreEqual(optimizationScoreMock[0].ToString(), result.value[2].OptimizationScore.ToString(), "Campaign optimization score does not match");
            Assert.AreEqual(optimizationScoreMock[4].ToString(), result.value[3].OptimizationScore.ToString(), "Campaign optimization score does not match");
            Assert.AreEqual(string.Empty, result.value[4].OptimizationScore.ToString(), "Campaign optimization score does not match");
            Assert.AreEqual(optimizationScoreMock[2].ToString(), result.value[5].OptimizationScore.ToString(), "Campaign optimization score does not match");

            var filter = "OptimizationScore lt 0.2";
            orderBy = "OptimizationScore asc";
            result = CallGetCampaignsGridData(customerInfo, select: this.GetSelect(select), orderBy: orderBy, queryFilter: filter);
            Assert.AreEqual(4, result.value.Count, "Campaign Count does not match");
            Assert.AreEqual(campaignCollection.Campaigns[2].Data.Name, result.value[0].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual(campaignCollection.Campaigns[3].Data.Name, result.value[1].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual(campaignCollection.Campaigns[4].Data.Name, result.value[2].Name.ToString(), "Campaign order does not match");
            Assert.AreEqual(campaignCollection.Campaigns[0].Data.Name, result.value[3].Name.ToString(), "Campaign order does not match");

            Assert.AreEqual(optimizationScoreMock[2].ToString(), result.value[0].OptimizationScore.ToString(), "Campaign optimization score does not match");
            Assert.AreEqual(string.Empty, result.value[1].OptimizationScore.ToString(), "Campaign optimization score does not match");
            Assert.AreEqual(optimizationScoreMock[4].ToString(), result.value[2].OptimizationScore.ToString(), "Campaign optimization score does not match");
            Assert.AreEqual(optimizationScoreMock[0].ToString(), result.value[3].OptimizationScore.ToString(), "Campaign optimization score does not match");
        }


        // NOTE: Any test method that relies on connecting to AdInsightMT must have "AdInsightIntegration" in its name
        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        public void AdInsightIntegration_GetCampaignsGridDataPerformanceInsights(int[] pilotFeatures)
        {
            // Mock AdInsightODataClient is only available in CI
            if (!mockReporting)
            {
                return;
            }

            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                DatabaseHelper.EnablePilotFeatures(this.cInfo.CustomerId, true, pilotFeatures);
            }

            var select = new string[] { "PerformanceInsights" };
            var result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));

            Assert.IsNotNull(result?.value);
            Assert.AreEqual(10, result.value.Count);
            foreach (var row in result?.value)
            {
                Assert.AreEqual(2, (int)row["PerformanceInsights"]?.HealthCheckCount);
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.AIM)]
        [DataRow(new int[] { })]
        //
        public void GetCampaignsGridData_VideoMetrics_WithSortAndFilter(int[] pilotFeatures)
        {
            List<int> featureList = new List<int>() { Features.VideoAds };
            if (pilotFeatures != null && pilotFeatures.Length > 0)
            {
                featureList.AddRange(pilotFeatures);
            }
            cInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, featureList.ToArray());
            this.CreateCampaignsForCustomer(cInfo, 10);
            var select = new[]
            {
                "Id",
                "PerformanceMetrics/VideoViews",
                "PerformanceMetrics/VideoViewsAt25Percent",
                "PerformanceMetrics/VideoViewsAt50Percent",
                "PerformanceMetrics/VideoViewsAt75Percent",
                "PerformanceMetrics/CompletedVideoViews",
                "PerformanceMetrics/TotalWatchTimeInMS",
                "PerformanceMetrics/VideoCompletionRate",
                "PerformanceMetrics/ViewThroughRate",
                "PerformanceMetrics/AverageWatchTimePerVideoView",
                "PerformanceMetrics/AverageWatchTimePerImpression",
                "PerformanceMetrics/AverageCPV",
            };

            var biData = PopulateRandomBiData(DateTime.Today);

            if (biData != null)
            {
                // direct metrics
                var longFilterValue = biData.Select(b => b.Value.VideoViews).Min();
                VerifyVideoMetricsWithSortAndFilter("PerformanceMetrics/VideoViews", biData, b => b.Value.VideoViews, b => b.Value.VideoViews > longFilterValue, select, longFilterValue);

                longFilterValue = biData.Select(b => b.Value.VideoViewsAt25Percent).Min();
                VerifyVideoMetricsWithSortAndFilter("PerformanceMetrics/VideoViewsAt25Percent", biData, b => b.Value.VideoViewsAt25Percent, b => b.Value.VideoViewsAt25Percent > longFilterValue, select, longFilterValue);

                longFilterValue = biData.Select(b => b.Value.VideoViewsAt50Percent).Min();
                VerifyVideoMetricsWithSortAndFilter("PerformanceMetrics/VideoViewsAt50Percent", biData, b => b.Value.VideoViewsAt50Percent, b => b.Value.VideoViewsAt50Percent > longFilterValue, select, longFilterValue);

                longFilterValue = biData.Select(b => b.Value.VideoViewsAt75Percent).Min();
                VerifyVideoMetricsWithSortAndFilter("PerformanceMetrics/VideoViewsAt75Percent", biData, b => b.Value.VideoViewsAt75Percent, b => b.Value.VideoViewsAt75Percent > longFilterValue, select, longFilterValue);

                longFilterValue = biData.Select(b => b.Value.CompletedVideoViews).Min();
                VerifyVideoMetricsWithSortAndFilter("PerformanceMetrics/CompletedVideoViews", biData, b => b.Value.CompletedVideoViews, b => b.Value.CompletedVideoViews > longFilterValue, select, longFilterValue);

                longFilterValue = biData.Select(b => b.Value.TotalWatchTimeInMS).Min();
                VerifyVideoMetricsWithSortAndFilter("PerformanceMetrics/TotalWatchTimeInMS", biData, b => b.Value.TotalWatchTimeInMS, b => b.Value.TotalWatchTimeInMS > longFilterValue, select, longFilterValue);

                // computed metrics
                var doubleFilterValue = biData.Select(b => b.Value.ViewThroughRate).Min();
                VerifyVideoMetricsWithSortAndFilter("PerformanceMetrics/ViewThroughRate", biData, b => b.Value.ViewThroughRate, b => b.Value.ViewThroughRate > doubleFilterValue, select, doubleFilterValue);

                doubleFilterValue = biData.Select(b => b.Value.VideoCompletionRate).Min();
                VerifyVideoMetricsWithSortAndFilter("PerformanceMetrics/VideoCompletionRate", biData, b => b.Value.VideoCompletionRate, b => b.Value.VideoCompletionRate > doubleFilterValue, select, doubleFilterValue);

                doubleFilterValue = biData.Select(b => b.Value.AverageWatchTimePerVideoView).Min();
                VerifyVideoMetricsWithSortAndFilter("PerformanceMetrics/AverageWatchTimePerVideoView", biData, b => b.Value.AverageWatchTimePerVideoView, b => b.Value.AverageWatchTimePerVideoView > doubleFilterValue, select, doubleFilterValue);

                doubleFilterValue = biData.Select(b => b.Value.AverageWatchTimePerImpression).Min();
                VerifyVideoMetricsWithSortAndFilter("PerformanceMetrics/AverageWatchTimePerImpression", biData, b => b.Value.AverageWatchTimePerImpression, b => b.Value.AverageWatchTimePerImpression > doubleFilterValue, select, doubleFilterValue);

                doubleFilterValue = biData.Select(b => b.Value.AverageCPV).Min();
                VerifyVideoMetricsWithSortAndFilter("PerformanceMetrics/AverageCPV", biData, b => b.Value.AverageCPV, b => b.Value.AverageCPV > doubleFilterValue, select, doubleFilterValue);
            }
            else
            {
                var orderBy = "PerformanceMetrics/TotalWatchTimeInMS asc";
                var filter = "PerformanceMetrics/VideoViews gt 0";
                CallGetCampaignsGridData(cInfo, today, orderBy: orderBy, queryFilter: filter, select: this.GetSelect(select));
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Labels)]
        public void GetCampaignGridData_LabelsWithFilter_AccountLabelsNotEnabled()
        {
            CustomerInfo cInfo = CustomerInfo.CreateStandardAdvertiserWithPilot();

            var select = AllSelectProperties;

            var campaignCollection = new TestCampaignCollection(1);
            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(cInfo));

            // Create 2 Labels.
            List<long> labelIds;
            List<dynamic> labels = new List<dynamic>();
            BulkEditLabelAssociationE2E.CreateLabels(cInfo, 2, out labels, out labelIds);

            // Associate.
            var bulkEditResponse = BulkEditLabelAssociationE2E.StartLabelAssociationBulkEdit(
                cInfo,
                EntityType.Campaign,
                labelIds.ToArray(),
                isLabelAssociationAction: true,
                filter: string.Format("$filter=(Id eq {0})", campaignCollection.Ids[0]));

            var result = CallGetCampaignsGridData(cInfo, select: this.GetSelect(select), expand: "Labels",
                queryFilter: $"Id eq {campaignCollection.Ids[0]} and Labels/any(d:d/Id eq {labelIds[0]})");

            Assert.AreEqual(1, result["@odata.count"].Value);
            var labelsAssociated = result["value"][0]["Labels"];
            Assert.AreEqual(2, ((JArray)labelsAssociated).Count);

            result = CallGetCampaignsGridData(cInfo, select: this.GetSelect(select), expand: "Labels",
               gridDataFilter: new dynamic[]
               {
                    new
                    {
                        PropertyToFilter = "Id",
                        FilterOperation = FilterOperation.Equal,
                        CustomColumnId = (string)null,
                        FilterOnValues = new object[] { campaignCollection.Ids[0]},
                    },
                    new
                    {
                        PropertyToFilter = "Labels",
                        FilterOperation = FilterOperation.ContainsAny,
                        CustomColumnId = (string)null,
                        FilterOnValues = new object[] { labelIds[0] },
                    }
               });

            Assert.AreEqual(1, result["@odata.count"].Value);
            labelsAssociated = result["value"][0]["Labels"];
            Assert.AreEqual(2, ((JArray)labelsAssociated).Count);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Labels)]
        public void GetCampaignGridData_LabelsWithFilter_AccountLabelsEnabled()
        {
            CustomerInfo cInfo = CustomerInfo.CreateStandardAdvertiserWithPilot(AccountPilotFeatures.AccountLabels);

            var select = AllSelectProperties;

            var campaignCollection = new TestCampaignCollection(1);
            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(cInfo));

            // Create 2 Labels.
            List<long> labelIds;
            List<dynamic> labels = new List<dynamic>();
            BulkEditLabelMccAssociationE2E.CreateLabels(cInfo, 2, out labels, out labelIds);

            // Associate.
            var bulkEditResponse = BulkEditLabelMccAssociationE2E.StartLabelMccAssociationBulkEdit(
                cInfo,
                EntityType.Campaign,
                labelIds.ToArray(),
                isLabelAssociationAction: true,
                filter: string.Format("$filter=(Id eq {0})", campaignCollection.Ids[0]),
                isMccLevel: false);

            var result = CallGetCampaignsGridData(cInfo, select: this.GetSelect(select), expand: "Labels",
                queryFilter: $"Id eq {campaignCollection.Ids[0]} and Labels/any(d:d/Id eq {labelIds[0]} and d/Scope eq Enum.EntityScope'Customer')");

            Assert.AreEqual(1, result["@odata.count"].Value);
            var labelsAssociated = result["value"][0]["Labels"];
            Assert.AreEqual(2, ((JArray)labelsAssociated).Count);

            result = CallGetCampaignsGridData(cInfo, select: this.GetSelect(select), expand: "Labels",
                gridDataFilter: new dynamic[]
                {
                    new
                    {
                        PropertyToFilter = "Id",
                        FilterOperation = FilterOperation.Equal,
                        CustomColumnId = (string)null,
                        FilterOnValues = new object[] { campaignCollection.Ids[0] },
                    },
                    new
                    {
                        PropertyToFilter = "Labels",
                        FilterOperation = FilterOperation.ContainsAny,
                        CustomColumnId = (string)null,
                        FilterOnValues = new object[] {"{\"Id\":\"" + labelIds[0] + "\", \"Scope\": \"Customer\"}"},
                    }
                },
                currentCustomerId: cInfo.CustomerId);

            Assert.AreEqual(1, result["@odata.count"].Value);
            labelsAssociated = result["value"][0]["Labels"];
            Assert.AreEqual(2, ((JArray)labelsAssociated).Count);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.Labels)]
        public void GetCampaignsGridData_LabelsWithFilter_AccountLabelsEnabled_NoComplexFilter_BackwardCompatibility()
        {
            CustomerInfo cInfo = CustomerInfo.CreateStandardAdvertiserWithPilot(AccountPilotFeatures.AccountLabels);

            var select = AllSelectProperties;

            var campaignCollection = new TestCampaignCollection(1);
            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(cInfo));

            // Create 2 Labels.
            List<long> labelIds;
            List<dynamic> labels = new List<dynamic>();
            BulkEditLabelAssociationE2E.CreateLabels(cInfo, 2, out labels, out labelIds);

            // Associate.
            var bulkEditResponse = BulkEditLabelAssociationE2E.StartLabelAssociationBulkEdit(
                cInfo,
                EntityType.Campaign,
                labelIds.ToArray(),
                isLabelAssociationAction: true,
                filter: string.Format("$filter=(Id eq {0})", campaignCollection.Ids[0]));

            var result = CallGetCampaignsGridData(cInfo, select: this.GetSelect(select), expand: "Labels",
                queryFilter: $"Id eq {campaignCollection.Ids[0]} and Labels/any(d:d/Id eq {labelIds[0]})");

            Assert.AreEqual(1, result["@odata.count"].Value);
            var labelsAssociated = result["value"][0]["Labels"];
            Assert.AreEqual(2, ((JArray)labelsAssociated).Count);

            result = CallGetCampaignsGridData(cInfo, select: this.GetSelect(select), expand: "Labels",
                gridDataFilter: new dynamic[]
                {
                    new
                    {
                        PropertyToFilter = "Id",
                        FilterOperation = FilterOperation.Equal,
                        CustomColumnId = (string)null,
                        FilterOnValues = new object[] { campaignCollection.Ids[0] },
                    },
                    new
                    {
                        PropertyToFilter = "Labels",
                        FilterOperation = FilterOperation.ContainsAny,
                        CustomColumnId = (string)null,
                        FilterOnValues = new object[] {"{\"Id\":\"" + labelIds[0] + "\"}"},
                    }
                },
                currentCustomerId: cInfo.CustomerId);

            Assert.AreEqual(1, result["@odata.count"].Value);
            labelsAssociated = result["value"][0]["Labels"];
            Assert.AreEqual(2, ((JArray)labelsAssociated).Count);
        }

        [TestMethod]
        public void GetCampaignsGridData_SeasonalityAdjustmentFilter()
        {
            // Case 1: campaign associations
            var adjustmentData = SeasonalityAdjustmentHelper.GetSeasonalityAdjustmentData(
                0,
                "SeasonalityAdjustment_CampaignFilter",
                "",
                25.50,
                new DateTime(2020, 07, 01, 15, 00, 00),
                new DateTime(2020, 07, 07, 16, 00, 00),
                new string[] { DeviceType.Computers.ToString(), DeviceType.Smartphones.ToString() },
                SeasonalityAdjustment,
                campaignAssociations: new[]
                {
                    new
                    {
                        CampaignId = this.campaignCollection.Ids[0]
                    },
                    new
                    {
                        CampaignId = this.campaignCollection.Ids[1]
                    }
                }
             );

            var adjustmentResult = SeasonalityAdjustmentHelper.CreateSeasonalityAdjustment(
                this.cInfo,
                adjustmentData);

            long adjustmentId = (long)adjustmentResult.value.Value;

            string filter = $"SeasonalityAdjustmentId eq {adjustmentId}";

            var result = CallGetCampaignsGridData(this.cInfo, today, queryFilter: filter, select: this.GetSelect(AllSelectNonBiProperties));

            var expectedCampaignIds = new List<long>() { this.campaignCollection.Ids[0], this.campaignCollection.Ids[1] };
            expectedCampaignIds.Sort();

            List<TestCampaign> expectedResults = new List<TestCampaign>() {
                this.campaignCollection.Campaigns[0],
                this.campaignCollection.Campaigns[1]
            };

            VerifyGridData(result, expectedResults.OrderBy(c => c.Data.Id).ToArray(), AllSelectNonBiProperties);

            // Case 2: campaign types
            adjustmentData = SeasonalityAdjustmentHelper.GetSeasonalityAdjustmentData(
                0,
                "SeasonalityAdjustment_CampaignFilter",
                "",
                25.50,
                new DateTime(2020, 07, 01, 15, 00, 00),
                new DateTime(2020, 07, 07, 16, 00, 00),
                new string[] { DeviceType.Computers.ToString(), DeviceType.Smartphones.ToString() },
                SeasonalityAdjustment,
                campaignTypeFilter: new string[] { CampaignType.Default.ToString() }
             );

            adjustmentResult = SeasonalityAdjustmentHelper.CreateSeasonalityAdjustment(
                this.cInfo,
                adjustmentData);

            adjustmentId = (long)adjustmentResult.value.Value;

            filter = $"SeasonalityAdjustmentId eq {adjustmentId}";

            result = CallGetCampaignsGridData(this.cInfo, today, queryFilter: filter, select: this.GetSelect(AllSelectNonBiProperties));

            VerifyGridData(result, new TestCampaign[] { }, AllSelectNonBiProperties);

            // Case 3: invalid seasonality adjustment id

            result = CallGetCampaignsGridData(this.cInfo, today, queryFilter: "SeasonalityAdjustmentId eq 0", select: this.GetSelect(AllSelectNonBiProperties));

            VerifyGridData(result, new TestCampaign[] { }, AllSelectNonBiProperties);
        }

        [TestMethod]
        public void GetCampaignsGridData_CampaignLevelDates()
        {
            var customer = CustomerInfo.CreateStandardAdvertiserWithPilot(1535);

            TestCampaignCollection.Clean(customer);
            var campaignCollection = new TestCampaignCollection(2);

            DateTime startDateAtCreation = DateTime.UtcNow.AddMonths(1);
            DateTime[] endDatesAtCreation = { DateTime.UtcNow.AddMonths(2), new DateTime(2050, 1, 1, 23, 59, 59) };
            DateTime?[] expectedEndDates = { DateTime.UtcNow.AddMonths(2), null };
            SimplifiedBudgetLimitType[] expectedBudgetTypes = { SimplifiedBudgetLimitType.LifetimeBudgetStandard, SimplifiedBudgetLimitType.DailyBudgetStandard };

            TestCampaignCollection.SetupLifetimeCampaign(campaignCollection.Campaigns[0], startDateAtCreation, endDatesAtCreation[0], SimplifiedBudgetLimitType.LifetimeBudgetStandard, true);
            TestCampaignCollection.SetupLifetimeCampaign(campaignCollection.Campaigns[1], startDateAtCreation, endDatesAtCreation[1], SimplifiedBudgetLimitType.DailyBudgetStandard, true);

            var response = campaignCollection.Add(customer, customer.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(response);

            var select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            var result = CallGetCampaignsGridData(customer, select: this.GetSelect(select), expand: "Budget");
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var actualStartDate = DateTime.Parse(actualRow.StartDate.ToString());
                var actualEndDate = (DateTime?)actualRow.EndDate != null ? DateTime.Parse(actualRow.EndDate.ToString()) : null;
                bool useCampaignLevelDates = (bool)actualRow.UseCampaignLevelDates;

                Assert.IsTrue(useCampaignLevelDates);
                Assert.AreEqual(startDateAtCreation.Date, actualStartDate.Date);
                if (expectedEndDates[i] == null)
                {
                    Assert.IsNull(actualEndDate);
                }
                else
                {
                    Assert.AreEqual(expectedEndDates[i]?.Date, actualEndDate.Date);
                }
                Assert.AreEqual(mtToODataBudgetType[expectedBudgetTypes[i]], actualRow.Budget.Type.ToString(), "Budget/Type mismatch");
            }
        }

        [TestMethod]
        public void GetCampaignsGridData_LifetimeBudget_CampaignExpiredStatus()
        {
            var customer = CustomerInfo.CreateStandardAdvertiserWithPilot(1535);

            TestCampaignCollection.Clean(customer);
            var campaignCollection = new TestCampaignCollection(3);

            SimplifiedBudgetLimitType[] expectedBudgetTypes = { SimplifiedBudgetLimitType.LifetimeBudgetStandard, SimplifiedBudgetLimitType.DailyBudgetStandard };

            TestCampaignCollection.SetupLifetimeCampaign(campaignCollection.Campaigns[0], DateTime.Now, DateTime.Now.AddDays(10), SimplifiedBudgetLimitType.LifetimeBudgetStandard, true);
            TestCampaignCollection.SetupLifetimeCampaign(campaignCollection.Campaigns[1], DateTime.Now, DateTime.Now.AddDays(10), SimplifiedBudgetLimitType.DailyBudgetStandard, true);
            TestCampaignCollection.SetupLifetimeCampaign(campaignCollection.Campaigns[2], DateTime.Now, DateTime.Now.AddDays(10), SimplifiedBudgetLimitType.DailyBudgetStandard, false);

            var response = campaignCollection.Add(customer, customer.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(response);

            var startDateString = DateTime.Now.AddDays(-50).ToString("yyyy-MM-dd");
            var endDateString = DateTime.Now.AddDays(-10).ToString("yyyy-MM-dd");
            var query = $"UPDATE Campaign SET StartDate = '{startDateString}', EndDate = '{endDateString}' WHERE CampaignId in({campaignCollection.Ids[0]}, {campaignCollection.Ids[1]})";
            TestSetting.Environment.CampaignService.CampaignDB.ExecuteSqlQuery(cInfo.CustomerId, query, CommandType.Text, out DataSet mainShardResult);

            var select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate", "DeliveryStatus" };
            var result = CallGetCampaignsGridData(customer, select: this.GetSelect(select), expand: "Budget");
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var deliveryStatus = (string)actualRow.DeliveryStatus;
                if ((long)actualRow.Id == campaignCollection.Ids[0])
                {
                    Assert.IsTrue(deliveryStatus == "CampaignExpired");
                    Assert.IsTrue((bool)actualRow.UseCampaignLevelDates);
                }
                else if ((long)actualRow.Id == campaignCollection.Ids[1])
                {
                    Assert.IsTrue(deliveryStatus == "CampaignExpired");
                    Assert.IsTrue((bool)actualRow.UseCampaignLevelDates);
                }
                else
                {
                    Assert.IsTrue(deliveryStatus == "Eligible");
                    Assert.IsFalse((bool)actualRow.UseCampaignLevelDates);
                }

            }

            string orderBy = "DeliveryStatus desc";
            result = CallGetCampaignsGridData(customer, select: this.GetSelect(select), orderBy: orderBy, expand: "Budget");
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var deliveryStatus = (string)actualRow.DeliveryStatus;
                if (i == 0)
                {
                    Assert.IsTrue(deliveryStatus == "Eligible");
                    Assert.IsFalse((bool)actualRow.UseCampaignLevelDates);
                }
                else if (i == 1)
                {
                    Assert.IsTrue(deliveryStatus == "CampaignExpired");
                    Assert.IsTrue((bool)actualRow.UseCampaignLevelDates);
                }
                else
                {
                    Assert.IsTrue(deliveryStatus == "CampaignExpired");
                    Assert.IsTrue((bool)actualRow.UseCampaignLevelDates);
                }

            }

            var filterBy = "DeliveryStatus eq Enum.DeliveryStatus'CampaignExpired'";
            result = CallGetCampaignsGridData(customer, select: this.GetSelect(select), queryFilter: filterBy, expand: "Budget");
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var deliveryStatus = (string)actualRow.DeliveryStatus;
                Assert.IsTrue(deliveryStatus == "CampaignExpired");
            }
        }

        [TestMethod]
        public void GetCampaignsGridData_LifetimeBudget_SortingAndFiltering_Success()
        {
            var customer = CustomerInfo.CreateStandardAdvertiserWithPilot(1535);

            TestCampaignCollection.Clean(customer);
            var campaignCollection = new TestCampaignCollection(7);

            DateTime expectedStartDate = DateTime.UtcNow.AddMonths(1);
            DateTime expectedEndDate = DateTime.UtcNow.AddMonths(2);

            TestCampaignCollection.SetupLifetimeCampaign(campaignCollection.Campaigns[0], expectedStartDate.AddDays(1), expectedEndDate, SimplifiedBudgetLimitType.LifetimeBudgetStandard, true, budgetAmt: 1200);
            TestCampaignCollection.SetupLifetimeCampaign(campaignCollection.Campaigns[1], expectedStartDate.AddDays(2), expectedEndDate.AddDays(15), SimplifiedBudgetLimitType.LifetimeBudgetStandard, true, budgetAmt: 1500);
            TestCampaignCollection.SetupLifetimeCampaign(campaignCollection.Campaigns[2], expectedStartDate.AddDays(3), expectedEndDate.AddDays(10), SimplifiedBudgetLimitType.LifetimeBudgetStandard, true, budgetAmt: 2000);

            campaignCollection.Campaigns[3].Data.DailyTargetBudgetAmount = 500;
            campaignCollection.Campaigns[3].Data.EndDate = DateTime.UtcNow.AddMonths(11);

            campaignCollection.Campaigns[4].Data.DailyTargetBudgetAmount = 600;

            campaignCollection.Campaigns[5].Data.DailyTargetBudgetAmount = 300;
            campaignCollection.Campaigns[5].Data.EndDate = new DateTime(3000, 01, 01, 23, 59, 59);

            TestCampaignCollection.SetupLifetimeCampaign(campaignCollection.Campaigns[6], expectedStartDate.AddDays(4), expectedEndDate.AddDays(20), SimplifiedBudgetLimitType.LifetimeBudgetStandard, true, budgetAmt: 2000);

            var response = campaignCollection.Add(customer, customer.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(response);

            var select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            var result = CallGetCampaignsGridData(customer, select: this.GetSelect(select));

            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                if ((string)actualRow.Budget.Type == "LifetimeBudgetStandard")
                {
                    Assert.IsNotNull(actualRow.Budget.LifetimeBudgetAmount);
                    Assert.IsTrue((double)actualRow.Budget.LifetimeBudgetAmount == (double)actualRow.Budget.Amount);
                    var campaignId = (long)actualRow.Id;
                    if (campaignId == campaignCollection.Ids[0])
                    {
                        Assert.AreEqual((double)actualRow.Budget.LifetimeBudgetAmount, 1200);
                    }
                    else if (campaignId == campaignCollection.Ids[1])
                    {
                        Assert.AreEqual((double)actualRow.Budget.LifetimeBudgetAmount, 1500);
                    }
                    else if (campaignId == campaignCollection.Ids[2])
                    {
                        Assert.AreEqual((double)actualRow.Budget.LifetimeBudgetAmount, 2000);
                    }
                }
            }

            select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            string orderBy = "Budget/LifetimeBudgetAmount desc";
            result = CallGetCampaignsGridData(customer, orderBy: orderBy, select: this.GetSelect(select));
            Assert.AreEqual((long)result.value[0].Id, campaignCollection.Ids[6]);
            Assert.AreEqual((double)result.value[0].Budget.LifetimeBudgetAmount, 2000);

            Assert.AreEqual((long)result.value[1].Id, campaignCollection.Ids[2]);
            Assert.AreEqual((double)result.value[1].Budget.LifetimeBudgetAmount, 2000);

            Assert.AreEqual((long)result.value[2].Id, campaignCollection.Ids[1]);
            Assert.AreEqual((double)result.value[2].Budget.LifetimeBudgetAmount, 1500);

            Assert.AreEqual((long)result.value[3].Id, campaignCollection.Ids[0]);
            Assert.AreEqual((double)result.value[3].Budget.LifetimeBudgetAmount, 1200);

            select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            orderBy = "Budget/LifetimeBudgetAmount asc";
            result = CallGetCampaignsGridData(customer, orderBy: orderBy, select: this.GetSelect(select));
            Assert.AreEqual((long)result.value[0].Id, campaignCollection.Ids[0]);
            Assert.AreEqual((double)result.value[0].Budget.LifetimeBudgetAmount, 1200);

            Assert.AreEqual((long)result.value[1].Id, campaignCollection.Ids[1]);
            Assert.AreEqual((double)result.value[1].Budget.LifetimeBudgetAmount, 1500);

            Assert.AreEqual((long)result.value[2].Id, campaignCollection.Ids[2]);
            Assert.AreEqual((double)result.value[2].Budget.LifetimeBudgetAmount, 2000);

            Assert.AreEqual((long)result.value[3].Id, campaignCollection.Ids[6]);
            Assert.AreEqual((double)result.value[3].Budget.LifetimeBudgetAmount, 2000);

            // sort: on daily budget.
            select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            orderBy = "Budget/Amount desc";
            result = CallGetCampaignsGridData(customer, orderBy: orderBy, select: this.GetSelect(select));
            Assert.AreEqual((long)result.value[0].Id, campaignCollection.Ids[4]);
            Assert.AreEqual((double)result.value[0].Budget.Amount, 600);

            Assert.AreEqual((long)result.value[1].Id, campaignCollection.Ids[3]);
            Assert.AreEqual((double)result.value[1].Budget.Amount, 500);

            Assert.AreEqual((long)result.value[2].Id, campaignCollection.Ids[5]);
            Assert.AreEqual((double)result.value[2].Budget.Amount, 300);

            select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            orderBy = "Budget/Amount asc";
            result = CallGetCampaignsGridData(customer, orderBy: orderBy, select: this.GetSelect(select));
            Assert.AreEqual((long)result.value[0].Id, campaignCollection.Ids[5]);
            Assert.AreEqual((double)result.value[0].Budget.Amount, 300);

            Assert.AreEqual((long)result.value[1].Id, campaignCollection.Ids[3]);
            Assert.AreEqual((double)result.value[1].Budget.Amount, 500);

            Assert.AreEqual((long)result.value[2].Id, campaignCollection.Ids[4]);
            Assert.AreEqual((double)result.value[2].Budget.Amount, 600);

            // sort: on end date.
            select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            orderBy = "EndDate desc";
            result = CallGetCampaignsGridData(customer, orderBy: orderBy, select: this.GetSelect(select));
            Assert.AreEqual((long)result.value[0].Id, campaignCollection.Ids[6]);
            Assert.AreEqual((long)result.value[1].Id, campaignCollection.Ids[1]);
            Assert.AreEqual((long)result.value[2].Id, campaignCollection.Ids[2]);
            Assert.AreEqual((long)result.value[3].Id, campaignCollection.Ids[0]);

            select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            orderBy = "EndDate asc";
            result = CallGetCampaignsGridData(customer, orderBy: orderBy, select: this.GetSelect(select));
            Assert.AreEqual((long)result.value[0].Id, campaignCollection.Ids[0]);
            Assert.AreEqual((long)result.value[1].Id, campaignCollection.Ids[2]);
            Assert.AreEqual((long)result.value[2].Id, campaignCollection.Ids[1]);
            Assert.AreEqual((long)result.value[3].Id, campaignCollection.Ids[6]);
            Assert.AreEqual((long)result.value[4].Id, campaignCollection.Ids[3]);

            select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            orderBy = "StartDate asc";
            result = CallGetCampaignsGridData(customer, orderBy: orderBy, select: this.GetSelect(select));
            Assert.AreEqual((long)result.value[0].Id, campaignCollection.Ids[0]);
            Assert.AreEqual((long)result.value[1].Id, campaignCollection.Ids[1]);
            Assert.AreEqual((long)result.value[2].Id, campaignCollection.Ids[2]);
            Assert.AreEqual((long)result.value[3].Id, campaignCollection.Ids[6]);

            select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            orderBy = "StartDate desc";
            result = CallGetCampaignsGridData(customer, orderBy: orderBy, select: this.GetSelect(select));
            Assert.AreEqual((long)result.value[0].Id, campaignCollection.Ids[6]);
            Assert.AreEqual((long)result.value[1].Id, campaignCollection.Ids[2]);
            Assert.AreEqual((long)result.value[2].Id, campaignCollection.Ids[1]);
            Assert.AreEqual((long)result.value[3].Id, campaignCollection.Ids[0]);

            // filter: on budget.
            select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            var filterBy = "Budget/LifetimeBudgetAmount eq 2000";
            result = CallGetCampaignsGridData(customer, queryFilter: filterBy, select: this.GetSelect(select));
            Assert.AreEqual((int)result.value.Count, 2);
            Assert.AreEqual((double)result.value[0].Budget.LifetimeBudgetAmount, 2000);
            Assert.AreEqual((double)result.value[1].Budget.LifetimeBudgetAmount, 2000);
            Assert.AreEqual((double)result["@ns.unfiltered.totals"].Budget.LifetimeBudget, 6700);
            Assert.AreEqual((double)result["@ns.unfiltered.totals"].Budget.DailyBudget, 1400);
            Assert.AreEqual((double)result["@ns.filtered.totals"].Budget.LifetimeBudget, 4000);

            // filter: on budget.
            select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            filterBy = "Budget/LifetimeBudgetAmount eq 1500";
            result = CallGetCampaignsGridData(customer, queryFilter: filterBy, select: this.GetSelect(select));
            Assert.AreEqual((int)result.value.Count, 1);
            Assert.AreEqual((long)result.value[0].Id, campaignCollection.Ids[1]);
            Assert.AreEqual((double)result.value[0].Budget.LifetimeBudgetAmount, 1500);
            Assert.AreEqual((double)result["@ns.filtered.totals"].Budget.LifetimeBudget, 1500);
            Assert.AreEqual((double)result["@ns.unfiltered.totals"].Budget.LifetimeBudget, 6700);
            Assert.AreEqual((double)result["@ns.unfiltered.totals"].Budget.DailyBudget, 1400);

            // filter: on budgetType.
            select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            filterBy = "Budget/Type eq Enum.BudgetType'LifetimeBudgetStandard'";
            result = CallGetCampaignsGridData(customer, queryFilter: filterBy, select: this.GetSelect(select));
            Assert.AreEqual((int)result.value.Count, 4);
            Assert.IsTrue((string)result.value[0].Budget.Type == "LifetimeBudgetStandard" && (string)result.value[1].Budget.Type == "LifetimeBudgetStandard" &&
                (string)result.value[2].Budget.Type == "LifetimeBudgetStandard" && (string)result.value[3].Budget.Type == "LifetimeBudgetStandard");


            select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            filterBy = "Budget/LifetimeBudgetAmount ge 1500";
            result = CallGetCampaignsGridData(customer, queryFilter: filterBy, select: this.GetSelect(select));
            Assert.AreEqual((int)result.value.Count, 3);
            Assert.AreEqual((double)result["@ns.filtered.totals"].Budget.LifetimeBudget, 5500);
            Assert.AreEqual((double)result["@ns.unfiltered.totals"].Budget.LifetimeBudget, 6700);
            Assert.AreEqual((double)result["@ns.filtered.totals"].Budget.DailyBudget, 0);
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                Assert.AreNotEqual((string)actualRow.Budget.Type, "DailyBudgetStandard");
            }

            select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            filterBy = "Budget/Amount lt 500";
            result = CallGetCampaignsGridData(customer, queryFilter: filterBy, select: this.GetSelect(select));
            Assert.AreEqual((int)result.value.Count, 1);
            Assert.AreEqual((double)result["@ns.filtered.totals"].Budget.LifetimeBudget, 0);
            Assert.AreEqual((double)result["@ns.unfiltered.totals"].Budget.LifetimeBudget, 6700);
            Assert.AreEqual((double)result["@ns.filtered.totals"].Budget.DailyBudget, 300);
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                Assert.AreNotEqual((string)actualRow.Budget.Type, "LifetimeBudgetStandard");
            }

            // filter and sorting
            select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            filterBy = "Budget/Type eq Enum.BudgetType'LifetimeBudgetStandard'";
            orderBy = "Budget/LifetimeBudgetAmount desc";
            result = CallGetCampaignsGridData(customer, orderBy: orderBy, queryFilter: filterBy, select: this.GetSelect(select));
            Assert.AreEqual((int)result.value.Count, 4);
            Assert.IsTrue((string)result.value[0].Budget.Type == "LifetimeBudgetStandard" && (string)result.value[1].Budget.Type == "LifetimeBudgetStandard" &&
                (string)result.value[2].Budget.Type == "LifetimeBudgetStandard" && (string)result.value[3].Budget.Type == "LifetimeBudgetStandard");
            Assert.AreEqual((double)result.value[0].Budget.LifetimeBudgetAmount, 2000);
            Assert.AreEqual((double)result.value[1].Budget.LifetimeBudgetAmount, 2000);
            Assert.AreEqual((double)result.value[2].Budget.LifetimeBudgetAmount, 1500);
            Assert.AreEqual((double)result.value[3].Budget.LifetimeBudgetAmount, 1200);

        }

        [TestMethod]
        public void GetCampaignsGridData_CampaignLevelDates_DefaultValues()
        {
            var customer = CustomerInfo.CreateStandardAdvertiserWithPilot(1535);

            TestCampaignCollection.Clean(customer);
            var campaignCollection = new TestCampaignCollection(1);

            DateTime expectedStartDate = DateTime.UtcNow.AddMonths(1);

            TestCampaignCollection.SetupLifetimeCampaign(campaignCollection.Campaigns[0], expectedStartDate);

            var response = campaignCollection.Add(customer, customer.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(response);

            var select = new string[] { "Id", "UseCampaignLevelDates", "StartDate", "EndDate" };
            var result = CallGetCampaignsGridData(customer, select: this.GetSelect(select), expand: "Budget");
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var actualStartDate = DateTime.Parse(actualRow.StartDate.ToString());
                bool useCampaignLevelDates = (bool)actualRow.UseCampaignLevelDates;

                Assert.IsFalse(useCampaignLevelDates);
                Assert.AreEqual(expectedStartDate.Date, actualStartDate.Date);
                Assert.IsNull((string)actualRow.EndDate);
                Assert.AreEqual(mtToODataBudgetType[SimplifiedBudgetLimitType.DailyBudgetStandard], actualRow.Budget.Type.ToString(), "Budget/Type mismatch");
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.LinkedInCampaigns)]
        [DataRow("'[0]'", 51, "Eligible")]
        [DataRow("'[1]'", 51, "LinkedInAccountServingHold")]
        [DataRow("0", 52, "Eligible")]
        [DataRow("3", 52, "LinkedInCompanyPagePaidMediaRequired")]
        [SkipInit]
        public void GetCampaignsGridData_LinkedInCampaign_CompanyPagePermissionStatus(string mockCompanyPagePermissionStatus, int accountPropertyId, string expectedCampaignDeliveryStatus)
        {
            var customer = CustomerInfo.CreateStandardAdvertiserWithPilot(1425);
            TestCampaignCollection.Clean(customer);

            var campaignCollection = new TestCampaignCollection(2);
            TestCampaignCollection.UpdateLinkedInCampaign(campaignCollection.Campaigns[0]);

            var response = campaignCollection.Add(customer, customer.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(response);

            // insert mock CompanyPagePermissionStatus
            DatabaseHelper.CreateOrUpdateAccountProperty(customer.CustomerId, customer.AccountIds[0], accountPropertyId, mockCompanyPagePermissionStatus);
            var select = new string[] { "Id", "CampaignDeliveryStatusDetails", "DeliveryStatus", "DeliveryStatusDetails" };
            var result = CallGetCampaignsGridData(customer, select: this.GetSelect(select));

            Assert.AreEqual(2, result.value.Count);
            for (int i = 0; i < result.value.Count; i++)
            {
                var deliveryStatus = result.value[i].Id.ToString() == campaignCollection.Campaigns[0].Data.Id.ToString() ? expectedCampaignDeliveryStatus : "Eligible";
                Assert.AreEqual(deliveryStatus, result.value[i].DeliveryStatus.ToString());
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.LinkedInCampaigns)]
        [DataRow("'[0]'", "Eligible")]
        [DataRow("'[1]'", "Eligible")]
        [DataRow("'[2]'", "AudienceTooNarrow")]
        [DataRow("'[1,2]'", "AudienceTooNarrow")]
        [SkipInit]
        public void GetCampaignsGridData_LinkedInCampaign_CampaignExternalDeliveryStatus(string mockCampaignExternalDeliveryStatus, string expectedCampaignDeliveryStatus)
        {
            var customer = CustomerInfo.CreateStandardAdvertiserWithPilot(1425);
            TestCampaignCollection.Clean(customer);

            var campaignCollection = new TestCampaignCollection(2);
            TestCampaignCollection.UpdateLinkedInCampaign(campaignCollection.Campaigns[0]);

            var response = campaignCollection.Add(customer, customer.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(response);

            // insert mock campaign ExternalDeliveryStatus
            DatabaseHelper.CreateOrUpdateCampaignSettingAssociation(customer.CustomerId, customer.AccountIds[0], campaignCollection.Campaigns[0].Data.Id, CampaignExternalDeliveryStatusPropertyTypeId, mockCampaignExternalDeliveryStatus);
            var select = new string[] { "Id", "CampaignDeliveryStatusDetails", "DeliveryStatus", "DeliveryStatusDetails" };
            var result = CallGetCampaignsGridData(customer, select: this.GetSelect(select));

            Assert.AreEqual(2, result.value.Count);
            for (int i = 0; i < result.value.Count; i++)
            {
                var deliveryStatus = result.value[i].Id.ToString() == campaignCollection.Campaigns[0].Data.Id.ToString() ? expectedCampaignDeliveryStatus : "Eligible";
                Assert.AreEqual(deliveryStatus, result.value[i].DeliveryStatus.ToString());
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        public void GetCampaignsGridData_GoalStatus_BVT(int[] pilotFeatures)
        {
            var select = new string[] { "Id", "GoalStatusDetails" };
            var result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var goalStatusDetails = actualRow.GoalStatusDetails;
                Assert.AreEqual(1, goalStatusDetails.Count);
                Assert.AreEqual("NoEligibleGoal", actualRow.GoalStatusDetails[0].GoalDeliveryStatus.ToString());
            }

            // Account level goal
            (long unverifiedTagId, long unverifiedGoalId) = ReportDownloadE2ETests.CreateConversionGoal(this.cInfo);

            // Inactive tag associated with one campaign
            (long inactiveTagId, long inactiveGoalId) = ReportDownloadE2ETests.CreateConversionGoal(this.cInfo);
            DatabaseHelper.SetUETTagStatus(this.cInfo, inactiveTagId, 2); // 2: tag inactive
            var inactiveTagCampaignId = campaignCollection.Campaigns[0].Data.Id;
            ReportDownloadE2ETests.CreateCampaignConversionGoal(this.cInfo, inactiveTagCampaignId, inactiveGoalId);

            // Active tag associated with one campaign
            (long activeTagId, long activeGoalId) = ReportDownloadE2ETests.CreateConversionGoal(this.cInfo, isMainGoal: false);
            DatabaseHelper.SetUETTagStatus(this.cInfo, activeTagId, 1); // 1: tag active
            var activeTagCampaignId = campaignCollection.Campaigns[1].Data.Id;
            ReportDownloadE2ETests.CreateCampaignConversionGoal(this.cInfo, activeTagCampaignId, activeGoalId);
            // Even the campaign has an inactive goal, it should still use the active goal for status
            ReportDownloadE2ETests.CreateCampaignConversionGoal(this.cInfo, activeTagCampaignId, inactiveGoalId);

            result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var goalStatusDetails = actualRow.GoalStatusDetails;

                if (actualRow.Id == activeTagCampaignId)
                {
                    Assert.AreEqual(0, goalStatusDetails.Count);
                    continue;
                }

                Assert.AreEqual(1, goalStatusDetails.Count);

                //if (actualRow.Id == inactiveTagCampaignId)
                //{
                Assert.AreEqual("TagInactive", actualRow.GoalStatusDetails[0].GoalDeliveryStatus.ToString()); // TagInActive has higher priority than TagUnverified in V2.
                //}
                //else
                //{
                //    Assert.AreEqual("TagUnverified", actualRow.GoalStatusDetails[0].GoalDeliveryStatus.ToString());
                //}
            }
        }

        [TestMethod, Priority(0)]
        [Owner(TestOwners.ODataTests)]
        [DataRow(new int[] { })]
        public void GetCampaignsGridData_GoalStatus_BVT_NoRecentOfflineConversion(int[] pilotFeatures)
        {
            var select = new string[] { "Id", "GoalStatusDetails" };
            var result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var goalStatusDetails = actualRow.GoalStatusDetails;
                Assert.AreEqual(1, goalStatusDetails.Count);
                Assert.AreEqual("NoEligibleGoal", actualRow.GoalStatusDetails[0].GoalDeliveryStatus.ToString());
            }

            // Account level goal
            (long unverifiedTagId, long unverifiedGoalId) = ReportDownloadE2ETests.CreateConversionGoal(this.cInfo);

            // Inactive tag associated with one campaign
            (long inactiveTagId, long inactiveGoalId) = ReportDownloadE2ETests.CreateConversionGoal(this.cInfo);
            DatabaseHelper.SetUETTagStatus(this.cInfo, inactiveTagId, 2); // 2: tag inactive
            var inactiveTagCampaignId = campaignCollection.Campaigns[0].Data.Id;
            ReportDownloadE2ETests.CreateCampaignConversionGoal(this.cInfo, inactiveTagCampaignId, inactiveGoalId);

            // Active tag associated with one campaign
            (long activeTagId, long activeGoalId) = ReportDownloadE2ETests.CreateConversionGoal(this.cInfo, isMainGoal: false);
            DatabaseHelper.SetUETTagStatus(this.cInfo, activeTagId, 1); // 1: tag active
            var activeTagCampaignId = campaignCollection.Campaigns[1].Data.Id;
            ReportDownloadE2ETests.CreateCampaignConversionGoal(this.cInfo, activeTagCampaignId, activeGoalId);
            // Even the campaign has an inactive goal, it should still use the active goal for status
            ReportDownloadE2ETests.CreateCampaignConversionGoal(this.cInfo, activeTagCampaignId, inactiveGoalId);

            //NoRecentConversion Offline Goal.
            long offlineGoalId = ReportDownloadE2ETests.CreateOfflineConversionGoal(this.cInfo, isMainGoal: false);
            var noRecentOfflineConversionCampaignId = campaignCollection.Campaigns[2].Data.Id;
            ReportDownloadE2ETests.CreateCampaignConversionGoal(this.cInfo, noRecentOfflineConversionCampaignId, offlineGoalId);

            result = CallGetCampaignsGridData(this.cInfo, select: this.GetSelect(select));
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var goalStatusDetails = actualRow.GoalStatusDetails;

                if (actualRow.Id == activeTagCampaignId)
                {
                    Assert.AreEqual(0, goalStatusDetails.Count);
                    continue;
                }

                Assert.AreEqual(1, goalStatusDetails.Count);

                if (actualRow.Id == inactiveTagCampaignId)
                {
                    Assert.AreEqual("TagInactive", actualRow.GoalStatusDetails[0].GoalDeliveryStatus.ToString());
                }
                else if (actualRow.Id == noRecentOfflineConversionCampaignId)
                {
                    Assert.AreEqual("NoRecentOfflineConversion", actualRow.GoalStatusDetails[0].GoalDeliveryStatus.ToString());
                }
                else
                {
                    Assert.AreEqual("TagInactive", actualRow.GoalStatusDetails[0].GoalDeliveryStatus.ToString()); // TagInactive has higher priority than TagUnverified in V2.
                }

            }
        }

        private Dictionary<long, List<FluctuationType>> AddFluctuations(IList<TestCampaign> campaigns)
        {
            var campaignInsights = campaigns.ToDictionary(c => c.Data.Id, c => PerformanceFluctuationTestHelper.MockFluctuationTypes(c.Data.Id, false));

            PerformanceFluctuationTestHelper.AddCampaignFluctuations(cInfo.AccountIds[0], campaignInsights);
            PerformanceFluctuationTestHelper.AddAccountToCampaignsMapping(cInfo.AccountIds[0], campaignInsights.Keys.ToList());

            return campaignInsights;
        }

        private List<FluctuationType> AddAccountFluctuation()
        {
            var accountInsights = new List<FluctuationType>() { FluctuationType.Conversion, FluctuationType.Click };

            PerformanceFluctuationTestHelper.AddAccountFluctuations(new Dictionary<long, List<FluctuationType>>()
            {
                { cInfo.AccountIds[0], accountInsights }
            });

            return accountInsights;
        }

        public static void VerifyTotalPerformanceForPartialConversion(dynamic result, TestCampaign[] expectedResults, Dictionary<long, BiData> biData)
        {
            var ConversionCreditArr = new List<float>();
            var AllConversionCreditArr = new List<float>();
            var ViewthroughConversionCreditArr = new List<float>();
            expectedResults.ForEach(x => ConversionCreditArr.Add(biData[x.Data.Id].ConversionsCredit ?? 0));
            expectedResults.ForEach(x => AllConversionCreditArr.Add(biData[x.Data.Id].AllConversionsCredit ?? 0));
            expectedResults.ForEach(x => ViewthroughConversionCreditArr.Add(biData[x.Data.Id].ViewThroughConversionsCredit ?? 0));
            Assert.AreEqual(Math.Round(ConversionCreditArr.Sum(), 2), result["@ns.unfiltered.totals"]["PerformanceMetrics"]["ConversionsCredit"].Value ?? 0, "mismatch ConversionsCredit in unfiltered.totals");
            Assert.AreEqual(Math.Round(AllConversionCreditArr.Sum(), 2), result["@ns.unfiltered.totals"]["PerformanceMetrics"]["AllConversionsCredit"].Value ?? 0, "mismatch AllConversionsCredit in unfiltered.totals");
            Assert.AreEqual(Math.Round(ViewthroughConversionCreditArr.Sum(), 2), result["@ns.unfiltered.totals"]["PerformanceMetrics"]["ViewThroughConversionsCredit"].Value ?? 0, "mismatch ViewThroughConversionsCredit in unfiltered.totals");
            Assert.AreEqual(Math.Round(ConversionCreditArr.Sum(), 2), result["@ns.search.non.deleted.totals"]["PerformanceMetrics"]["ConversionsCredit"].Value ?? 0, "mismatch ConversionsCredit in search.totals");
            Assert.AreEqual(Math.Round(AllConversionCreditArr.Sum(), 2), result["@ns.search.non.deleted.totals"]["PerformanceMetrics"]["AllConversionsCredit"].Value ?? 0, "mismatch AllConversionsCredit in search.totals");
            Assert.AreEqual(Math.Round(ViewthroughConversionCreditArr.Sum(), 2), result["@ns.search.non.deleted.totals"]["PerformanceMetrics"]["ViewThroughConversionsCredit"].Value ?? 0, "mismatch ViewThroughConversionsCredit in search.totals");
        }

        public static void VerifyGridData(
            dynamic result,
            TestCampaign[] expectedResults,
            string[] selectedColumns,
            Dictionary<long, BiData> biDataByCampaignId = null,
            Dictionary<long, BiData> comparisonBiDataByCampaignId = null,
            Dictionary<long, Dictionary<string, BiData>> segmentedBiDataByCampaignId = null,
            Dictionary<long, Dictionary<string, BiData>> comparisonSegmentedBiDataByCampaignId = null,
            Dictionary<long, BiData> projectedBiDataByCampaignId = null,
            long campaignIdForRSAAdCustomizerAttribute = -1,
            bool useConversionCredit = false,
            bool partialConversionEnabled = false,
            bool useCvrl = false)
        {
            Assert.IsNotNull(result?.value, "Value is null.");
            Assert.AreEqual(expectedResults.Length, result.value.Count, "Row count mismatch");
            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var expectedRow = expectedResults[i];

                foreach (var selectedColumn in selectedColumns)
                {
                    var expectedValue = GetEntityValue(selectedColumn, expectedRow);

                    if (expectedValue != null)
                    {
                        var actualValue = actualRow[selectedColumn].ToString();
                        Assert.AreEqual(expectedValue, actualValue, $"Mismatch on row {i + 1}, on column {selectedColumn}");
                    }
                }

                if (biDataByCampaignId != null)
                {
                    VerifyGridBiData(actualRow, selectedColumns, biDataByCampaignId[expectedRow.Data.Id], useConversionCredit, partialConversionEnabled, useCvrl: useCvrl);
                }

                if (comparisonBiDataByCampaignId != null)
                {
                    VerifyComparisonGridBiData(actualRow, selectedColumns, biDataByCampaignId[expectedRow.Data.Id], comparisonBiDataByCampaignId[expectedRow.Data.Id]);
                }

                if (segmentedBiDataByCampaignId != null)
                {
                    VerifySegmentedBiData(actualRow, selectedColumns, segmentedBiDataByCampaignId[expectedRow.Data.Id]);
                }

                if (comparisonSegmentedBiDataByCampaignId != null)
                {
                    VerifyPeriodSegmentedBiData(actualRow, selectedColumns, comparisonSegmentedBiDataByCampaignId[expectedRow.Data.Id]);
                }

                if (projectedBiDataByCampaignId != null)
                {
                    VerifyGridBiData(actualRow, selectedColumns, projectedBiDataByCampaignId[expectedRow.Data.Id], useConversionCredit);
                }
                if (campaignIdForRSAAdCustomizerAttribute >= 0)
                {
                    var actualValue = actualRow["RSAAdCustomizerAttributes"];
                    if (expectedRow.Data.Id == campaignIdForRSAAdCustomizerAttribute)
                    {
                        RSAAdCustomizerAttributeTestHelper.CheckAdCustomizerAttributes1(actualValue, true);
                    }
                    else
                    {
                        RSAAdCustomizerAttributeTestHelper.CheckAdCustomizerAttributes2(actualValue);
                    }
                }
            }
        }

        internal void CreateCampaignsForCustomer(CustomerInfo customer, int count, string finalUrlSuffix = null)
        {
            this.campaignCollection = new TestCampaignCollection(count);
            campaignCollection.Campaigns.First().Data.TrackingTemplate = "http://www.trackingTemplate.com/url={lpurl}";
            campaignCollection.Campaigns.First().Data.FinalUrlSuffix = finalUrlSuffix;

            var response = this.campaignCollection.Add(customer, customer.AccountIds[0]);
            ResponseValidator.ValidateBasicSuccess(response);
        }

        private Dictionary<long, BiData> PopulateRandomBiData(
            DateTime date,
            bool clear = true,
            bool populateRelativeCTR = true,
            int? mediumId = null,
            int? advertisingChannelTypeId = null,
            SegmentationGoalType GoalType = SegmentationGoalType.Smart,
            CustomerInfo customerInfo = null,
            bool useConversionCredit = false,
            TestCampaignCollection campaignCollection = null,
            bool viewThroughEnabled = false,
            bool enablePredictiveTargetingForOne = false,
            bool enableCvrl = false,
            bool isAppCampaign = false,
            bool enableNewCustomer = false,
            byte targetTypeId = 27,
            int? conversionLag = null)
        {
            if (!mockReporting)
            {
                return null;
            }

            Random rand = new Random();

            CustomerInfo effectiveCInfo = customerInfo != null ? customerInfo : this.cInfo;
            TestCampaignCollection effectiveCampaignCollection = campaignCollection == null ? this.campaignCollection : campaignCollection;

            var biData = effectiveCampaignCollection.Campaigns.Select(a =>
            {
                var data = new BiData();
                data.AccountId = effectiveCInfo.AccountIds[0];
                data.CampaignId = a.Data.Id;
                data.Date = date;

                data.AudienceId = InMarketAudienceIds.First();
                data.AudienceName = "Giant";
                data.TargetTypeId = targetTypeId;

                data.GoalTypeId = (byte)GoalType;

                data.Clicks = rand.Next(100);
                data.Impressions = data.Clicks + rand.Next(1000);

                data.ClickCalls = rand.Next(100);
                data.PhoneImpressions = data.Clicks + rand.Next(1000);

                data.Spent = rand.NextDouble() * 100;

                data.ImpressionWonCount = data.Impressions;
                data.AuctionParticipation = data.Impressions * 4;

                data.CQualityScore = 6;

                data.ConversionEnabledSpent = Math.Round(rand.Next(868) + rand.NextDouble(), 2, MidpointRounding.AwayFromZero);
                data.ConversionEnabledClicks = rand.Next(998);

                data.SalesCount = rand.Next(1, 100);
                data.Installs = rand.Next(1, 100);
                data.AdvertiserReportedRevenue = rand.NextDouble() * 100;
                data.ConversionLag = conversionLag ?? rand.Next(0, 10);

                if (enablePredictiveTargetingForOne)
                {
                    data.TargetTypeId = 45;
                    data.AudienceId = PredictiveTargetingAudienceId;

                    enablePredictiveTargetingForOne = false;
                }

                if (useConversionCredit)
                {
                    data.ConversionsCredit = (float)Math.Round((rand.Next(168) + rand.NextDouble()), 2, MidpointRounding.AwayFromZero);
                    data.FullClickConversionsCredit = (float)Math.Round((double)(data.ConversionsCredit + rand.Next(200)), 2, MidpointRounding.AwayFromZero);
                    data.ViewThroughConversionsCredit = (float)Math.Round((rand.Next(452) + +rand.NextDouble()), 2, MidpointRounding.AwayFromZero);
                    data.TotalConversionsCredit = (float)Math.Round((double)(data.FullClickConversionsCredit + data.ViewThroughConversionsCredit), 2, MidpointRounding.AwayFromZero);
                    data.AllConversionsCredit = viewThroughEnabled ? data.TotalConversionsCredit : data.FullClickConversionsCredit;
                    data.ViewThroughConversionsCredit = viewThroughEnabled ? data.ViewThroughConversionsCredit : null;

                    //to make sure it won't be zero (should be changed after fix the bug in MT or DB)
                    while (data.AllConversionsCredit == 0)
                    {
                        data.ConversionsCredit = (float)Math.Round((rand.Next(168) + rand.NextDouble()), 2, MidpointRounding.AwayFromZero);
                        data.FullClickConversionsCredit = (float)Math.Round((double)(data.ConversionsCredit + rand.Next(200)), 2, MidpointRounding.AwayFromZero);
                        data.ViewThroughConversionsCredit = (float)Math.Round((rand.Next(452) + +rand.NextDouble()), 2, MidpointRounding.AwayFromZero);
                        data.TotalConversionsCredit = (float)Math.Round((double)(data.FullClickConversionsCredit + data.ViewThroughConversionsCredit), 2, MidpointRounding.AwayFromZero);
                        data.AllConversionsCredit = viewThroughEnabled ? data.TotalConversionsCredit : data.FullClickConversionsCredit;
                        data.ViewThroughConversionsCredit = viewThroughEnabled ? data.ViewThroughConversionsCredit : null;
                    }

                    data.Conversions = (long)data.ConversionsCredit;
                    data.FullClickConversions = (long)data.FullClickConversionsCredit;
                    data.AllConversions = (long)data.AllConversionsCredit;
                    data.ViewThroughConversions = viewThroughEnabled ? (long?)data.ViewThroughConversionsCredit : null;
                }
                else
                {
                    data.AllConversionsCredit = data.FullClickConversionsCredit = 0;
                    data.ViewThroughConversions = rand.Next(452);
                }

                data.AllConversionAdvertiserReportedRevenue = rand.Next(1, 222);

                data.ViewThroughConversionsRevenue = rand.NextDouble() * 100;

                data.AudienceAuctionParticipantCnt = 21 + rand.Next(10);
                data.AudienceAuctionWonCnt = rand.Next(100);
                data.AudienceAuctionLostToBudgetCnt = rand.Next(100);
                data.AudienceAuctionLostToRankCnt = rand.Next(100);
                data.RelativeAuctionWonCnt = rand.Next(100);
                data.AdvertiserClicks = data.RelativeAuctionWonCnt + rand.Next(100);
                if (populateRelativeCTR)
                {
                    data.SameSectionCTR = rand.Next(100);
                    data.SameSectionCount = (long)data.SameSectionCTR + rand.Next(100);
                }

                data.TotalSearchAbsTopPosition = rand.Next(1, 10);
                data.TotalShoppingAbsTopPosition = rand.Next(1, 10);
                data.TotalSearchTopPosition = rand.Next(1, 10);

                data.SearchUniqueImpressionCount = rand.Next(1, 10);
                data.ShoppingUniqueImpressionCount = rand.Next(1, 10);

                data.CompletedVideoViews = rand.Next(1, 20);
                data.VideoViewsAt75Percent = data.CompletedVideoViews + rand.Next(1, 20);
                data.VideoViewsAt50Percent = data.VideoViewsAt75Percent + rand.Next(1, 20);
                data.VideoViewsAt25Percent = data.VideoViewsAt50Percent + rand.Next(1, 20);
                data.VideoViews = data.VideoViewsAt25Percent + rand.Next(1, 20);
                data.TotalWatchTimeInMS = rand.Next(1000, 10000);

                data.MediumId = mediumId;
                data.AdvertisingChannelTypeId = advertisingChannelTypeId;

                data.AdScenarioType = (byte)BiData.GetSegmentationAdScenario((int)a.Data.Id);

                if (enableCvrl)
                {
                    data.AdvertiserReportedRevenueAdjustment = rand.Next(10000) + (double)rand.Next(100) / 100;
                    data.AllConversionAdvertiserReportedRevenueAdjustment = rand.Next(10000) + (double)rand.Next(100) / 100;
                    data.ViewThroughConversionsRevenueAdjustment = rand.Next(10000) + (double)rand.Next(100) / 100;
                }
                if (enableNewCustomer)
                {
                    data.NewCustomerConversions = rand.Next(10000) + (double)rand.Next(100) / 100;
                    data.NewCustomerRevenue = rand.Next(10000) + (double)rand.Next(100) / 100;
                    data.UnknownCustomerConversions = rand.Next(10000) + (double)rand.Next(100) / 100;
                    data.UnknownCustomerRevenue = rand.Next(10000) + (double)rand.Next(100) / 100;
                    data.NewCustomerCount = rand.Next(10000);
                }

                // app campaign specific fields
                if (isAppCampaign)
                {
                    data.Downloads = rand.Next(1, 100);
                    data.FirstLaunches = rand.Next(1, 100);
                    data.Purchases = rand.Next(1, 100);
                    data.Subscriptions = rand.Next(1, 100);

                    data.PostClickDownloadRate = GetRoundedValue(SafeDivide(100D * data.Downloads, data.Clicks, true), true);
                    data.CostPerDownload = GetRoundedValue(SafeDivide(data.Spent, data.Downloads, true), true);
                    data.PostClickFirstLaunchRate = GetRoundedValue(SafeDivide(100D * data.FirstLaunches, data.Clicks, true), true);
                    data.CostPerFirstLaunch = GetRoundedValue(SafeDivide(data.Spent, data.FirstLaunches, true), true);
                    data.PostInstallPurchaseRate = GetRoundedValue(SafeDivide(100D * data.Purchases, data.FirstLaunches, true), true);
                    data.CostPerPurchase = GetRoundedValue(SafeDivide(data.Spent, data.Purchases, true), true);
                    data.PostInstallSubscriptionRate = GetRoundedValue(SafeDivide(100D * data.Subscriptions, data.FirstLaunches, true), true);
                    data.CostPerSubscription = GetRoundedValue(SafeDivide(data.Spent, data.Subscriptions, true), true);

                }

                return data;
            });

            var dict = biData.ToDictionary(b => b.CampaignId.Value, b => b);
            BiDatabaseHelper.SetCampaignMockBiData(effectiveCInfo, dict.Values, clear, useConversionCredit);
            BiDatabaseHelper.SetCampaignQualityScoreMockData(effectiveCInfo, dict.Values, clear);
            dict.ForEach(i =>
            {
                i.Value.RoundDoubles();
                i.Value.CalculateDependeningFields(true, 2, newTopImpressionRateFormula: true);
            });
            return dict;
        }

        private string GetSelect(IEnumerable<string> vals)
        {
            return string.Join(",", vals);
        }

        private static string GetEntityValue(string parameter, TestCampaign campaign)
        {
            switch (parameter)
            {
                case "Id":
                    return campaign.Data.Id.ToString();
                case "Name":
                    return campaign.Data.Name.ToString();
                case "Status":
                    return campaign.Data.Status.ToString();
                case "CampaignType":
                    return campaign.Data.CampaignType.ToString();
                case "TrackingUrlTemplate":
                    return campaign.Data.TrackingTemplate?.ToString() ?? string.Empty;
                case "FinalUrlSuffix":
                    return campaign.Data.FinalUrlSuffix?.ToString() ?? string.Empty;
                case "NativeBidAdjustment":
                    return campaign.Data.NativeBidAdjustment?.ToString() ?? string.Empty;
                case "KeywordVariantMatchEnabled":
                    return campaign.Data.KeywordVariantMatchEnabled?.ToString() ?? string.Empty;
                case "ExperimentId":
                    return campaign.Data.ExperimentId?.ToString() ?? string.Empty;
                case "BaseCampaignName":
                    return campaign.Data.BaseCampaignName;
                case "IsBidLandscapeAvailable":
                    return "False";
                case "AvailableInsightTypeIds":
                    return GetMockFluctuation(campaign.Data.Id);
                default:
                    return null;
            }
        }

        private static string GetMockFluctuation(long campaignId)
        {
            return GetFluctuationString(PerformanceFluctuationTestHelper.MockFluctuationTypes(campaignId, false));
        }

        private static string GetFluctuationString(List<FluctuationType> fluctuations)
        {
            var fluctList = fluctuations.OrderBy(ft => (byte)ft).Select(ft => $"  \"{ft}\"");

            //response json looks like
            //[
            //  "Click",
            //  "Impression",
            //  "Spend"
            //]
            return fluctList.EnumerableIsNullOrEmpty() ? "[]" : $"[\r\n{String.Join(",\r\n", fluctList)}\r\n]";
        }

        private static void VerifyGridBiData(dynamic actualRow, string[] selectedColumns, BiData expectedBiData, bool useConversionCredit = false, bool partialConversionEnabled = false, bool useCvrl = false)
        {
            bool allPerfMetrics = selectedColumns.Any(s => s.Equals(PerformanceMetrics));
            dynamic actualMetrics = actualRow[PerformanceMetrics];

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/Clicks")))
            {
                Assert.AreEqual(expectedBiData.Clicks.ToString(), actualMetrics["Clicks"].ToString(), "Clicks");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/Impressions")))
            {
                Assert.AreEqual(expectedBiData.Impressions.ToString(), actualMetrics["Impressions"].ToString(), "Impressions");
            }

            // SoV is explicitly opt-in, so only check if the exact column is selected.
            if (selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/AuctionWonPercent")))
            {
                double expectedAuctionWonPercent = ((expectedBiData.ImpressionWonCount.Value * 100d) / (expectedBiData.AuctionParticipation.Value));
                double.TryParse(actualMetrics["AuctionWonPercent"].ToString(), out double actualAuctionWonPercent);

                Assert.AreEqual(expectedAuctionWonPercent, actualAuctionWonPercent, 0.05, "AuctionWonPercent");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/SalesCount")))
            {
                Assert.AreEqual(expectedBiData.SalesCount ?? 0, (long)actualMetrics["SalesCount"], "SalesCount");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/AverageCPS")))
            {
                Assert.AreEqual(expectedBiData.AverageCPS ?? 0, (double)actualMetrics["AverageCPS"], "AverageCPS");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/Installs")))
            {
                Assert.AreEqual(expectedBiData.Installs ?? 0, (long)actualMetrics["Installs"], "Installs");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/CostPerInstall")))
            {
                Assert.AreEqual(expectedBiData.CostPerInstall ?? 0, (double)actualMetrics["CostPerInstall"], "CostPerInstall");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/RevenuePerInstall")))
            {
                Assert.AreEqual(expectedBiData.RevenuePerInstall ?? 0, (double)actualMetrics["RevenuePerInstall"], "RevenuePerInstall");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/Downloads")))
            {
                Assert.AreEqual(expectedBiData.Downloads ?? 0, (long)actualMetrics["Downloads"], "Downloads");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/FirstLaunches")))
            {
                Assert.AreEqual(expectedBiData.FirstLaunches ?? 0, (long)actualMetrics["FirstLaunches"], "FirstLaunches");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/Purchases")))
            {
                Assert.AreEqual(expectedBiData.Purchases ?? 0, (long)actualMetrics["Purchases"], "Purchases");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/Subscriptions")))
            {
                Assert.AreEqual(expectedBiData.Subscriptions ?? 0, (long)actualMetrics["Subscriptions"], "Subscriptions");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/NewCustomerConversions")))
            {
                Assert.AreEqual(expectedBiData.NewCustomerConversions ?? 0, (double)actualMetrics["NewCustomerConversions"], "NewCustomerConversions");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/NewCustomerRevenue")))
            {
                Assert.AreEqual(expectedBiData.NewCustomerRevenue ?? 0, (double)actualMetrics["NewCustomerRevenue"], "NewCustomerRevenue");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/UnknownCustomerConversions")))
            {
                Assert.AreEqual(expectedBiData.UnknownCustomerConversions ?? 0, (double)actualMetrics["UnknownCustomerConversions"], "UnknownCustomerConversions");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/UnknownCustomerRevenue")))
            {
                Assert.AreEqual(expectedBiData.UnknownCustomerRevenue ?? 0, (double)actualMetrics["UnknownCustomerRevenue"], "UnknownCustomerRevenue");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/NewCustomerCount")))
            {
                Assert.AreEqual(Convert.ToDouble(expectedBiData.NewCustomerCount ?? 0), Convert.ToDouble(actualMetrics["NewCustomerCount"]), "NewCustomerCount");
            }

            bool allQualityScore = selectedColumns.Any(s => s.Equals("QualityScore"));
            dynamic actualQuailtyScore = actualRow["QualityScore"];

            if (allQualityScore || selectedColumns.Any(s => s.Equals($"QualityScore/OverallQualityScore")))
            {
                Assert.AreEqual((expectedBiData.CQualityScore ?? 0).ToString(), actualQuailtyScore["OverallQualityScore"].ToString(), "OverallQualityScore");
            }

            //AllConversions
            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/AllConversions")))
            {
                if (useConversionCredit)
                {
                    double allConversionCreditsFloatVal = Double.Parse(expectedBiData.AllConversionsCredit.GetValueOrDefault().ToString());
                    var allConversionCredits = (int)Double.Parse(expectedBiData.AllConversionsCredit.GetValueOrDefault().ToString());
                    if (allConversionCreditsFloatVal - allConversionCredits >= 0.5)
                    {
                        allConversionCredits += 1;
                    }
                    var allConversion = Int32.Parse(actualMetrics["AllConversions"].ToString());
                    Assert.AreEqual(allConversionCredits, allConversion, "AllConversions");
                }

                else
                {
                    Assert.AreEqual(expectedBiData.AllConversions.GetValueOrDefault().ToString(),
                        actualMetrics["AllConversions"].ToString(),
                        "AllConversions");
                }
            }

            if (allPerfMetrics || selectedColumns.Any(s =>
                    s.Equals($"{PerformanceMetrics}/AllConversionAdvertiserReportedRevenue")))
            {
                Assert.AreEqual(expectedBiData.AllConversionAdvertiserReportedRevenue.GetValueOrDefault().ToString(),
                    actualMetrics["AllConversionAdvertiserReportedRevenue"].ToString(),
                    "AllConversionAdvertiserReportedRevenue");
            }

            // ViewThroughConversions
            if (selectedColumns.Any(s =>
                s.Equals($"{PerformanceMetrics}/ViewThroughConversions")))
            {
                if (useConversionCredit)
                {
                    var expectedValFloatVal = Double.Parse(expectedBiData.ViewThroughConversionsCredit.GetValueOrDefault().ToString());
                    var expectedVal = (int)Double.Parse(expectedBiData.ViewThroughConversionsCredit.GetValueOrDefault().ToString());
                    if (expectedValFloatVal - expectedVal >= 0.5)
                    {
                        expectedVal += 1;
                    }
                    var realVal = Int32.Parse(actualMetrics["ViewThroughConversions"].ToString());
                    Assert.AreEqual(expectedVal, realVal, "viewThroughConversions doesn't match FullViewConversions");
                }
                else
                {
                    Assert.AreEqual(expectedBiData.ViewThroughConversions.GetValueOrDefault().ToString(),
                        actualMetrics["ViewThroughConversions"].ToString(),
                        "ViewThroughConversions doesn't match");

                }
            }

            if (partialConversionEnabled)
            {
                VerifyPartialConversionField(allPerfMetrics, selectedColumns, actualMetrics, expectedBiData);
            }

            if (useCvrl)
            {
                string[] verifiedMetrics = new string[]
                {
                    "AdvertiserReportedRevenueAdjustment",
                    "AllConversionAdvertiserReportedRevenueAdjustment",
                    "ViewThroughConversionsRevenueAdjustment"
                };
                foreach (var cvrlMetric in verifiedMetrics)
                {
                    if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/{cvrlMetric}")))
                    {
                        var expectedVal = expectedBiData.GetType().GetProperty(cvrlMetric)?.GetValue(expectedBiData)?.ToString();
                        Assert.AreEqual(expectedVal ?? "0", actualMetrics[cvrlMetric].ToString(), cvrlMetric + " mismatch");
                    }
                }
            }
        }

        private static void VerifyPartialConversionField(bool allPerfMetrics, string[] selectedColumns, dynamic actualMetrics, BiData expectedBiData)
        {
            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ConversionsCredit")))
            {
                Assert.AreEqual(Math.Round(expectedBiData.ConversionsCredit.GetValueOrDefault(), 2).ToString(), actualMetrics["ConversionsCredit"].ToString() == "" ? "0" : actualMetrics["ConversionsCredit"].ToString(), "ConversionsCredit doesn't match");
            }
            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/AllConversionsCredit")))
            {
                var expectedVal = Math.Round((double)expectedBiData.AllConversionsCredit, 2, MidpointRounding.AwayFromZero).ToString();
                Assert.AreEqual(expectedVal, actualMetrics["AllConversionsCredit"].ToString() == "" ? "0" : actualMetrics["AllConversionsCredit"].ToString(), "AllConversionsCredit doesn't match");
            }
            if (selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ViewThroughConversionsCredit")))
            {
                var expectedVal = Math.Round(expectedBiData.ViewThroughConversionsCredit.GetValueOrDefault(), 2, MidpointRounding.AwayFromZero).ToString();
                Assert.AreEqual(expectedVal, actualMetrics["ViewThroughConversionsCredit"].ToString() == "" ? "0" : actualMetrics["ViewThroughConversionsCredit"].ToString(), "ViewThroughConversionsCredit doesn't match");
            }
            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/PartialConversionCPA")))
            {
                var expectedVal = Math.Round(expectedBiData.PartialConversionCPA.GetValueOrDefault(), 2, MidpointRounding.AwayFromZero).ToString();
                Assert.AreEqual(expectedVal, actualMetrics["PartialConversionCPA"].ToString() == "" ? "0" : actualMetrics["PartialConversionCPA"].ToString(), "PartialConversionCPA doesn't match");
            }
            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/PartialConversionRate")))
            {
                var expectedVal = Math.Round(expectedBiData.PartialConversionRate.GetValueOrDefault(), 2, MidpointRounding.AwayFromZero).ToString();
                Assert.AreEqual(expectedVal, actualMetrics["PartialConversionRate"].ToString() == "" ? "0" : actualMetrics["PartialConversionRate"].ToString(), "PartialConversionRate doesn't match");
            }
            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/AllPartialConversionCPA")))
            {
                var expectedVal = Math.Round(expectedBiData.AllPartialConversionCPA.GetValueOrDefault(), 2, MidpointRounding.AwayFromZero).ToString();
                Assert.AreEqual(expectedVal, actualMetrics["AllPartialConversionCPA"].ToString() == "" ? "0" : actualMetrics["AllPartialConversionCPA"].ToString(), "AllPartialConversionCPA doesn't match");
            }
            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/AllPartialConversionRate")))
            {
                var expectedVal = Math.Round(expectedBiData.AllPartialConversionRate.GetValueOrDefault(), 2, MidpointRounding.AwayFromZero).ToString();
                Assert.AreEqual(expectedVal, actualMetrics["AllPartialConversionRate"].ToString() == "" ? "0" : actualMetrics["AllPartialConversionRate"].ToString(), "AllPartialConversionRate doesn't match");
            }
            if (selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ViewThroughPartialConversionsCPA")))
            {
                var expectedVal = Math.Round(expectedBiData.ViewThroughPartialConversionsCPA.GetValueOrDefault(), 2, MidpointRounding.AwayFromZero).ToString();
                Assert.AreEqual(expectedVal, actualMetrics["ViewThroughPartialConversionsCPA"].ToString() == "" ? "0" : actualMetrics["ViewThroughPartialConversionsCPA"].ToString(), "ViewThroughPartialConversionsCPA doesn't match");
            }
            if (selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ViewThroughPartialConversionsRate")))
            {
                var expectedVal = Math.Round(expectedBiData.ViewThroughPartialConversionsRate.GetValueOrDefault(), 2, MidpointRounding.AwayFromZero).ToString();
                Assert.AreEqual(expectedVal, actualMetrics["ViewThroughPartialConversionsRate"].ToString() == "" ? "0" : actualMetrics["ViewThroughPartialConversionsRate"].ToString(), "ViewThroughPartialConversionsRate doesn't match");
            }
        }

        private static void VerifyComparisonGridBiData(dynamic actualRow, string[] selectedColumns, BiData biData, BiData comparisonBiData)
        {
            bool allPerformanceMetrics = selectedColumns.Any(s => s.Equals(PerformanceMetrics));

            // PeriodPerformanceMetrics
            bool allPeriodPerformanceMetrics = selectedColumns.Any(s => s.Equals(PeriodPerformanceMetrics));
            bool allPeriodPhonePerformanceMetrics = selectedColumns.Any(s => s.Equals(PhonePeriodPerformanceMetrics));

            dynamic actualMetrics = actualRow[PeriodPerformanceMetrics];
            dynamic actualPhoneMetrics = actualRow[PhonePeriodPerformanceMetrics];

            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/Clicks"))) && (allPeriodPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PeriodPerformanceMetrics}/Clicks"))))
            {
                Assert.AreEqual(comparisonBiData.Clicks.ToString(), actualMetrics["Clicks"].ToString(), "Clicks");
            }

            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/Impressions"))) && (allPeriodPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PeriodPerformanceMetrics}/Impressions"))))
            {
                Assert.AreEqual(comparisonBiData.Impressions.ToString(), actualMetrics["Impressions"].ToString(), "Impressions");
            }

            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ClickCalls"))) && (allPeriodPhonePerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PhonePeriodPerformanceMetrics}/ClickCalls"))))
            {
                Assert.AreEqual(comparisonBiData.ClickCalls.ToString(), actualPhoneMetrics["ClickCalls"].ToString(), "ClickCalls");
            }

            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/PhoneImpressions"))) && (allPeriodPhonePerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PhonePeriodPerformanceMetrics}/PhoneImpressions"))))
            {
                Assert.AreEqual(comparisonBiData.PhoneImpressions.ToString(), actualPhoneMetrics["PhoneImpressions"].ToString(), "PhoneImpressions");
            }

            //AllConversions
            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/AllConversions"))) && (allPeriodPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PeriodPerformanceMetrics}/AllConversions"))))
            {
                Assert.AreEqual(comparisonBiData.AllConversions.GetValueOrDefault().ToString(), actualMetrics["AllConversions"].ToString(),
                    "AllConversions");
            }

            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/AllConversionAdvertiserReportedRevenue"))) && (allPeriodPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PeriodPerformanceMetrics}/AllConversionAdvertiserReportedRevenue"))))
            {
                Assert.AreEqual(comparisonBiData.AllConversionAdvertiserReportedRevenue.GetValueOrDefault().ToString(),
                    actualMetrics["AllConversionAdvertiserReportedRevenue"].ToString(),
                    "AllConversionAdvertiserReportedRevenue");
            }

            // PerformanceMetricsChange
            bool allPerformanceMetricsChange = selectedColumns.Any(s => s.Equals(PerformanceMetricsChange));
            bool allPhonePerformanceMetricsChange = selectedColumns.Any(s => s.Equals(PhonePerformanceMetricsChange));

            actualMetrics = actualRow[PerformanceMetricsChange];
            actualPhoneMetrics = actualRow[PhonePerformanceMetricsChange];

            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/Clicks"))) && (allPerformanceMetricsChange || selectedColumns.Any(s => s.Equals($"{PerformanceMetricsChange}/Clicks"))))
            {
                Assert.AreEqual((biData.Clicks - comparisonBiData.Clicks).ToString(), actualMetrics["Clicks"].ToString(), "Clicks");
            }

            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/Impressions"))) && (allPerformanceMetricsChange || selectedColumns.Any(s => s.Equals($"{PerformanceMetricsChange}/Impressions"))))
            {
                Assert.AreEqual((biData.Impressions - comparisonBiData.Impressions).ToString(), actualMetrics["Impressions"].ToString(), "Impressions");
            }

            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ClickCalls"))) && (allPhonePerformanceMetricsChange || selectedColumns.Any(s => s.Equals($"{PhonePerformanceMetricsChange}/ClickCalls"))))
            {
                Assert.AreEqual((biData.ClickCalls - comparisonBiData.ClickCalls).ToString(), actualPhoneMetrics["ClickCalls"].ToString(), "ClickCalls");
            }

            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/PhoneImpressions"))) && (allPhonePerformanceMetricsChange || selectedColumns.Any(s => s.Equals($"{PhonePerformanceMetricsChange}/PhoneImpressions"))))
            {
                Assert.AreEqual((biData.PhoneImpressions - comparisonBiData.PhoneImpressions).ToString(), actualPhoneMetrics["PhoneImpressions"].ToString(), "PhoneImpressions");
            }

            //AllConversions
            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/AllConversions"))) && (allPerformanceMetricsChange || selectedColumns.Any(s => s.Equals($"{PerformanceMetricsChange}/AllConversions"))))
            {
                Assert.AreEqual((biData.AllConversions.GetValueOrDefault() - comparisonBiData.AllConversions.GetValueOrDefault()).ToString(),
                    actualMetrics["AllConversions"].ToString(), "AllConversions");
            }

            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/AllConversionAdvertiserReportedRevenue"))) && (allPerformanceMetricsChange || selectedColumns.Any(s => s.Equals($"{PerformanceMetricsChange}/AllConversionAdvertiserReportedRevenue"))))
            {
                Assert.AreEqual(
                    (biData.AllConversionAdvertiserReportedRevenue.GetValueOrDefault() -
                     comparisonBiData.AllConversionAdvertiserReportedRevenue.GetValueOrDefault()).ToString(),
                    actualMetrics["AllConversionAdvertiserReportedRevenue"].ToString(),
                    "AllConversionAdvertiserReportedRevenue");
            }

            // PerformanceMetricsChangePercentage
            // To keep this simple, just make sure the value exists
            bool allPerformanceMetricsChangePercentage = selectedColumns.Any(s => s.Equals(PerformanceMetricsChangePercentage));
            bool allPhonePerformanceMetricsChangePercentage = selectedColumns.Any(s => s.Equals(PhonePerformanceMetricsChangePercentage));

            actualMetrics = actualRow[PerformanceMetricsChangePercentage];
            actualPhoneMetrics = actualRow[PhonePerformanceMetricsChangePercentage];

            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/Clicks"))) && (allPerformanceMetricsChangePercentage || selectedColumns.Any(s => s.Equals($"{PerformanceMetricsChangePercentage}/Clicks"))))
            {
                Assert.IsNotNull(actualMetrics["Clicks"].ToString(), "Clicks");
            }

            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/Impressions"))) && (allPerformanceMetricsChangePercentage || selectedColumns.Any(s => s.Equals($"{PerformanceMetricsChangePercentage}/Impressions"))))
            {
                Assert.IsNotNull(actualMetrics["Impressions"].ToString(), "Impressions");
            }

            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ClickCalls"))) && (allPhonePerformanceMetricsChangePercentage || selectedColumns.Any(s => s.Equals($"{PhonePerformanceMetricsChangePercentage}/ClickCalls"))))
            {
                Assert.IsNotNull(actualPhoneMetrics["ClickCalls"].ToString(), "ClickCalls");
            }

            if ((allPerformanceMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/PhoneImpressions"))) && (allPhonePerformanceMetricsChangePercentage || selectedColumns.Any(s => s.Equals($"{PhonePerformanceMetricsChangePercentage}/PhoneImpressions"))))
            {
                Assert.IsNotNull(actualPhoneMetrics["PhoneImpressions"].ToString(), "PhoneImpressions");
            }
        }

        private static void VerifyConversionDelayGridData(dynamic result, TestCampaign[] expectedResults, string[] selectedColumns, Dictionary<long, List<BiData>> biDataByCampaignId, bool useConversionCredit = false, bool partialConversionEnabled = false, bool useCvrl = false)
        {
            bool allPerfMetrics = selectedColumns.Any(s => s.Equals(PerformanceMetrics));

            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var expectedRow = expectedResults[i];

                if (!biDataByCampaignId.IsNullOrEmpty())
                {
                    var expectedBiData = biDataByCampaignId[expectedRow.Data.Id];

                    dynamic actualMetrics = actualRow[PerformanceMetrics];

                    if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ConversionDelayZeroDay")))
                    {
                        var zeroDayValue = expectedBiData.Where(x => x.ConversionLag.Equals(0)).Sum(x => x.ConversionsCredit);
                        var totalConversions = expectedBiData.Sum(x => x.ConversionsCredit);
                        var expectedVal = Math.Round((double)(zeroDayValue / totalConversions), 4, MidpointRounding.AwayFromZero);
                        Assert.AreEqual(expectedVal, (double)actualMetrics["ConversionDelayZeroDay"], "ConversionDelayZeroDay");
                    }

                    if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ConversionDelayNinety")) || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ConversionDelayNinetyNine")))
                    {
                        var lagToConversionsDictionary = new Dictionary<double, long>();
                        foreach (var biData in expectedBiData)
                        {
                            if (lagToConversionsDictionary.ContainsKey(biData.ConversionLag))
                            {
                                lagToConversionsDictionary[biData.ConversionLag] += (long)(biData.ConversionsCredit ?? 0f);
                            }
                            else
                            {
                                lagToConversionsDictionary.Add(biData.ConversionLag, (long)(biData.ConversionsCredit ?? 0f));
                            }
                        }
                        lagToConversionsDictionary.OrderBy(x => x.Key);

                        var totalSumConversions = expectedBiData.Sum(x => x.ConversionsCredit ?? 0);

                        if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ConversionDelayNinety")))
                        {
                            double ninetyPercentConversions = (totalSumConversions * 0.9);

                            double currSumConversions = 0.0;
                            foreach (var kvp in lagToConversionsDictionary)
                            {
                                currSumConversions += kvp.Value;
                                if (currSumConversions >= ninetyPercentConversions)
                                {
                                    Assert.AreEqual(kvp.Key.ToString(), actualMetrics["ConversionDelayNinety"].ToString(), "ConversionDelayNinety");
                                    break;
                                }
                            }
                        }

                        if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ConversionDelayNinetyNine")))
                        {
                            double ninetyNinePercentConversions = (totalSumConversions * 0.99);

                            double currSumConversions = 0.0;
                            foreach (var kvp in lagToConversionsDictionary)
                            {
                                currSumConversions += kvp.Value;
                                if (currSumConversions >= ninetyNinePercentConversions)
                                {
                                    Assert.AreEqual(kvp.Key.ToString(), actualMetrics["ConversionDelayNinetyNine"].ToString(), "ConversionDelayNinetyNine");
                                    break;
                                }
                            }
                        }
                    }

                    if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetricsChangePercentage}/ConversionDelayZeroDay")))
                    {
                        Assert.IsNotNull(actualRow.PerformanceMetricsChangePercentage["ConversionDelayZeroDay"], "ConversionDelayZeroDay should not be null");
                        Assert.IsNotNull(actualRow.PerformanceMetricsChangePercentage.SegmentedData[0].Data["ConversionDelayZeroDay"], "ConversionDelayZeroDay should not be null");
                    }

                    if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetricsChangePercentage}/ConversionDelayNinety")))
                    {
                        Assert.IsNotNull(actualRow.PerformanceMetricsChangePercentage["ConversionDelayNinety"], "ConversionDelayNinety should not be null");
                        Assert.IsNotNull(actualRow.PerformanceMetricsChangePercentage.SegmentedData[0].Data["ConversionDelayNinety"], "ConversionDelayNinety should not be null");
                    }

                    if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetricsChangePercentage}/ConversionDelayNinetyNine")))
                    {
                        Assert.IsNotNull(actualRow.PerformanceMetricsChangePercentage["ConversionDelayNinetyNine"], "ConversionDelayNinetyNine should not be null");
                        Assert.IsNotNull(actualRow.PerformanceMetricsChangePercentage.SegmentedData[0].Data["ConversionDelayNinetyNine"], "ConversionDelayNinetyNine should not be null");
                    }

                    if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PeriodPerformanceMetrics}/ConversionDelayZeroDay")))
                    {
                        Assert.IsNotNull(actualRow.PeriodPerformanceMetrics["ConversionDelayZeroDay"], "ConversionDelayZeroDay should not be null");
                        Assert.IsNotNull(actualRow.PeriodPerformanceMetrics.SegmentedData[0].Data["ConversionDelayZeroDay"], "ConversionDelayZeroDay should not be null");
                    }

                    if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PeriodPerformanceMetrics}/ConversionDelayNinety")))
                    {
                        Assert.IsNotNull(actualRow.PeriodPerformanceMetrics["ConversionDelayNinety"], "ConversionDelayNinety should not be null");
                        Assert.IsNotNull(actualRow.PeriodPerformanceMetrics.SegmentedData[0].Data["ConversionDelayNinety"], "ConversionDelayNinety should not be null");
                    }

                    if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PeriodPerformanceMetrics}/ConversionDelayNinetyNine")))
                    {
                        Assert.IsNotNull(actualRow.PeriodPerformanceMetrics["ConversionDelayNinetyNine"], "ConversionDelayNinetyNine should not be null");
                        Assert.IsNotNull(actualRow.PeriodPerformanceMetrics.SegmentedData[0].Data["ConversionDelayNinetyNine"], "ConversionDelayNinetyNine should not be null");
                    }
                }
            }

            var allBiDatas = biDataByCampaignId.Values.SelectMany(x => x).ToList();

            dynamic actualUnfilteredTotals = result["@ns.unfiltered.totals"];

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ConversionDelayZeroDay")))
            {
                var zeroDayValue = allBiDatas.Where(x => x.ConversionLag.Equals(0)).Sum(x => x.ConversionsCredit);
                var totalConversions = allBiDatas.Sum(x => x.ConversionsCredit);
                var expectedVal = Math.Round((double)(zeroDayValue / totalConversions), 4, MidpointRounding.AwayFromZero);
                Assert.AreEqual(expectedVal, (double)actualUnfilteredTotals[PerformanceMetrics]["ConversionDelayZeroDay"], "ConversionDelayZeroDay");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ConversionDelayNinety")) || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ConversionDelayNinetyNine")))
            {
                var lagToConversionsDictionary = new Dictionary<double, long>();
                foreach (var biData in allBiDatas)
                {
                    if (lagToConversionsDictionary.ContainsKey(biData.ConversionLag))
                    {
                        lagToConversionsDictionary[biData.ConversionLag] += (long)(biData.ConversionsCredit ?? 0f);
                    }
                    else
                    {
                        lagToConversionsDictionary.Add(biData.ConversionLag, (long)(biData.ConversionsCredit ?? 0f));
                    }
                }
                lagToConversionsDictionary.OrderBy(x => x.Key);

                var totalSumConversions = allBiDatas.Sum(x => x.ConversionsCredit ?? 0);

                if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ConversionDelayNinety")))
                {
                    double ninetyPercentConversions = (totalSumConversions * 0.9);

                    double currSumConversions = 0.0;
                    foreach (var kvp in lagToConversionsDictionary)
                    {
                        currSumConversions += kvp.Value;
                        if (currSumConversions >= ninetyPercentConversions)
                        {
                            Assert.AreEqual(kvp.Key.ToString(), actualUnfilteredTotals[PerformanceMetrics]["ConversionDelayNinety"].ToString(), "ConversionDelayNinety");
                            break;
                        }
                    }
                }

                if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/ConversionDelayNinetyNine")))
                {
                    double ninetyNinePercentConversions = (totalSumConversions * 0.99);

                    double currSumConversions = 0.0;
                    foreach (var kvp in lagToConversionsDictionary)
                    {
                        currSumConversions += kvp.Value;
                        if (currSumConversions >= ninetyNinePercentConversions)
                        {
                            Assert.AreEqual(kvp.Key.ToString(), actualUnfilteredTotals[PerformanceMetrics]["ConversionDelayNinetyNine"].ToString(), "ConversionDelayNinetyNine");
                            break;
                        }
                    }
                }
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetricsChangePercentage}/ConversionDelayZeroDay")))
            {
                Assert.IsNotNull(actualUnfilteredTotals.PerformanceMetricsChangePercentage["ConversionDelayZeroDay"], "ConversionDelayZeroDay should not be null");
                Assert.IsNotNull(actualUnfilteredTotals.PerformanceMetricsChangePercentage.SegmentedData[0].Data["ConversionDelayZeroDay"], "ConversionDelayZeroDay should not be null");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetricsChangePercentage}/ConversionDelayNinety")))
            {
                Assert.IsNotNull(actualUnfilteredTotals.PerformanceMetricsChangePercentage["ConversionDelayNinety"], "ConversionDelayNinety should not be null");
                Assert.IsNotNull(actualUnfilteredTotals.PerformanceMetricsChangePercentage.SegmentedData[0].Data["ConversionDelayNinety"], "ConversionDelayNinety should not be null");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetricsChangePercentage}/ConversionDelayNinetyNine")))
            {
                Assert.IsNotNull(actualUnfilteredTotals.PerformanceMetricsChangePercentage["ConversionDelayNinetyNine"], "ConversionDelayNinetyNine should not be null");
                Assert.IsNotNull(actualUnfilteredTotals.PerformanceMetricsChangePercentage.SegmentedData[0].Data["ConversionDelayNinetyNine"], "ConversionDelayNinetyNine should not be null");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PeriodPerformanceMetrics}/ConversionDelayZeroDay")))
            {
                Assert.IsNotNull(actualUnfilteredTotals.PeriodPerformanceMetrics["ConversionDelayZeroDay"], "ConversionDelayZeroDay should not be null");
                Assert.IsNotNull(actualUnfilteredTotals.PeriodPerformanceMetrics.SegmentedData[0].Data["ConversionDelayZeroDay"], "ConversionDelayZeroDay should not be null");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PeriodPerformanceMetrics}/ConversionDelayNinety")))
            {
                Assert.IsNotNull(actualUnfilteredTotals.PeriodPerformanceMetrics["ConversionDelayNinety"], "ConversionDelayNinety should not be null");
                Assert.IsNotNull(actualUnfilteredTotals.PeriodPerformanceMetrics.SegmentedData[0].Data["ConversionDelayNinety"], "ConversionDelayNinety should not be null");
            }

            if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PeriodPerformanceMetrics}/ConversionDelayNinetyNine")))
            {
                Assert.IsNotNull(actualUnfilteredTotals.PeriodPerformanceMetrics["ConversionDelayNinetyNine"], "ConversionDelayNinetyNine should not be null");
                Assert.IsNotNull(actualUnfilteredTotals.PeriodPerformanceMetrics.SegmentedData[0].Data["ConversionDelayNinetyNine"], "ConversionDelayNinetyNine should not be null");
            }
        }

        private void VerifyColumnSortAndFilter<T>(Dictionary<long, BiData> biDatas, string columnName, bool expectNullTotal = false)
        {
            if (biDatas != null)
            {
                Func<BiData, T> getValue = biData => (T)(typeof(BiData).GetProperty(columnName)?.GetValue(biData) ?? default(T));
                var sortedExpectedResults = biDatas.OrderBy(s => getValue(s.Value), Comparer<T>.Default).ToArray();
                var middleIndedx = Convert.ToInt32(sortedExpectedResults.Length / 2);
                var filterValue = getValue(sortedExpectedResults[middleIndedx].Value);
                var expectedResults = sortedExpectedResults.
                    Where(s => Comparer<T>.Default.Compare(getValue(s.Value), filterValue) > 0).ToArray();

                string orderBy = $"PerformanceMetrics/{columnName} asc";

                string filter = $"PerformanceMetrics/{columnName} gt {filterValue}";

                var result = CallGetCampaignsGridData(this.cInfo, today, orderBy: orderBy, queryFilter: filter, select: $"PerformanceMetrics/{columnName}");

                if (expectNullTotal)
                {
                    Assert.AreEqual(result["@ns.unfiltered.totals"].PerformanceMetrics[columnName].ToString(), "", $"@ns.unfiltered.totals {columnName} doesn't match");
                }
                else
                {
                    var expectToalBiData = calculateBiDataTotalValue(biDatas, true, 2);
                    Assert.AreEqual(result["@ns.unfiltered.totals"].PerformanceMetrics[columnName].ToString(), getValue(expectToalBiData).ToString(), $"@ns.unfiltered.totals {columnName} doesn't match");
                }

                for (int i = 0; i < result.value.Count; i++)
                {
                    var actualRow = result.value[i];
                    var expectedRow = expectedResults[i].Value;
                    Assert.AreEqual(getValue(expectedRow).ToString(), actualRow.PerformanceMetrics[columnName].ToString(), $"{columnName} doesn't match");
                }
            }
            else
            {
                string orderBy = $"PerformanceMetrics/{columnName} asc";

                string filter = $"PerformanceMetrics/{columnName} gt 0";

                CallGetCampaignsGridData(this.cInfo, today, orderBy: orderBy, queryFilter: filter, select: $"PerformanceMetrics/{columnName}");
            }
        }

        private static void VerifySegmentedBiData(dynamic actualRow, string[] selectedColumns, Dictionary<string, BiData> segmentedBiData)
        {
            bool allPerfMetrics = selectedColumns.Any(s => s.Equals(PerformanceMetrics));
            bool allPhonePerfMetrics = selectedColumns.Any(s => s.Equals(PhonePerformanceMetrics));

            dynamic actualSegmentedMetrics = actualRow[PerformanceMetrics]["SegmentedData"];

            Assert.AreEqual(segmentedBiData.Count, actualSegmentedMetrics.Count, "Mismatch in number of expected segments.");

            foreach (dynamic segment in actualSegmentedMetrics)
            {
                // The zero index access is because we can have a list of keys if there are multiple segmentation types
                string key = segment["Key"][0]["StringValue"];
                BiData expectedBiData = segmentedBiData[key];

                var actualBiData = segment["Data"];

                if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/Clicks")))
                {
                    Assert.AreEqual(expectedBiData.Clicks.ToString(), actualBiData["Clicks"].ToString(), "Clicks");
                }

                if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/Impressions")))
                {
                    Assert.AreEqual(expectedBiData.Impressions.ToString(), actualBiData["Impressions"].ToString(), "Impressions");
                }

                if (selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/TopImpressionRate")))
                {
                    Assert.AreEqual(expectedBiData.TopImpressionRate.ToString(), actualBiData["TopImpressionRate"].ToString(), "TopImpressionRate");
                }

                if (selectedColumns.Any(s => s.Equals($"{PerformanceMetrics}/AbsoluteTopImpressionRate")))
                {
                    Assert.AreEqual(expectedBiData.AbsoluteTopImpressionRate.ToString(), actualBiData["AbsoluteTopImpressionRate"].ToString(), "AbsoluteTopImpressionRate");
                }
            }

            if (selectedColumns.Contains(PhonePerformanceMetrics))
            {
                dynamic actualPhoneSegmentedMetrics = actualRow[PhonePerformanceMetrics]["SegmentedData"];
                Assert.AreEqual(segmentedBiData.Count, actualPhoneSegmentedMetrics.Count, "Mismatch in number of expected phone segments.");
                foreach (dynamic segment in actualPhoneSegmentedMetrics)
                {
                    // The zero index access is because we can have a list of keys if there are multiple segmentation types
                    string key = segment["Key"][0]["StringValue"];
                    BiData expectedBiData = segmentedBiData[key];

                    var actualBiData = segment["Data"];

                    if (allPhonePerfMetrics || selectedColumns.Any(s => s.Equals($"{PhonePerformanceMetrics}/ClickCalls")))
                    {
                        Assert.AreEqual(expectedBiData.ClickCalls.ToString(), actualBiData["ClickCalls"].ToString(), "ClickCalls");
                    }

                    if (allPhonePerfMetrics || selectedColumns.Any(s => s.Equals($"{PhonePerformanceMetrics}/PhoneImpressions")))
                    {
                        Assert.AreEqual(expectedBiData.PhoneImpressions.ToString(), actualBiData["PhoneImpressions"].ToString(), "PhoneImpressions");
                    }
                }
            }
        }

        private static void VerifyPeriodSegmentedBiData(dynamic actualRow, string[] selectedColumns, Dictionary<string, BiData> segmentedBiData)
        {
            bool allPerfMetrics = selectedColumns.Any(s => s.Equals(PeriodPerformanceMetrics));

            dynamic actualSegmentedMetrics = actualRow[PeriodPerformanceMetrics]["SegmentedData"];

            Assert.AreEqual(segmentedBiData.Count, actualSegmentedMetrics.Count, "Mismatch in number of expected segments.");

            foreach (dynamic segment in actualSegmentedMetrics)
            {
                string key = segment["Key"][0]["StringValue"];
                BiData expectedBiData = segmentedBiData[key];

                var actualBiData = segment["Data"];

                if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PeriodPerformanceMetrics}/Clicks")))
                {
                    Assert.AreEqual(expectedBiData.Clicks.ToString(), actualBiData["Clicks"].ToString(), "Clicks");
                }

                if (allPerfMetrics || selectedColumns.Any(s => s.Equals($"{PeriodPerformanceMetrics}/Impressions")))
                {
                    Assert.AreEqual(expectedBiData.Impressions.ToString(), actualBiData["Impressions"].ToString(), "Impressions");
                }
            }
        }

        private static string GetFormattedDate(DateTime? date)
        {
            return date?.Date.ToString("yyyy-MM-dd") ?? "null";
        }

        private dynamic CallGetPerformanceTimeSeries(
            DateTime startDate,
            DateTime endDate,
            DateTime? comparisonStartDate = null,
            DateTime? comparisonEndDate = null,
            string timeGrain = "Day",
            string metrics = "All",
            bool movingAverage = false,
            string filter = null,
            CustomerInfo customerInfo = null)
        {
            customerInfo ??= this.cInfo;

            string queryFormat =
                "/Customers({0})/Accounts({1})/Campaigns/Default.PerformanceTimeSeries(" +
                "granularity=Enum.Granularity'{2}',startDate={3},endDate={4},comparisonStartDate={5},comparisonEndDate={6},metrics='{7}',movingAverage={8})" +
                "?&$top=20&$count=true";

            if (filter != null)
            {
                queryFormat += $"&$filter={filter}";
            }

            string query = string.Format(
                             queryFormat,
                             customerInfo.CustomerId,
                             customerInfo.AccountIds[0],
                             timeGrain,
                             GetFormattedDate(startDate),
                             GetFormattedDate(endDate),
                             GetFormattedDate(comparisonStartDate),
                             GetFormattedDate(comparisonEndDate),
                             metrics,
                             movingAverage.ToString().ToLowerInvariant());

            string url = ApiVersion.BaseUrl + query;
            Console.WriteLine("url:" + url);

            var resultJson = ApiHelper.CallApi(customerInfo, c => c.GetAsync(url), e => Assert.AreEqual(true, e.IsSuccessStatusCode, "Expect the Call to be Successful"), null);

            Assert.IsNotNull(resultJson);
            Assert.IsNotNull(resultJson["value"]);

            return resultJson.value;
        }

        private void VerifyData_InlineChart(List<BiData> biDataList, IEnumerable<dynamic> result, int numberOfDays, string[] verifiedMetrics = null, long? filterByCampaignId = null, bool validateMovingAverage = false)
        {
            var biDataByTime = biDataList.GroupBy(e => e.Date).ToList();
            List<BiData> aggregatorBiDataList = new List<BiData>();
            for (int j = 0; j < biDataByTime.Count; j++)
            {
                BiData biData = BiData.InitializeBiData();
                var oneDayBiDataList = biDataByTime[j].ToList();
                for (int i = 0; i < oneDayBiDataList.Count; i++)
                {
                    if (filterByCampaignId != null && oneDayBiDataList[i].CampaignId != filterByCampaignId)
                    {
                        continue;
                    }

                    biData += oneDayBiDataList[i];
                }

                biData.CalculateDependeningFields(true, 2, newTopImpressionRateFormula: true, useConversionEnabled: true);
                biData.PartialConversionRound();
                biData.RoundDoubles();
                aggregatorBiDataList.Add(biData);
            }

            List<BiData> expectedMovingAverageBiData = null;
            if (validateMovingAverage)
            {
                expectedMovingAverageBiData = ComputeExpectedDailyMovingAverage(aggregatorBiDataList, numberOfDays);
                aggregatorBiDataList = aggregatorBiDataList.Skip((int)timeAdjustmentForMovingAverage.TotalDays).ToList();
            }

            if (verifiedMetrics == null)
            {
                verifiedMetrics = new string[]
                        { "Clicks", "ConversionsCredit", "AllConversionsCredit", "ViewThroughConversionsCredit",
                          "PartialConversionCPA", "PartialConversionRate","AllPartialConversionCPA", "AllPartialConversionRate",
                          "ViewThroughPartialConversionsCPA", "ViewThroughPartialConversionsRate", "SalesCount", "AverageCPS", "Installs", "CostPerInstall", "RevenuePerInstall",
                          "Downloads","PostClickDownloadRate","CostPerDownload","FirstLaunches","PostClickFirstLaunchRate","CostPerFirstLaunch",
                          "Purchases","PostInstallPurchaseRate","CostPerPurchase","Subscriptions","PostInstallSubscriptionRate","CostPerSubscription"};
            }

            for (int j = 0; j < verifiedMetrics.Length; j++)
            {
                dynamic metric = result.Single(x => x.MetricName == verifiedMetrics[j]);
                Assert.AreEqual(numberOfDays, metric.PrimaryDataPoints.Count, metric + " Number of data points mismatch.");
                for (int i = 0; i < aggregatorBiDataList.Count; i++)
                {
                    var biData = aggregatorBiDataList[i];
                    var expected = biData.GetType().GetProperty(verifiedMetrics[j])?.GetValue(biData)?.ToString();
                    Assert.AreEqual(expected ?? "0", metric.PrimaryDataPoints[i].Value.ToString(), metric + " Value of data points mismatch.");
                }

                if (validateMovingAverage)
                {
                    Assert.AreEqual(numberOfDays, metric.ComparisonDataPoints.Count, metric + " Number of data points mismatch.");
                    for (int i = 0; i < expectedMovingAverageBiData.Count; i++)
                    {
                        var biData = expectedMovingAverageBiData[i];
                        var expected = biData.GetType().GetProperty(verifiedMetrics[j])?.GetValue(biData).ToString();
                        Assert.AreEqual(float.Parse(expected ?? "0"), float.Parse(metric.ComparisonDataPoints[i].Value.ToString()), acceptableFloatingDelta, metric + " Value of data points mismatch.");
                    }
                }
            }
        }

        private Dictionary<long, BiData> GetAggregatedBiDataByCampaignId(List<BiData> biDataList, long? filterByCampaignId = null)
        {
            var biDataByCampaignId = biDataList.GroupBy(e => e.CampaignId.Value).ToList();
            var aggregatedBiDataByCampaignId = new Dictionary<long, BiData>();
            for (int j = 0; j < biDataByCampaignId.Count; j++)
            {
                BiData biData = BiData.InitializeBiData();
                var oneCampaignIdBiDataList = biDataByCampaignId[j].ToList();
                for (int i = 0; i < oneCampaignIdBiDataList.Count; i++)
                {
                    if (filterByCampaignId.HasValue && oneCampaignIdBiDataList[i].CampaignId != filterByCampaignId)
                    {
                        continue;
                    }

                    biData += oneCampaignIdBiDataList[i];
                }

                biData.CalculateDependeningFields(true, 2, newTopImpressionRateFormula: true, useConversionEnabled: true);
                biData.PartialConversionRound();
                aggregatedBiDataByCampaignId.Add(biDataByCampaignId[j].Key, biData);
            }

            return aggregatedBiDataByCampaignId;
        }

        private List<BiData> SetDailyMockBiData(DateTime startDate, DateTime endDate, bool useConversionCredit = false, CustomerInfo customerInfo = null, bool isAppCampaign = false, bool mockSov = false)
        {
            customerInfo ??= this.cInfo;

            var biDataList = new List<BiData>();
            Random rand = new Random();
            for (int i = 0; i < campaignCollection.Campaigns.Count; i++)
            {
                var campaignId = (int)campaignCollection.Campaigns[i].Data.Id;
                for (DateTime currDate = startDate.Date; currDate <= endDate; currDate = currDate.AddDays(1))
                {
                    var conversionsCredit = 100 + .1f;
                    var fullClickConversionsCredit = 133 + .1f;
                    var viewThroughConversionsCredit = 8 + .1f;
                    var totalConversionsCredit = fullClickConversionsCredit + viewThroughConversionsCredit;
                    var conversionEnabledSpent = Math.Round(rand.Next(868) + rand.NextDouble(), 2);
                    var conversionEnabledClicks = 1 + rand.Next(998);

                    var biData = new BiData()
                    {
                        Date = currDate,
                        AccountId = customerInfo.AccountIds[0],
                        CampaignId = campaignId,

                        AdvertiserReportedRevenue = 0.1,
                        TotalPosition = 10,
                        Clicks = 2,
                        Conversions = (long)conversionsCredit,
                        FullClickConversions = (long)fullClickConversionsCredit,
                        AllConversions = (long)fullClickConversionsCredit,
                        ViewThroughConversions = (long)viewThroughConversionsCredit,
                        Impressions = 15,
                        RevenueOnAdSpend = 2.5,
                        Spent = 50.5,
                        AllConversionAdvertiserReportedRevenue = 71.5,
                        ViewThroughConversionsRevenue = 63.2,
                        ConversionEnabledClicks = conversionEnabledClicks,
                        ConversionEnabledSpent = conversionEnabledSpent,
                        ConversionsCredit = conversionsCredit,
                        FullClickConversionsCredit = fullClickConversionsCredit,
                        ViewThroughConversionsCredit = viewThroughConversionsCredit,
                        TotalConversionsCredit = totalConversionsCredit,
                        AllConversionsCredit = totalConversionsCredit,
                    };

                    if (isAppCampaign)
                    {
                        biData.Downloads = rand.Next(1, 100);
                        biData.FirstLaunches = rand.Next(1, 100);
                        biData.Purchases = rand.Next(1, 100);
                        biData.Subscriptions = rand.Next(1, 100);

                        biData.PostClickDownloadRate = GetRoundedValue(SafeDivide(100D * biData.Downloads, biData.Clicks, true), true);
                        biData.CostPerDownload = GetRoundedValue(SafeDivide(biData.Spent, biData.Downloads, true), true);
                        biData.PostClickFirstLaunchRate = GetRoundedValue(SafeDivide(100D * biData.FirstLaunches, biData.Clicks, true), true);
                        biData.CostPerFirstLaunch = GetRoundedValue(SafeDivide(biData.Spent, biData.FirstLaunches, true), true);
                        biData.PostInstallPurchaseRate = GetRoundedValue(SafeDivide(100D * biData.Purchases, biData.FirstLaunches, true), true);
                        biData.CostPerPurchase = GetRoundedValue(SafeDivide(biData.Spent, biData.Purchases, true), true);
                        biData.PostInstallSubscriptionRate = GetRoundedValue(SafeDivide(100D * biData.Subscriptions, biData.FirstLaunches, true), true);
                        biData.CostPerSubscription = GetRoundedValue(SafeDivide(biData.Spent, biData.Subscriptions, true), true);
                    }

                    if (mockSov)
                    {
                        biData.AuctionParticipation = 1;
                        biData.AuctionWonClickCount = 1;
                        biData.AuctionWonClickCount = 1;
                        biData.AuctionLostToBudgetCount = 1;
                        biData.AuctionLostToRankAggCount = 1;
                        biData.AuctionLostToRankCount = 1;
                        biData.ImpressionWonCount = 1;
                        biData.AuctionParticipationClickCount = 1;
                        biData.AuctionWonPercent = GetRoundedValue(SafeDivide(biData.ImpressionWonCount, biData.AuctionParticipation, true)*100, true);
                        biData.AuctionLostToRankPercent = GetRoundedValue(SafeDivide(100D * biData.AuctionLostToRankAggCount, biData.AuctionParticipation, true), true);
                        biData.AuctionLostToBudgetPercent = GetRoundedValue(SafeDivide(100D * biData.AuctionLostToBudgetCount, biData.AuctionParticipation, true), true);
                        biData.ClickSharePercent = GetRoundedValue(SafeDivide(100D * biData.AuctionWonClickCount, biData.AuctionParticipationClickCount, true), true);

                    }

                    biDataList.Add(biData);
                }
            }

            BiDatabaseHelper.SetCampaignMockBiData(customerInfo, biDataList, useConversionCredit: useConversionCredit);
            return biDataList;
        }

        private void VerifySegmentData_PartialConversion(dynamic result, Dictionary<long, BiData> SmartGoalBiDataList, Dictionary<long, BiData> DestinationBiDataList)
        {
            var expectedResults = campaignCollection.Campaigns.ToArray();
            int smartIdx = 0;
            int desIdx = 1;
            if (result.value[0]["PerformanceMetrics"]["SegmentedData"][0]["Key"][0]["StringValue"].Value == SegmentationGoalType.DestinationURL.ToString())
            {
                desIdx = 0;
                smartIdx = 1;
            }

            BiData smartTotalBiData = BiData.InitializeBiData();
            BiData desTotalBiData = BiData.InitializeBiData();
            BiData biTotalData = BiData.InitializeBiData();
            string[] verifiedMetrics = new string[]
                    { "Clicks", "Impressions", "ConversionsCredit", "AllConversionsCredit", "ViewThroughConversionsCredit",
                          "PartialConversionCPA", "PartialConversionRate","AllPartialConversionCPA", "AllPartialConversionRate",
                          "ViewThroughPartialConversionsCPA", "ViewThroughPartialConversionsRate"};

            for (int i = 0; i < expectedResults.Length; i++)
            {
                var actualMetrics = result.value[i][PerformanceMetrics];
                var campaignId = expectedResults[i].Data.Id;
                BiData smartGoalBiData = SmartGoalBiDataList[campaignId];
                BiData destinationBiData = DestinationBiDataList[campaignId];
                BiData biData = smartGoalBiData + destinationBiData;
                smartTotalBiData += smartGoalBiData;
                desTotalBiData += destinationBiData;
                biTotalData += biData;
                BiData.ProcessGoalSegmentationFields(smartGoalBiData, biData.Spent, biData.ConversionEnabledSpent, biData.ConversionEnabledClicks);
                BiData.ProcessGoalSegmentationFields(destinationBiData, biData.Spent, biData.ConversionEnabledSpent, biData.ConversionEnabledClicks);

                VerifyPartialConversionField_Segmentation(verifiedMetrics, smartGoalBiData, destinationBiData, actualMetrics, smartIdx, desIdx, biData);
            }

            BiData.ProcessGoalSegmentationFields(smartTotalBiData, biTotalData.Spent, biTotalData.ConversionEnabledSpent, biTotalData.ConversionEnabledClicks);
            BiData.ProcessGoalSegmentationFields(desTotalBiData, biTotalData.Spent, biTotalData.ConversionEnabledSpent, biTotalData.ConversionEnabledClicks);

            var expectTotalResult = result["@ns.unfiltered.totals"]["PerformanceMetrics"];
            VerifyPartialConversionField_Segmentation(verifiedMetrics, smartTotalBiData, desTotalBiData, expectTotalResult, smartIdx, desIdx, biTotalData);
        }

        private void VerifyPartialConversionField_Segmentation(string[] verifiedMetrics, BiData smartGoalBiData, BiData destinationBiData,
                                                               dynamic actualMetrics, int smartIdx, int desIdx, BiData biData)
        {
            for (int j = 0; j < verifiedMetrics.Length; j++)
            {
                var expected = smartGoalBiData.GetType().GetProperty(verifiedMetrics[j])?.GetValue(smartGoalBiData);
                var expectVal = Math.Round(Convert.ToDouble(expected ?? 0), 2).ToString();
                var actualed = actualMetrics["SegmentedData"][smartIdx]["Data"][verifiedMetrics[j]].Value;
                var actualVal = actualed != null ? Math.Round((double)actualed, 2).ToString() : "0";
                Assert.AreEqual(expectVal, actualVal, verifiedMetrics[j] + " doesn't match");

                expected = destinationBiData.GetType().GetProperty(verifiedMetrics[j])?.GetValue(destinationBiData);
                expectVal = Math.Round(Convert.ToDouble(expected ?? 0), 2).ToString();
                actualed = actualMetrics["SegmentedData"][desIdx]["Data"][verifiedMetrics[j]].Value;
                actualVal = actualed != null ? Math.Round((double)actualed, 2).ToString() : "0";
                Assert.AreEqual(expectVal, actualVal, verifiedMetrics[j] + " doesn't match");

                expected = biData.GetType().GetProperty(verifiedMetrics[j])?.GetValue(biData);
                expectVal = Math.Round(Convert.ToDouble(expected ?? 0), 2).ToString();
                Assert.AreEqual(expectVal, actualMetrics[verifiedMetrics[j]].Value?.ToString() ?? "0", verifiedMetrics[j] + " doesn't match");
            }
        }

        public static dynamic CallGetCampaignsGridData(
            CustomerInfo customerInfo,
            BiDateRange dateRange = null,
            BiDateRange comparisonDateRange = null,
            IEnumerable<string> segmentationTypes = null,
            int? skip = 0,
            int? top = 20,
            string orderBy = null,
            string queryFilter = null,
            dynamic[] gridDataFilter = null,
            string select = null,
            string expand = null,
            string lastWriteTime = "Max",
            HttpStatusCode expectedStatusCode = HttpStatusCode.OK,
            string lcidString = "1033",
            int? currentCustomerId = null,
            bool providePerformanceTimeSeriesParameters = false,
            string performanceTimesSeriesMetrics = null,
            bool movingAverage = false)
        {
            string urlTemplate = ApiVersion.BaseUrl + "/Customers({0})/Accounts({1})/Campaigns/Default.GridData{2}";

            string url = string.Format(
                urlTemplate,
                customerInfo.CustomerId,
                customerInfo.AccountIds[0],
                EntityQueryHelper.GetQueryString(null, null, top, skip, orderBy, queryFilter, select, "$count=true", expand: expand));

            dynamic request = new
            {
                gridDataParameters = EntityQueryHelper.SetupGridDataParameters(dateRange, comparisonDateRange, segmentationTypes, gridDataFilter, currentCustomerId)
            };

            if (providePerformanceTimeSeriesParameters)
            {
                request = new
                {
                    gridDataParameters = EntityQueryHelper.SetupGridDataParameters(dateRange, comparisonDateRange, segmentationTypes, gridDataFilter, currentCustomerId),
                    performanceTimeSeriesParameters = EntityQueryHelper.SetupPerformanceTimeSeriesParameters(metrics: performanceTimesSeriesMetrics, movingAverage: movingAverage)
                };
            }

            string testJson = JsonConvert.SerializeObject(request);

            Console.WriteLine("URL: " + url);
            Console.WriteLine("Content: " + testJson);

            var result = ApiHelper.CallApi(
                 customerInfo,
                 c => c.PostAsync(url, new StringContent(testJson, Encoding.UTF8, "application/json")),
                 e => Assert.AreEqual(expectedStatusCode, e.StatusCode, "Status code mismatch"),
                 lcidString: lcidString,
                 cacheOptions: new AggregatorCacheOptions(lastWriteTime));

            if (expectedStatusCode == HttpStatusCode.OK)
            {
                dynamic countResult = result[ApiHelper.ODataCount];
                Assert.IsNotNull(countResult, "@odata.count is missing");
            }

            return result;
        }

        private static void CreateAccountWithDSAMixedModeCampaign(out CustomerInfo dsaCustomerInfo, out TestCampaignCollection dsaCampaignCollection)
        {
            var featureIds = new[] { Features.DSAPilot, Features.DSAMixedModeCampaign };

            dsaCustomerInfo = CustomerInfo.CreateStandardAdvertiserWithPilot(featureIds.ToArray());

            dsaCampaignCollection = new TestCampaignCollection(1,
                                                               CampaignFactory.CampaignType.Default,
                                                               CampaignFactory.CampaignSettings.DynamicSearchAdsValid,
                                                               CampaignSettingsFactory.DynamicSearchAdsSetting.DynamicSearchAdsSettingValid,
                                                               CampaignSettingsFactory.DynamicSearchAdsCampaignSettingCount.One,
                                                               CampaignSettingsFactory.ShoppingSettingsCount.None);
            dsaCampaignCollection.Add(dsaCustomerInfo, dsaCustomerInfo.AccountIds[0]);
            dsaCampaignCollection.Reload(dsaCustomerInfo);
        }

        private BiData calculateBiDataTotalValue(Dictionary<long, BiData> totalBiData, bool isRound = false, int rountPostion = 2)
        {
            var bidata = new BiData();
            foreach (var bi in totalBiData)
            {
                bidata += bi.Value;
            }
            bidata.CalculateDependeningFields(isRound, rountPostion);
            return bidata;
        }

        private void VerifyVideoMetricsWithSortAndFilter<TKey>(
            string metricName,
            Dictionary<long, BiData> biData,
            Func<KeyValuePair<long, BiData>, TKey> keySelector,
            Func<KeyValuePair<long, BiData>, bool> predicate,
            string[] select,
            TKey filterValue)
        {
            var filter = $"{metricName} gt {filterValue}";
            var orderBy = $"{metricName} asc";

            var expectedResults = biData.Where(predicate)
                .OrderBy(keySelector).ThenBy(b => b.Key)
                .ToArray();

            Logger.Info($"Call GridData with '{filter}' and '{orderBy}', expected row count: {expectedResults.Length}");

            var result = CallGetCampaignsGridData(cInfo, today, orderBy: orderBy, queryFilter: filter, select: this.GetSelect(select));
            Assert.AreEqual(expectedResults.Length, result.value.Count, $"[{metricName}] count not match");

            for (int i = 0; i < result.value.Count; i++)
            {
                var actualRow = result.value[i];
                var expectedRow = expectedResults[i].Value;

                Assert.AreEqual(expectedResults[i].Key, (long?)actualRow.Id, $"[{metricName}] Id not match");
                Assert.AreEqual(expectedRow.VideoViews, (long?)actualRow.PerformanceMetrics.VideoViews, $"[{metricName}] VideoViews not match");
                Assert.AreEqual(expectedRow.VideoViewsAt25Percent, (long?)actualRow.PerformanceMetrics.VideoViewsAt25Percent, $"[{metricName}] VideoViewsAt25Percent not match");
                Assert.AreEqual(expectedRow.VideoViewsAt50Percent, (long?)actualRow.PerformanceMetrics.VideoViewsAt50Percent, $"[{metricName}] VideoViewsAt50Percent not match");
                Assert.AreEqual(expectedRow.VideoViewsAt75Percent, (long?)actualRow.PerformanceMetrics.VideoViewsAt75Percent, $"[{metricName}] VideoViewsAt75Percent not match");
                Assert.AreEqual(expectedRow.CompletedVideoViews, (long?)actualRow.PerformanceMetrics.CompletedVideoViews, $"[{metricName}] CompletedVideoViews not match");

                Assert.AreEqual(expectedRow.VideoCompletionRate.Value, (double)actualRow.PerformanceMetrics.VideoCompletionRate, 0.011, $"[{metricName}] VideoCompletionRate not match");
                Assert.AreEqual(expectedRow.ViewThroughRate.Value, (double)actualRow.PerformanceMetrics.ViewThroughRate, 0.011, $"[{metricName}] ViewThroughRate not match");
                Assert.AreEqual(expectedRow.TotalWatchTimeInMS.Value, (double)actualRow.PerformanceMetrics.TotalWatchTimeInMS, 0.011, $"[{metricName}] TotalWatchTimeInMS not match");
                Assert.AreEqual(expectedRow.AverageWatchTimePerVideoView.Value, (double)actualRow.PerformanceMetrics.AverageWatchTimePerVideoView, 0.011, $"[{metricName}] AverageWatchTimePerVideoView not match");
                Assert.AreEqual(expectedRow.AverageWatchTimePerImpression.Value, (double)actualRow.PerformanceMetrics.AverageWatchTimePerImpression, 0.011, $"[{metricName}] AverageWatchTimePerImpression not match");
                Assert.AreEqual(expectedRow.AverageCPV.Value, (double)actualRow.PerformanceMetrics.AverageCPV, 0.011, $"[{metricName}] AverageCPV not match");
            }
        }

        private static List<BiData> ComputeExpectedDailyMovingAverage(List<BiData> allBiData, int numberOfDays)
        {
            int expectedNumberOfChartPoints = numberOfDays + (int)timeAdjustmentForMovingAverage.TotalDays;
            if (allBiData.Count != expectedNumberOfChartPoints)
            {
                throw new ArgumentException($"Incorrect mock BiData setup. Expected to find {expectedNumberOfChartPoints} data points in input list, but found {allBiData.Count}");
            }

            var biDataByTime = allBiData.OrderBy(e => e.Date).ToList();

            List<BiData> results = new List<BiData>(numberOfDays);
            BiData[] pointsToAverage = new BiData[4];

            for (int i = 0; i < numberOfDays; i++)
            {
                // Get the 4 data points we need to average, one week apart so each point is on the same day
                pointsToAverage[0] = biDataByTime[i];
                pointsToAverage[1] = biDataByTime[i + 7];
                pointsToAverage[2] = biDataByTime[i + 14];
                pointsToAverage[3] = biDataByTime[i + 21];

                BiData result = new BiData
                {
                    AdvertiserReportedRevenue = pointsToAverage.Average(item => item.AdvertiserReportedRevenue.GetValueOrDefault()),
                    AverageCPC = pointsToAverage.Average(item => item.AverageCPC.GetValueOrDefault()),
                    AveragePosition = pointsToAverage.Average(item => item.AveragePosition.GetValueOrDefault()),
                    Clicks = (long)pointsToAverage.Average(item => item.Clicks.GetValueOrDefault()),
                    ClickThruRate = pointsToAverage.Average(item => item.ClickThruRate.GetValueOrDefault()),
                    ConversionRate = pointsToAverage.Average(item => item.ConversionRate.GetValueOrDefault()),
                    Conversions = (long)pointsToAverage.Average(item => item.Conversions.GetValueOrDefault()),
                    CPA = pointsToAverage.Average(item => item.CPA.GetValueOrDefault()),
                    Impressions = (long)pointsToAverage.Average(item => item.Impressions.GetValueOrDefault()),
                    RevenueOnAdSpend = pointsToAverage.Average(item => item.RevenueOnAdSpend.GetValueOrDefault()),
                    Spent = pointsToAverage.Average(item => item.Spent.GetValueOrDefault()),
                    AllConversions = (long)pointsToAverage.Average(item => item.AllConversions.GetValueOrDefault()),
                    FullClickConversions = (long)pointsToAverage.Average(item => item.FullClickConversions.GetValueOrDefault()),
                    ViewThroughConversions = (long)pointsToAverage.Average(item => item.ViewThroughConversions.GetValueOrDefault()),
                    ViewThroughConversionsRevenue = pointsToAverage.Average(item => item.ViewThroughConversionsRevenue.GetValueOrDefault()),
                    ViewThroughConversionsCPA = pointsToAverage.Average(item => item.ViewThroughConversionsCPA.GetValueOrDefault()),
                    ViewThroughConversionsReturnOnAdSpend = pointsToAverage.Average(item => item.ViewThroughConversionsReturnOnAdSpend.GetValueOrDefault()),
                    ViewThroughConversionsRate = pointsToAverage.Average(item => item.ViewThroughConversionsRate.GetValueOrDefault()),
                    AllConversionAdvertiserReportedRevenue = pointsToAverage.Average(item => item.AllConversionAdvertiserReportedRevenue.GetValueOrDefault()),
                    AllConversionCPA = pointsToAverage.Average(item => item.AllConversionCPA.GetValueOrDefault()),
                    AllConversionRate = pointsToAverage.Average(item => item.AllConversionRate.GetValueOrDefault()),
                    AllConversionRevenueOnAdSpend = pointsToAverage.Average(item => item.AllConversionRevenueOnAdSpend.GetValueOrDefault()),

                    ConversionsCredit = pointsToAverage.Average(item => item.ConversionsCredit.GetValueOrDefault()),
                    AllConversionsCredit = pointsToAverage.Average(item => item.AllConversionsCredit.GetValueOrDefault()),
                    ViewThroughConversionsCredit = pointsToAverage.Average(item => item.ViewThroughConversionsCredit.GetValueOrDefault()),
                    FullClickConversionsCredit = pointsToAverage.Average(item => item.FullClickConversionsCredit.GetValueOrDefault()),
                    PartialConversionCPA = pointsToAverage.Average(item => item.PartialConversionCPA.GetValueOrDefault()),
                    PartialConversionRate = pointsToAverage.Average(item => item.PartialConversionRate.GetValueOrDefault()),
                    AllPartialConversionCPA = pointsToAverage.Average(item => item.AllPartialConversionCPA.GetValueOrDefault()),
                    AllPartialConversionRate = pointsToAverage.Average(item => item.AllPartialConversionRate.GetValueOrDefault()),
                    ViewThroughPartialConversionsCPA = pointsToAverage.Average(item => item.ViewThroughPartialConversionsCPA.GetValueOrDefault()),
                    ViewThroughPartialConversionsRate = pointsToAverage.Average(item => item.ViewThroughPartialConversionsRate.GetValueOrDefault()),
                };

                results.Add(result);
            }

            return results;
        }

        public void VerifySegmentedBiData_PredictiveTargeting(BiData expectedBiData, dynamic actualBiData)
        {
            Assert.AreEqual(expectedBiData.Impressions, (long)actualBiData["Impressions"]);
            Assert.AreEqual(expectedBiData.Clicks, (long)actualBiData["Clicks"]);
            Assert.AreEqual(expectedBiData.AllConversions == null ? 0 : expectedBiData.AllConversions, (long)actualBiData["AllConversions"]);
        }
    }
}
