namespace FastDataPipeline.Contract.CDR
{
    using ProtoBuf;
    using System;

    [CsvContractV2(schema: "C2C", columnList: new[] { nameof(PublisherId), nameof(PubProcessId), nameof(UserId), nameof(UserName), nameof(FirstName), nameof(MiddleInitial),
        nameof(LastName), nameof(JobTitle), nameof(Email), nameof(PUID), nameof(CustomerId), nameof(AddressId), nameof(ContactInfoId), nameof(LCID),
        nameof(LifeCycleStatusId), nameof(CreatedDTim), nameof(ModifiedDTim), nameof(CreatedByUserId), nameof(ModifiedByUserId), nameof(ArchiveDTim), nameof(ActionFlag),
        nameof(SecretQuestionId), nameof(SecretAnswer), nameof(UsercontactMedia), nameof(Code), nameof(RowId), nameof(Timestamp), "0"/*BatchId, deprecated*/, nameof(PersonId),
        nameof(TradeScreeningStatusId) })]

    [ParquetContract("ADLSHourlyDelta", "Archive_User_Delta", "UserId", new[]{ nameof(PublisherId), nameof(PubProcessId), nameof(UserId), nameof(UserName),
        nameof(FirstName), nameof(MiddleInitial), nameof(LastName), nameof(JobTitle), nameof(Email), nameof(PUID), nameof(CustomerId), nameof(AddressId), nameof(ContactInfoId),
        nameof(LCID), nameof(LifeCycleStatusId), nameof(CreatedDTim), nameof(ModifiedDTim), nameof(CreatedByUserId), nameof(ModifiedByUserId), nameof(ArchiveDTim),
        nameof(ActionFlag), nameof(SecretQuestionId), nameof(SecretAnswer), nameof(UsercontactMedia), nameof(Code), nameof(RowId), nameof(Timestamp), nameof(PersonId) })]

    [CsvContractV2(schema: "StripeDeltaNoPartitionYahoo",
        columnList: new[]
        {
            nameof(UserId), nameof(UserName), nameof(LCID), nameof(LifeCycleStatusId),
            "(t) => t.CreatedDTim.HasValue ? t.CreatedDTim?.ToString(\"yyyyMMddHHmmss\") : \"19000101000000\"",
            "(t) => t.ModifiedDTim.HasValue ? t.ModifiedDTim?.ToString(\"yyyyMMddHHmmss\") : \"19000101000000\"", nameof(ActionFlag)
        })
    ]




    [DeltaLakeParquetContract(schema: "C2CStreaming", columnList: new[]{ nameof(PublisherId), nameof(PubProcessId), nameof(UserId), nameof(UserName), nameof(FirstName), nameof(MiddleInitial), nameof(LastName),
         nameof(JobTitle), nameof(Email), nameof(PUID), nameof(CustomerId), nameof(AddressId), nameof(ContactInfoId), nameof(LCID),
         nameof(LifeCycleStatusId), nameof(CreatedDTim), nameof(ModifiedDTim), nameof(CreatedByUserId), nameof(ModifiedByUserId), nameof(ArchiveDTim), nameof(ActionFlag),
         nameof(SecretQuestionId), nameof(SecretAnswer), nameof(UsercontactMedia), nameof(Code), nameof(RowId), nameof(Timestamp), nameof(PersonId),
         "BatchId, short, t => 0", "PartitionId, int, t => (t.UserId ?? 0) % 16", "DeltaPartitionId, Date, t => FastDataPipeline.DeltaLakeSub.C2CDeltaLakeEventTimeUtil.Get()", "EventTime, DateTime, t => FastDataPipeline.DeltaLakeSub.C2CDeltaLakeEventTimeUtil.Get()",
         nameof(TradeScreeningStatusId), nameof(FraudStatusId) },
        statsColumns: new[] { nameof(UserId), nameof(ArchiveDTim) })]

    [ClickhouseCsvContract(schema: "BICH", columnList: new[] { nameof(PublisherId), nameof(PubProcessId), nameof(UserId), nameof(UserName), nameof(FirstName), nameof(MiddleInitial), nameof(LastName), nameof(JobTitle), nameof(Email),
        nameof(PUID), nameof(CustomerId), nameof(AddressId), nameof(ContactInfoId), nameof(LCID), nameof(LifeCycleStatusId), nameof(CreatedDTim), nameof(ModifiedDTim),
        nameof(CreatedByUserId), nameof(ModifiedByUserId), nameof(ArchiveDTim), nameof(ActionFlag), nameof(SecretQuestionId), nameof(SecretAnswer), nameof(UsercontactMedia),
        nameof(Code), nameof(RowId), nameof(Timestamp), nameof(PersonId), nameof(TradeScreeningStatusId), nameof(FraudStatusId), nameof(FraudSource), nameof(FraudStatusChangeDateTime), nameof(FraudReasonCodeId) },
        Header = "PublisherId,PubProcessId,UserId,UserName,FirstName,MiddleInitial,LastName,JobTitle,Email,PUID,CustomerId,AddressId,ContactInfoId,LCID,LifeCycleStatusId,CreatedDTim,ModifiedDTim,CreatedByUserId,ModifiedByUserId,ArchiveDTim,ActionFlag,SecretQuestionId,SecretAnswer,UsercontactMedia,Code,RowId,Timestamp,PersonId,TradeScreeningStatusId,FraudStatusId,FraudSource,FraudStatusChangeDateTime,FraudReasonCodeId")]

    [ProtoContract]
    public partial class User_Delta
    {
        [ProtoMember(1)]
        [DbColumnName("PublisherId")]
        public Byte PublisherId { get; set; }

        [ProtoMember(2)]
        [DbColumnName("PubProcessId")]
        public Int64 PubProcessId { get; set; }

        [ProtoMember(3)]
        [DbColumnName("UserId")]
        public Int32? UserId { get; set; }

        [ProtoMember(4)]
        [DbColumnName("UserName")]
        [SqlString(Nullable = true, MaxLength = 255)]
        public String UserName { get; set; }

        [ProtoMember(5)]
        [DbColumnName("FirstName")]
        [SqlString(Nullable = true, MaxLength = 100)]
        public String FirstName { get; set; }

        [ProtoMember(6)]
        [DbColumnName("MiddleInitial")]
        [SqlString(Nullable = true, MaxLength = 1)]
        public String MiddleInitial { get; set; }

        [ProtoMember(7)]
        [DbColumnName("LastName")]
        [SqlString(Nullable = true, MaxLength = 100)]
        public String LastName { get; set; }

        [ProtoMember(8)]
        [DbColumnName("JobTitle")]
        [SqlString(Nullable = true, MaxLength = 50)]
        public String JobTitle { get; set; }

        [ProtoMember(9)]
        [DbColumnName("Email")]
        [SqlString(Nullable = true, MaxLength = 100)]
        public String Email { get; set; }

        [ProtoMember(10)]
        [DbColumnName("PUID")]
        [SqlString(Nullable = true, MaxLength = 32)]
        public String PUID { get; set; }

        [ProtoMember(11)]
        [DbColumnName("CustomerId")]
        public Int32? CustomerId { get; set; }

        [ProtoMember(12)]
        [DbColumnName("AddressId")]
        public Int32? AddressId { get; set; }

        [ProtoMember(13)]
        [DbColumnName("ContactInfoId")]
        public Int32? ContactInfoId { get; set; }

        [ProtoMember(14)]
        [DbColumnName("LCID")]
        public Int32? LCID { get; set; }

        [ProtoMember(15)]
        [DbColumnName("LifeCycleStatusId")]
        public Byte? LifeCycleStatusId { get; set; }

        [ProtoMember(16)]
        [DbColumnName("CreatedDTim")]
        public DateTime? CreatedDTim { get; set; }

        [ProtoMember(17)]
        [DbColumnName("ModifiedDTim")]
        public DateTime? ModifiedDTim { get; set; }

        [ProtoMember(18)]
        [DbColumnName("CreatedByUserId")]
        public Int32? CreatedByUserId { get; set; }

        [ProtoMember(19)]
        [DbColumnName("ModifiedByUserId")]
        public Int32? ModifiedByUserId { get; set; }

        [ProtoMember(20)]
        [DbColumnName("ArchiveDTim")]
        public DateTime? ArchiveDTim { get; set; }

        [ProtoMember(21)]
        [DbColumnName("ActionFlag")]
        public Byte? ActionFlag { get; set; }

        [ProtoMember(22)]
        [DbColumnName("SecretQuestionId")]
        public Byte? SecretQuestionId { get; set; }

        [ProtoMember(23)]
        [DbColumnName("SecretAnswer")]
        [SqlString(Nullable = true, MaxLength = 64)]
        public String SecretAnswer { get; set; }

        [ProtoMember(24)]
        [DbColumnName("UsercontactMedia")]
        public Byte? UsercontactMedia { get; set; }

        [ProtoMember(25)]
        [DbColumnName("Code")]
        [SqlString(Nullable = true, MaxLength = 64)]
        public String Code { get; set; }

        [ProtoMember(26)]
        [DbColumnName("RowId")]
        public Int64? RowId { get; set; }

        [ProtoMember(27)]
        [DbColumnName("Timestamp")]
        [SqlBinary(Nullable = true, MaxLength = 8)]
        public Byte[] Timestamp { get; set; }

        [ProtoMember(28)]
        [DbColumnName("PersonId")]
        public Int64? PersonId { get; set; }

        [ProtoMember(29)]
        [DbColumnName("TradeScreeningStatusId")]
        public Byte? TradeScreeningStatusId { get; set; }

        [ProtoMember(30)]
        [DbColumnName("FraudStatusId")]
        public Byte? FraudStatusId { get; set; }

        [ProtoMember(31)]
        [DbColumnName("FraudSource")]
        public Byte? FraudSource { get; set; }

        [ProtoMember(32)]
        [DbColumnName("FraudStatusChangeDateTime")]
        public DateTime? FraudStatusChangeDateTime { get; set; }

        [ProtoMember(33)]
        [DbColumnName("FraudReasonCodeId")]
        public Byte? FraudReasonCodeId { get; set; }
    }
}
