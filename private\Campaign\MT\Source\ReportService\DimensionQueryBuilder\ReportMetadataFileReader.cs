﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ReportService
{
    using ProtoBuf;
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using global:: ReportService.Interface.Common;

#pragma warning disable CS0618 // Type or member is obsolete
    public class ReportMetadataFileReader
    {
        readonly Func<ProtoReader, object>[] columnReaders;
        readonly Stream stream;
        readonly int columnCount;

        public ReportMetadataFileReader(Stream stream, string[] dbTypes)
        {
            if (dbTypes == null)
            {
                throw new ArgumentNullException(nameof(dbTypes));
            }

            this.stream = stream ?? throw new ArgumentNullException(nameof(stream));
            this.columnCount = dbTypes.Length;
            this.columnReaders = new Func<ProtoReader, object>[columnCount];

            for (int i = 0; i < columnCount; i++)
            {
                this.columnReaders[i] = DBTypeToColumnReaders(dbTypes[i]);
            }
        }

        public IEnumerable<List<object>> ReadRows(int totalRows)
        {
            using (var reader = ProtoReader.Create(stream, null, null))
            {
                int rowsLeft = totalRows;

                while (rowsLeft > 0)
                {
                    rowsLeft--;
                    reader.ReadFieldHeader();

                    var token = ProtoReader.StartSubItem(reader);
                    var values = new object[this.columnCount];
                    int fieldIndex = 0;

                    while ((fieldIndex = reader.ReadFieldHeader()) != 0)
                    {
                        values[fieldIndex - 1] = columnReaders[fieldIndex - 1](reader);
                    }

                    ProtoReader.EndSubItem(token, reader);
                    yield return new List<object>(values);
                }

                yield break;
            }
        }

        public IEnumerable<object[]> ReadRowsAsArray(int totalRows)
        {
            using (var reader = ProtoReader.Create(stream, null, null))
            {
                int rowsLeft = totalRows;

                while (rowsLeft > 0)
                {
                    rowsLeft--;
                    reader.ReadFieldHeader();

                    var token = ProtoReader.StartSubItem(reader);
                    var values = new object[this.columnCount];
                    int fieldIndex = 0;

                    while ((fieldIndex = reader.ReadFieldHeader()) != 0)
                    {
                        values[fieldIndex - 1] = columnReaders[fieldIndex - 1](reader);
                    }

                    ProtoReader.EndSubItem(token, reader);
                    yield return values;
                }

                yield break;
            }
        }

        public IEnumerable<(int RowId, IDictionary<string, object> Properties)> ReadRows(string[] fieldNames, int totalRows, int pageNumber = 1, int? pageSize = null)
        {
            using (var reader = ProtoReader.Create(stream, null, null))
            {
                int truePageSize = pageSize.HasValue ? pageSize.Value : totalRows;
                int start = ((pageNumber - 1) * truePageSize);
                int end = Math.Min((start + truePageSize), totalRows);
                int read = 0;

                while (read < end)
                {
                    reader.ReadFieldHeader();

                    var token = ProtoReader.StartSubItem(reader);
                    var values = fieldNames.ToDictionary(fn => fn, fn => (object)null);
                    var shouldSkip = read < start;
                    int fieldIndex = 0;

                    while ((fieldIndex = reader.ReadFieldHeader()) != 0)
                    {
                        if (shouldSkip)
                        {
                            reader.SkipField();
                        }
                        else
                        {
                            values[fieldNames[fieldIndex - 1]] = columnReaders[fieldIndex - 1](reader);
                        }
                    }

                    ProtoReader.EndSubItem(token, reader);

                    if (!shouldSkip)
                    {
                        yield return (read, values);
                    }

                    read++;
                }

                yield break;
            }
        }

        public IEnumerable<(int RowId, IDictionary<string, object> Properties)> ReadRows(string[] fieldNames, ReportColumn[] reportColumns, FormatHelper formatHelper, int totalRows, int pageNumber = 1, int? pageSize = null)
        {
            using (var reader = ProtoReader.Create(stream, null, null))
            {
                int truePageSize = pageSize.HasValue ? pageSize.Value : totalRows;
                int start = ((pageNumber - 1) * truePageSize);
                int end = Math.Min((start + truePageSize), totalRows);
                int read = 0;

                while (read < end)
                {
                    reader.ReadFieldHeader();

                    var token = ProtoReader.StartSubItem(reader);
                    var values = fieldNames.ToDictionary(fn => fn, fn => (object)null);
                    var shouldSkip = read < start;
                    int fieldIndex = 0;

                    while ((fieldIndex = reader.ReadFieldHeader()) != 0)
                    {
                        if (shouldSkip)
                        {
                            reader.SkipField();
                        }
                        else
                        {
                            var value = columnReaders[fieldIndex - 1](reader);
                            value = formatHelper.FormatOutputData(reportColumns[fieldIndex - 1], value,fieldIndex - 1);
                            values[fieldNames[fieldIndex - 1]] = value;
                        }
                    }

                    ProtoReader.EndSubItem(token, reader);

                    if (!shouldSkip)
                    {
                        yield return (read, values);
                    }

                    read++;
                }

                yield break;
            }
        }


        private static Func<ProtoReader, object> DBTypeToColumnReaders(string dbTypeName)
        {
            switch (dbTypeName.ToLowerInvariant())
            {
                case "varchar":
                case "nvarchar":
                case "char":
                case "nchar":
                case "string":
                case "nullable(string)":
                    return (ProtoReader reader) =>
                    {
                        return reader.ReadString();
                    };
                case "tinyint":
                case "uint8":
                    return (ProtoReader reader) =>
                    {
                        return reader.ReadByte();
                    };
                case "smallint":
                case "int16":
                    return (ProtoReader reader) =>
                    {
                        return reader.ReadInt16();
                    };
                case "uint16":
                case "ushort":
                    return (ProtoReader reader) =>
                    {
                        return reader.ReadUInt16();
                    };
                case "integer":
                case "int":
                case "int32":
                    return (ProtoReader reader) =>
                    {
                        return reader.ReadInt32();
                    };
                case "uint":
                case "uint32":
                    return (ProtoReader reader) =>
                    {
                        return reader.ReadUInt32();
                    };
                case "bigint":
                case "int64":
                case "long":
                case "nullable(int64)":
                    return (ProtoReader reader) =>
                    {
                        return reader.ReadInt64();
                    };
                case "ulong":
                case "uint64":
                    return (ProtoReader reader) =>
                    {
                        return reader.ReadUInt64();
                    };
                case "double":
                case "float":
                case "nullable(float64)":
                    return (ProtoReader reader) =>
                    {
                        return reader.ReadDouble();
                    };
                case "decimal":
                case "money":
                case "smallmoney":
                case "numeric":
                case "decimal128(12)":
                case "decimal128(4)":
                case "nullable(decimal128(12))":
                case "nullable(decimal128(4))":
                    return (ProtoReader reader) =>
                    {
                        return (decimal)(double)reader.ReadDouble();
                    };
                case "datetime":
                case "datetime2":
                case "smalldatetime":
                case "date":
                case "time":
                    return (ProtoReader reader) =>
                    {
                        return new DateTime(reader.ReadInt64());
                    };
                case "int8":
                    return (ProtoReader reader) =>
                    {
                        return reader.ReadSByte();
                    };
                case "variant":
                    return (ProtoReader reader) =>
                    {
                        var dict = new Dictionary<int, decimal?>();
                        var token = ProtoReader.StartSubItem(reader);
                        int field;
                        int? key = null;
                        decimal? value = null;
                        while ((field = reader.ReadFieldHeader()) != 0)
                        {
                            switch (field)
                            {
                                case 1:
                                    key = reader.ReadInt32();
                                    break;
                                case 2:
                                    value = (decimal)reader.ReadDouble();
                                    break;
                            }

                            // When both key and value are read, add to dictionary and reset
                            if (key.HasValue)
                            {
                                dict[key.Value] = value;
                                key = null;
                                value = null;
                            }
                        }
                        ProtoReader.EndSubItem(token, reader);
                        return dict;
                    };
                default:
                    throw new NotImplementedException($"Unexpected db data type:{dbTypeName}");
            }
        }
    }
#pragma warning restore CS0618 // Type or member is obsolete
}
