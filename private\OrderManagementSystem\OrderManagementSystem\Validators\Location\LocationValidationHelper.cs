﻿using OrderManagementSystem.Entities;

namespace OrderManagementSystem.Common.BusinessLogicHelpers
{
    public static class LocationValidationHelper
    {
        // Validates that no inclusion and exclusion occur at the same level (Country, Region, City).
        public static void ValidateNoMixedInclusionExclusion(
            string source,
            List<Location> included,
            List<Location> excluded,
            Result result,
            ILogger logger)
        {
            foreach (var level in new[] { TargetType.Country, TargetType.Region, TargetType.City })
            {
                bool hasInclude = included.Any(l => l.Type == level);
                bool hasExclude = excluded.Any(l => l.Type == level);

                if (hasInclude && hasExclude)
                {                    
                    result.AddError(new Error(
                        source,
                        ErrorMessage.CannotMixInclusionAndExclusionAtSameLevel,
                        property: [nameof(LocationTarget.Locations)],
                        type: ErrorType.UserError),
                        logger, $"{source}: Cannot mix inclusion and exclusion for {level} level. " +
                        "This will result in unexpected behavior. Please review your configuration.");
                }
            }
        }
        // Checks if any child region or city of the given parent is included (i.e., not excluded).
        public static bool HasIncludedChild(
            TargetType parentType,
            int parentId,
            IEnumerable<XandrRegion> regions,
            IEnumerable<XandrCity> cities,
            List<Location> includedLocations)
        {
            if (parentType == TargetType.Country)
            {
                var regionIds = regions.Where(r => r.CountryId == parentId).Select(r => r.Id).ToHashSet();
                var cityIds = cities.Where(c => c.CountryId == parentId).Select(c => c.Id).ToHashSet();

                return includedLocations.Any(l =>
                                 (l.Type == TargetType.Region && regionIds.Contains(l.Id)) ||
                                 (l.Type == TargetType.City && cityIds.Contains(l.Id)));
            }

            if (parentType == TargetType.Region)
            {
                var cityIds = cities.Where(c => c.RegionId == parentId).Select(c => c.Id).ToHashSet();
                return includedLocations.Any(l =>
                                          l.Type == TargetType.City &&
                                          cityIds.Contains(l.Id));
            }

            return false;
        }

        // Checks if any child region or city of the given parent exists in the location list (regardless of exclusion).
        public static bool HasAnyChild(
            TargetType parentType,
            int parentId,
            IEnumerable<XandrRegion> regions,
            IEnumerable<XandrCity> cities,
            List<Location> locations)
        {
            if (parentType == TargetType.Country)
            {
                var regionIds = regions.Where(r => r.CountryId == parentId).Select(r => r.Id).ToHashSet();
                var cityIds = cities.Where(c => c.CountryId == parentId).Select(c => c.Id).ToHashSet();

                return locations.Any(l =>
                    (l.Type == TargetType.Region && regionIds.Contains(l.Id)) ||
                    (l.Type == TargetType.City && cityIds.Contains(l.Id)));
            }

            if (parentType == TargetType.Region)
            {
                var cityIds = cities.Where(c => c.RegionId == parentId).Select(c => c.Id).ToHashSet();
                return locations.Any(l =>
                    l.Type == TargetType.City &&
                    cityIds.Contains(l.Id));
            }

            return false;
        }
    }
}
