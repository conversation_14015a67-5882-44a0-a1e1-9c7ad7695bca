﻿using OrderManagementSystem.Common;
using OrderManagementSystem.DataAccessObjects;
using OrderManagementSystem.Interfaces;
using OrderManagementSystem.Requests;

namespace OrderManagementSystem.Processors
{
    public class MediaPlanDeleteProcessor : BaseProcessor<MediaPlanDeleteRequest>
    {
        protected readonly IMediaPlanDao _mediaPlanDao;
        protected readonly IXandrApiHelper<string> _xandrApiHelper;
        protected readonly ILineDao _lineDao;
        protected readonly ILineProcessorHelper _lineProcessorHelper;
        protected readonly IMediaPlanOutputCache _MediaPlanOutputCache;

        public MediaPlanDeleteProcessor(IValidator<MediaPlanDeleteRequest> validator, IMediaPlanDao dao, IXandrApiHelper<string> xandr<PERSON>pi<PERSON>elper, ILineDao lineDao, ILineProcessorHelper lineProcessorHelper, IMediaPlanOutputCache MediaPlanOutputCache) : base(validator)
        {
            _mediaPlanDao = dao;
            _xandrApiHelper = xandrApiHelper;
            _lineDao = lineDao;
            _lineProcessorHelper = lineProcessorHelper;
            _MediaPlanOutputCache = MediaPlanOutputCache;
        }

        public override async Task<Result> Execute(MediaPlanDeleteRequest request)
        {
            var result = new Result();

            try
            {
                //Before deleting from DB, we will delete from Monetize if it exists there
                var mediaPlanGetRequest = new MediaPlanGetRequest(request.Logger, request.CustomerId, request.Id);
                var mediaPlanGetResult = await _mediaPlanDao.Get(mediaPlanGetRequest);
                result.Merge(mediaPlanGetResult);
                if (mediaPlanGetResult.Failed) { return result; }

                var mediaPlan = mediaPlanGetResult.Entity!;

                if (mediaPlan.MonetizeInsertionOrderId != null)
                {
                    //First, we must delete all the child Lines. We will only delete the lines that are committed
                    var lineGetAllRequest = new LineGetAllRequest(request.Logger, request.CustomerId, request.Id);
                    var lineResult = await _lineDao.Get(lineGetAllRequest);
                    result.Merge(lineResult);
                    if (lineResult.Failed) { return result; }

                    foreach (var line in lineResult.Entity!)
                    {
                        if (line.MonetizeLineItemId == null) { continue; }

                        var lineDeleteRequest = new LineDeleteRequest(request.Logger, request.CustomerId, request.Id, line.Id, request.UserId);
                        result.Merge(await _lineProcessorHelper.DeleteLineFromMonetize(lineDeleteRequest, line));
                        if (result.Failed) { return result; }

                        var lineDeleteResult = await _lineDao.Delete(lineDeleteRequest);
                        result.Merge(lineDeleteResult);
                        if (lineDeleteResult.Failed) { return result; }
                    }

                    //Now that all child Lines are deleted, we can delete the media plan
                    var monetizeDeleteUrl = $"{XandrApiHelper.XandrIOCommitBaseUrl}?id={mediaPlan.MonetizeInsertionOrderId!}&advertiser_id={request.CustomerId}";
                    var deleteResponse = await _xandrApiHelper.Delete(request, monetizeDeleteUrl);
                    result.Merge(deleteResponse);
                    if (deleteResponse.Failed) { return result; }
                }

                //Now delete from DB
                result.Merge(await _mediaPlanDao.Delete(request));

            }
            finally
            {  
                // Clear the media plan output cache 
                _MediaPlanOutputCache.Clear();
            }

            return result;
        }
    }
}
