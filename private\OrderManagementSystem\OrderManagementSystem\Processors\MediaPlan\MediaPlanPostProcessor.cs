﻿using OrderManagementSystem.Common;
using OrderManagementSystem.DataAccessObjects;
using OrderManagementSystem.Entities;
using OrderManagementSystem.Entities.Internal;
using OrderManagementSystem.Entities.External;
using OrderManagementSystem.Interfaces;
using OrderManagementSystem.Requests;

namespace OrderManagementSystem.Processors
{
    public class MediaPlanPostProcessor : BaseProcessor<MediaPlanPostRequest, MediaPlanOutput>
    {
        protected readonly IMediaPlanDao Dao;
        protected readonly IMediaPlanProcessorHelper ProcessorHelper;
        private readonly IMediaPlanOutputCache MediaPlanOutputCache;

        public MediaPlanPostProcessor(IValidator<MediaPlanPostRequest> validator, IMediaPlanDao dao, IMediaPlanProcessorHelper processorHelper, IMediaPlanOutputCache mediaPlanOutputCache) : base(validator)
        {
            this.Dao = dao;
            this.ProcessorHelper = processorHelper;
            this.MediaPlanOutputCache = mediaPlanOutputCache;
        }
        
        public override async Task<Result<MediaPlanOutput>> Execute(MediaPlanPostRequest request)
        {
            //Set the status to Draft
            request.MediaPlan.Status = MediaPlanStatus.Draft;

            var resultOutput = new Result<MediaPlanOutput>();

            try
            {
                var result = await this.Dao.Post(request);

                resultOutput.Merge(result);
                if (resultOutput.Failed) { return resultOutput; }

                var customerResult = ProcessorHelper.GetCustomer(request.Logger, request.MediaPlan.CustomerId);
                resultOutput.Merge(customerResult);
                if (resultOutput.Failed) { return resultOutput; }

                var entity = result.Entity?.ToOutput(customerResult.Entity!);
                resultOutput.Entity = entity;
                if (resultOutput.Entity != null)
                {
                    //No estimated impressions upon creation
                    resultOutput.Entity.EstimatedImpressions = 0;
                }

            }
            finally
            {
                // Clear the media plan output cache 
                MediaPlanOutputCache.Clear();

            }

            return resultOutput;
        }
    }
}
