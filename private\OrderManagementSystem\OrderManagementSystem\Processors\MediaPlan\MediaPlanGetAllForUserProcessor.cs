﻿using OrderManagementSystem.Common;
using OrderManagementSystem.DataAccessObjects;
using OrderManagementSystem.Entities.External;
using OrderManagementSystem.Interfaces;
using OrderManagementSystem.Requests;

namespace OrderManagementSystem.Processors
{
    public class MediaPlanGetAllForUserProcessor : BaseProcessor<MediaPlanGetAllForUserRequest, MediaPlanOutput[]>
    {
        protected readonly IMediaPlanDao Dao;
        protected readonly ICustomerDao CustomerDao;
        protected readonly ILineDao LineDao;
        protected readonly IMediaPlanProcessorHelper ProcessorHelper;
        protected readonly IMediaPlanOutputCache MediaPlanOutputCache;

        public MediaPlanGetAllForUserProcessor(IValidator<MediaPlanGetAllForUserRequest> validator, IMediaPlanDao dao, ICustomerDao customerDao, ILineDao lineDao, IMediaPlanProcessorHelper processorHelper, IMediaPlanOutputCache MediaPlanOutputCache) : base(validator)
        {
            this.Dao = dao;
            this.CustomerDao = customerDao;
            this.LineDao = lineDao;
            this.ProcessorHelper = processorHelper;
            this.MediaPlanOutputCache = MediaPlanOutputCache;
        }

        public override async Task<Result<MediaPlanOutput[]>> Execute(MediaPlanGetAllForUserRequest request)
        {
            var result = new Result<MediaPlanOutput[]>();

            // Check if the desired page is already cached
            var cacheOutput = MediaPlanOutputCache.GetOutput();
            if (cacheOutput != null && cacheOutput.Count != 0)
            {
                cacheOutput = FilterSortPageOutput(cacheOutput, request, out int cachedCount);
                result.Count = cachedCount;
                result.Entity = cacheOutput.ToArray();
                return result;
            }

            var getMediaPlanResult = await this.Dao.Get(request);
            result.Merge(getMediaPlanResult, true);
            if (result.Failed) { return result; }

            var customerResult = await this.CustomerDao.Get(new CustomerGetAllRequest(request.Logger));
            result.Merge(customerResult);
            if (result.Failed) { return result; }

            var customerMap = customerResult.Entity!.ToDictionary(c => c.Id, c => c);
            var outputList = await ProcessorHelper.GetAllEstimatedImpressions(request.Logger, customerMap, getMediaPlanResult.Entity);

            // Cache the output 
            MediaPlanOutputCache.SetOutput(outputList);
            
            // Filter and sort the output list based on request parameters
            outputList = FilterSortPageOutput(outputList, request, out int count);
            result.Count = count;
            result.Entity = outputList!.ToArray();

            return result;
        }

        private List<MediaPlanOutput> FilterSortPageOutput(List<MediaPlanOutput> outputList, MediaPlanGetAllForUserRequest request, out int count)
        {
            outputList = request.Filter != null ? ProcessorHelper.Filter(outputList, request.Filter)!.ToList() : outputList;
            
            // Tracking the count after filtering
            count = outputList.Count;
            
            outputList = request.OrderBy != null ? ProcessorHelper.Sort(outputList, request.OrderBy)!.ToList() : outputList;
            outputList = request.Skip != null ? outputList.Skip((int)request.Skip).ToList() : outputList;
            outputList = request.Top != null ? outputList.Take((int)request.Top).ToList() : outputList;
            
            return outputList;
        }
    }
}
