﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.DAO
{
    using System;
    using Microsoft.Data.SqlClient;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common;
    using Microsoft.AdCenter.Shared.MT.DAO;
    using Microsoft.AdCenter.Shared.MT.Piloting;

    public static partial class SpWrappers
    {
        public static SqlCommand CreateGetCampaignsSummaryByAccountIdCommand(AccountCallContext context, DateTime? lastSyncTime)
        {
            var procName = "dbo.prc_PublicCampaignSummaryGet_V50";
            SqlCommand cmd = SPHelper.MakeSqlCommandWithTracking(procName, context.Logger.CallTrackingData, 0);
            //SpWrapperUtil.AddCallContextParameters(cmd, context);
            SpWrapperUtil.AddAccountCallContextParameters(cmd, context);

            if (lastSyncTime.HasValue)
            {
                cmd.Parameters.Add(DateTimeParam("@LastSyncTime", lastSyncTime.Value));
            }

            return cmd;
        }
    }
}
