﻿namespace Microsoft.Advertising.Advertiser.Api.V2
{
    using CampaignMiddleTierTest.Framework;
    using CampaignMiddleTierTest.Framework.DBValidators;
    using CampaignMiddleTierTest.Framework.Utilities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using Microsoft.Test.MaDLybZ;
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Configuration;
    using System.Linq;
    using System.Net.Http;
    using System.Text;
    using VisualStudio.TestTools.UnitTesting;

    [TestClass]
    public class BulkUpsertAccountPlacementExclusionList : CampaignTestBase
    {
        const int dataRowsOffset = 2;

        private CustomerInfo cInfo;
        private long providerId = 1;
        private long secondProviderId = 3;
        private string url;
        private string tryUrl;
        private string taskStatusUrlFmt;
        private ODataCaller oDataCaller = new ODataCaller();

        [TestInitialize]
        public void Initialize()
        {
            cInfo = CustomerInfo.CreateStandardAdvertiserForExpertWithPilot(Features.AccountPlacementExclusionList, Features.AccountPlacementInclusionList);
            url = ApiVersion.BaseUrl + string.Format("/Customers({0})/Accounts({1})/Default.BulkUpsert", cInfo.CustomerId, cInfo.AccountIds.First());
            tryUrl = ApiVersion.BaseUrl + string.Format("/Customers({0})/Accounts({1})/Default.TryBulkUpsertSync", cInfo.CustomerId, cInfo.AccountIds.First());
            taskStatusUrlFmt = ApiVersion.BaseUrl + string.Format("/Customers({0})/Accounts({1})/TaskItemExecutions({{0}})?$expand=TaskItem", cInfo.CustomerId, cInfo.AccountIds.First());
            SetSiteExclusionListRelatedLimits(cInfo, 3, 10, 2, 5);
        }

        [TestCleanup]
        public void Cleanup()
        {
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void BulkUpsert_AccountPlacementExclusionList_CreateAndUpdate_Success()
        {
            string AccountPlacementExclusionName = "AccountPlacementExclusion-" + StringUtil.GenerateUniqueId();

            var json =
                $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id,Site List Item Url"",
                           ""Format Version,6,,,,"",
                           ""Account Placement Exclusion List,{AccountPlacementExclusionName},0-0-0,-1,,"",
                           ""Account Placement Exclusion List Item,,0-0-0-0,,-1,www.BulkUpsert_AccountPlacementExclusionList_CreateAndUpdate_Success.com""
                        ]
                    }}";

            var responseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var upsertResponse = responseJson.ToString();
            var result = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(upsertResponse);
            Assert.AreEqual("Type", result[0][0]);
            Assert.AreEqual("Format Version", result[1][0]);

            var errorColumnIndex = result[0].IndexOf("Error");
            Assert.IsFalse(errorColumnIndex > 0, "Unexpected error during create");
            if (errorColumnIndex > 0)
            {
                Console.WriteLine($"unexpected errors in response: {upsertResponse}");
            }

            var idColumnIndex = result[0].IndexOf("Id");
            Assert.IsTrue(idColumnIndex > 0, "Id column was not found in header of BulkUpsert response.");
            long AccountPlacementExclusionListId = long.Parse(result[2][idColumnIndex]);

            int urlColumnIndex = result[0].IndexOf("Site List Item Url");
            // confirm result contains excluded url column
            Assert.AreNotEqual(-1, urlColumnIndex, "Missing column");

            Assert.IsFalse(string.IsNullOrEmpty(result[3][urlColumnIndex]), "Expected Site List Item Url column");

            AccountPlacementExclusionName = "updated-" + AccountPlacementExclusionName;

            json =
                $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id,AccountPlacementExclusionListId"",
                           ""Format Version,6,,,,"",
                           ""Account Placement Exclusion List,{AccountPlacementExclusionName},0-0-0,{AccountPlacementExclusionListId},,""
                        ]
                    }}";

            var updResponseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var updResult = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(updResponseJson.ToString());
            errorColumnIndex = updResult[0].IndexOf("Error");
            Assert.IsFalse(errorColumnIndex > 0, "Unexpected error during update");

            json =
                $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id,Status"",
                           ""Format Version,6,,,,"",
                           ""Account Placement Exclusion List,{AccountPlacementExclusionName},0-0-0,{AccountPlacementExclusionListId},,Deleted""
                        ]
                    }}";

            var delResponseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var delResult = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(delResponseJson.ToString());
            errorColumnIndex = delResult[0].IndexOf("Error");
            Assert.IsFalse(errorColumnIndex > 0, "Unexpected error during delete");

        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void BulkUpsert_AccountPlacementExclusionList_CreateAndDeleteItems_Success()
        {
            string AccountPlacementExclusionName = "AccountPlacementExclusion-" + StringUtil.GenerateUniqueId();

            var json =
                $@"{{
                         ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id,Site List Item Url"",
                           ""Format Version,6,,,,"",
                           ""Account Placement Exclusion List,{AccountPlacementExclusionName},0-0-0,-1,,,"",
                           ""Account Placement Exclusion List Item,,0-0-0-0,,-1,www.BulkUpsert_AccountPlacementExclusionList_CreateAndDeleteItems_Success1.com""
                        ]
                    }}";

            var responseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var result = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(responseJson.ToString());
            Assert.AreEqual("Type", result[0][0]);
            Assert.AreEqual("Format Version", result[1][0]);
            var idColumnIndex = result[0].IndexOf("Id");
            Assert.IsTrue(idColumnIndex > 0, "Id column was not found in header of BulkUpsert response.");
            long AccountPlacementExclusionListId = long.Parse(result[2][idColumnIndex]);
            long itemId = long.Parse(result[3][idColumnIndex]);

            AssertAccountPlacementExclusionInfoSuccess(3, result);

            json =

                $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id,Site List Item Url,Status,AccountPlacementExclusion Name,AccountPlacementExclusion Url,Editorial Status,Editorial Status Date"",
                           ""Format Version,6,,,,"",
                           ""Account Placement Exclusion List Item,,0-0-0-0,{itemId},{AccountPlacementExclusionListId},wwww.BulkUpsert_AccountPlacementExclusionList_CreateAndDeleteItems_Success1.com,Deleted,wrong name,wrong url,wrong status,wrong date"",
                           ""Account Placement Exclusion List Item,,0-0-0-0,,{AccountPlacementExclusionListId},wwww.BulkUpsert_AccountPlacementExclusionList_CreateAndDeleteItems_Success2.com,Active,wrong name,wrong url,wrong status,wrong date"",
                           ""Account Placement Exclusion List Item,,0-0-0-0,,{AccountPlacementExclusionListId},wwww.BulkUpsert_AccountPlacementExclusionList_CreateAndDeleteItems_Success3.com,Active,,,,""
                        ],
                        ""ReturnAllFields"":true
                    }}";

            var updResponseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var updResult = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(updResponseJson.ToString());

            Assert.AreEqual(5, updResult.Count, "Unexpected row count in response");

            AssertAccountPlacementExclusionInfoSuccess(2, updResult);
            AssertAccountPlacementExclusionInfoSuccess(3, updResult);
            AssertAccountPlacementExclusionInfoSuccess(4, updResult);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void BulkUpsert_AccountPlacementExclusionList_CreateItems_PartialSuccess()
        {
            string AccountPlacementExclusionName = "AccountPlacementExclusion-" + StringUtil.GenerateUniqueId();

            var json =
                $@"{{
                         ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id,Site List Item Url"",
                           ""Format Version,6,,,,"",
                           ""Account Placement Exclusion List,{AccountPlacementExclusionName},0-0-0,-1,,,"",
                           ""Account Placement Exclusion List Item,,0-0-0-0,,-1,www.Success.com"",
                           ""Account Placement Exclusion List Item,,0-0-0-0,,-1,+#_invalid_URL_#!""
                        ]
                    }}";

            var responseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var result = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(responseJson.ToString());
            Assert.AreEqual("Type", result[0][0]);
            Assert.AreEqual("Format Version", result[1][0]);
            var idColumnIndex = result[0].IndexOf("Id");
            Assert.IsTrue(idColumnIndex > 0, "Id column was not found in header of BulkUpsert response.");
            long AccountPlacementExclusionListId = long.Parse(result[2][idColumnIndex]);

            Assert.AreEqual(6, result.Count, "Unexpected row count in response");

            AssertAccountPlacementExclusionInfoSuccess(5, result);
            var errorColumnIndex = result[0].IndexOf("Error");
            var errorNumberColumnIndex = result[0].IndexOf("Error Number");
            Assert.IsTrue(errorColumnIndex > 0 && errorNumberColumnIndex > 0, "Error column was not found in header of BulkUpsert response.");

            Assert.AreEqual("CampaignServiceInvalidNegativeSiteURL", result[4][errorColumnIndex]);
            Assert.AreEqual("1012", result[4][errorNumberColumnIndex]);
        }


        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [TestCategory("CIOnly")]
        public void BulkUpsert_AccountPlacementExclusionList_DeleteItems_Fail_InvalidAccountPlacementExclusionItemId_CIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(cInfo.CustomerId, false, Features.BroadOnlyCampaign);

            string AccountPlacementExclusionName = "AccountPlacementExclusion-" + StringUtil.GenerateUniqueId();

            var json =
               $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id,Site List Item Url"",
                           ""Format Version,6,,,,"",
                           ""Account Placement Exclusion List,{AccountPlacementExclusionName},0-0-0,-1,,"",
                           ""Account Placement Exclusion List Item,,0-0-0-0,,-1,wwww.AccountPlacementExclusionId1.com"",
                           ""Account Placement Exclusion List Item,,0-0-0-0,,-1,wwww.AccountPlacementExclusionId2.com"",
                           ""Account Placement Exclusion List Item,,0-0-0-0,,-1,wwww.AccountPlacementExclusionId3.com""
                        ]
                    }}";

            var responseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var result = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(responseJson.ToString());
            Assert.AreEqual("Type", result[0][0]);
            Assert.AreEqual("Format Version", result[1][0]);
            var idColumnIndex = result[0].IndexOf("Id");
            Assert.IsTrue(idColumnIndex > 0, "Id column was not found in header of BulkUpsert response.");
            long AccountPlacementExclusionListId = long.Parse(result[2][idColumnIndex]);
            long itemId1 = long.Parse(result[3][idColumnIndex]);
            long itemId2 = long.Parse(result[4][idColumnIndex]);

            json =
               $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id,Site List Item Url,Status,AccountPlacementExclusion Name,AccountPlacementExclusion Url,Editorial Status,Editorial Status Date"",
                           ""Format Version,6,,,,"",
                           ""Account Placement Exclusion List Item,,0-0-0-0,{itemId1},-1,wwww.AccountPlacementExclusionId.com,Deleted,wrong name,wrong url,wrong status,wrong date"",
                           ""Account Placement Exclusion List Item,,0-0-0-0,{itemId2},-1,wwww.AccountPlacementExclusionId3.com,Deleted,wrong name,wrong url,wrong status,wrong date""
                        ],
                        ""ReturnAllFields"":true
                    }}";

            responseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            result = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(responseJson.ToString());

            var errorColumnIndex = result[0].IndexOf("Error");
            var errorNumberColumnInex = result[0].IndexOf("Error Number");
            Assert.IsTrue(errorColumnIndex > 0 && errorNumberColumnInex > 0, "Error expected during update");
            Assert.AreEqual("CampaignServiceSharedListIdInvalid", result[3][errorColumnIndex]);
            Assert.AreEqual("4317", result[3][errorNumberColumnInex]);
            Assert.AreEqual("CampaignServiceSharedListIdInvalid", result[5][errorColumnIndex]);
            Assert.AreEqual("4317", result[5][errorNumberColumnInex]);

            AssertAccountPlacementExclusionInfoFail(2, result);
            AssertAccountPlacementExclusionInfoFail(3, result);
            AssertAccountPlacementExclusionInfoFail(4, result);
            AssertAccountPlacementExclusionInfoFail(5, result);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [TestCategory("CIOnly")]
        public void BulkUpsert_AccountPlacementExclusionList_CreateItems_Fail_OverLimitItemCount_CIOnly()
        {
            string AccountPlacementExclusionName = "AccountPlacementExclusion-" + StringUtil.GenerateUniqueId();

            StringBuilder AccountPlacementExclusionItems = new StringBuilder();

            int itemCount = 12;
            // Generate max+1 "AccountPlacementExclusion Item" rows
            for (int i = 0; i < itemCount; i++)
            {
                string nextAccountPlacementExclusionId = "AccountPlacementExclusionId" + i.ToString();
                AccountPlacementExclusionItems.Append($@"""Account Placement Exclusion List Item,,0-0-0-0,,-1,www.{nextAccountPlacementExclusionId}.com""");
                if (i < itemCount - 1)
                {
                    AccountPlacementExclusionItems.Append(",");
                }
                AccountPlacementExclusionItems.AppendLine();
            }

            var json =
                    $@"{{
                ""CsvRows"":
                [
                ""Type,Name,Client Id,Id,Parent Id,Site List Item Url"",
                ""Format Version,6,,,,"",
                ""Account Placement Exclusion List,{AccountPlacementExclusionName},0-0-0,-1,,"",
                {AccountPlacementExclusionItems.ToString()}
                ]
                }}";

            var responseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var result = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(responseJson.ToString());
            Assert.AreEqual((2* itemCount) + 3, result.Count, "Bulk upsert response count does not match");

            var errorColumnIndex = result[0].IndexOf("Error");
            var errorNumberColumnIndex = result[0].IndexOf("Error Number");
            Assert.IsTrue(errorColumnIndex > 0 && errorNumberColumnIndex > 0, "Error expected during update");
            Assert.AreEqual("CampaignServiceMaxListItemLimitExceededForList", result[result.Count - 1][errorColumnIndex]);
            Assert.AreEqual("4355", result[result.Count - 1][errorNumberColumnIndex]);

        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        [TestCategory("CIOnly")]
        public void BulkUpsert_AccountPlacementExclusionList_UpdateItems_Fail_OverLimitItemCount_CIOnly()
        {
            string AccountPlacementExclusionName = "AccountPlacementExclusion-" + StringUtil.GenerateUniqueId();

            StringBuilder AccountPlacementExclusionItems = new StringBuilder();
            int nextItemId = 0;
            // Generate 9000 "AccountPlacementExclusion Item" rows
            for (int i = 0; i < 9; i++)
            {
                nextItemId++;
                string nextAccountPlacementExclusionId = "AccountPlacementExclusionId" + nextItemId.ToString();
                AccountPlacementExclusionItems.AppendLine($@"""Account Placement Exclusion List Item,,0-0-0-0,,-1,www.{nextAccountPlacementExclusionId}.com"",");
            }
            AccountPlacementExclusionItems.Length = AccountPlacementExclusionItems.Length - 3;

            var json =
                    $@"{{
                ""CsvRows"":
                [
                ""Type,Name,Client Id,Id,Parent Id,Site List Item Url"",
                ""Format Version,6,,,,"",
                ""Account Placement Exclusion List,{AccountPlacementExclusionName},0-0-0,-1,,"",
                {AccountPlacementExclusionItems.ToString()}
                ]
                }}";

            var responseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var result = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(responseJson.ToString());
            Assert.AreEqual(12, result.Count, "Bulk upsert response count does not match");
            var idIndex = result[0].IndexOf("Id");
            var parentId = result[2][idIndex];

            //update Account Placement Exclusion List to delete and add some item within threshold in total
            StringBuilder AccountPlacementExclusionItemsDeleted = new StringBuilder();
            StringBuilder AccountPlacementExclusionItemsAdded = new StringBuilder();
            for (var i = 3; i < 6; i++)
            {
                var itemId = result[i][idIndex];
                string nextAccountPlacementExclusionId = "AccountPlacementExclusionId" + i.ToString();
                AccountPlacementExclusionItemsDeleted.AppendLine($@"""Account Placement Exclusion List Item,,0-0-0-0,{itemId},{parentId},www.{nextAccountPlacementExclusionId}.com,Deleted"",");
            }
            AccountPlacementExclusionItemsDeleted.Length = AccountPlacementExclusionItemsDeleted.Length - 3;

            for (int i = 0; i < 3; i++)
            {
                nextItemId++;
                string nextAccountPlacementExclusionId = "AccountPlacementExclusionId" + nextItemId.ToString();
                AccountPlacementExclusionItemsAdded.AppendLine($@"""Account Placement Exclusion List Item,,0-0-0-0,,{parentId},www.{nextAccountPlacementExclusionId}.com,"",");
            }
            AccountPlacementExclusionItemsAdded.Length = AccountPlacementExclusionItemsAdded.Length - 3;

            json =
                    $@"{{
                ""CsvRows"":
                [
                ""Type,Name,Client Id,Id,Parent Id,Site List Item Url,Status"",
                ""Format Version,6,,,,,"",
                {AccountPlacementExclusionItemsDeleted.ToString()},
                {AccountPlacementExclusionItemsAdded.ToString()}
                ]
                }}";

            responseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            result = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(responseJson.ToString());
            Assert.AreEqual(5, result.Count, "Bulk upsert response count does not match");


            ////update Account Placement Exclusion List to delete and add some item over threshold in total
            AccountPlacementExclusionItemsDeleted = new StringBuilder();
            AccountPlacementExclusionItemsAdded = new StringBuilder();
            for (var i = 2; i < 4; i++)
            {
                var itemId = result[i][idIndex];
                string nextAccountPlacementExclusionId = "AccountPlacementExclusionId" + i.ToString();
                AccountPlacementExclusionItemsDeleted.AppendLine($@"""Account Placement Exclusion List Item,,0-0-0-0,{itemId},{parentId},www.{nextAccountPlacementExclusionId}.com,Deleted"",");
            }
            AccountPlacementExclusionItemsDeleted.Length = AccountPlacementExclusionItemsDeleted.Length - 3;

            for (int i = 0; i < 10; i++)
            {
                nextItemId++;
                string nextAccountPlacementExclusionId = "AccountPlacementExclusionId" + nextItemId.ToString();
                AccountPlacementExclusionItemsAdded.AppendLine($@"""Account Placement Exclusion List Item,,0-0-0-0,,{parentId},wwww.{nextAccountPlacementExclusionId}.com,"",");
            }
            AccountPlacementExclusionItemsAdded.Length = AccountPlacementExclusionItemsAdded.Length - 3;

            json =
                    $@"{{
                ""CsvRows"":
                [
                ""Type,Name,Client Id,Id,Parent Id,Site List Item Url,Status"",
                ""Format Version,6,,,,,"",
                {AccountPlacementExclusionItemsDeleted.ToString()},
                {AccountPlacementExclusionItemsAdded.ToString()}
                ]
                }}";

            responseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            result = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(responseJson.ToString());
            Assert.AreEqual(22, result.Count, "Bulk upsert response count does not match");

            var errorColumnIndex = result[0].IndexOf("Error");
            var errorNumberColumnInex = result[0].IndexOf("Error Number");
            Assert.IsTrue(errorColumnIndex > 0 && errorNumberColumnInex > 0, "Error expected during update");
            Assert.AreEqual("CampaignServiceMaxListItemLimitExceededForList", result[7][errorColumnIndex]);
            Assert.AreEqual("4355", result[7][errorNumberColumnInex]);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void BulkUpsert_AccountPlacementExclusionList_CreateAndDelete_Success()
        {
            string AccountPlacementExclusionName = "AccountPlacementExclusion-" + StringUtil.GenerateUniqueId();

            var json =
                $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id,Site List Item Url"",
                           ""Format Version,6,,,,"",
                           ""Account Placement Exclusion List,{AccountPlacementExclusionName},0-0-0,-1,,"",
                           ""Account Placement Exclusion List Item,,0-0-0-0,,-1,BulkUpsert_AccountPlacementExclusionList_CreateAndDelete_Success""
                        ]
                    }}";

            var responseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var result = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(responseJson.ToString());
            Assert.AreEqual("Type", result[0][0]);
            Assert.AreEqual("Format Version", result[1][0]);
            var idColumnIndex = result[0].IndexOf("Id");
            Assert.IsTrue(idColumnIndex > 0, "Id column was not found in header of BulkUpsert response.");
            long listItemId = long.Parse(result[2][idColumnIndex]);

            json =
                $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id,Account Placement Exclusion List Item Id,Status"",
                           ""Format Version,6,,,,"",
                           ""Account Placement Exclusion List,,0-0-0-0,{listItemId},,,Deleted""
                        ]
                    }}";

            var delResponseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var delResult = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(delResponseJson.ToString());

            var errorColumnIndex = delResult[0].IndexOf("Error");
            Assert.IsFalse(errorColumnIndex > 0, "Unexpected error during update");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void BulkUpsert_AccountPlacementInclusionList_CreateAndDelete_Success()
        {
            string listName = "AccountPlacementInclusion-" + StringUtil.GenerateUniqueId();

            var json =
                $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id,Site List Item Url"",
                           ""Format Version,6,,,,"",
                           ""Account Placement Inclusion List,{listName},0-0-0,-1,,"",
                           ""Account Placement Inclusion List Item,,0-0-0-0,,-1,www.BulkUpsert_AccountPlacementInclusionList_CreateAndDelete_Success.com""
                        ]
                    }}";

            var responseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var result = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(responseJson.ToString());
            Assert.AreEqual("Type", result[0][0]);
            Assert.AreEqual("Format Version", result[1][0]);
            var idColumnIndex = result[0].IndexOf("Id");
            Assert.IsTrue(idColumnIndex > 0, "Id column was not found in header of BulkUpsert response.");
            long listItemId = long.Parse(result[2][idColumnIndex]);

            json =
                $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id,Account Placement Exclusion List Item Id,Status"",
                           ""Format Version,6,,,,"",
                           ""Account Placement Inclusion List,,0-0-0-0,{listItemId},,,Deleted""
                        ]
                    }}";

            var delResponseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var delResult = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(delResponseJson.ToString());

            var errorColumnIndex = delResult[0].IndexOf("Error");
            Assert.IsFalse(errorColumnIndex > 0, "Unexpected error during update");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void BulkUpsert_AccountPlacementExclusionList_SearchCampaignAssociation_Success()
        {
            var campaignCollection = CreateTestSearchCampaigns(cInfo, 1);

            var AccountPlacementExclusionListCollection = Verify_BulkUpsert_AccountPlacementExclusionList_CampaignAssociation(cInfo, campaignCollection, AssertNoBulkUpsertErrors);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void BulkUpsert_AccountPlacementExclusionList_AudienceCampaignAssociation_Success()
        {
            var campaignCollection = CreateTestAudienceCampaigns(cInfo, 1);

            var AccountPlacementExclusionListCollection = Verify_BulkUpsert_AccountPlacementExclusionList_CampaignAssociation(cInfo, campaignCollection, AssertNoBulkUpsertErrors);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void BulkUpsert_AccountPlacementInclusionList_AudienceCampaignAssociation_Success()
        {
            var campaignCollection = CreateTestAudienceCampaigns(cInfo, 1);

            var AccountPlacementExclusionListCollection = Verify_BulkUpsert_AccountPlacementInclusionList_CampaignAssociation(cInfo, campaignCollection, AssertNoBulkUpsertErrors);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void BulkUpsert_AccountPlacementExclusionList_ShoppingCampaignAssociation_Success_CIOnly()
        {
            var campaignCollection = CreateTestShoppingCampaigns(cInfo, 1);

            var AccountPlacementExclusionListCollection = Verify_BulkUpsert_AccountPlacementExclusionList_CampaignAssociation(cInfo, campaignCollection, AssertNoBulkUpsertErrors);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void BulkUpsert_AccountPlacementExclusionList_CampaignAssociation_Failure()
        {
            var cInfo2 = CustomerInfo.CreateStandardAdvertiser();
            var campaignCollection = CreateTestSearchCampaigns(cInfo2, 1);

            Action<dynamic> verifyAssociationResults = (dynamic associationResult) =>
            {
                AssertBulkUpsertError(associationResult, 1, errorCode: "CampaignServiceInvalidCampaignId", errorNumber: "1100");
            };
            var AccountPlacementExclusionListCollection = Verify_BulkUpsert_AccountPlacementExclusionList_CampaignAssociation(cInfo, campaignCollection, verifyAssociationResults);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void BulkUpsert_AccountPlacementExclusionList_CampaignAssociation_Duplicate_Failure()
        {
            var campaignCollection = CreateTestSearchCampaigns(cInfo, 1);

            long campaignId = campaignCollection.Campaigns[0].Data.Id;

            var AccountPlacementExclusionListCollection = CreateTestAccountPlacementExclusionLists(cInfo, 1, 1);
            long AccountPlacementExclusionListId = AccountPlacementExclusionListCollection.SharedLists[0].Id.Value;

            var url = BuildBulkUpsertUrl(cInfo);
            var json =
                $@"{{
                    ""CsvRows"":
                    [
                        ""Type,Name,Client Id,Id,Parent Id"",
                        ""Format Version,6,,,"",
                        ""Campaign Account Placement Exclusion List Association,,0-0-0,{AccountPlacementExclusionListId},{campaignId}"",
                        ""Campaign Account Placement Exclusion List Association,,0-0-1,{AccountPlacementExclusionListId},{campaignId}""
                    ]
                }}";

            var associationResponseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var associationResult = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(associationResponseJson.ToString());

            AssertBulkUpsertError(associationResult, clientId: "0-0-1", errorCode: "CampaignServiceSharedEntityAssociationDuplicate", errorNumber: "4309");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void BulkUpsert_AccountPlacementExclusionList_CampaignAssociation_Multiple_Success()
        {
            var campaignCollection = CreateTestSearchCampaigns(cInfo, 1);

            var AccountPlacementExclusionListCollection = Verify_BulkUpsert_AccountPlacementExclusionList_CampaignAssociation_Multiple(cInfo, campaignCollection, AssertNoBulkUpsertErrors);

            var campaignId = campaignCollection.Ids[0];
            var AccountPlacementExclusionListId = AccountPlacementExclusionListCollection.SharedLists[0].Id.Value;
            Verify_BulkUpsert_AccountPlacementExclusionList_CampaignDisassociation(cInfo, campaignId, AccountPlacementExclusionListId, AssertNoBulkUpsertErrors);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void BulkUpsert_AccountPlacementExclusionList_CampaignAssociation_Multiple_Failure()
        {
            var cInfo2 = CustomerInfo.CreateStandardAdvertiser();
            var campaignCollection = CreateTestSearchCampaigns(cInfo2, 1);

            Action<dynamic> verifyAssociationResults = (dynamic associationResult) =>
            {
                AssertBulkUpsertError(associationResult, 1, errorCode: "CampaignServiceInvalidCampaignId", errorNumber: "1100");
                AssertBulkUpsertError(associationResult, 3, errorCode: "CampaignServiceInvalidCampaignId", errorNumber: "1100");
            };
            var AccountPlacementExclusionListCollection = Verify_BulkUpsert_AccountPlacementExclusionList_CampaignAssociation_Multiple(cInfo, campaignCollection, verifyAssociationResults);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void BulkUpsert_AccountPlacementExclusionList_CampaignAssociation_WithIsExcluded_Duplicate_Failure()
        {
            var campaignCollection = CreateTestSearchCampaigns(cInfo, 1);

            long campaignId = campaignCollection.Campaigns[0].Data.Id;

            var AccountPlacementExclusionListCollection = CreateTestAccountPlacementExclusionLists(cInfo, 1, 1);
            long AccountPlacementExclusionListId = AccountPlacementExclusionListCollection.SharedLists[0].Id.Value;

            var url = BuildBulkUpsertUrl(cInfo);
            var json =
                $@"{{
                    ""CsvRows"":
                    [
                        ""Type,Name,Client Id,Id,Parent Id,Is Excluded"",
                        ""Format Version,6,,,"",
                        ""Campaign Account Placement Exclusion List Association,,0-0-0,{AccountPlacementExclusionListId},{campaignId},wrong"",
                        ""Campaign Account Placement Exclusion List Association,,0-0-1,{AccountPlacementExclusionListId},{campaignId},wrong""
                    ]
                }}";

            var associationResponseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var associationResult = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(associationResponseJson.ToString());

            AssertBulkUpsertError(associationResult, clientId: "0-0-1", errorCode: "CampaignServiceSharedEntityAssociationDuplicate", errorNumber: "4309");

        }

        private static TestSharedListCollection Verify_BulkUpsert_AccountPlacementExclusionList_CampaignAssociation(CustomerInfo cInfo, TestCampaignCollection campaignCollection, Action<dynamic> verifyAssociationResults)
        {
            long campaignId = campaignCollection.Campaigns[0].Data.Id;

            var accountPlacementExclusionListCollection = CreateTestAccountPlacementExclusionLists(cInfo, 1, 1);
            long AccountPlacementExclusionListId = accountPlacementExclusionListCollection.SharedLists[0].Id.Value;

            var url = BuildBulkUpsertUrl(cInfo);
            var json =
                $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id"",
                           ""Format Version,6,,,"",
                           ""Campaign Account Placement Exclusion List Association,,0-0-0,{AccountPlacementExclusionListId},{campaignId}""
                        ]
                    }}";

            var associationResponseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var associationResult = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(associationResponseJson.ToString());
            verifyAssociationResults(associationResult);

            return accountPlacementExclusionListCollection;
        }

        private static TestSharedListCollection Verify_BulkUpsert_AccountPlacementInclusionList_CampaignAssociation(CustomerInfo cInfo, TestCampaignCollection campaignCollection, Action<dynamic> verifyAssociationResults)
        {
            long campaignId = campaignCollection.Campaigns[0].Data.Id;

            var accountPlacementExclusionListCollection = CreateTestAccountPlacementExclusionLists(cInfo, 1, 1, inclusion:true);
            long AccountPlacementExclusionListId = accountPlacementExclusionListCollection.SharedLists[0].Id.Value;

            var url = BuildBulkUpsertUrl(cInfo);
            var json =
                $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id"",
                           ""Format Version,6,,,"",
                           ""Campaign Account Placement Inclusion List Association,,0-0-0,{AccountPlacementExclusionListId},{campaignId}""
                        ]
                    }}";

            var associationResponseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var associationResult = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(associationResponseJson.ToString());
            verifyAssociationResults(associationResult);

            return accountPlacementExclusionListCollection;
        }

        private static void Verify_BulkUpsert_AccountPlacementExclusionList_CampaignAssociationWithSomeNullEntityIds(CustomerInfo cInfo, TestCampaignCollection campaignCollection)
        {
            long campaignId = campaignCollection.Campaigns[0].Data.Id;

            var accountPlacementExclusionListCollection = CreateTestAccountPlacementExclusionLists(cInfo, 1, 1);
            long AccountPlacementExclusionListId = accountPlacementExclusionListCollection.SharedLists[0].Id.Value;

            var url = BuildBulkUpsertUrl(cInfo);
            var json =
                $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id"",
                           ""Format Version,6,,,"",
                           ""Campaign Account Placement Exclusion List Association,,0-0-0,{AccountPlacementExclusionListId},{campaignId}""
                           ""Campaign Account Placement Exclusion List Association,,0-0-0,{AccountPlacementExclusionListId},""
                        ]
                    }}";

            var associationResponseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m =>
                {
                    Assert.IsFalse(m.IsSuccessStatusCode);
                    Assert.IsTrue(m.Content.ReadAsStringAsync().Result.Contains(@"""Code"":""EntityIsNull"""));
                });
        }

        private static TestSharedListCollection Verify_BulkUpsert_AccountPlacementExclusionList_CampaignAssociation_Multiple(CustomerInfo cInfo, TestCampaignCollection campaignCollection, Action<dynamic> verifyAssociationResults)
        {
            long campaignId = campaignCollection.Campaigns[0].Data.Id;

            var accountPlacementExclusionListCollection = CreateTestAccountPlacementExclusionLists(cInfo, 2, 1);
            long AccountPlacementExclusionListId1 = accountPlacementExclusionListCollection.SharedLists[0].Id.Value;
            long AccountPlacementExclusionListId2 = accountPlacementExclusionListCollection.SharedLists[1].Id.Value;

            var url = BuildBulkUpsertUrl(cInfo);
            var json =
                $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id"",
                           ""Format Version,6,,,"",
                           ""Campaign Account Placement Exclusion List Association,,0-0-0,{AccountPlacementExclusionListId1},{campaignId}"",
                           ""Campaign Account Placement Exclusion List Association,,0-0-0,{AccountPlacementExclusionListId2},{campaignId}""
                        ]
                    }}";

            var associationResponseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var associationResult = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(associationResponseJson.ToString());
            verifyAssociationResults(associationResult);

            return accountPlacementExclusionListCollection;
        }

        private static void Verify_BulkUpsert_AccountPlacementExclusionList_CampaignDisassociation(CustomerInfo cInfo, long campaignId, long AccountPlacementExclusionListId, Action<dynamic> verifyDisassociationResults)
        {
            var json =
                    $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id,Status"",
                           ""Format Version,6,,,,"",
                           ""Account Placement Exclusion List,,0-0-0,{AccountPlacementExclusionListId},{cInfo.AccountIds[0]},Deleted"",
                           ""Campaign Account Placement Exclusion List Association,,0-0-0,{AccountPlacementExclusionListId},{campaignId},Deleted""
                        ],
                        ""ReturnAllFields"":true
                    }}";

            var url = BuildBulkUpsertUrl(cInfo);
            var deleteResponseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var deleteResult = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(deleteResponseJson.ToString());
            verifyDisassociationResults(deleteResult);
        }

        private static TestSharedListCollection Verify_BulkUpsert_AccountPlacementExclusionList_CampaignAssociation_WithIsExcluded_Multiple(CustomerInfo cInfo, TestCampaignCollection campaignCollection, Action<dynamic> verifyAssociationResults)
        {
            long campaignId = campaignCollection.Campaigns[0].Data.Id;

            var accountPlacementExclusionListCollection = CreateTestAccountPlacementExclusionLists(cInfo, 2, 1);
            long AccountPlacementExclusionListId1 = accountPlacementExclusionListCollection.SharedLists[0].Id.Value;
            long AccountPlacementExclusionListId2 = accountPlacementExclusionListCollection.SharedLists[1].Id.Value;

            var url = BuildBulkUpsertUrl(cInfo);
            var json =
                $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id,Is Excluded"",
                           ""Format Version,6,,,"",
                           ""Campaign Account Placement Exclusion List Association,,0-0-0,{AccountPlacementExclusionListId1},{campaignId},wrong"",
                           ""Campaign Account Placement Exclusion List Association,,0-0-0,{AccountPlacementExclusionListId2},{campaignId},wrong""
                        ]
                    }}";

            var associationResponseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var associationResult = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(associationResponseJson.ToString());
            verifyAssociationResults(associationResult);

            return accountPlacementExclusionListCollection;
        }

        private static void Verify_BulkUpsert_AccountPlacementExclusionList_CampaignDisassociation_WithIsExcluded(CustomerInfo cInfo, long campaignId, long AccountPlacementExclusionListId, Action<dynamic> verifyDisassociationResults)
        {
            var json =
                    $@"{{
                        ""CsvRows"":
                        [
                           ""Type,Name,Client Id,Id,Parent Id,Status,Is Excluded"",
                           ""Format Version,6,,,,"",
                           ""Account Placement Exclusion List,,0-0-0,{AccountPlacementExclusionListId},{cInfo.AccountIds[0]},Deleted,wrong"",
                           ""Campaign Account Placement Exclusion List Association,,0-0-0,{AccountPlacementExclusionListId},{campaignId},Deleted,wrong""
                        ],
                        ""ReturnAllFields"":true
                    }}";

            var url = BuildBulkUpsertUrl(cInfo);
            var deleteResponseJson = ApiHelper.CallApi(
                cInfo,
                c => c.PostAsync(url, new StringContent(json, Encoding.UTF8, "application/json")),
                m => Assert.IsTrue(m.IsSuccessStatusCode));

            var deleteResult = JsonConvert.DeserializeObject<IEnumerable<IEnumerable<string>>>(deleteResponseJson.ToString());
            verifyDisassociationResults(deleteResult);
        }

        private static void AssertBulkUpsertError(dynamic result, int? dataRowIndex = null, string clientId = null, string errorNumber = null, string errorCode = null)
        {
            if (dataRowIndex == null && string.IsNullOrWhiteSpace(clientId))
            {
                throw new ArgumentException("AssertBulkUpsertError: Either dataRowIndex or clientId must be non-null");
            }
            if (errorNumber == null && errorCode == null)
            {
                throw new ArgumentException("AssertBulkUpsertError: Either errorCode or errorNumber must be non-null");
            }

            var errorCodeColumnIndex = result[0].IndexOf("Error");
            var errorNumberColumnIndex = result[0].IndexOf("Error Number");

            if (errorCodeColumnIndex == -1 || errorNumberColumnIndex == -1)
            {
                throw new ArgumentException("AssertBulkUpsertError: Error or Error Number column not found in header of BulkUpsert response.");
            }

            dynamic dataRow = null;
            if (clientId != null)
            {
                var typeColumnIndex = result[0].IndexOf("Type");
                var clientIdColumnIndex = result[0].IndexOf("Client Id");
                for (var rowIndex = dataRowsOffset; rowIndex < result.Count; rowIndex++)
                {
                    dataRow = result[rowIndex];
                    if (dataRow[typeColumnIndex].ToString().EndsWith(" Error") && dataRow[clientIdColumnIndex] == clientId)
                    {
                        break;
                    }
                }

                Assert.IsNotNull(dataRow, $"AssertBulkUpsertError: An error data row with Client Id \"{clientId}\" not found in BulkUpsert response");
            }
            else
            {
                dataRow = result[(int)dataRowIndex + dataRowsOffset];
                Assert.IsNotNull(dataRow, $"AssertBulkUpsertError: An error data row with index \"{dataRow}\" not found in BulkUpsert response");
            }

            if (errorCode != null)
            {
                string retErrorCode = dataRow[errorCodeColumnIndex];
                Assert.AreEqual(errorCode, retErrorCode, "Wrong error code in BulkUpsert response");
            }

            if (errorNumber != null)
            {
                string retErrorMessage = dataRow[errorNumberColumnIndex];
                Assert.AreEqual(errorNumber, retErrorMessage, "Wrong error number in BulkUpsert response");
            }
        }

        private static void AssertNoBulkUpsertErrors(dynamic result)
        {
            var errorCodeColumnIndex = result[0].IndexOf("Error");
            var errorNumberColumnIndex = result[0].IndexOf("Error Number");
            if (errorCodeColumnIndex != -1 && errorNumberColumnIndex != -1) //require all files
            {
                for (var i = 2; i < result.Count; i++)
                {
                    Assert.IsTrue(String.IsNullOrEmpty(result[i][errorCodeColumnIndex]) && String.IsNullOrEmpty(result[i][errorNumberColumnIndex]), "AssertNoBulkUpsertErrors: one or more errors present in BulkUpsert response");

                }
            }
            else
            {
                Assert.IsTrue(errorCodeColumnIndex == -1 && errorNumberColumnIndex == -1, "AssertNoBulkUpsertErrors: one or more errors present in BulkUpsert response");

            }
        }

        private static void AssertAccountPlacementExclusionInfoSuccess(int rowNumber, dynamic result)
        {
            //var AccountPlacementExclusionNameIndex = result[0].IndexOf("AccountPlacementExclusion Name");
            //Assert.IsTrue(AccountPlacementExclusionNameIndex > 0, "AccountPlacementExclusion Name column was not found in header of BulkUpsert response.");
            //Assert.IsFalse(String.IsNullOrEmpty(result[rowNumber][AccountPlacementExclusionNameIndex]), "AccountPlacementExclusion Name is not found in BulkUpsert response.");
            //Assert.AreNotEqual("wrong name", result[rowNumber][AccountPlacementExclusionNameIndex],"AccountPlacementExclusion name is wrong name");

            //var AccountPlacementExclusionUrlIndex = result[0].IndexOf("AccountPlacementExclusion Url");
            //Assert.IsTrue(AccountPlacementExclusionUrlIndex > 0, "AccountPlacementExclusion Url column was not found in header of BulkUpsert response.");
            //Assert.IsFalse(String.IsNullOrEmpty(result[rowNumber][AccountPlacementExclusionUrlIndex]), "AccountPlacementExclusion Url is not found in BulkUpsert response.");
            //Assert.AreNotEqual("wrong url", result[rowNumber][AccountPlacementExclusionNameIndex], "AccountPlacementExclusion url is wrong url");
        }

        private static void AssertAccountPlacementExclusionInfoFail(int rowNumber, dynamic result)
        {
            //var AccountPlacementExclusionNameIndex = result[0].IndexOf("AccountPlacementExclusion Name");
            //Assert.IsTrue(AccountPlacementExclusionNameIndex > 0, "AccountPlacementExclusion Name column was not found in header of BulkUpsert response.");
            //Assert.IsFalse(String.IsNullOrEmpty(result[rowNumber][AccountPlacementExclusionNameIndex]), "AccountPlacementExclusion Name is not found in BulkUpsert response.");
            //Assert.AreEqual("wrong name", result[rowNumber][AccountPlacementExclusionNameIndex], "AccountPlacementExclusion name is not wrong name");

            //var AccountPlacementExclusionUrlIndex = result[0].IndexOf("AccountPlacementExclusion Url");
            //Assert.IsTrue(AccountPlacementExclusionUrlIndex > 0, "AccountPlacementExclusion Url column was not found in header of BulkUpsert response.");
            //Assert.IsFalse(String.IsNullOrEmpty(result[rowNumber][AccountPlacementExclusionUrlIndex]), "AccountPlacementExclusion Url is not found in BulkUpsert response.");
            //Assert.AreEqual("wrong url", result[rowNumber][AccountPlacementExclusionUrlIndex], "AccountPlacementExclusion url is not wrong url");
        }

        private static string BuildBulkUpsertUrl(CustomerInfo cInfo)
        {
            return ApiVersion.BaseUrl + string.Format("/Customers({0})/Accounts({1})/Default.BulkUpsert", cInfo.CustomerId, cInfo.AccountIds.First());
        }

        private static TestCampaignCollection CreateTestSearchCampaigns(CustomerInfo cInfo, int numberOfCampaigns)
        {
            var campaignCollection = new TestCampaignCollection(
                numberOfCampaigns,
                CampaignFactory.CampaignType.Default
            );
            foreach (var campaign in campaignCollection.Campaigns)
            {
                campaign.Data.Languages = new Language[] { Language.All };
            }
            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(cInfo));
            return campaignCollection;
        }

        private static TestCampaignCollection CreateTestAudienceCampaigns(CustomerInfo cInfo, int numberOfCampaigns)
        {
            var campaignCollection = new TestCampaignCollection(
                numberOfCampaigns,
                CampaignFactory.CampaignType.Audience
            );
            foreach (var campaign in campaignCollection.Campaigns)
            {
                campaign.Data.Languages = new Language[] { Language.All };
            }
            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(cInfo));
            return campaignCollection;
        }
 
        private static TestCampaignCollection CreateTestShoppingCampaigns(CustomerInfo cInfo, int campaignsPerAccountToCreate)
        {
            var campaignCollection = new TestCampaignCollection(
                campaignsPerAccountToCreate,
                CampaignFactory.CampaignType.Shopping, CampaignFactory.CampaignSettings.Valid, CampaignSettingsFactory.ShoppingSetting.Valid, ShoppingSettingsFactory.SalesCountry.US, ShoppingSettingsFactory.ProviderId.One
            );
            ResponseValidator.ValidateBasicSuccess(campaignCollection.Add(cInfo));
            return campaignCollection;
        }

        private static TestSharedListCollection CreateTestAccountPlacementExclusionLists(CustomerInfo cInfo, int listCount, int listItemCount, string listNameBase = "BVT List Add", bool inclusion = false)
        {

            var placementListCollection = new TestSharedListCollection(
                    cInfo,
                    cInfo.AccountIds[0],
                    defaultToCustomerLevel: false,
                    TestSharedListCollection.AccountPlacementExclusionListType);

            var list = CreateAccountPlacementExclusionList(listNameBase, inclusion);

            var addResponse = placementListCollection.AddSharedEntity(null, list);


            var list2 = CreateAccountPlacementExclusionList(listNameBase, inclusion);

            addResponse = placementListCollection.AddSharedEntity(null, list2);
            return placementListCollection;
        }

        private static AccountPlacementExclusionList CreateAccountPlacementExclusionList(string name, bool isInclusion = false)
        {
            return new AccountPlacementExclusionList()
            {
                Name = $"{name} - {Guid.NewGuid().ToString().Substring(0, 10)}",
                Type = TestSharedListCollection.AccountPlacementExclusionListType,
                IsInclusion = isInclusion
            };
        }

        private static void SetSiteExclusionListRelatedLimits(CustomerInfo cinfo, int maxExListPerAccount = 3, int maxExListItemsPerList=5, int maxInListPerAccount = 3, int maxInListItemsPerList = 5)
        {
            var query = 
                @$"INSERT INTO dbo.CustomSystemLimit(CustomerId, SystemLimitTypeId, SystemLimitValue, ModifiedByUserId) 
                SELECT {cinfo.CustomerId}, 105, {maxExListPerAccount}, 0 UNION
                SELECT {cinfo.CustomerId}, 106, {maxExListItemsPerList}, 0 UNION
                SELECT {cinfo.CustomerId}, 107, {maxInListPerAccount}, 0 UNION
                SELECT {cinfo.CustomerId}, 108, {maxInListItemsPerList}, 0
                ";

            DBValidator.ExecuteInCampaignDB(cinfo.CustomerId, query);
        }
    }
}
