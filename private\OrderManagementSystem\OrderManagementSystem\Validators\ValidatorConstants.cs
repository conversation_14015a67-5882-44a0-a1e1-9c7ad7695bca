﻿using OrderManagementSystem.Common;
using System.Text.RegularExpressions;

namespace OrderManagementSystem.Validators
{
    public static class ValidatorConstants
    {
        public static readonly int[] ALLOWED_PUBLISHER_IDS =
        [
            XandrConstants.XBOX_PUBLISHER_ID,
            XandrConstants.FUNCTIONALTEST_PUBLISHER_ID,
            XandrConstants.XBOX_PPE_PUBLISHER_ID,
            XandrConstants.MSN_PPE_PUBLISHER_ID,
            XandrConstants.MCG_PPE_PUBLISHER_ID,
            XandrConstants.LOTUS_PPE_PUBLISHER_ID
        ];

        public static readonly string[] ALLOWED_OPERATORS =
        [
            "eq",
            "ne",
            "lt",
            "le",
            "ge",
            "gt",
            "in",
            "contains",
            "between"
        ];

        public const int PRODUCT_NAME_MAX_LENGTH = 255;
        public const int PRODUCT_PLACEMENTS_MAX = 10;
        public const int PRODUCT_FILTER_MAX_LENGTH = 1024;

        public const int MEDIA_PLAN_NAME_MAX_LENGTH = 255;
        public const int MEDIA_PLAN_DESCRIPTION_MAX_LENGTH = 255;
        public const int MEDIA_PLAN_OPPORTUNITY_ID_MAX_LENGTH = 18;

        public const int LINE_NAME_MAX_LENGTH = 255;
        public const int LINE_DESCRIPTION_MAX_LENGTH = 255;
        public const int LINE_FREQUENCY_CAP_LENGTH = 7;
        public const int LINE_FILTER_MAX_LENGTH = 1024;

        public const int FILTER_MAX_LENGTH = 1024;
        public const int ORDER_BY_MAX_LENGTH = 255;

        public static readonly Regex ForbiddenSymbolRegex = new Regex(@"[;,]", RegexOptions.Compiled); // Add more symbols as needed
    }
}