namespace FastDataPipeline.Contract.FastBI
{
    using ProtoBuf;
    using System;

    [ClickhouseCsvContract(schema: "BICH", columnList: new[] { nameof(DateKey), nameof(HourNum), nameof(AccountId), nameof(OrderId), nameof(PropertyUrl), nameof(DistributionChannelId),
        "(t) => {return t.LoadTime.ToString(\"yyyy-MM-dd HH:mm:ss\");}", nameof(ImpressionCount), nameof(ClickCount), nameof(ConversionCount),
        nameof(TotalPosition), nameof(TotalAmount), nameof(AssistCount), nameof(AdvertiserReportedRevenue), nameof(ExtendedCost),
        nameof(DeviceOSId), nameof(DeviceTypeId), nameof(MatchTypeId), nameof(BiddedMatchTypeId), nameof(DeviceOSId2), nameof(CampaignId),
        nameof(NetworkId), nameof(ConversionEnabledClickCount), nameof(ConversionEnabledTotalAmount), nameof(TotalSearchAbsTopPosition),
        nameof(TotalSearchTopPosition), nameof(TotalShoppingAbsTopPosition), nameof(SearchUniqueImpressionCount), nameof(ShoppingUniqueImpressionCount), nameof(ViewCnt),
        nameof(Percent25ViewCnt), nameof(Percent50ViewCnt), nameof(Percent75ViewCnt), nameof(CompletedViewCnt), nameof(TotalWatchTime), nameof(StartedViewCnt), nameof(SkippedViewCnt), nameof(WebsiteCountry), nameof(AdLanguage),
        nameof(MobileAppName), nameof(MobileAppBundle), nameof(MobileAppStoreUrl), nameof(TargetValueId), nameof(TargetTypeId) },
        Header = "DateKey,HourNum,AccountId,OrderId,PropertyUrl,DistributionChannelId,LoadTime,ImpressionCnt,ClickCnt,ConversionCnt,TotalPosition,TotalAmount,AssistCnt,AdvertiserReportedRevenue,ExtendedCost,DeviceOSId,DeviceTypeId,MatchTypeId,BiddedMatchTypeId,DeviceOSId2,CampaignId,NetworkId,ConversionEnabledClickCnt,ConversionEnabledTotalAmount,TotalSearchAbsTopPosition,TotalSearchTopPosition,TotalShoppingAbsTopPosition,SearchUniqueImpressionCnt,ShoppingUniqueImpressionCnt,ViewCnt,Percent25ViewCnt,Percent50ViewCnt,Percent75ViewCnt,CompletedViewCnt,TotalWatchTime,StartedViewCnt,SkippedViewCnt,WebsiteCountry,AdLanguage,AppName,AppBundle,AppStoreUrl,TargetValueId,TargetTypeId")]

    [ProtoContract]
    public partial class ContentPerformanceUsage
    {
        [ProtoMember(1)]
        [DbColumnName("DATEKEY")]
        public Int32 DateKey { get; set; }

        [ProtoMember(2)]
        [DbColumnName("HOURNUM")]
        public SByte HourNum { get; set; }

        [ProtoMember(3)]
        [DbColumnName("ACCOUNTID")]
        public Int32 AccountId { get; set; }

        [ProtoMember(4)]
        [DbColumnName("ORDERID")]
        [AcceptableDsvType(typeof(UInt64))]
        public Int64 OrderId { get; set; }

        [ProtoMember(5)]
        [DbColumnName("PROPERTYURL")]
        public String PropertyUrl { get; set; }

        [ProtoMember(6)]
        [DbColumnName("DISTRIBUTIONCHANNELID")]
        public Int16 DistributionChannelId { get; set; }

        [ProtoMember(7)]
        [DbColumnName("LOADTIME")]
        public DateTime LoadTime { get; set; }

        [ProtoMember(8)]
        [DbColumnName("IMPRESSIONCNT")]
        public Int32 ImpressionCount { get; set; }

        [ProtoMember(9)]
        [DbColumnName("CLICKCNT")]
        public Int32 ClickCount { get; set; }

        [ProtoMember(10)]
        [DbColumnName("CONVERSIONCNT")]
        public Int32 ConversionCount { get; set; }

        [ProtoMember(11)]
        [DbColumnName("TOTALPOSITION")]
        public Int32 TotalPosition { get; set; }

        [ProtoMember(12)]
        [DbColumnName("TOTALAMOUNT")]
        public Decimal TotalAmount { get; set; }

        [ProtoMember(13)]
        [DbColumnName("ASSISTCNT")]
        public Int32 AssistCount { get; set; }

        [ProtoMember(14)]
        [DbColumnName("ADVERTISERREPORTEDREVENUE")]
        public Decimal AdvertiserReportedRevenue { get; set; }

        [ProtoMember(15)]
        [DbColumnName("EXTENDEDCOST")]
        public Decimal ExtendedCost { get; set; }

        [ProtoMember(16)]
        [DbColumnName("DEVICEOSID")]
        public Int32 DeviceOSId { get; set; }

        [ProtoMember(17)]
        [DbColumnName("DEVICETYPEID")]
        public SByte DeviceTypeId { get; set; }

        [ProtoMember(18)]
        [DbColumnName("MATCHTYPEID")]
        public SByte MatchTypeId { get; set; }

        [ProtoMember(19)]
        [DbColumnName("BIDDEDMATCHTYPEID")]
        public SByte BiddedMatchTypeId { get; set; }

        [ProtoMember(20)]
        [DbColumnName("DEVICEOSID2")]
        public Int32 DeviceOSId2 { get; set; }

        [ProtoMember(21)]
        [DbColumnName("CAMPAIGNID")]
        [AcceptableDsvType(typeof(UInt64))]
        public Int64 CampaignId { get; set; }

        [ProtoMember(22)]
        [DbColumnName("NETWORKID")]
        public SByte NetworkId { get; set; }

        public Int32 CustomerId { get; set; }

        [ProtoMember(23)]
        [DbColumnName("ConversionEnabledClickCnt")]
        public Int32 ConversionEnabledClickCount { get; set; }

        [ProtoMember(24)]
        [DbColumnName("ConversionEnabledTotalAmount")]
        public Decimal ConversionEnabledTotalAmount { get; set; }

        [ProtoMember(25)]
        [DbColumnName("TotalSearchAbsTopPosition")]
        public Int32 TotalSearchAbsTopPosition { get; set; }

        [ProtoMember(26)]
        [DbColumnName("TotalSearchTopPosition")]
        public Int32 TotalSearchTopPosition { get; set; }

        [ProtoMember(27)]
        [DbColumnName("TotalShoppingAbsTopPosition")]
        public Int32 TotalShoppingAbsTopPosition { get; set; }

        [ProtoMember(28)]
        [DbColumnName("SearchUniqueImpressionCnt")]
        public Int32 SearchUniqueImpressionCount { get; set; }

        [ProtoMember(29)]
        [DbColumnName("ShoppingUniqueImpressionCnt")]
        public Int32 ShoppingUniqueImpressionCount { get; set; }

        [ProtoMember(30)]
        [DbColumnName("ViewCnt")]
        public Int32 ViewCnt { get; set; }

        [ProtoMember(31)]
        [DbColumnName("Percent25ViewCnt")]
        public Int32 Percent25ViewCnt { get; set; }

        [ProtoMember(32)]
        [DbColumnName("Percent50ViewCnt")]
        public Int32 Percent50ViewCnt { get; set; }

        [ProtoMember(33)]
        [DbColumnName("Percent75ViewCnt")]
        public Int32 Percent75ViewCnt { get; set; }

        [ProtoMember(34)]
        [DbColumnName("CompletedViewCnt")]
        public Int32 CompletedViewCnt { get; set; }

        [ProtoMember(35)]
        [DbColumnName("TotalWatchTime")]
        [AcceptableDsvType(typeof(UInt64))]
        public Int64 TotalWatchTime { get; set; }

        [ProtoMember(36)]
        [DbColumnName("StartedViewCnt")]
        public Int32 StartedViewCnt { get; set; }

        [ProtoMember(37)]
        [DbColumnName("SkippedViewCnt")]
        public Int32 SkippedViewCnt { get; set; }

        [ProtoMember(38)]
        [DbColumnName("WebsiteCountry")]
        public String WebsiteCountry { get; set; }

        [ProtoMember(39)]
        [DbColumnName("AdLanguage")]
        public String AdLanguage { get; set; }

        [ProtoMember(40)]
        [DbColumnName("AppName")]
        public String MobileAppName { get; set; }

        [ProtoMember(41)]
        [DbColumnName("AppBundle")]
        public String MobileAppBundle { get; set; }

        [ProtoMember(42)]
        [DbColumnName("AppStoreUrl")]
        public String MobileAppStoreUrl { get; set; }

        [ProtoMember(43)]
        [DbColumnName("TargetValueId")]
        [AcceptableDsvType(typeof(Int32))]
        public Int64 TargetValueId { get; set; }

        [ProtoMember(44)]
        [DbColumnName("TargetTypeId")]
        public Byte TargetTypeId { get; set; }
    }
}
