apiVersion: "clickhouse.altinity.com/v1"
kind: "ClickHouseInstallation"
metadata:
  name: "account"
  namespace: clickhouse

spec:
  defaults:
    templates: 
      dataVolumeClaimTemplate: managed-premium-volume
      podTemplate: clickhouse-pod
      serviceTemplate: chi-service-template
      shardServiceTemplate: shard-service-template
      replicaServiceTemplate: replica-service-template

  # Optional, allows tuning reconciling cycle for ClickhouseInstallation from clickhouse-operator side
  reconciling:
    # DISCUSSED TO BE DEPRECATED
    # Syntax sugar
    # Overrides all three 'reconcile.host.wait.{exclude, queries, include}' values from the operator's config
    # Possible values:
    #  - wait - should wait to exclude host, complete queries and include host back into the cluster
    #  - nowait - should NOT wait to exclude host, complete queries and include host back into the cluster
    policy: "nowait"
    configMapPropagationTimeout: 90
  #settings:
    # to allow scrape metrics via embedded prometheus protocol
  #  prometheus/endpoint: /metrics
  #  prometheus/port: 8888
  #  prometheus/metrics: true
  #  prometheus/events: true
  #  prometheus/asynchronous_metrics: true
  configuration:
    zookeeper:
      nodes:
      - host: clickhouse-keeper.chkeeper

    users:
      admin/password_sha256_hex: 37a8eec1ce19687d132fe29051dca629d164e2c4958ba141d5f4133a33f0688f

    #<!-- <dhParamsFile>/mnt/azure/share/certs/key.pem</dhParamsFile> -->
    files:
      config.d/server-setting.xml: |
        <yandex>
            <https_port>8443</https_port>
            <tcp_port_secure>9440</tcp_port_secure>
            <listen_host>0.0.0.0</listen_host>
        </yandex>
      config.d/openSSL-setting.xml: |
        <yandex>
            <openSSL>
                <server replace="replace">
                    <certificateFile>/etc/clickhouse-server/certs/clickhousecert.crt</certificateFile>
                    <privateKeyFile>/etc/clickhouse-server/certs/clickhousecert.key</privateKeyFile>
                    <verificationMode>none</verificationMode>
                    <loadDefaultCAFile>true</loadDefaultCAFile>
                    <cacheSessions>true</cacheSessions> 
                    <disableProtocols>sslv2,sslv3,tlsv1,tlsv1_1</disableProtocols>
                    <cipherList>ALL:!ADH:!LOW:!EXP:!MD5:!3DES:@STRENGTH</cipherList>
                    <preferServerCiphers>true</preferServerCiphers>
                </server>
            </openSSL>
        </yandex>
      config.d/Logger-setting.xml: |
        <yandex>
          <logger>
            <level>trace</level>
            <log>/var/lib/clickhouse/log/clickhouse-server/clickhouse-server.log</log>
            <errorlog>/var/lib/clickhouse/log/clickhouse-server/clickhouse-server.err.log</errorlog>
            <size>1024M</size>
            <count>96</count>
            <console>false</console>
          </logger>
        </yandex>
      config.d/backup_disk.xml: |
        <clickhouse>
            <storage_configuration>
                <disks>
                    <backups>
                        <type>local</type>
                        <path>/backups/</path>
                    </backups>
                </disks>
            </storage_configuration>
            <backups>
                <allowed_disk>backups</allowed_disk>
                <allowed_path>/backups/</allowed_path>
            </backups>
        </clickhouse>
      users.d/clickhouse-user-settings.xml: |
       <yandex>
            <profiles>
                <default>
                    <prefer_column_name_to_alias>0</prefer_column_name_to_alias>
                    <allow_experimental_join_condition>1</allow_experimental_join_condition>
                    <max_query_size>67108864</max_query_size>
                    <log_queries>1</log_queries>
                    <join_use_nulls>1</join_use_nulls>
                    <max_threads>8</max_threads>
                </default>
            </profiles>
       </yandex>

    clusters:
      - name: advbi
        layout:
          shardsCount: 3
          shards:
            - name: "0"
              replicas:
                - name: "0"
                  templates:
                    dataVolumeClaimTemplate: managed-premium-volume4
                - name: "1"
                  templates:
                    dataVolumeClaimTemplate: managed-premium-volume4
                - name: "2"
                  templates:
                    dataVolumeClaimTemplate: managed-premium-volume4
            - name: "1"
              replicas:
                - name: "0"
                  templates:
                    dataVolumeClaimTemplate: managed-premium-volume
                - name: "1"
                  templates:
                    dataVolumeClaimTemplate: managed-premium-volume
                - name: "2"
                  templates:
                    dataVolumeClaimTemplate: managed-premium-volume4
            - name: "2"
              replicas:
                - name: "0"
                  templates:
                    dataVolumeClaimTemplate: managed-premium-volume
                - name: "1"
                  templates:
                    dataVolumeClaimTemplate: managed-premium-volume
                - name: "2"
                  templates:
                    dataVolumeClaimTemplate: managed-premium-volume4
          replicasCount: 3

  templates:
    volumeClaimTemplates:
      - name: managed-premium-volume
        spec:
          storageClassName: managed-premium
          accessModes:
            - ReadWriteOnce
          resources:
            requests:
              storage: 3.5Ti
      - name: managed-premium-volume4
        spec:
          storageClassName: managed-premium
          accessModes:
            - ReadWriteOnce
          resources:
            requests:
              storage: 500Gi
    podTemplates:
      - name: clickhouse-pod
        metadata:
          labels:
            azure.workload.identity/use: "true"
            app: chi-clickhouse-server
        spec:
          serviceAccountName: aks-advbi-clickhouse-ppe
          nodeSelector:
            "agentpool": biaccount 
          volumes:
            - name: adv-bi-azurefile-volumne
              persistentVolumeClaim:
                 claimName: adv-bi-azurefile-pvc
            - name: adv-bi-cdrshare-volume
              persistentVolumeClaim:
                 claimName: adv-bi-si-cdrshare-pvc
            - name: adv-bi-bigbatchshare-volume
              persistentVolumeClaim:
                  claimName: adv-bi-si-bigbatchshare-pvc
            - name: clickhouse-cert-volume
              csi:
                driver: secrets-store.csi.k8s.io
                readOnly: true
                volumeAttributes:
                  secretProviderClass: azure-kv-clickhouse-cert      
          containers:
            - name: clickhouse-container
              image: campaignplatform.azurecr.io/dspbi/clickhouse-server:24.5
              env:
                 - name: REMOTE_SYNC_PASSWORD
                   value: "__ENV_REMOTE_SYNC_PASSWORD_PLACEHOLDER__"
              volumeMounts:
                - name: adv-bi-azurefile-volumne
                  mountPath: /mnt/azure/share
                - name: adv-bi-cdrshare-volume
                  mountPath: /mnt/cdrshare
                - name: adv-bi-bigbatchshare-volume
                  mountPath: /mnt/bigbatchshare
                - name: clickhouse-cert-volume
                  mountPath: /etc/clickhouse-server/certs
                  readOnly: true  
              resources:
                requests:
                  cpu: 10
                limits:
                  cpu: 16
              startupProbe:
                failureThreshold: 400
                httpGet:
                  path: /ping
                  port: http
                  scheme: HTTP
                initialDelaySeconds: 3
                periodSeconds: 3
                successThreshold: 1
                timeoutSeconds: 2
              readinessProbe:
                failureThreshold: 400
                httpGet:
                  path: /ping
                  port: http
                  scheme: HTTP
                initialDelaySeconds: 1
                periodSeconds: 3
                successThreshold: 1
                timeoutSeconds: 2
              livenessProbe:
                failureThreshold: 403
                httpGet:
                  path: /ping
                  port: http
                  scheme: HTTP
                initialDelaySeconds: 1
                periodSeconds: 3
                successThreshold: 1
                timeoutSeconds: 2 
            - name: clickhouse-backup
              image: campaignplatform.azurecr.io/dspbi/clickhouse-backup:latest
              imagePullPolicy: Always
              args: ["server"]
              env:
                 - name: LOG_LEVEL
                   value: "debug"
                 - name: ALLOW_EMPTY_BACKUPS
                   value: "true"
                 - name: API_LISTEN
                   value: "0.0.0.0:7171"
                 - name: BACKUPS_TO_KEEP_LOCAL
                   value: "-1"
                 - name: BACKUPS_TO_KEEP_REMOTE
                   value: "35"
                 # INSERT INTO system.backup_actions to execute backup 
                 - name: API_CREATE_INTEGRATION_TABLES
                   value: "true"
                 - name: REMOTE_STORAGE
                   value: "azblob"
                 - name: AZBLOB_ACCOUNT_NAME
                   value: "advbichdatastoragesi"
                 - name: AZBLOB_CONTAINER
                   value: "backup"
                 - name: AZBLOB_PATH
                   value: clickhouse/backup/{replica}
                 - name: AZBLOB_ACCOUNT_KEY
                   valueFrom:
                      secretKeyRef:
                        name: clickhouse-secret
                        key: account_key
              ports:
                 - name: backup-rest
                   containerPort: 7171

    serviceTemplates:
      - name: chi-service-template
        metadata:
          annotations:
            service.beta.kubernetes.io/azure-load-balancer-internal: "false"
        spec:
          ports:
            - name: https
              port: 8443
          type: LoadBalancer
      - name: shard-service-template
        metadata:
          annotations:
            service.beta.kubernetes.io/azure-load-balancer-internal: "false"
            # s2-account.eastus2.cloudapp.azure.com
            service.beta.kubernetes.io/azure-dns-label-name: s{shard}-{chi}
        spec:
          ports:
            - name: https
              port: 8443
          type: LoadBalancer
      - name: replica-service-template
        # type ServiceSpec struct from k8s.io/core/v1
        spec:
          ports:
            - name: http
              port: 8123
            - name: tcp
              port: 9000
            - name: interserver
              port: 9009
          type: ClusterIP
          clusterIP: None
