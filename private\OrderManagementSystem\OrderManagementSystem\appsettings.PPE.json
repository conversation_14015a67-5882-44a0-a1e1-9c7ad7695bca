{
  "ApplicationEnv": "PPE",
  "MetaDataService": {
    "Endpoint": "http://metadataservice-si-adsapps.trafficmanager.net:818",
    "KeyVaultName": "MetadataDB-KeyVaultSI",
    "OrderManagementSystemDBKeyVaultName": "CampaignDB-KeyVaultSI",
    "OrderManagementSystemCustomerDBKeyVaultName": "CampaignDB-KeyVaultSI",
    "AADAuthUserIdentityClientId": "271efe02-3add-46b7-be27-ecfac6c3f45c"
  },
  "AzureKeyVault": {
    "KeyVaultName": "CampaignSecretsKVSI",
    "OMSKeyVaultName": "CampaignOMSKVSI",
    "KeyVaultClientId": "644646e3-7089-4e32-b417-24edfa15f2f9",
    "OMSAdsUserPwdSecret": "OMSAdsUserPwd",
    "UseManagedIdentity": true
  },
  "KustoWriterConfig": {
    "KustoUrl": "https://ingest-bingadsppe.kusto.windows.net:443",
    "KustoApplicationId": "********-77a7-44ed-bd51-b52cc96d87e3"
  },
  "ConfigurationOptions": {
    "AppConfigStoreName": "OMS-SI-Config",
    "Label": "SI"
  },
  "ClientCenter": {
    "Endpoint": "https://siccmt.trafficmanager.net:3089/ClientCenter",
    "ClientCertificateThumbprint": "a6891a60b50398be469d5bde6085ae49c75d9c3c",
    "CcmtHttpsImpersonationTokenCertificateName": "ads-ccimpersonation-si"
  },
  "XandrConfig": {
    "Username": "omsservicetest",
    "PasswordKV": "OMSXandrLoginPwd",
    "AuthTokenKV": "OMSXandrAuthToken",
    "MemberId": 16505
  },
  "TLSCertName": "campaignplugin-tls",
  "ManagedIdentityId": "********-77a7-44ed-bd51-b52cc96d87e3",
  "EmailConfig": {
    "PublisherEndpoint": "https://mucp.api.account.microsoft-int.com/events/v1/trigger",
    "PublisherScope": "https://mucp.api.account.microsoft-int.com//.default",
    "OMSBaseUrl": "https://ui.ads-int.microsoft.com/campaign/oms/"
  },
  "StorageAccountConfig": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=ordermanagementsystemsi;ManagedIdentityId=********-77a7-44ed-bd51-b52cc96d87e3",
    "StorageAccountName": "ordermanagementsystemsi"
  },
  "AllowedPublisherIds": [
    2533173, // XBOX_PPE_PUBLISHER_ID
    2578529, // MSN_PPE_PUBLISHER_ID
    2597487, // MCG_PPE_PUBLISHER_ID
    2634549 //LOTUS_PPE_PUBLISHER_ID
  ],
  "DRSConfig": {
    "DRSScope": "6239bb2a-c29d-4c51-9f47-16416e93b6bd/.default",
    "ServiceBaseURL": "https://moderndrs.cp.microsoft-int.com/",
    "PartnerId": "1d8cbf93-ab53-4d1c-8a84-0ba34a69561a",
    "DRSAppId": "********-77a7-44ed-bd51-b52cc96d87e3",
    "APIVersion": "2018-05-31"
  },
  "DynamicConfig": {
    "FilterSegmentCacheResults": "false",
    "EnableDMATargeting": true,
    "EnableKVPTargeting": true,
    "IsImportEnabled": true,
    //System
    "Sentinal": 1
  }
}