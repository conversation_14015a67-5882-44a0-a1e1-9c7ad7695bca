using Microsoft.Ads.Mca.Common.Interface;
using Microsoft.Advertiser.CampaignManagement.BusinessRules;
using System.Reflection;

namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO
{
    using global::BingAds.Common.AdInsightODataClient;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Common;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EditorialRulesEngine;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ExtensionMethods;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.AdCenter.Shared.MT.EditorialManager;
    using Microsoft.AdCenter.Shared.MT.Facades;
    using Microsoft.Ads.Mca.Common.Client.Common;
    using Microsoft.Advertiser.AdInsight.MT.Interfaces.RSAV2.AggregationService;
    using Microsoft.Advertiser.CampaignManagement.BusinessRules.ExtensionMethods;
    using Microsoft.Advertising.ServiceLocation;
    using Microsoft.IdentityModel.Clients.ActiveDirectory;
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Collections.Specialized;
    using System.IO;
    using System.Linq;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using System.Text;
    using System.Threading.Tasks;
    using System.Web;
    using AdAutoComplete = Microsoft.Ads.Mca.BusinessMT.Model.Ads.AdAutoComplete;
    using AdSuggestion = Microsoft.Ads.Mca.BusinessMT.Model.Ads.AdSuggestion;
    using Editorial = Microsoft.AdCenter.Editorial.MT.Enums;
    using McaAds = Microsoft.Ads.Mca.BusinessMT.Model.Ads;
    using RnR = global::Ads.RnR.SmartCampaigns;
    using SiteWord = Microsoft.Ads.Mca.BusinessMT.Model.Ads.SiteWord;
    using Microsoft.AdCenter.Common.Caching;    
    using Microsoft.BingAds.Utils;    
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenterFacade;
    using Microsoft.Advertiser.ClientCenter.MT.Proxy;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.AIGC;    
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenter;
    using Microsoft.Ads.Mca.Utils;
    using GenerateLogoRequest = Entities.AIGC.GenerateLogoRequest;
    using Microsoft.Ads.TokenProvider;
    using AIGCBrandKit = Entities.AIGC.AIGCBrandKit;
    using Unity;    
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Messages;
    using Locale = Entities.Locale;
    using GetBrandKitRecommendationResponse = Entities.AIGC.GetBrandKitRecommendationResponse;

    public class AggregationServiceEO : IAggregationServiceEO
    {
        private readonly string endpoint;
        private readonly string autoImageEndPoint;
        private readonly ClientCredential clientCredential;
        private readonly ClientCredential adsRankingClientCredential;
        private readonly AuthenticationContext authenticationContext;
        private readonly HttpClient httpClient;
        private readonly HttpClient autoImageHttpClient;
        private readonly HttpClient imageRecommendationHttpClient;
        private readonly HttpClient imageRecommendationHttpClient_AggSrv;
        private readonly ISmartCaller smartCaller;
        private readonly JsonSerializerSettings generationServiceSerializerSetting;
        private ISocialMediaImageCacheHelper socialMediaImageCacheHelper;
        private readonly TokenProvider tokenProvider;

        private const string SCControllerDataTable = "SCControllerData";
        private const string GetKeywordRecommendationsCoprocName = "SCControllerAPI.GetKeywordRecommendations";

        private const string PrepareAdsRequestUrl = "PrepareAds";
        private const string CreateAdsRequestUrl = "CreateAds";        
        private const string CompleteAdAssetsRequestUrl = "CompleteAdAssets";
        private const string CreateKeywordsRequestUrl = "CreateKeywords";
        private const string PrepareEntitiesRequestUrl = "PrepareEntities";
        private const string ValidateUrlRequestUrl = "crawling/validate";
        private const string RecommendCategoryRequestUrl = "RecommendCategory/inference";
        private const string TemplateAdsSourceName = "TemplateAds";
        private const string CreateAdAssetsRequestUrl = "CreateAdAssets";
        private const string CreateDisplayAdsRecommendationUrl = "CreateDisplayAdsRecommendation";
        private const string EditDisplayAdsRecommendationUrl = "EditDisplayAdsRecommendation";
        private const string CreateVideoAssetsRecommendationUrl = "CreateVideoAssetsRecommendation";
        private const string ExportClipchampVideoRecommendationUrl = "ExportClipchampVideoRecommendation";
        private const string EditVideoAssetRecommendationUrl = "EditVideoAssetRecommendation";
        private const string GetBrandKitRecommendationUrl = "GetBrandkitRecommendation";
        private const string EditKeyFrameUrl = "EditKeyFrame";
        private const string GenerateVideoClipsUrl = "GenerateVideoClips";
        private const string HeadlineKey = "headline";
        private const string DescriptionKey = "description";
        private const string AdKey = "ad";
        private const string SiteWordKey = "siteWord";
        private const string CreateImagesRequestUrl = "CreateImages";
        private const string MediaGenerationRequestUrl = "generate";
        private const string AggSvcMediaGenerationUrl = "GenerateImages";
        private const string ImageRecommendationReuestUrl = "recommend_v2";
        private const string AdRecommendationUrl = "recommend_ads_with_image";
        private const string AggSvcImageAdUrl = "CreateImageAds";
        private const string OfflineGeneratedAssetsUrl = "GetOfflineGeneratedAssets";
        private const string GenerateImagesRequestUrl = "GenerateImages";
        private const string GetThemeAssetsRecommendationRequestUrl = "GetThemeAssetsRecommendation";
        private const string GetAssetsRecommendationRequestUrl = "GetAssetsRecommendation";
        private const string RefineTextAssetRequestUrl = "RefineTextAsset";
        private const string GetDsaMigrationAssetsUrl = "GetDsaMigrationAssets";
        private const string RefineImageRequestUrl = "RefineImage";
        private const string MediaAltTextGenerationRequestUrl = "GenerateMediaAltText";
        private const string AssetsSearchAddOrUpdateImagesUrl = "AddImages";
        private const string AssetsSearchImageSearchUrl = "SearchImages";
        private const string AssetsSearchDeleteImagesUrl = "DeleteImages";
        private const string GetDisplayAdsTemplateDefinitionUrl = "GetDisplayAdsTemplateDefinition";
        private const string GetDisplayAdsTemplateGroupsUrl = "GetDisplayAdsTemplateGroups";
        private const string GetDisplayAdsTemplateGroupDetailUrl = "GetDisplayAdsTemplateGroupDetail";
        private const string GetSocialSitesRecommendationUrl = "GetSocialSitesRecommendation";
        private const string AddressExtractionUrl = "addressextraction/";
        private const string GenerateLogoUrl = "GenerateLogo/";
        private const string GetClipchampTemplatesUrl = "GetClipchampTemplates";
        private const string GetSupportedClipchampAudioUrl = "GetSupportedClipchampAudio";
        private const string GetSupportedFontsUrl = "GetSupportedFonts";
        private const string GenerateVideoClipsStatusUrl = "GenerateVideoClipsStatus";

        private const int DesiredKeywordsCount = 20;
        private const int ResponsiveSearchAdHeadlineCount = 15;
        private const int ResponsiveSearchAdDescriptionCount = 4;
        private const int MultiMediaAdDescriptionCount = 10;
        private const double ImageRecommendationTimeoutInSeconds = 5;

        private ICheckPilotFlag pilotFlagChecker;
        private ICache _cache;

        public ICache Cache
        {
            get
            {
                if (_cache == null)
                {
                    return CampaignRedisCache.Instance;
                }

                return _cache;
            }

            set => _cache = value;
        }

        private static readonly Dictionary<RnR.CountryCode, PublisherCountry>
            rnrCountryCodeLookup = Enum.GetValues(typeof(PublisherCountry))
                .Cast<PublisherCountry>().Select(e => (e, PublisherCountryToRnRCountryCode(e))).ToDictionary(
                    x => x.Item2,
                    x => x.e);

        private static readonly List<string> UrlScanFailureErrorCodes = new List<string>() { "401", "403", "408", "429", "503" };

        public AggregationServiceEO(ISmartCaller smartCaller, TokenProvider tokenProvider, ICheckPilotFlag pilotFlagChecker, HttpClient client): this(smartCaller, tokenProvider, pilotFlagChecker)
        {
            this.httpClient = client;
        }

        [InjectionConstructor]
        public AggregationServiceEO(ISmartCaller smartCaller, TokenProvider tokenProvider, ICheckPilotFlag pilotFlagChecker)
        {
            this.endpoint = DynamicConfigValues.AggregationServiceEndpoint;
            this.autoImageEndPoint = DynamicConfigValues.AutoImageServiceEndpoint;
            this.generationServiceSerializerSetting = new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore };
            this.socialMediaImageCacheHelper = new SocialMediaImageCacheHelper();

            if (string.IsNullOrWhiteSpace(DynamicConfigValues.AggregationServiceClientId) || string.IsNullOrWhiteSpace(DynamicConfigValues.AggregationServiceAuthority))
            {
                this.clientCredential = null;
                this.authenticationContext = null;
            }
            else
            {
                this.clientCredential = new ClientCredential(DynamicConfigValues.AggregationServiceClientId, DynamicConfigValues.AggregationServiceClientSecret);
                this.authenticationContext = new AuthenticationContext(DynamicConfigValues.AggregationServiceAuthority, TokenCache.DefaultShared);
            }

            if (string.IsNullOrWhiteSpace(DynamicConfigValues.AdsRankingClientId))
            {
                this.adsRankingClientCredential = null;
            }
            else
            {
                this.adsRankingClientCredential = new ClientCredential(DynamicConfigValues.AdsRankingClientId, DynamicConfigValues.AdsRankingClientSecret);
            }

            this.httpClient = new HttpClient 
            {
                BaseAddress = new Uri(this.endpoint)
            };

            this.autoImageHttpClient = new HttpClient
            {
                BaseAddress = new Uri(this.autoImageEndPoint)
            };

            this.imageRecommendationHttpClient_AggSrv = new HttpClient
            {
                BaseAddress = new Uri(this.endpoint),
                Timeout = TimeSpan.FromSeconds(ImageRecommendationTimeoutInSeconds)
            };

            this.imageRecommendationHttpClient = new HttpClient
            {
                BaseAddress = new Uri(this.autoImageEndPoint),
                Timeout = TimeSpan.FromSeconds(ImageRecommendationTimeoutInSeconds)
            };

            this.smartCaller = smartCaller;
            this.tokenProvider = tokenProvider;
            this.pilotFlagChecker = pilotFlagChecker;
        }

        private HttpRequestMessage CreatePostRequest(
            AccountCallContext context,
            long? campaignId,
            string requestUrl,
            string authToken,
            string content,
            bool useCCMTToken = false,
            bool useV2 = false)
        {
            var request = Helpers.CreateRequest(
                HttpMethod.Post,
                requestUrl,
                new StringContent(content, Encoding.UTF8, "application/json"));

            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
            request.Headers.Add("RequestId", context.Request.Tracking.RequestIdString);
            request.Headers.Add("TrackingId", context.Request.Tracking.TrackingIdString);
            request.Headers.Add("SessionId", context.Request.Tracking.SessionIdString);
            request.Headers.Add("CustomerId", context.AdvertiserCustomerID.ToString());
            request.Headers.Add("AccountId", context.AccountId.ToString());
            if (campaignId != null)
            {
                request.Headers.Add("CampaignId", campaignId.ToString());
            }

            if (!Enum.TryParse(context.ApplicationSource, out AggregationServiceCaller callername))
            {
                callername = AggregationServiceCaller.unknown;
            }

            request.Headers.Add("CallerName", callername.ToString());

            if (useCCMTToken)
            {
                request.Headers.Add("x-ms-usertoken", "CCMTSmallToken " + GetSmallToken(context));
            }

            if (useV2)
            {
                request.Headers.Add("x-data-version", "v2");
            }

            return request;
        }

        private async Task<ResultOneEntity<TResult>> PostAsync<TResult>(
            AccountCallContext context,
            long? campaignId,
            string requestUrl,
            string authToken,
            Object content,
            HttpClient httpClient,
            JsonSerializerSettings serializerSettings = null,
            bool useCCMTToken = false,
            bool getContentOnError = false,
            bool useV2 = false,
            Func<ILogShared, HttpRequestMessage, HttpResponseMessage, string, ResultOneEntity<TResult>, Task> badRequestResponseHandler = null)
        {
            using (SafeSharedLog.MiddleTierPerformanceLogger(context.Logger, $"AggregationServiceEO.Post.{httpClient.BaseAddress + requestUrl}"))
            {
                string serializedContent = serializerSettings == null ? JsonConvert.SerializeObject(content) : JsonConvert.SerializeObject(content, serializerSettings);

                using (var request = CreatePostRequest(context, campaignId, requestUrl, authToken, serializedContent, useCCMTToken, useV2: useV2))
                {
                    using (var response = await httpClient.SendAsync(request))
                    {
                        return await ProcessResponseAsync<TResult>(context.Logger, request, response, getContentOnError, badRequestResponseHandler: badRequestResponseHandler);
                    }
                }
            }
        }

        private async Task<ResultOneEntity<TResult>> PostAsync<TResult>(
            AccountCallContext context,
            long? campaignId,
            string requestUrl,
            string authToken,
            Object content,
            bool useCCMTToken = false,
            bool getContentOnError = false,
            bool useV2 = false)
        {
            return await PostAsync<TResult>(
                context,
                campaignId,
                requestUrl,
                authToken,
                content,
                this.httpClient,
                useCCMTToken: useCCMTToken,
                getContentOnError: getContentOnError,
                useV2: useV2);
        }

        private async Task<ResultOneEntity<bool>> PostAsync(AccountCallContext context, long campaignId, string requestUrl, string authToken, Object content)
        {
            using (SafeSharedLog.MiddleTierPerformanceLogger(context.Logger, $"AggregationServiceEO.Post.{requestUrl}"))
            using (var request = CreatePostRequest(context, campaignId, requestUrl, authToken, JsonConvert.SerializeObject(content)))
            {
                using (var response = await this.httpClient.SendAsync(request))
                {
                    return ProcessResponseAsync(context.Logger, request, response);
                }
            }
        }

        private AssetSearchImages GeneratePayloadForSearch(AccountCallContext context, Dictionary<string, MediaMetaData> validAssets)
        {
            var imagesList = new List<AssetSearchImages.Image>();

            foreach (var kvp in validAssets)
            {
                var assetData = new AssetSearchImages.Image
                {
                    Id = kvp.Value.Id.ToString(),
                    Title = kvp.Value.Text ?? null,
                    Url = kvp.Value.Url ?? kvp.Value.BingUrlHttps
                };
                imagesList.Add(assetData);
            }

            return new AssetSearchImages
            {
                Images = imagesList
            };
        }

        private string GenerateUrl(AccountCallContext context, string endPath, string queryString = "")
        {
            var url = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/AssetsSearch/{endPath}";

            if (!string.IsNullOrEmpty(queryString))
            {
                url += $"?{queryString}";
            }

            return url;
        }


        public async Task<ResultOneEntity<AssetSearchResponse>> AddAssetsAsync(AccountCallContext context, Dictionary<string, MediaMetaData> content)
        {
            ResultOneEntity<AssetSearchResponse> result = new ResultOneEntity<AssetSearchResponse>();
            var payload = GeneratePayloadForSearch(context, content);

            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var finalUrl = GenerateUrl(context, AssetsSearchAddOrUpdateImagesUrl);

                result = await this.PostAsync<AssetSearchResponse>(context, null, finalUrl, token, payload, this.imageRecommendationHttpClient_AggSrv, useCCMTToken: true, getContentOnError: true);
            }
            catch (Exception ex)
            {
                context.Logger.LogError("Failed to add asset to AIGC search index due to exception: {0}", ex);
                throw;
            }

            if (result.Failed || !result.Errors.IsNullOrEmpty())
            {
                context.Logger.LogError(
                    "Failed to add asset to AIGC search index with payload {0}. Error: {1}",
                    JsonConvert.SerializeObject(payload), string.Join(",", result.Errors ?? []));
            }

            return result;
        }

        public async Task<ResultOneEntity<AssetSearchSearchImagesResponse>> SearchAssetAsync(AccountCallContext context, string query, int maxCount, bool returnUrl = false)
        {
            var response = new ResultOneEntity<AssetSearchSearchImagesResponse>();
            var payload = new AssetSearchSearchImages
            {
                Query = query,
                MaxCount = maxCount
            };

            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);

                var queryString = "";
                if (returnUrl)
                {
                    queryString = "returnUrl=true";
                }
                var finalUrl = GenerateUrl(context, AssetsSearchImageSearchUrl, queryString);

                response = await this.PostAsync<AssetSearchSearchImagesResponse>(context, null, finalUrl, token, payload, useCCMTToken: true, getContentOnError: true);
                context.Logger.LogInfo("AIGC image search responded with {0} results, query: '{1}', ", response.Entity?.Data?.Results?.Count ?? 0, query);
            }
            catch (Exception ex)
            {
                context.Logger.LogError("Failed to search into AIGC search index due to exception: {0}", ex);
                response.AddError(CampaignManagementErrorCode.InternalError);
            }

            if (response.Failed || !response.Errors.IsNullOrEmpty())
            {
                context.Logger.LogError(
                    "Failed to search into AIGC search index with payload {0}. Error: {1}",
                    JsonConvert.SerializeObject(payload), string.Join(",", response.Errors ?? [])
                );
            }

            return response;
        }

        private HttpRequestMessage CreateGetRequest(AccountCallContext context, long? campaignId, string requestUrl, string authToken)
        {
            var request = Helpers.CreateRequest(
                HttpMethod.Get,
                requestUrl);

            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
            request.Headers.Add("RequestId", context.Request.Tracking.RequestIdString);
            request.Headers.Add("TrackingId", context.Request.Tracking.TrackingIdString);
            request.Headers.Add("SessionId", context.Request.Tracking.SessionIdString);
            request.Headers.Add("CustomerId", context.AdvertiserCustomerID.ToString());
            request.Headers.Add("AccountId", context.AccountId.ToString());
            request.Headers.Add("CampaignId", campaignId?.ToString());

            if (!Enum.TryParse(context.ApplicationSource, out AggregationServiceCaller callername))
            {
                callername = AggregationServiceCaller.unknown;
            }

            request.Headers.Add("CallerName", callername.ToString());
            request.Headers.Add("x-ms-usertoken", "CCMTSmallToken " + GetSmallToken(context));

            return request;
        }

        private string GetSmallToken(AccountCallContext context)
        {
            var smalltokenRequest = new GetSmallTokenByUserTokenRequest
            {
                UserToken = context.Request.SecurityTicket.SecurityTicketId,
                TrackingId = context.Logger.TrackingData.TrackingId.ToString()
            };

            return ClientCenterCaller.Instance.GetSmallTokenByUserToken(context.Logger, smalltokenRequest).SmallToken;
        }

        private async Task<ResultOneEntity<TResult>> GetAsync<TResult>(AccountCallContext context, long? campaignId, string requestUrl, string authToken, HttpClient httpClient)
        {
            using (SafeSharedLog.MiddleTierPerformanceLogger(context.Logger, $"AggregationServiceEO.Get.{httpClient.BaseAddress + requestUrl}"))
            {
                using (var request = CreateGetRequest(context, campaignId, requestUrl, authToken))
                {
                    using (var response = await httpClient.SendAsync(request))
                    {
                        return await ProcessResponseAsync<TResult>(context.Logger, request, response);
                    }
                }
            }
        }

        private async Task<ResultOneEntity<TResult>> ProcessResponseAsync<TResult>(
            ILogShared logger,
            HttpRequestMessage request,
            HttpResponseMessage response,
            bool getContentOnError = false,
            Func<ILogShared, HttpRequestMessage, HttpResponseMessage, string, ResultOneEntity<TResult>, Task> badRequestResponseHandler = null)
        {
            var result = new ResultOneEntity<TResult>();
            var content = String.Empty;
            if (response.Content != null)
            {
                using (var stream = await response.Content.ReadAsStreamAsync())
                using (var reader = new StreamReader(stream))
                {
                    content = reader.ReadToEnd();
                }
            }
            if (response.IsSuccessStatusCode)
            {
                logger.LogInfo($"Response content: {content}");
                result.Entity = JsonConvert.DeserializeObject<TResult>(content);
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.GatewayTimeout)
            {
                logger.LogError($"Gateway timeout from {request.Method} {request.RequestUri}: {content}, ignore and continue");
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.BadRequest && badRequestResponseHandler != null)
            {
                await badRequestResponseHandler(logger, request, response, content, result);
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.BadRequest && content == "All Target Languages Are Not Supported")
            {
                logger.LogUserError($"Error {response.StatusCode} from {request.Method} {request.RequestUri}: {content}");
                result.AddError(CampaignManagementErrorCode.UnsupportedLanguage);
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.BadRequest && content == "TargetLanguages did not match any Languages detected in the UserAd")
            {
                logger.LogUserError($"Error {response.StatusCode} from {request.Method} {request.RequestUri}: {content}");
                result.AddError(CampaignManagementErrorCode.LanguageMismatchUserAd);
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.BadRequest && getContentOnError)
            {
                logger.LogUserError($"Error {response.StatusCode} from {request.Method} {request.RequestUri}: {content}");
                result.Entity = JsonConvert.DeserializeObject<TResult>(content);
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                logger.LogUserError($"Error {response.StatusCode} from {request.Method} {request.RequestUri}: {content}");
                logger.LogError($"Unexpected fallback path since the user is authorized in MT layer, but failed to be authorized in downstream.");
                result.AddError(CampaignManagementErrorCode.InternalError);
            }
            else
            {
                logger.LogError($"Error {response.StatusCode} from {request.Method} {request.RequestUri}: {content}");
                result.AddError(CampaignManagementErrorCode.InternalError);
            }

            return result;
        }

        private ResultOneEntity<bool> ProcessResponseAsync(ILogShared logger, HttpRequestMessage request, HttpResponseMessage response)
        {
            var result = new ResultOneEntity<bool>();
            if (response.IsSuccessStatusCode)
            {
                result.Entity = true;
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.GatewayTimeout)
            {
                logger.LogWarning($"Gateway timeout from {request.Method} {request.RequestUri}, ignore and continue");
            }
            else
            {
                logger.LogError($"Error {response.StatusCode} from {request.Method} {request.RequestUri}");
                result.AddError(CampaignManagementErrorCode.InternalError);
            }

            return result;
        }

        private async Task<string> GetAccessTokenAsync(ClientCredential credential, ILogShared logger)
        {
            if (DynamicConfigValues.UseAzureServiceTokenProviderInAggregationService)
            {
                var scope = string.Empty;
                if (!CrossPlatformHelpers.RunningInLocal)
                {
                    string clientId = System.Configuration.ConfigurationManager.AppSettings["AADAuthUserIdentityClientId"];
                    if (string.IsNullOrWhiteSpace(clientId))
                    {
                        clientId = CrossPlatformHelpers.GetManageIdentityId();
                        if (string.IsNullOrWhiteSpace(clientId))
                        {
                            logger.LogError("Neither AADAuthUserIdentityClientId nor CampaignMTManageIdentityId is configured");
                            throw new InvalidOperationException("Client ID configuration is missing. Neither AADAuthUserIdentityClientId nor CampaignMTManageIdentityId is configured");
                        }
                    }
                    scope = clientId;
                }
                else
                {
                    scope = System.Configuration.ConfigurationManager.AppSettings["DefaultAADScopeForLocalDev"];
                    if (string.IsNullOrWhiteSpace(scope))
                    {
                        scope = "https://management.azure.com";
                    }                    
                }
                string[] scopes = new string[] { scope };
                return await tokenProvider.FetchAccessTokenAsync(scopes, Guid.NewGuid().ToString()).ConfigureAwait(false);
            }
            else
            {
                if (credential == null || this.authenticationContext == null)
                {
                    logger.LogInfo("No Credential and Authentication provided so return empty access token.");
                    return string.Empty;
                }

                var authenticationResult = await this.authenticationContext.AcquireTokenAsync(credential.ClientId, credential);

                return authenticationResult.AccessToken;
            }
        }

        private string AdTypeToString(AdType adType)
        {
            switch (adType)
            {
                case AdType.Text:
                case AdType.ExpandedText:
                    return "EXTA+";
                case AdType.Responsive:
                case AdType.ResponsiveSearch:
                    return "RSA";
                default:
                    throw new NotSupportedException($"{adType} is not supproted");
            }
        }

        private List<Ad> AggAdsToExpandedTextAd(IList<AggregationServiceAd> aggAds, long campaignId, string website)
        {
            List<Ad> ads = new List<Ad>();

            if (aggAds == null || aggAds.Count == 0)
                return ads;

            foreach (var aggAd in aggAds)
            {
                var exta = new ExpandedTextAd
                {
                    TitlePart1 = aggAd.Ad[0],
                    TitlePart2 = aggAd.Ad[1],
                    TitlePart3 = aggAd.Ad[2],
                    Text = aggAd.Ad[3],
                    TextPart2 = aggAd.Ad[4],
                    CampaignId = campaignId,
                    DisplayUrl = website,
                    OfflineAdsSource = aggAd.Source,
                    AdGroupId = 0
                };

                ads.Add(exta);
            }

            return ads;
        }

        private List<ExpandedTextAdAdd> AggAdsToExpandedTextAdAdd(IList<AggregationServiceAd> aggAds, long campaignId, string website)
        {
            List<ExpandedTextAdAdd> ads = new List<ExpandedTextAdAdd>();

            if (aggAds == null || aggAds.Count == 0)
                return ads;

            foreach (var aggAd in aggAds)
            {
                var exta = new ExpandedTextAdAdd
                {
                    TitlePart1 = aggAd.Ad[0],
                    TitlePart2 = aggAd.Ad[1],
                    TitlePart3 = aggAd.Ad[2],
                    Text = aggAd.Ad[3],
                    TextPart2 = aggAd.Ad[4],
                    CampaignId = campaignId,
                    DisplayUrl = website,
                    OfflineAdsSource = aggAd.Source,
                    AdGroupId = 0,
                    PerformEditorialChecks = true,
                    EffectivePublisherCountries = new List<PublisherCountryInfo>(),
                    RecommendationId = aggAd.RecommendationId
                };

                ads.Add(exta);
            }

            return ads;
        }

        private ExpandedTextAd ToExpandedTextAd(ExpandedTextAdAdd expandedTextAdAdd)
        {
            return new ExpandedTextAd
            {
                TitlePart1 = expandedTextAdAdd.TitlePart1,
                TitlePart2 = expandedTextAdAdd.TitlePart2,
                TitlePart3 = expandedTextAdAdd.TitlePart3,
                Text = expandedTextAdAdd.Text,
                TextPart2 = expandedTextAdAdd.TextPart2,
                CampaignId = expandedTextAdAdd.CampaignId,
                DisplayUrl = expandedTextAdAdd.DisplayUrl,
                OfflineAdsSource = expandedTextAdAdd.OfflineAdsSource,
                AdGroupId = expandedTextAdAdd.AdGroupId,
                RecommendationId = expandedTextAdAdd.RecommendationId
            };
        }

        private List<SmartListing> AggKeywwordsToSmartListing(IEnumerable<AggKeywordEntity> aggKeywords)
        {
            List<SmartListing> smartListings = new List<SmartListing>();

            if (aggKeywords == null)
            {
                return smartListings;
            }

            foreach (var aggKeyword in aggKeywords)
            {
                var smartListing = new SmartListing
                {
                    Text = aggKeyword.Text,
                    Type = SmartListingType.ProductOrService,
                    Status = SmartListingStatus.Active,
                    IsSelected = aggKeyword.IsSelected,
                    IsSuggested = aggKeyword.IsInputByUser != true,
                    Source = aggKeyword.SourceId,
                    IsGeneratedByKeywordSuggestionService = true,
                    RecommendationId = aggKeyword.RecommendationId
                };

                smartListings.Add(smartListing);
            }

            return smartListings;
        }

        private List<ResponsiveSearchAdAdd> AggAdAssetsToResponsiveSearchAdAdd(IDictionary<string, List<AggregationServiceAdAsset>> aggAdAssets, long? campaignId, string website)
        {
            List<ResponsiveSearchAdAdd> ads = new List<ResponsiveSearchAdAdd>();

            if (aggAdAssets == null)
            {
                return ads;
            }

            if (aggAdAssets.TryGetValue(HeadlineKey, out var headlines))
            {
                foreach (var headline in headlines)
                {
                    var responsiveSearchAdAdd = new ResponsiveSearchAdAdd
                    {
                        Headlines = new AssetLink[] { new AssetLink() { AssociationType = AssetAssociationType.Headlines, Asset = new TextAsset() { Text = headline.AdAssetValue }, RecommendationId = headline.RecommendationId, SuggestionId = headline.SuggestionId } },
                        DisplayUrl = website,
                        OfflineAdsSource = headline.TracingId,
                        AdGroupId = 0,
                        PerformEditorialChecks = true,
                        EffectivePublisherCountries = new List<PublisherCountryInfo>(),
                    };

                    if (campaignId.HasValue)
                    {
                        responsiveSearchAdAdd.CampaignId = campaignId.Value;
                    }

                    ads.Add(responsiveSearchAdAdd);
                }
            }

            if (aggAdAssets.TryGetValue(DescriptionKey, out var descriptions))
            {
                foreach (var description in descriptions)
                {
                    var responsiveSearchAdAdd = new ResponsiveSearchAdAdd
                    {
                        Descriptions = new AssetLink[] { new AssetLink() { AssociationType = AssetAssociationType.Descriptions, Asset = new TextAsset() { Text = description.AdAssetValue }, RecommendationId = description.RecommendationId, SuggestionId = description.SuggestionId } },
                        DisplayUrl = website,
                        OfflineAdsSource = description.TracingId,
                        AdGroupId = 0,
                        PerformEditorialChecks = true,
                        EffectivePublisherCountries = new List<PublisherCountryInfo>(),
                    };

                    if (campaignId.HasValue)
                    {
                        responsiveSearchAdAdd.CampaignId = campaignId.Value;
                    }

                    ads.Add(responsiveSearchAdAdd);
                }
            }

            return ads;
        }

        public async Task<ResultOneEntity<List<Ad>>> CompleteAdRecommendation(AccountCallContext context, long campaignId, string businessUrl, IList<string> userAds, int completeIndex, AdType adType, int count)
        {
            var completeAd = new AggCompleteAds
            {
                Url = businessUrl,
                UserAd = userAds,
                AdType = AdTypeToString(adType),
                Count = count,
                AutoCompleteFieldIndex = completeIndex
            };

            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);

            var adAssetCollection = await this.PostAsync<AggregationServiceAdAssetCollection>(context, campaignId, CompleteAdAssetsRequestUrl, token, completeAd);
            ResultOneEntity<List<Ad>> result = new ResultOneEntity<List<Ad>>();
            var ads = new List<Ad>();
            if (adAssetCollection?.Entity?.AdAssets != null && adAssetCollection.Entity.AdAssets.TryGetValue(AdKey, out var assets))
            {
                ads.AddRange(assets.Select(ad =>
                {
                    var adText = JsonConvert.DeserializeObject<List<string>>(ad.AdAssetValue);

                    return new ExpandedTextAd
                    {
                        TitlePart1 = adText[0],
                        TitlePart2 = adText[1],
                        TitlePart3 = adText[2],
                        Text = adText[3],
                        TextPart2 = adText[4],
                        DisplayUrl = businessUrl,
                        CampaignId = campaignId,
                        OfflineAdsSource = ad.TracingId,
                        AdGroupId = 0,
                    };
                }));
            }

            result.Entity = GetRandomItemsBySource(ads, count);
            result.AddErrors(adAssetCollection.Errors);

            context.Logger.LogInfo($"CompleteAd recommendation : {JsonConvert.SerializeObject(result.Entity)}");

            return result;
        }

        public async Task<ResultOneEntity<AIGCResponse<AIGCTaskInfo>>> GenerateImagesTask(
            AccountCallContext context,
            long campaignId,
            GenerateImagesRequest generateImagesRequest)
        {
            string puid = "";
            bool? isAADAccount = null;
            if (this.pilotFlagChecker.IsAccountFeatureEnabled(context.Logger, context.Request, AccountFeatureFlag.EnableAIGCForAADUser))
            {
                var puidInfo = await SmartHelper.GetPuidInfoFromCcmt(context);
                if (!puidInfo.Failed)
                {
                    puid = puidInfo.Entities.FirstOrDefault().Item1;
                    isAADAccount = puidInfo.Entities.FirstOrDefault().Item2 == AuthenticationType.AzureActiveDirectory;
                }
            }
            else
            {
                puid = await SmartHelper.GetPuidFromCcmt(context).ConfigureAwait(false);
            }
            if (string.IsNullOrEmpty(puid))
            {
                context.Logger.LogError($"PUID received from CCMT is empty.");
                var result = new ResultOneEntity<AIGCResponse<AIGCTaskInfo>>();
                result.AddError(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InternalError, $"Get PUID from CC Failed."));
                return result;
            }
            generateImagesRequest.PUID = puid;
            generateImagesRequest.IsAADAccount = isAADAccount;

            return await CreateAIGCTask<AIGCTaskInfo>(context, campaignId, generateImagesRequest, GenerateImagesRequestUrl, nameof(GenerateImagesTask));
        }

        public async Task<ResultOneEntity<AIGCResponse<Entities.AIGC.GenerateImagesResponse>>> GenerateImagesTaskResult(
            AccountCallContext context,
            long campaignId,
            string taskId)
        {
            string puid = await SmartHelper.GetPuidFromCcmt(context).ConfigureAwait(false);
            if (string.IsNullOrEmpty(puid))
            {
                context.Logger.LogError($"PUID received from CCMT is empty.");
                var result = new ResultOneEntity<AIGCResponse<Entities.AIGC.GenerateImagesResponse>>();
                result.AddError(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InternalError, $"Get PUID from CC Failed."));
                return result;
            }

            return await GetAIGCTaskResult<Entities.AIGC.GenerateImagesResponse>(context, campaignId, taskId, GenerateImagesRequestUrl, nameof(GenerateImagesTaskResult), puid);
        }

        public async Task<ResultOneEntity<AIGCResponse<GetThemeAssetsRecommendationResponse>>> GetThemeAssetsRecommendationV2(
            AccountCallContext context,
            long campaignId,
            GetThemeAssetsRecommendationRequest request,
            bool enableLogo = false,
            bool enableCTA = false)
        {
            return await CreateAIGCTask<GetThemeAssetsRecommendationResponse>(
                context,
                campaignId,
                request,
                GetThemeAssetsRecommendationRequestUrl,
                nameof(GetThemeAssetsRecommendationV2),
                useV2: true,
                enableLogo: enableLogo,
                enableCTA: enableCTA);
        }

        public async Task<ResultOneEntity<AIGCResponse<GetAllAssetRecommendationResponse>>> GetAssetsRecommendationV2(
            AccountCallContext context,
            long campaignId,
            GetAssetsRecommendationRequest request,
            bool enableLogo = false,
            bool enableCTA = false)
        {
            return await CreateAIGCTask<GetAllAssetRecommendationResponse>(
                context,
                campaignId,
                request,
                GetAssetsRecommendationRequestUrl,
                nameof(GetAssetsRecommendationV2),
                useV2: true,
                enableLogo: enableLogo,
                enableCTA: enableCTA);
        }

        public async Task<ResultOneEntity<AIGCResponse<GetAllAssetRecommendationResponse>>> GetDsaMigrationAssets(
            AccountCallContext context,
            GetDsaMigrationAssetsRequest request)
        {
            return await CreateAIGCTask<GetAllAssetRecommendationResponse>(
                context,
                request.CampaignId,
                request,
                GetDsaMigrationAssetsUrl,
                nameof(GetDsaMigrationAssets));
        }

        public async Task<ResultOneEntity<AIGCResponse<RefineTextAssetResponse>>> RefineTextAssetV2(
            AccountCallContext context,
            long campaignId,
            RefineTextAssetRequest request)
        {
            return await CreateAIGCTask<RefineTextAssetResponse>(
                context,
                campaignId,
                request,
                RefineTextAssetRequestUrl,
                nameof(RefineTextAssetV2),
                useV2: true);
        }

        public async Task<ResultOneEntity<AIGCResponse<string>>> RefineImage(
            AccountCallContext context,
            long campaignId,
            RefineImageRequest request)
        {
            return await CreateAIGCTask<string>(context, campaignId, request, RefineImageRequestUrl, nameof(RefineImage));
        }

        public async Task<ResultOneEntity<AIGCResponse<RefineImageResponse>>> RefineImageV2(
            AccountCallContext context,
            long campaignId,
            RefineImageRequest request)
        {
            return await CreateAIGCTask<RefineImageResponse>(
                context,
                campaignId,
                request,
                RefineImageRequestUrl,
                nameof(RefineImage),
                useV2: true);
        }

        public async Task<ResultOneEntity<AIGCResponse<RefineImageResponse>>> RefineImageResult(
            AccountCallContext context,
            long campaignId,
            string taskId)
        {
            return await GetAIGCTaskResult<RefineImageResponse>(context, campaignId, taskId, RefineImageRequestUrl, nameof(RefineImageResult));
        }

        public async Task<ResultOneEntity<AdAutoComplete>> CreateAdAutoCompleteRecommendation(AccountCallContext context, long campaignId, string businessUrl, IList<string> userAds, int completeIndex, string adType, int count, Language language)
        {
            ResultOneEntity<AdAutoComplete> result = new ResultOneEntity<AdAutoComplete>();

            var completeAd = new AggCompleteAds
            {
                Url = businessUrl,
                UserAd = userAds,
                AdType = adType,
                Count = count,
                AutoCompleteFieldIndex = completeIndex,
                TargetLanguages = new List<string>() { LanguageInfo.GetLanguageCode(language).ToLower() }
            };

            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);

            var adAssetCollection = await this.PostAsync<AggregationServiceAdAssetCollection>(context, campaignId, CompleteAdAssetsRequestUrl, token, completeAd);

            result.AddErrors(adAssetCollection.Errors);

            if (result.Errors.Any())
            {
                return result;
            }

            result.Entity = new AdAutoComplete();
            if (adAssetCollection?.Entity?.AdAssets != null)
            {
                if (adAssetCollection.Entity.AdAssets.TryGetValue(AdKey, out var assets))
                {
                    assets.Shuffle();
                    var ads = assets.Take(count).Select(ad => JsonConvert.DeserializeObject<List<string>>(ad.AdAssetValue));
                    result.Entity.Suggestions = ads.Select(ad => new AdSuggestion { Text = ad[completeIndex] });
                }

                if (adAssetCollection.Entity.AdAssets.TryGetValue(SiteWordKey, out var siteWords))
                {
                    result.Entity.SiteWords = siteWords.Select(word => new SiteWord { Word = word.AdAssetValue, Count = Convert.ToInt32(word.Score) });
                }
            }
            context.Logger.LogInfo($"AdAutoComplete recommendation : {JsonConvert.SerializeObject(result.Entity)}");

            return result;
        }

        public async Task<ResultOneEntity<List<Ad>>> CreateAdRecommendation(
            AccountCallContext context,
            long campaignId,
            IEnumerable<string> categories,
            Business business,
            IEnumerable<Criterion> criterions,
            AdType adType,
            int count,
            Language language,
            bool refreshCache = false)
        {
            ResultOneEntity<List<Ad>> result = new ResultOneEntity<List<Ad>>();
            criterions = criterions.ToList();
            string locationString = criterions.CollectionIsNullOrEmpty()
                ? string.Empty
                : SmartHelper.ValidateAndSelectLocation(
                    result,
                    criterions);
            if (result.Failed)
            {
                result.Entity = new List<Ad>();
                return result;
            }

            var rnrCountryCodes = SmartHelper.GetCountryCodesFromCriterions(
                context.Logger,
                criterions);
            var countryCodes = rnrCountryCodes?.Select(x => x.ToString("G"));

            var createAd = new AggCreateAds
            {
                Url = business.Website,
                AdType = AdTypeToString(adType),
                Categories = categories?.ToList(),
                CountryCodes = countryCodes?.ToList(),
                BusinessName = business.Name,
                Location = locationString,
                Count = count,
                LanguageCode = LanguageInfo.GetLanguageCode(language).ToLower()
            };

            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
            var parameters = new Dictionary<string, string>
            {
                { "refreshCache", refreshCache.ToString() }
            };
            var requestUrl = CreateAdsRequestUrl + "?" + GenerateQueryParameters(parameters).ToString();

            var adCollection = await this.PostAsync<AggregationServiceAdCollection>(context, campaignId, requestUrl, token, createAd);

            // process errors from AggSvc

            if (adCollection.Failed)
            {
                context.Logger.LogError("CreateAdsRequest failed with error: " + adCollection.GetErrorMessage());
                result.AddErrors(adCollection.Errors);
                return result;
            }

            List<Ad> ads = new List<Ad>();
            if (adCollection.Entity?.Ads?.Count > 0)
            {
                var expandedTextAdAdds = AggAdsToExpandedTextAdAdd(adCollection.Entity.Ads, campaignId, business.Website);

                // Ad auto generation service only support English Ad. To support MultiLanguage Ads,
                // the language shall be provided by it.
                var localeIds = EditorialUtilities.GenerateLCIDKey(context.Logger, new Language[] { language });
                BatchResultWithEditorialErrors<long> batchResult = new BatchResultWithEditorialErrors<long>();
                var adsProvider = new AdsProvider<ExpandedTextAdAdd>(expandedTextAdAdds, batchResult, new AuthenticatedUserInfo(context.UserId));

                // For DMC and Smartcampaign customers, the DecisionAgent will be set to AEVP due to an issue in editorial service. 
                // Otherwise, DMC & SC customers will be marked as self-serve customer. All algo generated ads will be marked as 213(pending) status.
                EditorialValidation.RunEditorialChecks(context.Logger, adsProvider, localeIds, context.AdvertiserCustomerID, context.Request, Editorial.DecisionAgent.AEVP);

                for (var index = 0; index < expandedTextAdAdds.Count; index++)
                {
                    var expandedTextAdAdd = expandedTextAdAdds[index];
                    if (expandedTextAdAdd.EditorialStatus == null)
                    {
                        context.Logger.LogInfo($"Empty EditorialStatus in ad {expandedTextAdAdd.DisplayUrl}");

                        ads.Add(ToExpandedTextAd(expandedTextAdAdd));
                    }
                    else if (expandedTextAdAdd.EditorialStatus >= EditorialStatusForDatabase.Approved)
                    {
                        ads.Add(ToExpandedTextAd(expandedTextAdAdd));
                    }
                    else
                    {
                        bool approved = CheckEditorialStatusByCountry(
                            rnrCountryCodes,
                            expandedTextAdAdd,
                            rnrCountryCodeLookup);

                        if (approved)
                        {
                            ads.Add(ToExpandedTextAd(expandedTextAdAdd));
                            context.Logger.LogInfo($"Ad at index {index} is partial approved.");
                        }
                        else
                        {
                            context.Logger.LogInfo($"Ad at index {index} is disapproved.");
                            if (batchResult.EditorialErrors.TryGetValue(index, out List<Entities.EditorialErrorDetail> details))
                            {
                                foreach (var detail in details)
                                {
                                    context.Logger.LogInfo($"EditorialErrorDetail -- {JsonConvert.SerializeObject(detail)}");
                                }
                            }
                        }
                    }
                }

                context.Logger.LogInfo($"{ads.Count} out of {adCollection.Entity.Ads.Count} ads passed editorial verification");
            }
            var otherAds = ads.FindAll(ad => !ad.OfflineAdsSource.Contains(TemplateAdsSourceName));
            var templateAds = ads.FindAll(ad => ad.OfflineAdsSource.Contains(TemplateAdsSourceName));

            // Only use templateAds as back fill
            var selectedAds = GetRandomItemsBySource(otherAds, count);
            if (selectedAds.Count < count && templateAds.Count > 0)
            {
                templateAds.Shuffle();
                int take = templateAds.Count + selectedAds.Count < count ? templateAds.Count : count - selectedAds.Count;
                selectedAds.AddRange(templateAds.Take(take));
            }
            result.Entity = selectedAds;
            result.AddErrors(adCollection.Errors);
            var logSelectedAds = new
            {
                Ad = otherAds.OrEmptyCollection(),
                AdType = adType.ToString(),
                Website = business.Website,
                Date = DateTime.UtcNow.ToString("O"),
                ApplicationSource = context.ApplicationSource,
                Language = language.ToString()
            };
            context.Logger.LogInfo($"CreateAd recommendation: {JsonConvert.SerializeObject(logSelectedAds)}");

            return result;
        }

        public static bool CheckEditorialStatusByCountry(
            List<RnR.CountryCode> rnrCountryCodes,
            ExpandedTextAdAdd expandedTextAdAdd,
            Dictionary<RnR.CountryCode, PublisherCountry> rnrCountryCodeLookup)
        {
            bool approved = false;
            if (rnrCountryCodes.CollectionIsNullOrEmpty())
            {
                rnrCountryCodes = new List<RnR.CountryCode>()
                {
                    RnR.CountryCode.US
                };
            }

            foreach (var countryCode in rnrCountryCodes)
            {
                if (rnrCountryCodeLookup != null && rnrCountryCodeLookup.TryGetValue(
                        countryCode,
                        out var publisherCountry))
                {
                    if (expandedTextAdAdd.EditorialStatusByCountry != null && expandedTextAdAdd.EditorialStatusByCountry.TryGetValue(
                            publisherCountry,
                            out var editorialStatus))
                    {
                        if (editorialStatus.EditorialStatus >= EditorialStatusForDatabase.Approved)
                        {
                            approved = true;
                        }
                    }
                }
            }

            return approved;
        }

        public async Task<ResultOneEntity<RecommendedImages>> CreateImageRecommendation(
            AccountCallContext context,
            long accountId,
            long campaignId,
            string url,
            string source,
            IEnumerable<string> sources,
            ImageRankingCriteria rankingCriteria,
            IEnumerable<SelectedImage> selectedImages,
            ImageRecommendationOptions options,
            ImageRecommendationPageDefination paging)
        {
            ResultOneEntity<RecommendedImages> result = new ResultOneEntity<RecommendedImages>();

            var createImageRecommendation = new AggImageRecommendation
            {
                Url = url,
                Source = source,
                Sources = sources?.ToList(),
                SelectedImages = selectedImages?.ToList(),
                Options = options,
                Paging = paging
            };

            var hasAccountSource = createImageRecommendation.Sources?.Where(s => s.ToUpper().Equals("ACCOUNT")).Count() > 0;

            // When sources is empty, if account id is specified, auto image will add "account" in sources list. 
            // So we can't always set account id, in case autoimage always recommends account images.
            if (hasAccountSource)
            {
                createImageRecommendation.AccountId = accountId;
            }

            if (rankingCriteria != null)
            {
                createImageRecommendation.RankingCriteria = new AggImageRankingCriteria
                {
                    Type = rankingCriteria.Type,
                    Languagecode = rankingCriteria.LanguageCode,
                    Criteria = new Dictionary<string, IList<string>>()
                };
                foreach (var rankingCriteriaItem in rankingCriteria.Criteria)
                {
                    createImageRecommendation.RankingCriteria.Criteria.Add(rankingCriteriaItem.Name, rankingCriteriaItem.Value);
                }
            }
            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);

            var aggResponse = await this.PostAsync<AggImageRecommendationResponse>(context, campaignId, CreateImagesRequestUrl, token, createImageRecommendation);
            if (aggResponse.Entity != null)
            {
                result.Entity = new RecommendedImages
                {
                    Images = aggResponse.Entity.Images,
                    Paging = aggResponse.Entity.Paging,
                    Wip = aggResponse.Entity.Wip
                };
            }

            result.AddErrors(aggResponse.Errors);
            context.Logger.LogInfo($"CreateImage recommendation: {JsonConvert.SerializeObject(result.Entity)}");

            return result;
        }

        public async Task<ResultOneEntity<MediaGenerationResult>> CreateMediaGeneration(
            AccountCallContext context,
            long campaignId,
            string jobId,
            ImageGeneration image,
            MediaOutput output)
        {
            ResultOneEntity<MediaGenerationResult> result = new ResultOneEntity<MediaGenerationResult>();

            var mediaGeneration = new MediaGeneration
            {
                JobId = jobId,
                Output = new MediaGenerationExpectOutput
                {
                    MediaType = output.MediaType,
                    Image = new MediaGeneratioExpectOutputImage
                    {
                        Width = output.Image.Width,
                        Height = output.Image.Height,
                        Type = output.Image.Type,
                    }
                },
                Image = new MediaGenerationImage
                {
                    BaseImageUrl = image.BaseImageUrl,
                    Filters = new List<MediaGenerationImageFilter>(),
                }
            };

            if (image.Filters?.Count > 0)
            {
                foreach (var filter in image.Filters)
                {
                    MediaGenerationImageFilter mgFilter = new MediaGenerationImageFilter
                    {
                        Name = filter.Name,
                        Param = new MediaGenerationImageFilterParameter()
                    };

                    if (filter.Param?.ExpectedCrops?.Count > 0)
                    {
                        mgFilter.Param.ExpectedCrops = new List<MediaGenerationSmartCropSetting>();
                        foreach (var expectCrop in filter.Param.ExpectedCrops)
                        {
                            mgFilter.Param.ExpectedCrops.Add(new MediaGenerationSmartCropSetting
                            {
                                AspectRatio = expectCrop.AspectRatio,
                                MinHeight = expectCrop.MinHeight,
                            });
                        }
                    }

                    if (!string.IsNullOrEmpty(filter.Param?.ManualBgColor))
                    {
                        mgFilter.Param.ManualBgColor = filter.Param.ManualBgColor;
                    }

                    if (filter.Param?.ManualBgBlurLevel != null)
                    {
                        mgFilter.Param.ManualBgBlurLevel = filter.Param.ManualBgBlurLevel;
                    }

                    mediaGeneration.Image.Filters.Add(mgFilter);
                }
            }

            if (image.Crop != null)
            {
                mediaGeneration.Image.Crop = new MediaGenerationManualCropSetting
                {
                    X = image.Crop.X,
                    Y = image.Crop.Y,
                    Width = image.Crop.Width,
                    Height = image.Crop.Height,
                    ScaleX = image.Crop.ScaleX,
                    ScaleY = image.Crop.ScaleY,
                    ZIndex = image.Crop.ZIndex
                };
            }

            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
            var url = MediaGenerationRequestUrl;
            var httpClient = this.autoImageHttpClient;

            if (DynamicConfigValues.UseAggregationServiceEndpointForAutoImage)
            {
                context.Logger.LogInfo("Call Aggregation service endpoint to generate images");
                url = AggSvcMediaGenerationUrl;
                httpClient = this.httpClient;
            }

            var mediaGenerationResponse = await this.PostAsync<MediaGenerationResponse>(context, campaignId, url, token, mediaGeneration, httpClient, this.generationServiceSerializerSetting);


            if (mediaGenerationResponse.Entity != null)
            {
                result.Entity = new MediaGenerationResult
                {
                    JobId = mediaGenerationResponse.Entity.JobId,
                    Assets = new List<MediaAsset>()
                };

                foreach (var mgAsset in mediaGenerationResponse.Entity.Assets)
                {
                    MediaAsset asset = new MediaAsset
                    {
                        AssetProperties = new Dictionary<string, object>()
                    };

                    foreach (var prop in mgAsset.GetType().GetProperties())
                    {
                        var val = prop.GetValue(mgAsset);
                        if (val != null)
                            asset.AssetProperties.Add(prop.Name, val);
                    }

                    result.Entity.Assets.Add(asset);
                }
            }

            result.AddErrors(mediaGenerationResponse.Errors);
            context.Logger.LogInfo($"CreateMedia generation: {JsonConvert.SerializeObject(result.Entity)}");

            return result;
        }

        public async Task<ResultOneEntity<bool>> PrepareAdRecommendation(AccountCallContext context, long campaignId, Business business)
        {
            var prepareAds = new AggPrepareAds
            {
                Url = business.Website,
            };

            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);

            return await this.PostAsync(context, campaignId, PrepareAdsRequestUrl, token, prepareAds);
        }

        public async Task<ResultOneEntity<bool>> PrepareRecommendation(
            AccountCallContext context,
            long campaignId,
            string website,
            IEnumerable<string> entities)
        {
            bool prepareAds = false;
            bool prepareKeywords = false;

            if (entities.Contains("ad"))
            {
                prepareAds = true;
            }

            if (entities.Contains("keyword"))
            {
                prepareKeywords = true;
            }

            if (prepareAds == false && prepareKeywords == false)
            {
                context.Logger.LogError("No supported entity found, entities: ", JsonConvert.SerializeObject(entities));

                var errors = new List<CampaignManagementErrorDetail> { new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidParameters) };
                return await Task.Run(() => new ResultOneEntity<bool>(false) { Errors = errors });
            }

            var prepareEntities = new AggPrepareEntities
            {
                PrepareAds = prepareAds,
                PrepareKeywords = prepareKeywords,
                Url = website
            };

            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);

            return await this.PostAsync(context, campaignId, PrepareEntitiesRequestUrl, token, prepareEntities);
        }

        public async Task<ResultOneEntity<CreateKeywordRecommendationResponse>> CreateKeywordRecommendation(
            AccountCallContext context,
            long campaignId,
            string businessWebsite,
            Language language,
            CampaignGoal campaignGoal,
            IEnumerable<SmartListing> smartListings,
            IEnumerable<Criterion> criterions,
            List<PublisherCountryInfo> effectivePublisherCountries,
            long adGroupId,
            bool refreshCache = false)
        {
            var keywordListForAnalysis = new List<SmartListing>();

            var result = new ResultOneEntity<CreateKeywordRecommendationResponse>(new CreateKeywordRecommendationResponse
            {
                SmartListings = new List<SmartListing>()
            });

            var selectedSmartListings = smartListings.Where(s => s.IsSelected == true).ToList();
            var selectedKeywords = new List<AggKeywordEntity>();

            foreach (var smartListing in selectedSmartListings)
            {
                selectedKeywords.Add(new AggKeywordEntity
                {
                    Text = smartListing.Text,
                    IsSelected = smartListing.IsSelected == true,
                    IsInputByUser = smartListing.IsSuggested != true,
                    SourceId = smartListing.Source,
                    RecommendationId = smartListing.RecommendationId
                });
            }

            var createKeywords = new AggCreateKeywords
            {
                RGUID = context.Logger.TrackingData.RequestIdString,
                CustomerId = (uint)context.AdvertiserCustomerID,
                AccountId = (uint)context.AccountId,
                CampaignId = (ulong)campaignId,
                Url = businessWebsite,
                DesiredKeywordsCount = DesiredKeywordsCount,
                LanguageCode = LanguageInfo.GetLanguageCode(language).ToLower(),
                SelectedKeywords = selectedKeywords
            };

            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
            var parameters = new Dictionary<string, string>
            {
                { "refreshCache", refreshCache.ToString() }
            };
            var requestUrl = CreateKeywordsRequestUrl + "?" + GenerateQueryParameters(parameters).ToString();

            ResultOneEntity<AggKeywordsResponse> keywordsResponse = null;

            try
            {
                keywordsResponse = await this.PostAsync<AggKeywordsResponse>(context, campaignId, requestUrl, token, createKeywords);

                if (keywordsResponse.Failed)
                {
                    context.Logger.LogError("CreateKeywordsRequest failed with error: " + keywordsResponse.GetErrorMessage());
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("CreateKeywordsRequest failed with exception:" + ex);
            }

            var validSmartListings = new List<SmartListing>();
            if (keywordsResponse?.Entity?.SmartListings?.Count > 0)
            {
                var newSmartListings = AggKeywwordsToSmartListing(keywordsResponse.Entity.SmartListings.Where(s => s.IsSelected == false));
                keywordListForAnalysis.AddRange(newSmartListings);

                if (newSmartListings.Count == 0)
                {
                    context.Logger.LogInfo("No new keywords from KeywordSuggesionService");
                }
                else
                {
                    newSmartListings.ForEach(s => s.EffectivePublisherCountries = effectivePublisherCountries);

                    var editorialResult = RunEditorialCheck(context, language, adGroupId, newSmartListings);

                    for (int idx = 0; idx < newSmartListings.Count; idx++)
                    {
                        if (newSmartListings[idx].EditorialStatus != EditorialStatusForDatabase.Approved)
                        {
                            context.Logger.LogInfo("Keyword is not approved, text: " + newSmartListings[idx].Text + " editorial status: " + newSmartListings[idx].EditorialStatus);
                            if (editorialResult.EditorialErrors.TryGetValue(idx, out List<Entities.EditorialErrorDetail> details))
                            {
                                foreach (var detail in details)
                                {
                                    context.Logger.LogInfo($"EditorialErrorDetail -- {JsonConvert.SerializeObject(detail)}");
                                }
                            }
                            continue;
                        }

                        if (editorialResult.BatchErrors.TryGetValue(idx, out var errorDetails))
                        {
                            context.Logger.LogInfo("Keyword has batch error, text: " + newSmartListings[idx].Text + " error: " + JsonConvert.SerializeObject(errorDetails));
                            continue;
                        }

                        context.Logger.LogInfo("Keyword is approved, text: " + newSmartListings[idx].Text);
                        validSmartListings.Add(newSmartListings[idx]);
                    }
                }
            }

            context.Logger.LogInfo($"CreateKeyword recommendation: {JsonConvert.SerializeObject(validSmartListings)}");

            var mergedSmartListings = smartListings.ToList();
            mergedSmartListings.AddRange(validSmartListings.Take(DesiredKeywordsCount));

            //Remove duplicate entity while preserving request input if exist
            mergedSmartListings = mergedSmartListings.GroupBy(s => s.Text).Select(g => g.First()).ToList();
            keywordListForAnalysis = keywordListForAnalysis.GroupBy(s => s.Text).Select(g => g.First()).ToList();
            var pageContent = keywordsResponse?.Entity?.PageContent;

            List<RnR.CountryCode> countryCodes = SmartHelper.GetCountryCodesFromCriterions(context.Logger, criterions);
            var requestParam = new RnR.GetKeywordRecommendationsParams
            {
                Header = SmartHelper.GetRequestHeader(
                    context.AdvertiserCustomerID,
                    (int)context.AccountId,
                    campaignId),
                BasicBusinessInfo = SmartHelper.ConvertToRnRBasicBusinessInfo(
                    campaignGoal,
                    businessWebsite,
                    criterions,
                    countryCodes),
                DetailedBusinessInfoV2 = SmartHelper.ConvertToRnRDetailedBusinessInfoV2(
                    language,
                    mergedSmartListings,
                    true),
                WebPageInfos = SmartHelper.ConvertToWebPageInfos(pageContent, businessWebsite),
            };

            RnR.GetKeywordRecommendationsResponse response = null;

            try
            {
                response = await ExternalServiceHelper.CallWithRetryAsync(
                    async () => await smartCaller.CallOsCoproc<
                            RnR.Controller.SCControllerDataKey, RnR.Controller.SCControllerDataValue,
                            RnR.GetKeywordRecommendationsResponse, RnR.GetKeywordRecommendationsParams>(
                            SCControllerDataTable,
                            GetKeywordRecommendationsCoprocName,
                            requestParam,
                            context.Logger)
                        .ConfigureAwait(false),
                    context.Logger,
                    "GetKeywordRecommendations",
                    (SCControllerDataTable, GetKeywordRecommendationsCoprocName, requestParam)).ConfigureAwait(false);

                context.Logger.LogInfo($"CreateKeywordRecommendation CallOsCoproc response - {JsonConvert.SerializeObject(response)}");
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("CreateKeywordRecommendation catch CallOsCoproc exception:" + ex);
            }

            result.Entity = ConvertToCreateKeywordRecommendationResponse(context, response, mergedSmartListings);

            var keywordTextToSmartListingMapping = keywordListForAnalysis.ToDictionary(x => x.Text, x => x);
            foreach (var smartListing in result.Entity.SmartListings)
            {
                if (keywordTextToSmartListingMapping.TryGetValue(smartListing.Text, out var originalSmartListing))
                {
                    originalSmartListing.RankingScore = smartListing.RankingScore;
                    originalSmartListing.IsSelected = smartListing.IsSelected;
                }
                else
                {
                    smartListing.Source = "MPSoffline";
                    smartListing.RecommendationId = Guid.NewGuid().ToString();
                    keywordListForAnalysis.Add(smartListing);
                }
            }

            var keywordRecommendationAnalysisData = new
            {
                ProductType = context.ApplicationSource,
                AccountId = context.AccountId,
                CampaignId = campaignId,
                Url = businessWebsite,
                HasUserSelectedKeywords = selectedSmartListings.Any(),
                KeywordList = keywordListForAnalysis.Select(k =>
                    new
                    {
                        KeywordText = k.Text,
                        Source = k.Source,
                        RecommendationId = k.RecommendationId,
                        RankingScore = k.RankingScore,
                        EditorialStatus = k.EditorialStatus.ToString(),
                        IsSelected = k.IsSelected
                    }
                ),
                Language = language.ToString()
            };

            context.Logger.LogInfo(
                $"Log keyword recommendation result for analysis: " +
                $"{JsonConvert.SerializeObject(keywordRecommendationAnalysisData)}");

            context.Logger.LogInfo(
                "Result - " + $"Customer Id: {context.AdvertiserCustomerID}; " + $"Account Id: {context.AccountId}; " +
                $"Campaign Id: {campaignId}; " + $"Result: {JsonConvert.SerializeObject(result)}");

            return result;
        }

        public async Task<ResultOneEntity<GenerateMediaAltTextResponse>> CreateMediaAltTextGeneration(
             AccountCallContext context,
             long campaignId,
             long adId,
             long orderId,
             string landingPageUrl,
             string mediaUrl,
             string mediaType,
             bool skipRAI = false,
             int maxCharacterLength = 30)
        {
            ResultOneEntity<GenerateMediaAltTextResponse> result = new ResultOneEntity<GenerateMediaAltTextResponse>(new GenerateMediaAltTextResponse { Texts = new List<string>() });

            var mediaAltTextGenerationRequest = new GenerateMediaAltTextRequest
            {
                AccountId = context.AccountId,
                CampaignId = campaignId,
                AdId = adId,
                OrderId = orderId,
                LandingPageUrl = landingPageUrl,
                MediaUrl = mediaUrl,
                MediaType = mediaType,
                MaxCharacterLength = maxCharacterLength,
                MaxTextCount = 10
            };
            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
            var requestUrl = MediaAltTextGenerationRequestUrl;

            if (skipRAI)
            {
                requestUrl = requestUrl + "?enableRAI=false";
            }

            try
            {
                result = await this.PostAsync<GenerateMediaAltTextResponse>(context, campaignId, requestUrl, token, mediaAltTextGenerationRequest);

                if (result.Failed)
                {
                    context.Logger.LogWarning("CreateMediaAltTextGeneration failed with error: " + result.GetErrorMessage() + " for adId = {0}", adId);
                }
                else
                {
                    context.Logger.LogInfo("CreateMediaAltTextGeneration, text returned from aigc : " + result.Entity.Texts[0] + " for adId = {0}", adId);
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("CreateMediaAltTextGeneration failed with exception:" + ex + " for adId = {0}", adId);
            }
            return result;
        }

        private static BatchResultWithEditorialErrors<long> RunEditorialCheck(AccountCallContext context, Language language, long adGroupId, List<SmartListing> smartListings)
        {
            var result = new BatchResultWithEditorialErrors<long>();

            var keywordEditorialData = new List<IKeywordEditorialData>(smartListings);
            var keywordsProvider = new KeywordsProvider<IKeywordEditorialData>(keywordEditorialData, result, context.Request.UserInfo);

            var lcidKey = EditorialUtilities.GenerateLCIDKey(context.Logger, new Language[] { language });

            // User can only manage smart listings in one ad group, and the EditorialValidation ask for the ad group ID for each smart listing
            var adGroupIds = new List<long>();
            smartListings.ForEach(s => adGroupIds.Add(adGroupId));

            EditorialValidation.RunAdQualityForKeywords(context.Logger, keywordsProvider, lcidKey, adGroupIds, context.AdvertiserCustomerID, context.Request.UserInfo, context.Request);

            return result;
        }

        public async Task<ResultOneEntity<string>> ValidateUrl(
             AccountCallContext context,
             long campaignId,
             string url,
             bool skipCache)
        {
            var result = new ResultOneEntity<string>();
            var validateUrlRequestPayload = new AggValidateUrl
            {
                Url = url,
                SkipCache = skipCache
            };

            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);

            var validateUrlResponse = await ExternalServiceHelper.CallWithRetryAsync(
                async () => await this.PostAsync<AggValidateUrlResponse>(
                    context,
                    campaignId,
                    ValidateUrlRequestUrl,
                    token,
                    validateUrlRequestPayload),
                context.Logger,
                "ValidateUrlFromAggregationService",
                (context, campaignId, ValidateUrlRequestUrl, token, validateUrlRequestPayload));

            if (validateUrlResponse.Failed)
            {
                context.Logger.LogError("ValidateUrlRequest failed with error: " + validateUrlResponse.GetErrorMessage());
                result.AddErrors(validateUrlResponse.Errors);
                return result;
            }

            if (validateUrlResponse.Entity?.UrlError != null)
            {
                context.Logger.LogInfo($"ValidateUrl cannot access URL: {url}, " +
                    $"status code: {validateUrlResponse.Entity.UrlError.StatusCode}, " +
                    $"error message: {validateUrlResponse.Entity.UrlError.Message}");

                if (string.Equals(validateUrlResponse.Entity.UrlError.Message, "Blocked by Cloudflare"))
                {
                    result.AddError(CampaignManagementErrorCode.BlockedByCloudflare);
                }
                else if (string.Equals(validateUrlResponse.Entity.UrlError.Message, "SSL Verification Failed"))
                {
                    result.AddError(CampaignManagementErrorCode.UrlSSLVerificationFailed);
                }
                else if (UrlScanFailureErrorCodes.Contains(validateUrlResponse.Entity.UrlError.StatusCode))
                {
                    result.AddError(CampaignManagementErrorCode.UrlScanFailed);
                }
                else
                {
                    result.AddError(CampaignManagementErrorCode.UrlNotAccessible);
                }
            }
            else if (string.IsNullOrEmpty(validateUrlResponse.Entity?.ValidatedUrl))
            {
                context.Logger.LogError("ValidateUrlRequest succeeded but received empty validated URL.");
                result.AddError(CampaignManagementErrorCode.InternalError);
            }
            else
            {
                result.Entity = validateUrlResponse.Entity.ValidatedUrl;
            }

            context.Logger.LogInfo($"ValidateUrl result: {JsonConvert.SerializeObject(result.Entity)}");

            return result;
        }

        public async Task<ResultOneEntity<List<McaAds.McaCategory>>> GetCategoryRecommendation(
            AccountCallContext context,
            long? campaignId,
            string url,
            List<McaAds.McaKeyword> keywords,
            int categoryCount)
        {
            var result = new ResultOneEntity<List<McaAds.McaCategory>>(new List<McaAds.McaCategory>());
            var selectedKeywords = keywords.Select(k => k.KeywordText).ToList();

            var recommendCategoryRequestPayload = new AggRecommendCategory
            {
                Url = url,
                Keywords = selectedKeywords,
                CategoryCount = categoryCount
            };

            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);

            try
            {
                var recommendCategoryResponse = await this.PostAsync<AggRecommendCategoryResponse>(context, campaignId ?? 0, RecommendCategoryRequestUrl, token, recommendCategoryRequestPayload);

                if (recommendCategoryResponse.Failed)
                {
                    context.Logger.LogError("RecommendCategoryRequest failed with error: " + recommendCategoryResponse.GetErrorMessage());
                    result.AddErrors(recommendCategoryResponse.Errors);
                    return result;
                }

                var categories = new List<McaAds.McaCategory>();
                if (recommendCategoryResponse.Entity?.RecommendedCategories?.Count > 0)
                {
                    categories = recommendCategoryResponse.Entity.RecommendedCategories
                        .OrderByDescending(c => c.Score)
                        .Select(c => new McaAds.McaCategory
                        {
                            Id = c.Id,
                            CategoryName = c.CategoryName
                        }).ToList();
                }
                else
                {
                    context.Logger.LogInfo("No recommended category from CategorySuggestionService");
                    return result;
                }

                result.Entity = categories;
            }
            catch (Exception e)
            {
                context.Logger.LogInfo(e.ToString());
                result.AddError(CampaignManagementErrorCode.InternalError);
                return result;
            }

            context.Logger.LogInfo($"Category recommendation: {JsonConvert.SerializeObject(result.Entity)}");

            return result;
        }

        private async Task<ResultOneEntity<AggregationServiceAdAssetCollection>> CreateAdAssetRecommendation(
            AccountCallContext context,
            long? campaignId,
            Business business,
            AdType adType,
            IList<string> categories,
            IList<string> countryCodes,
            string location,
            int count)
        {
            var createAdAssets = new AggCreateAdAssets
            {
                Url = business.Website,
                AdType = AdTypeToString(adType),
                Categories = categories,
                CountryCodes = countryCodes,
                BusinessName = business.Name,
                Location = location,
                Count = count
            };

            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
            return await this.PostAsync<AggregationServiceAdAssetCollection>(context, campaignId, CreateAdAssetsRequestUrl, token, createAdAssets);
        }

        public async Task<ResultOneEntity<List<ResponsiveSearchAd>>> CreateResponsiveSearchAdRecommendation(
            AccountCallContext context,
            long? campaignId,
            IEnumerable<string> categories,
            Business business,
            IEnumerable<Criterion> criterions,
            AdType adType,
            int headlineCount,
            int descriptionCount)
        {
            ResultOneEntity<List<ResponsiveSearchAd>> result = new ResultOneEntity<List<ResponsiveSearchAd>>
            {
                Entity = new List<ResponsiveSearchAd>(),
            };
            string locationString = criterions == null ? null : SmartHelper.ValidateAndSelectLocation(result, criterions);
            var countryCodes = criterions == null ? null : SmartHelper.GetCountryCodesFromCriterions(context.Logger, criterions)?.Select(x => x.ToString("G")).ToList();

            ResultOneEntity<AggregationServiceAdAssetCollection> adAssetCollection = null;
            try
            {
                adAssetCollection = await CreateAdAssetRecommendation(context, campaignId, business, adType, categories?.ToList(), countryCodes, locationString, 1);
                if (adAssetCollection.Failed)
                {
                    context.Logger.LogError("CreateAdAssetsRequest failed with error: " + adAssetCollection.GetErrorMessage());
                    result.AddErrors(adAssetCollection.Errors);
                    return result;
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("CreateAdAssetsRequest CreateAdAsset failed with exception:" + ex);
            }

            if (adAssetCollection?.Entity?.AdAssets?.Count > 0)
            {
                var RSAAdRecommendation = new
                {
                    Ad = adAssetCollection.Entity.AdAssets,
                    AdType = adType.ToString(),
                    Website = business.Website,
                    Date = DateTime.UtcNow.ToString("O"),
                    ApplicationSource = context.ApplicationSource,
                    Language = Language.English.ToString()
                };
                context.Logger.LogInfo($"Get CreateAdAsset recommendation: {JsonConvert.SerializeObject(RSAAdRecommendation)}");

                var responsiveSearchAdAdds = AggAdAssetsToResponsiveSearchAdAdd(adAssetCollection.Entity.AdAssets, campaignId, business.Website);

                BatchResultWithEditorialErrors<long> batchResult = new BatchResultWithEditorialErrors<long>();
                var localeIds = EditorialUtilities.GenerateLCIDKey(context.Logger, new Language[] { Language.English });
                var adsProvider = new AdsProvider<ResponsiveSearchAdAdd>(responsiveSearchAdAdds, batchResult, new AuthenticatedUserInfo(context.UserId));

                EditorialValidation.RunEditorialChecks(context.Logger, adsProvider, localeIds, context.AdvertiserCustomerID, context.Request, assetEditorialContext: new AssetEditorialContext());

                var adsForRanking = new List<ResponsiveSearchAdAdd>();
                for (var index = 0; index < responsiveSearchAdAdds.Count; index++)
                {
                    var responsiveSearchAdAdd = responsiveSearchAdAdds[index];

                    if (responsiveSearchAdAdd.EditorialStatus == null)
                    {
                        context.Logger.LogInfo($"Empty EditorialStatus in ad {responsiveSearchAdAdd.DisplayUrl}");

                        adsForRanking.Add(responsiveSearchAdAdd);
                    }
                    else if (responsiveSearchAdAdd.EditorialStatus >= EditorialStatusForDatabase.Approved)
                    {
                        adsForRanking.Add(responsiveSearchAdAdd);
                    }
                    else
                    {
                        context.Logger.LogInfo($"Ad at index {index} is disapproved:");
                        if (batchResult.EditorialErrors.TryGetValue(index, out List<Entities.EditorialErrorDetail> details))
                        {
                            foreach (var detail in details)
                            {
                                context.Logger.LogInfo($"EditorialErrorDetail -- {JsonConvert.SerializeObject(detail)}");
                            }
                        }
                    }
                }
                context.Logger.LogInfo($"{adsForRanking.Count} out of {responsiveSearchAdAdds.Count} ad assets passed editorial verification");
                var rsaAd = await GetAdInsightResponsiveSearchAdsRanking(context, campaignId, business, adType, categories?.ToList(), countryCodes, locationString, headlineCount, descriptionCount, adsForRanking);
                result.Entity.Add(rsaAd);
            }

            context.Logger.LogInfo($"CreateAdAsset recommendation: {JsonConvert.SerializeObject(result.Entity)}");
            return result;
        }

        public async Task<ResultOneEntity<DisplayRecommendationResponse>> EditDisplayAdsRecommendation(
            AccountCallContext context,
            DisplayEditRecommendationRequest request
            )
        {
            ResultOneEntity<DisplayRecommendationResponse> result = new ResultOneEntity<DisplayRecommendationResponse>
            {
                Entity = null,
            };

            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var destUrl = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{EditDisplayAdsRecommendationUrl}";
                result = await this.PostAsync<DisplayRecommendationResponse>(context, null, destUrl, token, request, useCCMTToken: true, getContentOnError: true);

                if (result.Failed)
                {
                    context.Logger.LogError("EditDisplayAdsRecommendation failed with error: " + result.GetErrorMessage());
                    result.AddErrors(result.Errors);
                    return result;
                }
                else if (result.Entity?.errors != null)
                {
                    context.Logger.LogUserError("EditDisplayAdsRecommendation failed with user error: " + result.Entity.errors.FirstOrDefault().message);
                    return result;
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("EditDisplayAdsRecommendation failed with exception:" + ex);
            }

            context.Logger.LogInfo($"EditDisplayAdsRecommendation: {JsonConvert.SerializeObject(result.Entity)}");
            return result;
        }

        public async Task<ResultOneEntity<DisplayRecommendationResponse>> CreateDisplayAdsRecommendation(
            AccountCallContext context,
            string url,
            bool mock,
            string languageCode,
            int count,
            string templateVersion = null,
            BrandKit brandKit = null)
        {
            ResultOneEntity<DisplayRecommendationResponse> result = new ResultOneEntity<DisplayRecommendationResponse>
            {
                Entity = null,
            };

            var payload = new
            {
                Url = url,
                Mock = mock,
                LanguageCode = languageCode,
                Count = count,
                TemplateVersion = !string.IsNullOrEmpty(templateVersion) ? [templateVersion] : GetSupportedDisplayAdsTemplateVersion(context),
                BrandKit = brandKit != null ? GetConvertedMTBrandKit(brandKit) : null
            };

            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var destUrl = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{CreateDisplayAdsRecommendationUrl}";

                ApplyMistralEnablementOnUrl(ref destUrl, context);

                result = await this.PostAsync<DisplayRecommendationResponse>(context, null, destUrl, token, payload, useCCMTToken: true, getContentOnError: true);

                if (result.Failed)
                {
                    context.Logger.LogError("CreateDisplayAdsRecommendation failed with error: " + result.GetErrorMessage());
                    result.AddErrors(result.Errors);
                    return result;
                }
                else if (result.Entity != null && result.Entity.errors?.Count > 0)
                {
                    context.Logger.LogUserError("CreateDisplayAdsRecommendation failed with user error: " + result.Entity.errors.FirstOrDefault().message);
                    return result;
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("CreateDisplayAdsRecommendation failed with exception:" + ex);
            }

            context.Logger.LogInfo($"CreateDisplayAdsRecommendation: {JsonConvert.SerializeObject(result.Entity)}");
            return result;
        }



        public async Task<ResultOneEntity<VideoRecommendationResponse>> CreateVideoAssetsRecommendation(
            AccountCallContext context,
            string url,
            bool mock,
            int count,
            string recommendationType,
            bool useClipchamp,
            VideoRecommendationTemplateFilter templateFilter,
            BrandKit brandKit
        )
        {
            ResultOneEntity<VideoRecommendationResponse> result = new ResultOneEntity<VideoRecommendationResponse>
            {
                Entity = null,
            };

            try
            {
                var payload = new
                {
                    Url = url,
                    Mock = mock,
                    Count = count,
                    RecommendationType = recommendationType,
                    TemplateFilter = templateFilter,
                    BrandKit = brandKit != null ? GetConvertedMTBrandKit(brandKit) : null
                };

                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var destUrl = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{CreateVideoAssetsRecommendationUrl}";

                ApplyMistralEnablementOnUrl(ref destUrl, context);

                if (useClipchamp)
                {
                    AppendParameter(ref destUrl, nameof(useClipchamp), useClipchamp.ToString().ToLower());
                }

                result = await this.PostAsync<VideoRecommendationResponse>(context, null, destUrl, token, payload, useCCMTToken: true, getContentOnError: true);

                if (result.Failed)
                {
                    context.Logger.LogError("VideoRecommendationResponse failed with error: " + result.GetErrorMessage());
                    result.AddErrors(result.Errors);
                    return result;
                }
                else if (result.Entity != null && result.Entity.errors?.Count > 0)
                {
                    context.Logger.LogUserError("VideoRecommendationResponse failed with user error: " + result.Entity.errors.FirstOrDefault().message);
                    return result;
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("VideoRecommendationResponse failed with exception:" + ex);
            }

            context.Logger.LogInfo($"CreateDisplayAdsRecommendation: {JsonConvert.SerializeObject(result.Entity)}");
            return result;
        }

        public async Task<ResultOneEntity<VideoClipchampConversionResponse>> ExportClipchampVideoRecommendation(
            AccountCallContext context,
            string url,
            string videoId,
            string generationModel = ""
        )
        {
            ResultOneEntity<VideoClipchampConversionResponse> result = new ResultOneEntity<VideoClipchampConversionResponse>
            {
                Entity = null,
            };

            try
            {
                var payload = new
                {
                    url = url,
                    videoId = videoId,
                    VideoGenerationMode = generationModel
                };

                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var destUrl = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{ExportClipchampVideoRecommendationUrl}";

                result = await this.PostAsync<VideoClipchampConversionResponse>(context, null, destUrl, token, payload, useCCMTToken: true, getContentOnError: true);

                if (result.Failed)
                {
                    context.Logger.LogError("VideoClipchampConversionResponse failed with error: " + result.GetErrorMessage());
                    result.AddErrors(result.Errors);
                    return result;
                }
                if (result.Entity != null && result.Entity.errors?.Count > 0)
                {
                    context.Logger.LogUserError("VideoClipchampConversionResponse failed with user error: " + result.Entity.errors.FirstOrDefault().errorMessage);
                    return result;
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("VideoClipchampConversionResponse failed with exception:" + ex);
            }

            context.Logger.LogInfo($"ExportClipchampVideoRecommendation: {JsonConvert.SerializeObject(result.Entity)}");
            return result;
        }

        private AIGCBrandKit GetConvertedMTBrandKit(BrandKit brandKit)
        {
            var aIGCBrandKit = new AIGCBrandKit
            {
                BusinessName = brandKit.BusinessName,
                BrandVoice = new AIGCBrandKit.BrandKitVoice(),
                Palettes = new List<AIGCBrandKit.BrandPalette>(),
                Fonts = new List<AIGCBrandKit.BrandFont>(),
                Logos = new List<AIGCBrandKit.BrandImage>(),
                ProductImages = new List<AIGCBrandKit.BrandImage>()
            };

            if (brandKit.Palettes != null)
            {
                foreach (var palette in brandKit.Palettes)
                {

                    aIGCBrandKit.Palettes.Add(new AIGCBrandKit.BrandPalette
                    {
                        ColorType = palette.ColorType,
                        Colors = palette.Colors.Select(c => c.HexCode).ToList()
                    });
                }
            }

            if (brandKit.Fonts != null)
            {
                foreach (var font in brandKit.Fonts)
                {
                    aIGCBrandKit.Fonts.Add(new AIGCBrandKit.BrandFont
                    {
                        Type = font.Typeface,
                        Weight = font.Weight,
                        AdComponent = font.TextAssetType
                    });
                }
            }

            BrandKitImage[] brandKitLogos = (brandKit.SquareLogos ?? Array.Empty<object>())
                .Concat(brandKit.LandScapeLogos ?? Array.Empty<object>())
                .OfType<BrandKitImage>()
                .ToArray();
            foreach (var asset in brandKitLogos)
            {
                aIGCBrandKit.Logos.Add(new AIGCBrandKit.BrandImage
                {
                    ImageAssetId = asset.Id.ToString(),
                    Url = asset.Url,
                    AspectRatio = asset.AssetAssociationType == AssetAssociationType.SquareLogoMedia ? "1:1" : "1.91:1",
                    CropSettings = new List<AIGCBrandKit.BrandImage.CropSettingProperty>()
                    {
                        new AIGCBrandKit.BrandImage.CropSettingProperty
                        {
                            SourceX = asset.CropSettings.SourceX,
                            SourceY = asset.CropSettings.SourceY,
                            SourceWidth = asset.CropSettings.SourceWidth,
                            SourceHeight = asset.CropSettings.SourceHeight
                        }
                    }
                });
            }

            if (brandKit.Images != null)
            {
                foreach (var asset in brandKit.Images)
                {
                    aIGCBrandKit.ProductImages.Add(new AIGCBrandKit.BrandImage
                    {
                        ImageAssetId = asset.Id.ToString(),
                        Url = asset.Url,
                        AspectRatio = null,
                        CropSettings = new List<AIGCBrandKit.BrandImage.CropSettingProperty>()
                        {
                            new AIGCBrandKit.BrandImage.CropSettingProperty
                            {
                                SourceX = asset.CropSettings.SourceX,
                                SourceY = asset.CropSettings.SourceY,
                                SourceWidth = asset.CropSettings.SourceWidth,
                                SourceHeight = asset.CropSettings.SourceHeight
                            }
                        }
                    });
                }

            }

            if (brandKit.BrandVoice != null)
            {
                aIGCBrandKit.BrandVoice = new AIGCBrandKit.BrandKitVoice
                {
                    Tones = new List<string>(brandKit.BrandVoice.Tones),
                    Personality = brandKit.BrandVoice.Personality != null ? new List<string> { brandKit.BrandVoice.Personality } : null,
                };
            }

            return aIGCBrandKit;
        }

        public async Task<ResultOneEntity<EditKeyFrameResponse>> EditKeyFrame(
            AccountCallContext context,
            EditKeyFrameRequest request
        )
        {
            ResultOneEntity<EditKeyFrameResponse> result = new ResultOneEntity<EditKeyFrameResponse>
            {
                Entity = null,
            };

            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var destUrl = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{EditKeyFrameUrl}";
                result = await this.PostAsync<EditKeyFrameResponse>(context, null, destUrl, token, request, useCCMTToken: true, getContentOnError: true);

                if (result.Failed)
                {
                    context.Logger.LogError("EditKeyFrame failed with error: " + result.GetErrorMessage());
                    result.AddErrors(result.Errors);
                    return result;
                }
                else if (result.Entity?.errors != null)
                {
                    context.Logger.LogUserError("EditKeyFrame failed with user error: " + result.Entity.errors.FirstOrDefault().message);
                    return result;
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("EditKeyFrame failed with exception:" + ex);
            }

            context.Logger.LogInfo($"EditKeyFrame: {JsonConvert.SerializeObject(result.Entity)}");
            return result;
        }
        
        public async Task<ResultOneEntity<GenerateVideoClipsResponse>> GenerateVideoClips(
            AccountCallContext context,
            GenerateVideoClipsRequest request
        )
        {
            ResultOneEntity<GenerateVideoClipsResponse> result = new ResultOneEntity<GenerateVideoClipsResponse>
            {
                Entity = null,
            };

            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var destUrl = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{GenerateVideoClipsUrl}";
                result = await this.PostAsync<GenerateVideoClipsResponse>(context, null, destUrl, token, request, useCCMTToken: true, getContentOnError: true);

                if (result.Failed)
                {
                    context.Logger.LogError("GenerateVideoClips failed with error: " + result.GetErrorMessage());
                    result.AddErrors(result.Errors);
                    return result;
                }
                else if (result.Entity?.Errors != null)
                {
                    context.Logger.LogUserError("GenerateVideoClips failed with AIGC error: " + result.Entity.Errors.FirstOrDefault().message);
                    return result;
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("GenerateVideoClips failed with exception:" + ex);
            }

            context.Logger.LogInfo($"GenerateVideoClips: {JsonConvert.SerializeObject(result.Entity)}");
            return result;
        }

        public async Task<ResultOneEntity<VideoRecommendationResponse>> EditVideoAssetRecommendation(AccountCallContext context, VideoEditRecommendationRequest request, BrandKit brandKit, bool useClipchamp)
        {
            ResultOneEntity<VideoRecommendationResponse> result = new ResultOneEntity<VideoRecommendationResponse>
            {
                Entity = null,
            };

            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var destUrl = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{EditVideoAssetRecommendationUrl}";
                if (useClipchamp)
                {
                    AppendParameter(ref destUrl, nameof(useClipchamp), useClipchamp.ToString().ToLower());
                }

                var payload = new
                {
                    Mock = request.Mock,
                    ads = request.ads,
                    Brandkit = brandKit != null ? GetConvertedMTBrandKit(brandKit) : null,
                    Styles = request.Styles
                };

                result = await this.PostAsync<VideoRecommendationResponse>(context, null, destUrl, token, payload, useCCMTToken: true, getContentOnError: true);

                if (result.Failed)
                {
                    context.Logger.LogError("EditVideoAssetRecommendation failed with error: " + result.GetErrorMessage());
                    result.AddErrors(result.Errors);
                    return result;
                }
                else if (result.Entity?.errors != null)
                {
                    context.Logger.LogUserError("EditVideoAssetRecommendation failed with user error: " + result.Entity.errors.FirstOrDefault().message);
                    return result;
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("EditVideoAssetRecommendation failed with exception:" + ex);
            }

            context.Logger.LogInfo($"EditVideoAssetRecommendation: {JsonConvert.SerializeObject(result.Entity)}");
            return result;
        }

        public List<Ad> GetRandomItemsBySource(List<Ad> ads, int count)
        {
            List<Ad> result = new List<Ad>();
            List<Queue<Ad>> adsQueues = ads.GroupBy(ad => ad.OfflineAdsSource).Select(a => new Queue<Ad>(a.ToList())).ToList();

            while (result.Count < count && adsQueues.Count > 0)
            {
                var indices = Enumerable.Range(0, adsQueues.Count).ToList();
                indices.Shuffle();

                foreach (int index in indices)
                {
                    var ad = adsQueues[index].Dequeue();
                    result.Add(ad);
                }

                for (int index = adsQueues.Count - 1; index >= 0; index--)
                {
                    // remove empty queue
                    if (adsQueues[index].Count == 0)
                    {
                        adsQueues.RemoveAt(index);
                    }
                }
            }

            if (result.Count > count)
            {
                result = result.Take(count).ToList();
            }

            return result;
        }

        public async Task<Result<ResponsiveAd>> GetResponsiveAdRecommendationByFinalURL(
            AccountCallContext context,
            string finalURL,
            string locale)
        {
            var result = new Result<ResponsiveAd>();

            var socialMediaImages = await this.GetSocialMediaImage(context.AccountId, finalURL, context.Logger);

            // API docs: https://msasg.visualstudio.com/Bing_Ads/_wiki/wikis/Bing_Ads.wiki/107376/Recommend-Ads-with-images
            var sources = new string[] {
                "lp", // image form the final url landing page
                "search", // from bing search
                "account", // from Asset table. User uploaded, google import ..
                "template_ad", // 12000+ high quality boost images
            };
            var request = new
            {
                url = finalURL,
                account_id = context.AccountId.ToString(),
                extra_image_list = socialMediaImages.Select(image => new
                {
                    image_url = image.Url,
                    width = image.Width,
                    height = image.Height,
                }),
                sources = sources,
                language_code = locale
            };

            context.Logger.LogInfo($"The request body is: {JsonConvert.SerializeObject(request)}.");

            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
            var url = AdRecommendationUrl;
            var httpClient = this.autoImageHttpClient;

            if (DynamicConfigValues.UseAggregationServiceEndpointForAutoImage)
            {
                context.Logger.LogInfo("Call Aggregation service endpoint to create images ads");
                url = AggSvcImageAdUrl;
                httpClient = this.httpClient;
            }

            var aggResponse = await this.PostAsync<RecommededResponsiveAdResponse>(context, null, url, token, request, httpClient);
            if (aggResponse.Failed)
            {
                context.Logger.LogError("ResponsiveAdRecommendation failed with error: " + aggResponse.GetErrorMessage());
                result.AddErrors(aggResponse.Errors);
                return result;
            }

            // editorial check
            var responsiveSearchAdAdds = new List<ResponsiveSearchAdAdd>();
            foreach (var ad in aggResponse.Entity.Ads)
            {
                foreach (string headline in ad.short_headlines)
                {
                    var responsiveSearchAdAdd = new ResponsiveSearchAdAdd
                    {
                        Headlines = new AssetLink[] {
                            new AssetLink()
                            {
                                AssociationType = AssetAssociationType.Headlines,
                                Asset = new TextAsset() { Text = headline },
                            }
                        },
                        DisplayUrl = finalURL,
                        AdGroupId = 0,
                        PerformEditorialChecks = true,
                        EffectivePublisherCountries = new List<PublisherCountryInfo>(),
                    };
                    responsiveSearchAdAdds.Add(responsiveSearchAdAdd);
                }

                foreach (string description in ad.long_headlines)
                {
                    var responsiveSearchAdAdd = new ResponsiveSearchAdAdd
                    {
                        Descriptions = new AssetLink[] {
                            new AssetLink()
                            {
                                AssociationType = AssetAssociationType.Descriptions,
                                Asset = new TextAsset() { Text = description },
                            }
                        },
                        DisplayUrl = finalURL,
                        AdGroupId = 0,
                        PerformEditorialChecks = true,
                        EffectivePublisherCountries = new List<PublisherCountryInfo>(),
                    };
                    responsiveSearchAdAdds.Add(responsiveSearchAdAdd);
                }
            }

            var rejectedText = new HashSet<string>();
            try
            {
                BatchResultWithEditorialErrors<long> batchResult = new BatchResultWithEditorialErrors<long>();
                var localeIds = EditorialUtilities.GenerateLCIDKey(context.Logger, new Language[] { Language.English });
                var adsProvider = new AdsProvider<ResponsiveSearchAdAdd>(responsiveSearchAdAdds, batchResult, new AuthenticatedUserInfo(context.UserId));
                EditorialValidation.RunEditorialChecks(context.Logger, adsProvider, localeIds, context.AdvertiserCustomerID, context.Request);

                for (var index = 0; index < responsiveSearchAdAdds.Count; index++)
                {
                    var responsiveSearchAdAdd = responsiveSearchAdAdds[index];

                    if (responsiveSearchAdAdd.EditorialStatus != null && (
                        responsiveSearchAdAdd.EditorialStatus == EditorialStatusForDatabase.Rejected ||
                        responsiveSearchAdAdd.EditorialStatus == EditorialStatusForDatabase.RejectedAlias
                    ))
                    {
                        string text = responsiveSearchAdAdd.Descriptions == null ? responsiveSearchAdAdd.Headlines[0].Asset.Text : responsiveSearchAdAdd.Descriptions[0].Asset.Text;
                        rejectedText.Add(text);

                        context.Logger.LogInfo($"Ad at index {index} is disapproved. The text is: {text}. The final url is: {finalURL}.");
                        if (batchResult.EditorialErrors.TryGetValue(index, out List<Entities.EditorialErrorDetail> details))
                        {
                            context.Logger.LogInfo($"EditorialErrorDetail -- {JsonConvert.SerializeObject(details)}");
                        }
                    }
                }

                context.Logger.LogInfo($"EditorialReject test list: {JsonConvert.SerializeObject(rejectedText)}");
                foreach (var ad in aggResponse.Entity.Ads)
                {
                    ad.short_headlines = ad.short_headlines.Where(i => !rejectedText.Contains(i)).ToArray();
                    ad.long_headlines = ad.long_headlines.Where(i => !rejectedText.Contains(i)).ToArray();
                    ad.ad_texts = ad.ad_texts.Where(i => !rejectedText.Contains(i)).ToArray();
                }

                aggResponse.Entity.Ads = aggResponse.Entity.Ads.Where(i =>
                    i.short_headlines.Length > 0 &&
                    i.long_headlines.Length > 0 &&
                    i.images.Count > 0).ToList();
                context.Logger.LogInfo($"After editorial review. The recommendation ads are: {JsonConvert.SerializeObject(aggResponse.Entity.Ads)}");
            }
            catch (Exception e)
            {
                context.Logger.LogInfo($"Failed to get the editorial review result {e.StackTrace}");
                context.Logger.LogException(e);
            }

            if (aggResponse.Entity != null && aggResponse.Entity.Ads != null)
            {
                result.Entities = aggResponse.Entity.Ads.Select(i => ConvertToMTResponsiveAd(i)).ToList();

                context.Logger.LogInfo($"Get ResponsiveAdRecommendation by FinalURL: {JsonConvert.SerializeObject(aggResponse.Entity.Ads)}");
            }
            else
            {
                context.Logger.LogInfo($"No ResponsiveAdRecommendation by FinalURL: {finalURL}");
            }

            return result;
        }

        private async Task<List<SocialMediaImage>> GetSocialMediaImage(long accountId, string finalURL, ILogShared logger)
        {
            var result = new List<SocialMediaImage>();

            try
            {
                SocialMediaAccountFetchResult socialAccountFetchResult = null;

                bool hasAccountCacheResult = SocialMediaAccountCacheHelper.TryGetSocialAccountsFromCache(Cache, logger, finalURL, out socialAccountFetchResult);

                if (!hasAccountCacheResult)
                {
                    return result;
                }

                foreach (var account in socialAccountFetchResult.SocialAccounts)
                {
                    var socialMediaService = account.Key;
                    var socialMediaAccount = account.Value;
                    var socialMeidaImageCacheResult = await this.socialMediaImageCacheHelper.TryGetSocialMediaImagesFromCacheAsync(logger, accountId, socialMediaService, socialMediaAccount);

                    if (socialMeidaImageCacheResult != null && socialMeidaImageCacheResult.SocialMediaImages != null)
                    {
                        result.AddRange(socialMeidaImageCacheResult.SocialMediaImages);
                    }
                }

                logger.LogInfo($"Get Social Meida images by FinalURL: {finalURL}, Count: {result.Count}");

                return result;
            }
            catch (Exception ex)
            {
                logger.LogError("Get Social Media images failed with exception: " + ex);
                return result;
            }

        }

        private ResponsiveAd ConvertToMTResponsiveAd(RecommededResponsiveAd ad)
        {
            var mtAd = new ResponsiveAd();
            mtAd.Headline = ad.short_headlines.First();
            mtAd.LongHeadline = ad.long_headlines.First();
            mtAd.Text = ad.ad_texts.First();
            mtAd.FinalUrls = new string[] { ad.url };
            mtAd.BusinessName = ad.business_name;

            mtAd.Headlines = ad.short_headlines.Select(i => new AssetLink()
            {
                Asset = new TextAsset()
                {
                    Text = i,
                },
            }).ToArray();

            mtAd.LongHeadlines = ad.long_headlines.Select(i => new AssetLink()
            {
                Asset = new TextAsset()
                {
                    Text = i,
                },
            }).ToArray();

            mtAd.Descriptions = ad.long_headlines.Select(i => new AssetLink()
            {
                Asset = new TextAsset()
                {
                    Text = i,
                },
            }).ToArray();

            mtAd.Images = ad.images.Select(i => new AssetLink()
            {
                Asset = new ImageAsset()
                {
                    CropSettings = new CropSettings()
                    {
                        SourceHeight = i.image_size.height,
                        SourceWidth = i.image_size.width,
                    },
                    Id = Convert.ToInt64(i.asset_id),
                    Url = i.image_url,
                },
            }).ToList();

            return mtAd;
        }

        public async Task<Result<RecommededImageByKeyword>> GetImageRecommendationByKeywords(
            AccountCallContext context,
            List<string> keywords,
            string source,
            ImageRecommendationByKeywordPage paging)
        {
            if (DynamicConfigValues.UseAggregationServiceEndpointForAutoImage)
            {
                context.Logger.LogInfo("Call Aggregation service to get image recommendation by keywords.");
                return await GetImageRecommendationByKeywords_AggSvc(context, keywords, source, paging);
            }
            else
            {
                context.Logger.LogInfo("Call AutoImage service to get image recommendation by keywords.");
                return await GetImageRecommendationByKeywords_AutoImage(context, keywords, source, paging);
            }
        }

        public async Task<Result<RecommededImageByKeyword>> GetImageRecommendationByKeywords_AutoImage(
            AccountCallContext context,
            List<string> keywords,
            string source,
            ImageRecommendationByKeywordPage paging)
        {
            var result = new Result<RecommededImageByKeyword>();

            var request = new
            {
                query = new
                {
                    properties = new
                    {
                        keywords = keywords
                    }
                },
                images = new
                {
                    source = source,
                },
                paging = paging
            };

            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);

            var aggResponse = await this.PostAsync<ImageRecommendationByKeywordsResponse>(context, null, ImageRecommendationReuestUrl, token, request, this.imageRecommendationHttpClient);
            if (aggResponse.Failed)
            {
                context.Logger.LogError("RecommendImageRequest failed with error: " + aggResponse.GetErrorMessage());
                result.AddErrors(aggResponse.Errors);
                return result;
            }

            if (aggResponse.Entity != null && aggResponse.Entity.Result != null)
            {
                result.Entities = aggResponse.Entity.Result.ToList();

                context.Logger.LogInfo($"Get image recommendation by keywords: {JsonConvert.SerializeObject(aggResponse.Entity.Result)}");
            }
            else
            {
                context.Logger.LogInfo($"No recommended images by input keywords: {string.Join(",", keywords)}");
            }

            return result;
        }

        public async Task<Result<RecommededImageByKeyword>> GetImageRecommendationByKeywords_AggSvc(
            AccountCallContext context,
            List<string> keywords,
            string source,
            ImageRecommendationByKeywordPage paging)
        {
            var result = new Result<RecommededImageByKeyword>();

            var createImageRecommendation = new AggImageRecommendation
            {
                RankingCriteria = new AggImageRankingCriteria()
                {
                    Criteria = new Dictionary<string, IList<string>>(),
                },
                Sources = new List<string>()
            };

            if (!string.IsNullOrEmpty(source))
            {
                createImageRecommendation.Sources.Add(source);
            }

            if (paging != null)
            {
                createImageRecommendation.Paging = new ImageRecommendationPageDefination()
                {
                    Page = paging.page,
                    PageSize = paging.page_size,
                };
            }

            if (keywords != null && keywords.Count > 0)
            {
                createImageRecommendation.RankingCriteria.Criteria.Add("keywords", keywords);
            }

            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);

            var aggResponse = await this.PostAsync<AggImageRecommendationResponse>(context, null, CreateImagesRequestUrl, token, createImageRecommendation, this.imageRecommendationHttpClient_AggSrv);
            if (aggResponse.Failed)
            {
                context.Logger.LogError("RecommendImageRequest failed with error: " + aggResponse.GetErrorMessage());
                result.AddErrors(aggResponse.Errors);
                return result;
            }

            if (aggResponse.Entity != null && aggResponse.Entity.Images != null)
            {
                context.Logger.LogInfo($"Get image recommendation by keywords: {JsonConvert.SerializeObject(aggResponse.Entity.Images)}");

                var images = aggResponse.Entity.Images.ToList();
                result.Entities = new List<RecommededImageByKeyword>();

                foreach (var image in images)
                {
                    result.Entities.Add(new RecommededImageByKeyword()
                    {
                        image_url = image.ImageUrl,
                        thumbnail_url = image.ThumbnailUrl,
                        image_source = image.Source,
                        algo = image.Algo,
                        score = image.Score,
                    });
                }
                context.Logger.LogInfo($"Translated image recommendations: {JsonConvert.SerializeObject(result.Entities)}");

            }
            else
            {
                context.Logger.LogInfo($"No recommended images by input keywords: {string.Join(",", keywords)}");
            }

            return result;
        }

        public static RnR.CountryCode PublisherCountryToRnRCountryCode(PublisherCountry publisherCountry)
        {
            var fi = publisherCountry.GetType().GetField(publisherCountry.ToString());
            var attr = fi.GetCustomAttribute(typeof(StringAttribute), false) as StringAttribute;
            var value = attr?.Values?.FirstOrDefault();
            if (!string.IsNullOrEmpty(value) && Enum.TryParse(
                    value,
                    out RnR.CountryCode code))
            {
                return code;
            }
            else
            {
                return 0;
            }
        }

        public async Task<ResultOneEntity<GetOfflineGeneratedAssetsResponse.PMAXAssetsSuggestion>> GetOfflineGeneratedAssets(AccountCallContext context)
        {
            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);

            var url = $"v1/{OfflineGeneratedAssetsUrl}/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})";

            var response = await this.GetAsync<AIServiceResponse<GetOfflineGeneratedAssetsResponse.PMAXAssetsSuggestion>>(context, null, url, token, this.httpClient);

            var result = new ResultOneEntity<GetOfflineGeneratedAssetsResponse.PMAXAssetsSuggestion>();
            if (response.Failed)
            {
                response.CopyToResult(result);
            }
            else
            {
                if (response.Entity?.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    context.Logger.LogError($"Error {response.Entity?.StatusCode} from GetOfflineGeneratedAssets, message {response?.Entity.Message}");
                    result.AddError(CampaignManagementErrorCode.InternalError);
                }
                else
                {
                    result.Entity = response.Entity.Data;
                }
            }

            return result;
        }

        private async Task<ResponsiveSearchAd> GetAdInsightResponsiveSearchAdsRanking(
            AccountCallContext context,
            long? campaignId,
            Business business,
            AdType adType,
            IList<string> categories,
            IList<string> countryCodes,
            string location,
            int headlineCount,
            int descriptionCount,
            List<ResponsiveSearchAdAdd> adAdds)
        {
            var result = new ResponsiveSearchAd
            {
                DisplayUrl = business.Website,
                AdGroupId = 0,
                OfflineAdsSource = Guid.NewGuid().ToString(),
            };

            if (campaignId.HasValue)
            {
                result.CampaignId = campaignId.Value;
            }

            if (adAdds.IsNullOrEmpty())
            {
                return result;
            }

            var headline = adAdds.FindAll(ad => !ad.Headlines.IsNullOrEmpty()).Select(ad => new AdAssetsEntityForRequest
            {
                AdAssetValue = ad.Headlines.Select(asset => asset.Asset.Text).ToList(),
                TracingId = ad.OfflineAdsSource,
            }).ToList();
            var description = adAdds.FindAll(ad => !ad.Descriptions.IsNullOrEmpty()).Select(ad => new AdAssetsEntityForRequest
            {
                AdAssetValue = ad.Descriptions.Select(asset => asset.Asset.Text).ToList(),
                TracingId = ad.OfflineAdsSource,
            }).ToList();

            int returnHeadlineCount = headlineCount == 0 ? ResponsiveSearchAdHeadlineCount : headlineCount;
            int returnDescriptionCount = descriptionCount == 0 ? ResponsiveSearchAdDescriptionCount : descriptionCount;
            var parameter = new GetRSAV2RankerAssetsSuggestionsRequest
            {
                AdUrl = business.Website,
                BusinessName = business.Name,
                Categories = (categories?.ToList()).OrEmpty(),
                CountryCodes = (countryCodes?.ToList()).OrEmpty(),
                Location = string.IsNullOrEmpty(location) ? "" : location,
                AdType = adType.ToString(),
                AdAssets = new AdAssetsForRequest
                {
                    Headline = headline,
                    Description = description,
                },
                RequestedAssetAmount = new AssetAmount
                {
                    Headline = returnHeadlineCount,
                    Description = returnDescriptionCount,
                }
            };

            var adInsightODataClient = new AdInsightODataClient(
                context.Logger,
                ServiceLocator.Current.Resolve<IHttpClientHandlerFactory>(),
                ServiceLocator.Current.Resolve<IClientCenterAsyncFacade>(),
                DynamicConfigValues.AdInsightODataEndpointUrl,
                "CampaignMTService",
                DynamicConfigValues.UseCCMTSmallTokenToCallAdInsightODataThreshold);

            var inputHeadlines = adAdds.Where(ad => !ad.Headlines.IsNullOrEmpty()).SelectMany(ad => ad.Headlines, (ad, hl) => ToResultAssetLinks(ad, hl, AssetAssociationType.Headlines)).ToList();
            var inputDescriptions = adAdds.Where(ad => !ad.Descriptions.IsNullOrEmpty()).SelectMany(ad => ad.Descriptions, (ad, dc) => ToResultAssetLinks(ad, dc, AssetAssociationType.Descriptions)).ToList();
            try
            {
                var token = await this.GetAccessTokenAsync(this.adsRankingClientCredential, context.Logger);
                var response = await adInsightODataClient.GetAssetRanking(parameter, token);

                var headlineScoreLookup = response.AdAssets.Headline.ToLookup(v => (v.AdAssetValue, v.TracingId), v => v.Score).ToDictionary(x => x.Key, x => x.Max());
                var resultHeadlines = inputHeadlines.OrderByDescending(x =>
                {
                    headlineScoreLookup.TryGetValue((x.Asset.Text, x.Asset.TracingId), out var score);
                    return score;
                });

                var descriptionScoreLookup = response.AdAssets.Description.ToLookup(v => (v.AdAssetValue, v.TracingId), v => v.Score).ToDictionary(x => x.Key, x => x.Max());
                var resultDescriptions = inputDescriptions.OrderByDescending(x =>
                {
                    headlineScoreLookup.TryGetValue((x.Asset.Text, x.Asset.TracingId), out var score);
                    return score;
                });

                result.Headlines = resultHeadlines.Take(returnHeadlineCount).ToArray();
                result.Descriptions = resultDescriptions.Take(returnDescriptionCount).ToArray();
                return result;
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("CreateAdAssetsRequest Ranking failed with exception:" + ex);
                //Pick random asset to return.
                inputHeadlines.Shuffle();
                result.Headlines = inputHeadlines.Take(returnHeadlineCount).ToArray();

                inputDescriptions.Shuffle();
                result.Descriptions = inputDescriptions.Take(returnDescriptionCount).ToArray();

                return result;
            }
        }

        private AssetLink ToResultAssetLinks(ResponsiveSearchAdAdd inputAd, AssetLink inputAssetLink, AssetAssociationType assetAssociationType)
        {
            return new AssetLink
            {
                AssociationType = assetAssociationType,
                Asset = new TextAsset { Text = inputAssetLink.Asset.Text, TracingId = inputAd.OfflineAdsSource },
                RecommendationId = inputAssetLink.RecommendationId
            };
        }

        private static CreateKeywordRecommendationResponse ConvertToCreateKeywordRecommendationResponse(AccountCallContext context, RnR.GetKeywordRecommendationsResponse response, List<SmartListing> originalSmartListings)
        {
            if (response == null)
            {
                return new CreateKeywordRecommendationResponse
                {
                    SmartListings = new List<SmartListing>(),
                    IsLimitedAudienceReach = true
                };
            }

            if (response?.Header?.ErrorCode != null)
            {
                context.Logger.LogWarning("GetProductServiceRecommendationsAsync CallOsCoproc error:" + response.Header.ErrorCode.ToString());

                return new CreateKeywordRecommendationResponse
                {
                    SmartListings = new List<SmartListing>(),
                    IsLimitedAudienceReach = true
                };
            }

            var smartListings = new List<SmartListing>();
            var keywordTextToSmartListingMapping = originalSmartListings.ToDictionary(x => x.Text, x => x);

            if (response.ProductsAndServices != null)
            {
                foreach (var productService in response.ProductsAndServices)
                {
                    var isOriginalSmartListing = keywordTextToSmartListingMapping.TryGetValue(productService.Keyword, out var originalSmartListing);

                    smartListings.Add(new SmartListing
                    {
                        Text = productService.Keyword,
                        IsSuggested = productService.Attributes.IsInputByUser != true,
                        IsSelected = productService.Attributes.IsSelected,
                        Source = isOriginalSmartListing ? originalSmartListing.Source : null,
                        RecommendationId = isOriginalSmartListing ? originalSmartListing.RecommendationId : null,
                        RankingScore = productService.Attributes.Score
                    });
                }
            }

            return new CreateKeywordRecommendationResponse
            {
                SmartListings = smartListings,
                EstimatedAudienceReach = (long?)response.EstimatedAudienceReach,
                IsLimitedAudienceReach = response.IsLimitedAudienceReach == true
            };
        }

        private NameValueCollection GenerateQueryParameters(Dictionary<string, string> customKeyValue)
        {
            var outgoingQueryString = HttpUtility.ParseQueryString(String.Empty);
            foreach (var keyValuePair in customKeyValue)
            {
                outgoingQueryString.Add(keyValuePair.Key, keyValuePair.Value);
            }

            return outgoingQueryString;
        }

        private async Task<ResultOneEntity<AIGCResponse<TResponse>>> CreateAIGCTask<TResponse>(
            AccountCallContext context,
            long campaignId,
            Object request,
            string requestUrl,
            string function,
            bool useV2 = false,
            bool enableLogo = false,
            bool enableCTA = false)
        {
            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);

            var url = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{requestUrl}";

            if (enableLogo)
            {
                AppendParameter(ref url, nameof(enableLogo), "true");
            }
            if (enableCTA)
            {
                AppendParameter(ref url, nameof(enableCTA), "true");
            }

            ApplyBestPracticeEnablementOnUrl(ref url, context);
            ApplyDKIEnablementOnUrl(ref url, context);

            ResultOneEntity<AIGCResponse<TResponse>> createTaskResponse = null;

            try
            {
                createTaskResponse = await this.PostAsync<AIGCResponse<TResponse>>(
                    context,
                    campaignId,
                    url,
                    token,
                    request,
                    useCCMTToken: true,
                    getContentOnError: true,
                    useV2: useV2);

                if (createTaskResponse.Failed)
                {
                    context.Logger.LogError(function + " failed with error: " + createTaskResponse.GetErrorMessage());
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning(function + " failed with exception:" + ex);
                createTaskResponse = new ResultOneEntity<AIGCResponse<TResponse>>
                {
                    Errors = new List<CampaignManagementErrorDetail>
                    {
                        new CampaignManagementErrorDetail(CampaignManagementErrorCode.AIGCException, ex.Message)
                    }
                };
            }

            return createTaskResponse;
        }

        private void AppendParameter(ref string url, string key, string value = "true")
        {
            if (url.Contains("?"))
            {
                url += $"&{key}={value}";
            }
            else
            {
                url += $"?{key}={value}";
            }
        }

        private async Task<ResultOneEntity<AIGCResponse<TResponse>>> GetAIGCTaskResult<TResponse>(
            AccountCallContext context,
            long campaignId,
            string taskId,
            string requestUrl,
            string function,
            string puid = null)
        {
            var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);

            var url = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{requestUrl}/{taskId}";

            if (!string.IsNullOrEmpty(puid))
            {
                url = url + $"?puid={puid}";
            }

            ResultOneEntity<AIGCResponse<TResponse>> getResultResponse = null;

            try
            {
                getResultResponse = await this.GetAsync<AIGCResponse<TResponse>>(context, null, url, token, this.httpClient);

                if (getResultResponse.Failed)
                {
                    context.Logger.LogError(function + " failed with error: " + getResultResponse.GetErrorMessage());
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning(function + " failed with exception:" + ex);
                getResultResponse = new ResultOneEntity<AIGCResponse<TResponse>>
                {
                    Errors = new List<CampaignManagementErrorDetail>
                    {
                        new CampaignManagementErrorDetail(CampaignManagementErrorCode.AIGCException, ex.Message)
                    }
                };
            }

            return getResultResponse;
        }

        public async Task<ResultOneEntity<AssetSearchResponse>> DeleteAssetsFromAIGCSearchIndex(
            AccountCallContext context, List<string> assetIds)
        {
            ResultOneEntity<AssetSearchResponse> result = new ResultOneEntity<AssetSearchResponse>(new AssetSearchResponse());
            var content = new AssetSearchImageIds
            {
                ImageIds = assetIds
            };

            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var url = this.GenerateUrl(context, AssetsSearchDeleteImagesUrl);
                result = await PostAsync<AssetSearchResponse>(context, null, url, token, content, this.imageRecommendationHttpClient_AggSrv, useCCMTToken: true, getContentOnError: true);
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning($"DeleteAssetsFromAIGCSearchIndex failed with exception: {ex}");
                throw;
            }

            if (result.Failed || !result.Errors.IsNullOrEmpty())
            {
                context.Logger.LogError(
                    "Failed to delete assets from AIGC search index with payload {0}. Error: {1}",
                    JsonConvert.SerializeObject(content), string.Join(",", result.Errors ?? []));
            }

            return result;
        }

        public void ApplyMistralEnablementOnUrl(ref string url, AccountCallContext context)
        {
            var customerCallContext = CustomerCallContext.Create(context);
            bool enableMistral = this.pilotFlagChecker.IsFeatureIdEnabled(customerCallContext, (int)CustomerFeatureFlag.EnableMistralTextGeneration);
            if (enableMistral)
            {
                AppendParameter(ref url, nameof(enableMistral), "true");
            }

            context.Logger.LogInfo($"Mistral is {enableMistral}. Will call API with URL {url}");
        }

        public void ApplyBestPracticeEnablementOnUrl(ref string url, AccountCallContext context)
        {
            var customerCallContext = CustomerCallContext.Create(context);
            bool enableBestPractice = DynamicConfigValues.EnableBestPracticeThemeGeneration &&
                this.pilotFlagChecker.IsFeatureIdEnabled(customerCallContext, (int)CustomerFeatureFlag.EnableBestPracticeThemeGeneration);
            if (enableBestPractice)
            {
                AppendParameter(ref url, nameof(enableBestPractice), "true");
            }

            context.Logger.LogInfo($"BestPractice is {enableBestPractice}. Will call API with URL {url}");
        }

        public async Task<ResultOneEntity<DisplayAdsTemplateDefinitionResponse>> GetDisplayAdsTemplateDefinition(
            AccountCallContext context, DisplayAdsTemplateDefinitionRequest request)
        {
            ResultOneEntity<DisplayAdsTemplateDefinitionResponse> result = new ResultOneEntity<DisplayAdsTemplateDefinitionResponse>();

            var payload = new
            {
                TemplateId = request.templateId,
            };

            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var destUrl = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{GetDisplayAdsTemplateDefinitionUrl}";
                result = await this.PostAsync<DisplayAdsTemplateDefinitionResponse>(context, null, destUrl, token, payload, useCCMTToken: true, getContentOnError: true);

                if (result.Failed || !result.Errors.IsNullOrEmpty())
                {
                    context.Logger.LogError("GetDisplayAdsTemplateDefinition failed with error: " + result.GetErrorMessage());
                    result.AddErrors(result.Errors);
                    return result;
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("GetDisplayAdsTemplateDefinition failed with exception:" + ex);
            }

            context.Logger.LogInfo($"GetDisplayAdsTemplateDefinition: {JsonConvert.SerializeObject(result.Entity)}");
            return result;
        }

        public async Task<ResultOneEntity<GetDisplayAdsTemplateGroupsResponse>> GetDisplayAdsTemplateGroups(
            AccountCallContext context, GetDisplayAdsTemplateGroupsRequest request, int? lcid)
        {
            ResultOneEntity<GetDisplayAdsTemplateGroupsResponse> result = new ResultOneEntity<GetDisplayAdsTemplateGroupsResponse>();

            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var payload = new GetDisplayAdsTemplateGroupsRequest
                {
                    version = request.version,
                    locale = lcid == 0 ? SupportedLCID.EN_US.ToString() : ((SupportedLCID)lcid).ToString(),
                };
                var destUrl = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{GetDisplayAdsTemplateGroupsUrl}";
                result = await this.PostAsync<GetDisplayAdsTemplateGroupsResponse>(context, null, destUrl, token, payload, useCCMTToken: true, getContentOnError: true);
                if (result.Failed || !result.Errors.IsNullOrEmpty())
                {
                    context.Logger.LogError("GetDisplayAdsTemplateGroups failed with error: " + result.GetErrorMessage());
                    result.AddErrors(result.Errors);
                    return result;
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("GetDisplayAdsTemplateGroups failed with exception:" + ex);
            }
            return result;
        }

        public async Task<ResultOneEntity<GetDisplayAdsTemplateGroupDetailResponse>> GetDisplayAdsTemplateGroupDetail(

            AccountCallContext context, GetDisplayAdsTemplateGroupDetailRequest request, int? lcid)
        {
            ResultOneEntity<GetDisplayAdsTemplateGroupDetailResponse> result = new ResultOneEntity<GetDisplayAdsTemplateGroupDetailResponse>();
            
            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var destUrl = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{GetDisplayAdsTemplateGroupDetailUrl}";
                var payload = new GetDisplayAdsTemplateGroupDetailRequest
                {
                    templateGroupId = request.templateGroupId,
                    version =request.version,
                    locale = lcid == 0 ? SupportedLCID.EN_US.ToString() : ((SupportedLCID)lcid).ToString()
                };
                result = await this.PostAsync<GetDisplayAdsTemplateGroupDetailResponse>(context, null, destUrl, token, payload, useCCMTToken: true, getContentOnError: true);
                if (result.Failed || !result.Errors.IsNullOrEmpty())
                {
                    context.Logger.LogError("GetDisplayAdsTemplateGroupDetail failed with error: " + result.GetErrorMessage());
                    result.AddErrors(result.Errors);
                    return result;
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("GetDisplayAdsTemplateGroupDetail failed with exception:" + ex);
            }
            return result;
        }

        public async Task<ResultOneEntity<AIGCResponse<GetSocialSitesRecommendationResponse>>> GetSocialSitesRecommendation(
            AccountCallContext context,
            long campaignId,
            bool mock,
            GetSocialSitesRecommendationRequest request)
        {
            if (mock)
            {
                return new ResultOneEntity<AIGCResponse<GetSocialSitesRecommendationResponse>>(new AIGCResponse<GetSocialSitesRecommendationResponse>
                {
                    Data = new GetSocialSitesRecommendationResponse
                    {
                        SocialSites = new[]
                        {
                            new SocialSitesRecommendation
                            {
                                Url = "https://www.facebook.com/TheChristmasGuys",
                                Source = "Facebook",
                                RecommendationId = "d8d83b3f-eacb-4dfd-9d4f-e23152264a94" ,
                                ModelSource = "Search"
                            },
                            new SocialSitesRecommendation
                            {
                                Url = "https://www.instagram.com/TheChristmasGuys",
                                Source = "Instagram",
                                RecommendationId = "d8d83b3f-eacb-4dfd-9d4f-e23152264a94" ,
                                ModelSource = "Search"
                            },
                            new SocialSitesRecommendation
                            {
                                Url = "https://www.twitter.com/TheChristmasGuys",
                                Source = "Twitter",
                                RecommendationId = "d8d83b3f-eacb-4dfd-9d4f-e23152264a94" ,
                                ModelSource = "Search"
                            }
                        }
                    }
                });
            }

            return await CreateAIGCTask<GetSocialSitesRecommendationResponse>(
                context,
                campaignId,
                request,
                mock ? $"{GetSocialSitesRecommendationUrl}?mock=true" : GetSocialSitesRecommendationUrl,
                nameof(GetSocialSitesRecommendation),
                useV2: true);
        }

        public async Task<ResultOneEntity<ExtractedBusinessInfo>> GetExtractedBusinessInfoAsync(
            AccountCallContext context,
            long customerId,
            long accountId,
            string url,
            Language language,
            bool refreshCache = false)
        {
            const string cacheNameSpace = "AddressExtraction/AddressInfo";
            var cacheTTL = TimeSpan.FromHours(12);

            var logger = context.Logger;
            ResultOneEntity<ExtractedBusinessInfo> result = new ResultOneEntity<ExtractedBusinessInfo>();
            const CacheOptions cacheOptions = CacheOptions.IgnoreVersion;
            var cachedAddressInfo = Cache.GetItem<BusinessInfo>(cacheNameSpace, url, logger);

            BusinessInfo addressInfo = null;
            if (!refreshCache && cachedAddressInfo?.Value != null)
            {
                addressInfo = cachedAddressInfo.Value;
                logger.LogInfo(
                    $"GetExtractedBusinessInfoAsync Cache Hit. Url: {url}, Extracted Addresses: {JsonConvert.SerializeObject(addressInfo)}");
            }
            else
            {
                var addressExtractionServiceSecret = DynamicConfigValues.AddressExtractionServiceSecret;

                var payload = new AddressExtractionRequest
                {
                    url = url,
                    language = LanguageInfo.GetLanguageCode(language).ToLower(),
                    password = addressExtractionServiceSecret,
                    trace_id = context.Request.Tracking.TrackingId.ToString(),
                    refresh_cache = refreshCache,
                };

                using var mtPerfLogger = new PerformanceLogger(SafeSharedLog.CallTrackingData(logger), null,
                    "ApiOpMT", "CallAddressExtractionService", 0);

                var token = await this.GetAccessTokenAsync(this.clientCredential, logger);

                Func<ILogShared, HttpRequestMessage, HttpResponseMessage, string, ResultOneEntity<BusinessInfo>, Task> badRequestResponseHandler =
                    (ILogShared logger, HttpRequestMessage request, HttpResponseMessage response, string content, ResultOneEntity<BusinessInfo> result) =>
                    {
                        var errorResponseContent = JsonConvert.DeserializeObject<Dictionary<string, string>>(content);

                        if (string.Equals(errorResponseContent["message"], "url cannot be reached"))
                        {
                            result.AddError(new CampaignManagementErrorDetail(CampaignManagementErrorCode.UrlNotAccessible, "url cannot be reached", null, propertyName: "url"));
                        }

                        return Task.CompletedTask;
                    };

                var aggResponse = await this.PostAsync(context, null, AddressExtractionUrl, token, payload, httpClient, null, false, false, false, badRequestResponseHandler);
                if (aggResponse.Entity != null)
                {
                    addressInfo = aggResponse.Entity;

                    logger.LogInfo(
                        $"GetExtractedBusinessInfoAsync Cache Not Hit, Query Address Extraction Service. Url: {url}, Extracted Addresses: {JsonConvert.SerializeObject(addressInfo)}");

                    Cache.PutItem(cacheNameSpace,
                        url,
                        new CacheItem<BusinessInfo>(addressInfo, cacheTTL, cacheOptions),
                        logger);

                }

                result.AddErrors(aggResponse.Errors);
            }

            if (addressInfo != null)
            {
                result.Entity = new ExtractedBusinessInfo
                {
                    Url = url,
                    Addresses = addressInfo.AddressList.Select(a =>
                    {
                        var timezoneId = McaTimeZoneConverter.GetIdforIana(a.Timezone);
                        if (timezoneId == 0)
                        {
                            timezoneId = null;
                        }

                        return new ExtractedAddress
                        {
                            StreetAddress = a.AddressLine,
                            City = a.Locality,
                            Province = a.AdminDistrict,
                            Country = a.CountryRegion,
                            CountryCode = a.CountryRegionIso2,
                            PostalCode = a.PostalCode,
                            LocalizedAddress = a.FormattedAddress,
                            TimezoneId = timezoneId
                        };
                    }).ToList(),
                    PhoneNumber = addressInfo.PhoneNumber,
                    InternationalPhoneNumber = addressInfo.InternationalPhoneNumber,
                    BusinessName = addressInfo.BusinessName
                };
            }

            return result;
        }
        public async Task<ResultOneEntity<GeneratedLogoInfo>> GenerateLogoAsync(
            AccountCallContext context,
            string url)
        {
            var logger = context.Logger;
            ResultOneEntity<GeneratedLogoInfo> result = new ResultOneEntity<GeneratedLogoInfo>();
            var payload = new GenerateLogoRequest
            {
                DomainUrl = url,
            };

            using var mtPerfLogger = new PerformanceLogger(SafeSharedLog.CallTrackingData(logger), null,
                "ApiOpMT", "CallGenerateLogo", 0);

            var token = await this.GetAccessTokenAsync(this.clientCredential, logger);

            var aggResponse = await this.PostAsync<GenerateLogoResponse>(context, null, GenerateLogoUrl, token, payload, httpClient, null, false, false, false);
            if (aggResponse.Entity != null)
            {
                result.Entity = new GeneratedLogoInfo { LogoUrl = aggResponse.Entity.LogoUrl };
                logger.LogInfo(
                    $"{nameof(GenerateLogoAsync)} Generate Logo for DomainUrl: {url}, Response Entity: {JsonConvert.SerializeObject(result.Entity)}");
            }

            result.AddErrors(aggResponse.Errors);

            return result;
        }

        private List<string> GetSupportedDisplayAdsTemplateVersion(AccountCallContext context)
        {
            var customerCallContext = CustomerCallContext.Create(context);

            if (this.pilotFlagChecker.IsAccountEnabledForBrandKit(context.Logger, context.Request))
            {
                return new List<string>() { "1.2" };
            }

            if (this.pilotFlagChecker.IsFeatureIdEnabled(customerCallContext, (int)CustomerFeatureFlag.EnableDisplayAdsTemplateV1_1))
            {
                return new List<string>() { "1.1" };
            }

            return new List<string>() { "1.0" };
        }

        public void ApplyDKIEnablementOnUrl(ref string url, AccountCallContext context)
        {
            var customerCallContext = CustomerCallContext.Create(context);
            bool enableDKI = DynamicConfigValues.EnableDKI && this.pilotFlagChecker.IsFeatureIdEnabled(customerCallContext, (int)CustomerFeatureFlag.EnableDKI);
            if (enableDKI)
            {
                AppendParameter(ref url, nameof(enableDKI), "true");
            }

            context.Logger.LogInfo($"DKI is {enableDKI}. Will call API with URL {url}");
        }

        public async Task<ResultOneEntity<ClipchampTemplatesResponse>> GetClipchampTemplates(AccountCallContext context, ClipchampTemplatesRequest request)
        {
            ResultOneEntity<ClipchampTemplatesResponse> result = new ResultOneEntity<ClipchampTemplatesResponse>()
            {
                Entity = null,
            };

            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var destUrl = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{GetClipchampTemplatesUrl}";
                result = await this.PostAsync<ClipchampTemplatesResponse>(context, null, destUrl, token, request, this.httpClient, serializerSettings: this.generationServiceSerializerSetting, useCCMTToken: true, getContentOnError: true);

                if (result.Failed)
                {
                    context.Logger.LogError("ClipchampTemplatesResponse failed with error: " + result.GetErrorMessage());
                    result.AddErrors(result.Errors);
                    return result;
                }
                else if (result.Entity != null && result.Entity.errors?.Count > 0)
                {
                    context.Logger.LogUserError("ClipchampTemplatesResponse failed with user error: " + result.Entity.errors.FirstOrDefault().message);
                    return result;
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("ClipchampTemplatesResponse failed with exception:" + ex);
            }

            context.Logger.LogInfo($"ClipchampTemplatesResponse: {JsonConvert.SerializeObject(result.Entity)}");
            return result;
        }
        public async Task<ResultOneEntity<SupportedClipchampAudioResponse>> GetSupportedClipchampAudio(
                    AccountCallContext context, SupportedClipchampAudioRequest request)
        {
            ResultOneEntity<SupportedClipchampAudioResponse> result = new ResultOneEntity<SupportedClipchampAudioResponse>()
            {
                Entity = null
            };

            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var destUrl = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{GetSupportedClipchampAudioUrl}";
                result = await this.PostAsync<SupportedClipchampAudioResponse>(context, null, destUrl, token, request, useCCMTToken: true, getContentOnError: true);

                if (result.Failed)
                {
                    context.Logger.LogError("GetSupportedClipchampAudio failed with error: " + result.GetErrorMessage());
                    result.AddErrors(result.Errors);
                    return result;
                }
                else if (result.Entity?.Errors != null)
                {
                    context.Logger.LogError($"GetSupportedClipchampAudio failed with AIGC error: {0}", result.Entity.Errors.FirstOrDefault().Message);
                    return result;
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("ClipchampTemplatesResponse failed with exception:" + ex);
            }

            context.Logger.LogInfo($"GetSupportedClipchampAudio: {JsonConvert.SerializeObject(result.Entity)}");
            return result;
        }

        public async Task<ResultOneEntity<SupportedFontsResponse>> GetSupportedFonts(AccountCallContext context, string adType)
        {
            ResultOneEntity<SupportedFontsResponse> result = new ResultOneEntity<SupportedFontsResponse>()
            {
                Entity = null
            };
            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var destUrl = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{GetSupportedFontsUrl}";
                if (adType != null)
                {
                    AppendParameter(ref destUrl, "adType", adType);
                }

                result = await this.GetAsync<SupportedFontsResponse>(context, null, destUrl, token, this.httpClient);

                if (result.Failed)
                {
                    context.Logger.LogError("GetSupportedFontsResponse failed with error: " + result.GetErrorMessage());
                    result.AddErrors(result.Errors);
                    return result;
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("GetSupportedFonts failed with exception:" + ex);
            }

            context.Logger.LogInfo($"GetSupportedFontsResponse: {JsonConvert.SerializeObject(result.Entity)}");
            return result;
        }

        public async Task<ResultOneEntity<GenerateVideoClipsStatusResponse>> GenerateVideoClipsStatus(AccountCallContext context, GenerateVideoClipsStatusRequest request)
        {
            ResultOneEntity<GenerateVideoClipsStatusResponse> result = new ResultOneEntity<GenerateVideoClipsStatusResponse>()
            {
                Entity = null
            };

            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var destUrl = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{GenerateVideoClipsStatusUrl}";
                result = await this.PostAsync<GenerateVideoClipsStatusResponse>(context, null, destUrl, token, request, useCCMTToken: true, getContentOnError: true);
                if (result.Failed)
                {
                    context.Logger.LogError($"GenerateVideoClipsStatus failed with error: {result.GetErrorMessage()}");
                    result.AddErrors(result.Errors);
                    return result;
                }
                else if (result.Entity?.Errors != null)
                {
                    context.Logger.LogUserError($"GenerateVideoClipsStatus failed with user error: {result.Entity.Errors.FirstOrDefault().message}");
                    return result;
                }
            } catch (Exception ex)
            {
                context.Logger.LogWarning($"GenerateVideoClipsStatus failed with exception: {ex}");
            }
            return result;
        }

        public async Task<ResultOneEntity<BrandKit>> GetBrandKitRecommendationAsync(AccountCallContext context, string finalUrl)
        {
            ResultOneEntity<GetBrandKitRecommendationResponse> apiResult = new()
            {
                Entity = null,
            };

            ResultOneEntity<BrandKit> result = new();

            var payload = new
            {
                FinalURL = finalUrl,
            };

            try
            {
                var token = await this.GetAccessTokenAsync(this.clientCredential, context.Logger);
                var destUrl = $"v1/Customers({context.AdvertiserCustomerID})/Accounts({context.AccountId})/{GetBrandKitRecommendationUrl}";

                apiResult = await this.PostAsync<GetBrandKitRecommendationResponse>(context, null, destUrl, token, payload, useCCMTToken: true, getContentOnError: true);

                if (apiResult.Failed)
                {
                    context.Logger.LogError("GetBrandKitRecommendation failed with error: " + apiResult.GetErrorMessage());
                    result.AddErrors(apiResult.Errors);
                    return result;
                }
                else if (apiResult.Errors != null && apiResult.Errors.Count > 0)
                {
                    context.Logger.LogUserError("GetBrandKitRecommendation failed with user error: " + apiResult.Errors.FirstOrDefault()?.ErrorMessage);
                    result.AddErrors(apiResult.Errors);
                    return result;
                }

                if (apiResult.Entity?.data == null)
                {
                    if (apiResult.Entity?.errors != null && apiResult.Entity.errors.Any())
                    {
                        var errorMessage = "Cannot create BrandKit Recommendation from the provided FinalURL";
                        var aigcError = apiResult.Entity.errors.FirstOrDefault();

                        // Check for the specific HTML content error message from AIGC
                        if (aigcError?.code == System.Net.HttpStatusCode.BadRequest.ToString() && aigcError.message == "Cannot get HTMLContent from this FinalURL.")
                        {
                            context.Logger.LogUserError($"GetBrandKitRecommendation failed: {errorMessage}. URL is valid but content cannot be crawled.");
                            result.AddError(new CampaignManagementErrorDetail(CampaignManagementErrorCode.UncrawlableUrl, errorMessage));
                        }
                        // Account for all other badRequest's
                        else if (aigcError?.code == System.Net.HttpStatusCode.BadRequest.ToString())
                        {
                            context.Logger.LogError($"GetBrandKitRecommendation failed: {errorMessage}. Original error: {aigcError?.message}");
                            result.AddError(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InvalidParameters));
                        }
                        else
                        {
                            context.Logger.LogError($"GetBrandKitRecommendation failed with error: {aigcError?.code} - {aigcError?.message}");
                            result.AddError(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InternalError));
                        }
                    }
                    else
                    {
                        context.Logger.LogError("GetBrandKitRecommendation returned empty response without error details");
                        result.AddError(new CampaignManagementErrorDetail(CampaignManagementErrorCode.InternalError));
                    }
                    return result;
                }

                AIGCRecommendationBrandKit recommendedData = apiResult.Entity.data.recommendedBrandkit;

                if (recommendedData == null)
                {
                    context.Logger.LogError("GetBrandKitRecommendation returned null recommended brand kit");
                    result.AddError(CampaignManagementErrorCode.InternalError);
                    return result;
                }
                         
                BrandKit brandKit = new()
                {
                    BusinessName = recommendedData.businessName
                };

                // Convert images
                if (recommendedData.images != null && recommendedData.images.Count > 0)
                {
                    brandKit.Images = recommendedData.images.Select(img => new BrandKitImage
                    {
                        Url = img.url,
                    }).ToArray();
                }

                // Convert logos based on aspect ratio
                if (recommendedData.logos != null && recommendedData.logos.Count > 0)
                {
                    var squareLogos = new List<BrandKitImage>();
                    var landscapeLogos = new List<BrandKitImage>();

                    foreach (var logo in recommendedData.logos)
                    {
                        var brandKitImage = new BrandKitImage
                        {
                            Url = logo.url
                        };

                        if (logo.aspectRatio == "1:1")
                        {
                            brandKitImage.AssetAssociationType = AssetAssociationType.SquareLogoMedia;
                            squareLogos.Add(brandKitImage);
                        }
                        else if (logo.aspectRatio == "4:1")
                        {
                            brandKitImage.AssetAssociationType = AssetAssociationType.LandscapeLogoMedia;
                            landscapeLogos.Add(brandKitImage);
                        }
                    }

                    brandKit.SquareLogos = squareLogos.Count > 0 ? squareLogos.ToArray() : null;
                    brandKit.LandScapeLogos = landscapeLogos.Count > 0 ? landscapeLogos.ToArray() : null;
                }

                // Convert palettes
                if (recommendedData.palettes != null && recommendedData.palettes.Count > 0)
                {
                    brandKit.Palettes = recommendedData.palettes.Select(palette =>
                    {
                        return new BrandKitPalette
                        {
                            ColorType = palette.colorType,
                            Colors = palette.colors.Select(color => new BrandKitColor
                            {
                                HexCode = color
                            }).ToArray()
                        };
                    }).ToArray();
                }

                // Convert fonts
                if (recommendedData.fonts != null && recommendedData.fonts.Count > 0)
                {
                    brandKit.Fonts = recommendedData.fonts.Select(font =>
                    {
                        FontTextAssetType textAssetType;
                        if (Enum.TryParse(font.adComponent, out textAssetType))
                        {
                            return new BrandKitFont
                            {
                                Typeface = font.type,
                                Weight = font.weight,
                                TextAssetType = textAssetType.ToString(),
                            };
                        }
                        return null;
                    })
                    .Where(f => f != null)
                    .ToArray();
                }

                if (recommendedData.brandVoice != null)
                {
                    brandKit.BrandVoice = new BrandVoice
                    {
                        Tones = recommendedData.brandVoice.tones.ToArray(),
                        Personality = recommendedData.brandVoice.personality != null && recommendedData.brandVoice.personality.Count > 0 ? recommendedData.brandVoice.personality.First() : null

                    };
                }

                result.Entity = brandKit;
                context.Logger.LogInfo($"GetBrandKitRecommendation successful: {JsonConvert.SerializeObject(brandKit)}");
            }
            catch (Exception ex)
            {
                context.Logger.LogWarning("GetBrandKitRecommendation failed with exception:" + ex);
                result.AddError(CampaignManagementErrorCode.InternalError);
            }
            return result;
        }

    }
}
