﻿namespace AggSvcMockCore
{
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using Microsoft.AspNetCore.Mvc;
    using Newtonsoft.Json;

    public partial class AggSvcMockController
    {
        private static readonly Dictionary<string, RecommendationAd> DisplayAdsTemplateMap = new Dictionary<string, RecommendationAd>
        {
            { "********-0000-0000-0000-********0005",
                new RecommendationAd
                {
                    recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                    url = mockThaiAd1,
                    template = new RecommendationTemplate
                    {
                        style = 1,
                        templateId = "********-0000-0000-0000-********0005",
                        templateName = "970x250 - Learn More CTA",
                        version = "1.3",
                        image = new RecommendationImage
                        {
                            url = mockThaiImage1,
                            editable = true,
                            assetId = "23453",
                            imageSize = new RecommendationImageSize
                            {
                                width = 970,
                                height = 250
                            },
                            cropSetting = new List<RecommendationCropSetting>
                            {
                                new RecommendationCropSetting
                                {
                                    sourceX = 0,
                                    sourceY = 0,
                                    sourceWidth = 970,
                                    sourceHeight = 250,
                                    aspectRatio = "1:1"
                                }
                            }
                        },
                        logo = new RecommendationImage
                        {
                            url = new Uri(baseProdIntegrationTestUri, "Logo-216x54-Modern minimalist-1.3.jpg").ToString(),
                            editable = true,
                            assetId = "23453",
                            imageSize = new RecommendationImageSize
                            {
                                width = 216,
                                height = 54
                            },
                            cropSetting = new List<RecommendationCropSetting>
                            {
                                new RecommendationCropSetting
                                {
                                    sourceX = 0,
                                    sourceY = 0,
                                    sourceWidth = 216,
                                    sourceHeight = 54,
                                    aspectRatio = "4:1"
                                }
                            }
                        },
                        businessName = new RecommendationText
                        {
                            text = "TestBusinessName",
                            editable = true
                        },
                        shortHeadline = new RecommendationText
                        {
                            text = "Thai Food",
                            editable = true
                        },
                        longHeadline = new RecommendationText
                        {
                            text = "Premium Thai Food",
                            editable = true
                        },
                        description = new RecommendationText
                        {
                            text = "Quality ingredients make good food and they are delicious",
                            editable = true
                        },
                        cta = new RecommendationText
                        {
                            text = "Learn more",
                            editable = false
                        },
                        color = new List<RecommendationColor>
                        {
                            new RecommendationColor
                            {
                                elementName = "ShortHeadline",
                                suffix = "",
                                editable = true,
                                color = "#616161"
                            },
                            new RecommendationColor
                            {
                                elementName = "Description",
                                suffix = "",
                                editable = true,
                                color = "#1A1A1A"
                            },
                            new RecommendationColor
                            {
                                elementName = "Background",
                                suffix = "",
                                editable = true,
                                color = "#FFFFFF"
                            }
                        },
                        assetProperties = new List<RecommendationAssetProperty>
                        {
                            new RecommendationAssetProperty {
                                assetName = "ShortHeadline",
                                assetProperty = "FontFamily",
                                value = "Helvetica Neue"
                            },
                            new RecommendationAssetProperty {
                                assetName = "ShortHeadline",
                                assetProperty = "FontWeight",
                                value = "800"
                            },
                            new RecommendationAssetProperty {
                                assetName = "LongHeadline",
                                assetProperty = "FontFamily",
                                value = "Arial"
                            },
                            new RecommendationAssetProperty {
                                assetName = "LongHeadline",
                                assetProperty = "FontWeight",
                                value = "500"
                            },
                            new RecommendationAssetProperty {
                                assetName = "Description",
                                assetProperty = "FontFamily",
                                value = "Sodo Sans"
                            },
                            new RecommendationAssetProperty {
                                assetName = "Description",
                                assetProperty = "FontWeight",
                                value = "200"
                            },
                            new RecommendationAssetProperty {
                                assetName = "Background",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty {
                                assetName = "ShortHeadline",
                                assetProperty = "Color",
                                value = "#616161"
                            },
                            new RecommendationAssetProperty {
                                assetName = "CTAWithoutText",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty {
                                assetName = "CTAWithoutText",
                                assetProperty = "BackgroundColor",
                                value = "#000000"
                            }
                        }
                    }
                }
            },
            { "********-0000-0000-0000-********0007",
                new RecommendationAd
                {
                    recommendationId = "950bd852-b433-4e73-b046-00b481913847",
                    url = mockThaiAd1,
                    template = new RecommendationTemplate
                    {
                        style = 1,
                        templateId = "********-0000-0000-0000-********0007",
                        templateName = "320x50 - 1:1 asset",
                        version = "1.3",
                        image = new RecommendationImage
                        {
                            url = mockThaiImage1,
                            editable = true,
                            assetId = "23453",
                            imageSize = new RecommendationImageSize
                            {
                                width = 320,
                                height = 50
                            },
                            cropSetting = new List<RecommendationCropSetting>
                            {
                                new RecommendationCropSetting
                                {
                                    sourceX = 0,
                                    sourceY = 0,
                                    sourceWidth = 320,
                                    sourceHeight = 50,
                                    aspectRatio = "1:1"
                                }
                            }
                        },
                        logo = new RecommendationImage
                        {
                            url = "https://aggsvcstorage.z13.web.core.windows.net/$web/integration-test/Logo-Modern-minimalist-1.2.jpg", // not a valid URL
                            editable = true,
                            assetId = "23453",
                            imageSize = new RecommendationImageSize
                            {
                                width = 216,
                                height = 54
                            },
                            cropSetting = new List<RecommendationCropSetting>
                            {
                                new RecommendationCropSetting
                                {
                                    sourceX = 0,
                                    sourceY = 0,
                                    sourceWidth = 216,
                                    sourceHeight = 54,
                                    aspectRatio = "4:1"
                                }
                            }
                        },
                        businessName = new RecommendationText
                        {
                            text = "TestBusinessName",
                            editable = true
                        },
                        shortHeadline = new RecommendationText
                        {
                            text = "Thai Food",
                            editable = true
                        },
                        longHeadline = new RecommendationText
                        {
                            text = "Premium Thai Food",
                            editable = true
                        },
                        description = new RecommendationText
                        {
                            text = "Quality ingredients make good food and they are delicious",
                            editable = true
                        },
                        cta = new RecommendationText
                        {
                            text = "Learn more",
                            editable = false
                        },
                        color = new List<RecommendationColor>
                        {
                            new RecommendationColor
                            {
                                elementName = "ShortHeadline",
                                suffix = "",
                                editable = true,
                                color = "#616161"
                            },
                            new RecommendationColor
                            {
                                elementName = "Description",
                                suffix = "",
                                editable = true,
                                color = "#1A1A1A"
                            },
                            new RecommendationColor
                            {
                                elementName = "Background",
                                suffix = "",
                                editable = true,
                                color = "#FFFFFF"
                            }
                        },
                        assetProperties = new List<RecommendationAssetProperty>
                        {
                            new RecommendationAssetProperty {
                                assetName = "ShortHeadline",
                                assetProperty = "FontFamily",
                                value = "Helvetica Neue"
                            },
                            new RecommendationAssetProperty {
                                assetName = "ShortHeadline",
                                assetProperty = "FontWeight",
                                value = "800"
                            },
                            new RecommendationAssetProperty {
                                assetName = "LongHeadline",
                                assetProperty = "FontFamily",
                                value = "Arial"
                            },
                            new RecommendationAssetProperty {
                                assetName = "LongHeadline",
                                assetProperty = "FontWeight",
                                value = "500"
                            },
                            new RecommendationAssetProperty {
                                assetName = "Description",
                                assetProperty = "FontFamily",
                                value = "Sodo Sans"
                            },
                            new RecommendationAssetProperty {
                                assetName = "Description",
                                assetProperty = "FontWeight",
                                value = "200"
                            },
                            new RecommendationAssetProperty {
                                assetName = "Background",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty {
                                assetName = "ShortHeadline",
                                assetProperty = "Color",
                                value = "#616161"
                            },
                            new RecommendationAssetProperty {
                                assetName = "CTAWithoutText",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty {
                                assetName = "CTAWithoutText",
                                assetProperty = "BackgroundColor",
                                value = "#000000"
                            }
                        }
                    }
                }
            },
            { "********-0000-0000-0000-************",
                new RecommendationAd
                {
                    recommendationId = "6b837c30-bb7b-4847-a789-290c92310f59",
                    url = new Uri(baseDevIntegrationTestUri, "320x50 - Modern minimalist-1.3.jpeg").ToString(),
                    template = new RecommendationTemplate
                    {
                        style = 1,
                        templateId = "********-0000-0000-0000-************",
                        version = "1.3",
                        templateName = "320x50 - Modern minimalist",
                        image =
                        new RecommendationImage
                        {
                            url = modernMinimalist_954x500_13,
                            editable = true,
                            imageSize = new RecommendationImageSize
                            {
                                width = 268,
                                height = 140
                            },
                            cropSetting = new List<RecommendationCropSetting>
                            {
                                new RecommendationCropSetting
                                {
                                    sourceX = 0,
                                    sourceY = 0,
                                    sourceWidth = 268,
                                    sourceHeight = 140
                                }
                            }
                        },
                        shortHeadline = new RecommendationText
                        {
                            text = "Timeless Precision, Redefined",
                            editable = false,
                        },
                        longHeadline = new RecommendationText
                        {
                            text = "",
                            editable = false
                        },
                        description = new RecommendationText
                        {
                            text = "Experience precision craftsmanship that stands the test of time, redefined for today.",
                            editable = false
                        },
                        cta = new RecommendationText
                        {
                            text = "",
                            editable = false
                        },
                        businessName = new RecommendationText
                        {
                            text = "TestBusinessName",
                            editable = false
                        },
                        color = new List<RecommendationColor>
                        {
                            new RecommendationColor
                            {
                                elementName = "Background",
                                suffix = "",
                                editable = true,
                                color = "#FFFFFF"
                            }
                        },
                        assetProperties = new List<RecommendationAssetProperty>
                        {
                            new RecommendationAssetProperty
                            {
                            assetName = "Background",
                            assetProperty = "Color",
                            value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "CTAWithoutText",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "CTAWithoutText",
                                assetProperty = "BackgroundColor",
                                value = "#000000"
                            },
                        }
                    }
                }
            },
            { "********-0000-0000-0000-000010001001",
                new RecommendationAd
                {
                    recommendationId = "fd1a5ae6-6f13-4dc7-aef0-72c17487e280",
                    url = modernMinimalist_300x250_13,
                    template = new RecommendationTemplate
                    {
                        style = 1,
                        templateId = "********-0000-0000-0000-000010001001",
                        version = "1.3",
                        templateName = "320x50 - Modern minimalist",
                        image =
                        new RecommendationImage
                        {
                            url = modernMinimalist_954x500_13,
                            editable = true,
                            imageSize = new RecommendationImageSize
                            {
                                width = 268,
                                height = 140
                            },
                            cropSetting = new List<RecommendationCropSetting> {
                                new RecommendationCropSetting
                                {
                                    sourceX = 0,
                                    sourceY = 0,
                                    sourceWidth = 268,
                                    sourceHeight = 140
                                }
                            }
                        },
                        shortHeadline = new RecommendationText
                        {
                            text = "Timeless Precision, Redefined",
                            editable = false,
                        },
                        longHeadline = new RecommendationText
                        {
                            text = "",
                            editable = false
                        },
                        description = new RecommendationText
                        {
                            text = "Experience precision craftsmanship that stands the test of time, redefined for today.",
                            editable = false
                        },
                        cta = new RecommendationText
                        {
                            text = "",
                            editable = false
                        },
                        businessName = new RecommendationText
                        {
                            text = "TestBusinessName",
                            editable = false
                        },
                        color = new List<RecommendationColor>
                        {
                            new RecommendationColor
                            {
                                elementName = "ShortHeadline",
                                suffix = "",
                                editable = true,
                                color = "#616161"
                            },
                            new RecommendationColor
                            {
                                elementName = "Background",
                                suffix = "",
                                editable = true,
                                color = "#FFFFFF"
                            }
                        },
                        assetProperties = new List<RecommendationAssetProperty>
                        {
                            new RecommendationAssetProperty
                            {
                                assetName = "ShortHeadline",
                                assetProperty = "Color",
                                value = "#616161"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "ShortHeadline",
                                assetProperty = "FontFamily",
                                value = "Segoe UI Bold"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "ShortHeadline",
                                assetProperty = "FontWeight",
                                value = "700"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Background",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "CTAWithoutText",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "CTAWithoutText",
                                assetProperty = "BackgroundColor",
                                value = "#000000"
                            },
                        }
                    }

                }
            },
            { "********-0000-0000-0000-000010001002", new RecommendationAd
                {
                    recommendationId = "1761aeb1-a60c-42d0-9351-85b9fffbc617",
                    url = new Uri(baseDevTestEnUsUri, "728x90 - Modern minimalist-1.3.jpeg").ToString(),
                    template = new RecommendationTemplate
                    {
                        style = 1,
                        templateId = "********-0000-0000-0000-000010001002",
                        version = "1.3",
                        templateName = "728x90 - Modern minimalist",
                        image =
                        new RecommendationImage
                        {
                            url = modernMinimalist_954x500_13,
                            editable = true,
                            imageSize = new RecommendationImageSize
                            {
                                width = 268,
                                height = 140
                            },
                            cropSetting = new List<RecommendationCropSetting>
                            {
                                new RecommendationCropSetting
                                {
                                    sourceX = 0,
                                    sourceY = 0,
                                    sourceWidth = 268,
                                    sourceHeight = 140
                                }
                            }
                        },
                        shortHeadline = new RecommendationText
                        {
                            text = "Timeless Precision, Redefined",
                            editable = false,
                        },
                        longHeadline = new RecommendationText
                        {
                            text = "",
                            editable = false
                        },
                        description = new RecommendationText
                        {
                            text = "Experience precision craftsmanship that stands the test of time, redefined for today.",
                            editable = false
                        },
                        cta = new RecommendationText
                        {
                            text = "",
                            editable = false
                        },
                        businessName = new RecommendationText
                        {
                            text = "TestBusinessName",
                            editable = false
                        },
                        color = new List<RecommendationColor>
                        {
                            new RecommendationColor
                            {
                                elementName = "ShortHeadline",
                                suffix = "",
                                editable = true,
                                color = "#616161"
                            },
                            new RecommendationColor
                            {
                                elementName = "Description",
                                suffix = "",
                                editable = true,
                                color = "#1A1A1A"
                            },
                            new RecommendationColor
                            {
                                elementName = "Background",
                                suffix = "",
                                editable = true,
                                color = "#FFFFFF"
                            }
                        },
                        assetProperties = new List<RecommendationAssetProperty>
                        {
                            new RecommendationAssetProperty
                            {
                                assetName = "ShortHeadline",
                                assetProperty = "Color",
                                value = "#616161"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "ShortHeadline",
                                assetProperty = "FontFamily",
                                value = "Segoe UI Bold"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "ShortHeadline",
                                assetProperty = "FontWeight",
                                value = "700"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Description",
                                assetProperty = "Color",
                                value = "#1A1A1A"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Description",
                                assetProperty = "FontFamily",
                                value = "Segoe UI Regular"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Description",
                                assetProperty = "FontWeight",
                                value = "400"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Background",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "CTAWithoutText",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "CTAWithoutText",
                                assetProperty = "BackgroundColor",
                                value = "#000000"
                            },
                        }
                    }

                }
            },
            { "********-0000-0000-0000-000010001003",
                new RecommendationAd
                {
                    recommendationId = "099d99e1-0fff-48db-8493-03f55d3780b9",
                    url = new Uri(baseDevTestEnUsUri, "970x250 - Modern minimalist-1.3.jpeg").ToString(),
                    template = new RecommendationTemplate
                    {
                        style = 1,
                        templateId = "********-0000-0000-0000-000010001003",
                        version = "1.3",
                        templateName = "970x250 - Modern minimalist",
                        image =
                        new RecommendationImage
                        {
                            url = modernMinimalist_954x500_13,
                            editable = true,
                            imageSize = new RecommendationImageSize
                            {
                                width = 268,
                                height = 140
                            },
                            cropSetting = new List<RecommendationCropSetting>
                            {
                                new RecommendationCropSetting
                                {
                                    sourceX = 0,
                                    sourceY = 0,
                                    sourceWidth = 268,
                                    sourceHeight = 140
                                }
                            }
                        },
                        shortHeadline = new RecommendationText
                        {
                            text = "Timeless Precision, Redefined",
                            editable = false,
                        },
                        longHeadline = new RecommendationText
                        {
                            text = "",
                            editable = false
                        },
                        description = new RecommendationText
                        {
                            text = "Experience precision craftsmanship that stands the test of time, redefined for today.",
                            editable = false
                        },
                        cta = new RecommendationText
                        {
                            text = "",
                            editable = false
                        },
                        businessName = new RecommendationText
                        {
                            text = "TestBusinessName",
                            editable = false
                        },
                        color = new List<RecommendationColor>
                        {
                            new RecommendationColor
                            {
                                elementName = "ShortHeadline",
                                suffix = "",
                                editable = true,
                                color = "#616161"
                            },
                            new RecommendationColor
                            {
                                elementName = "Description",
                                suffix = "",
                                editable = true,
                                color = "#1A1A1A"
                            },
                            new RecommendationColor
                            {
                                elementName = "Background",
                                suffix = "",
                                editable = true,
                                color = "#FFFFFF"
                            }
                        },
                        assetProperties = new List<RecommendationAssetProperty>
                        {
                            new RecommendationAssetProperty
                            {
                                assetName = "ShortHeadline",
                                assetProperty = "Color",
                                value = "#616161"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "ShortHeadline",
                                assetProperty = "FontFamily",
                                value = "Segoe UI Bold"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "ShortHeadline",
                                assetProperty = "FontWeight",
                                value = "700"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Description",
                                assetProperty = "Color",
                                value = "#1A1A1A"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Description",
                                assetProperty = "FontFamily",
                                value = "Segoe UI Regular"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Description",
                                assetProperty = "FontWeight",
                                value = "400"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Background",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "CTAWithoutText",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "CTAWithoutText",
                                assetProperty = "BackgroundColor",
                                value = "#000000"
                            },
                        }
                    }

                }
            },
            { "********-0000-0000-0000-000010001004",
                new RecommendationAd
                {
                    recommendationId = "f946e87e-3621-4483-a93d-3caf6777a585",
                    url = new Uri(baseDevTestEnUsUri, "300x600 - Modern minimalist-1.3.jpeg").ToString(),
                    template = new RecommendationTemplate
                    {
                        style = 1,
                        templateId = "********-0000-0000-0000-000010001004",
                        version = "1.3",
                        templateName = "300x600 - Modern minimalist",
                        image =
                        new RecommendationImage
                        {
                            url = modernMinimalist_954x500_13,
                            editable = true,
                            imageSize = new RecommendationImageSize
                            {
                                width = 268,
                                height = 140
                            },
                            cropSetting = new List<RecommendationCropSetting>
                            {
                                new RecommendationCropSetting
                                {
                                    sourceX = 64,
                                    sourceY = 0,
                                    sourceWidth = 140,
                                    sourceHeight = 140
                                }
                            }
                        },
                        shortHeadline = new RecommendationText
                        {
                            text = "Timeless Precision, Redefined",
                            editable = false,
                        },
                        longHeadline = new RecommendationText
                        {
                            text = "",
                            editable = false
                        },
                        description = new RecommendationText
                        {
                            text = "Experience precision craftsmanship that stands the test of time, redefined for today.",
                            editable = false
                        },
                        cta = new RecommendationText
                        {
                            text = "",
                            editable = false
                        },
                        businessName = new RecommendationText
                        {
                            text = "TestBusinessName",
                            editable = false
                        },
                        color = new List<RecommendationColor>
                        {
                            new RecommendationColor
                            {
                                elementName = "ShortHeadline",
                                suffix = "",
                                editable = true,
                                color = "#616161"
                            },
                            new RecommendationColor
                            {
                                elementName = "Description",
                                suffix = "",
                                editable = true,
                                color = "#1A1A1A"
                            },
                            new RecommendationColor
                            {
                                elementName = "Background",
                                suffix = "",
                                editable = true,
                                color = "#FFFFFF"
                            }
                        },
                        assetProperties = new List<RecommendationAssetProperty>
                        {
                            new RecommendationAssetProperty
                            {
                                assetName = "ShortHeadline",
                                assetProperty = "Color",
                                value = "#616161"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "ShortHeadline",
                                assetProperty = "FontFamily",
                                value = "Segoe UI Bold"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "ShortHeadline",
                                assetProperty = "FontWeight",
                                value = "700"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Description",
                                assetProperty = "Color",
                                value = "#1A1A1A"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Description",
                                assetProperty = "FontFamily",
                                value = "Segoe UI Regular"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Description",
                                assetProperty = "FontWeight",
                                value = "400"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Background",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "CTAWithoutText",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "CTAWithoutText",
                                assetProperty = "BackgroundColor",
                                value = "#000000"
                            },
                        }
                    }

                }
            },
            { "********-0000-0000-0000-000010001005",
                new RecommendationAd
                {
                    recommendationId = "ab08f5bf-8534-4b56-a1b9-b2cc0afecac2",
                    url = new Uri(baseDevTestEnUsUri, "160x600 - Modern minimalist-1.3.jpeg").ToString(),
                    template = new RecommendationTemplate
                    {
                        style = 1,
                        templateId = "********-0000-0000-0000-000010001005",
                        version = "1.3",
                        templateName = "160x600 - Modern minimalist",
                        image =
                        new RecommendationImage
                        {
                            url = modernMinimalist_954x500_13,
                            editable = true,
                            imageSize = new RecommendationImageSize
                            {
                            width = 268,
                            height = 140
                            },
                            cropSetting = new List<RecommendationCropSetting>
                            {
                                new RecommendationCropSetting
                                {
                                    sourceX = 64,
                                    sourceY = 0,
                                    sourceWidth = 140,
                                    sourceHeight = 140
                                }
                            }
                        },
                        shortHeadline = new RecommendationText
                        {
                            text = "Timeless Precision, Redefined",
                            editable = false,
                        },
                        longHeadline = new RecommendationText
                        {
                            text = "",
                            editable = false
                        },
                        description = new RecommendationText
                        {
                            text = "Experience precision craftsmanship that stands the test of time, redefined for today.",
                            editable = false
                        },
                        cta = new RecommendationText
                        {
                            text = "",
                            editable = false
                        },
                        businessName = new RecommendationText
                        {
                            text = "TestBusinessName",
                            editable = false
                        },
                        color = new List<RecommendationColor>
                        {
                            new RecommendationColor
                            {
                                elementName = "ShortHeadline",
                                suffix = "",
                                editable = true,
                                color = "#616161"
                            },
                            new RecommendationColor
                            {
                                elementName = "Description",
                                suffix = "",
                                editable = true,
                                color = "#1A1A1A"
                            },
                            new RecommendationColor
                            {
                                elementName = "Background",
                                suffix = "",
                                editable = true,
                                color = "#FFFFFF"
                            }
                        },
                        assetProperties = new List<RecommendationAssetProperty>
                        {
                            new RecommendationAssetProperty
                            {
                                assetName = "ShortHeadline",
                                assetProperty = "Color",
                                value = "#616161"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "ShortHeadline",
                                assetProperty = "FontFamily",
                                value = "Segoe UI Bold"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "ShortHeadline",
                                assetProperty = "FontWeight",
                                value = "700"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Description",
                                assetProperty = "Color",
                                value = "#1A1A1A"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Description",
                                assetProperty = "FontFamily",
                                value = "Segoe UI Regular"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Description",
                                assetProperty = "FontWeight",
                                value = "400"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "Background",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "CTAWithoutText",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName = "CTAWithoutText",
                                assetProperty = "BackgroundColor",
                                value = "#000000"
                            },
                        }
                    }

                }
            }
        };

        [HttpPost]
        [Route("v1/Customers({customerId})/Accounts({accountId})/EditDisplayAdsRecommendation")]
        public IActionResult EditDisplayAdsRecommendation([FromBody] object request)
        {
            if ((request as dynamic)?.ads[0].template.templateId == "bad-template-id")
            {
                var response = new DisplayRecommendationResponse
                {
                    errors = new List<RecommendationError>
                    {
                        new RecommendationError
                        {
                            code = "BadRequest",
                            message = "Invalid Template Id bad-template-id",
                            target = "Ads.TempalteId"
                        }
                    }
                };

                return BadRequest(response);
            }
            else
            {
                var templateVersion = (request as dynamic)?.ads[0].template.version;
                if (templateVersion != null && Convert.ToDouble(templateVersion) >= 1.2)
                {
                    var adsDynamic = (request as dynamic)?.ads as IEnumerable<dynamic>;
                    var adsList = adsDynamic?.Cast<dynamic>().ToList() ?? new List<dynamic>();
                    var response = new DisplayRecommendationResponse
                    {
                        data = new RecommendationData
                        {
                            ads = adsList.Select(ad => DisplayAdsTemplateMap[(string)ad.template.templateId]).ToList()
                        }
                    };
                    var content = JsonConvert.SerializeObject(response);
                    return Ok(content);
                }
                else
                {
                    var response = new DisplayRecommendationResponse
                    {
                        data = new RecommendationData
                        {
                            ads = new List<RecommendationAd> {
                            new RecommendationAd
                            {
                                recommendationId = "fe0cf20e-d974-4aca-ab03-bb56088c8006",
                                url = mockThaiAd1,
                                template = new RecommendationTemplate {
                                    style = 1,
                                    templateId = "********-0000-0000-0000-********0005",
                                    templateName = "970x250 - Learn More CTA",
                                    image = new RecommendationImage {
                                        url = mockThaiImage1,
                                        editable = true
                                    },
                                    shortHeadline = new RecommendationText {
                                        text = "Thai Food",
                                        editable = true
                                    },
                                    longHeadline = new RecommendationText {
                                        text = "Premium Thai Food",
                                        editable = true
                                    },
                                    description = new RecommendationText {
                                        text = "Quality ingredients make good food and they are delicious",
                                        editable = true
                                    },
                                    cta = new RecommendationText {
                                        text = "Learn more",
                                        editable = false
                                    },
                                }
                            },
                            new RecommendationAd {
                                recommendationId = "b1a46c14-c8bd-4f08-ba38-5aa928a66123",
                                url = mockThaiAd1,
                                template = new RecommendationTemplate {
                                    style = 1,
                                    templateId = "********-0000-0000-0000-********0005",
                                    templateName = "970x250 - Learn More CTA",
                                    image = new RecommendationImage {
                                        url = mockThaiImage1,
                                        editable = true
                                    },
                                    shortHeadline = new RecommendationText {
                                        text = "Thai Food",
                                        editable = true
                                    },
                                    longHeadline = new RecommendationText {
                                        text = "Premium Thai Food",
                                        editable = true
                                    },
                                    description = new RecommendationText {
                                        text = "Quality ingredients make good food and they are delicious",
                                        editable = true
                                    },
                                    cta = new RecommendationText {
                                        text = "Learn more",
                                        editable = false
                                    },
                                }
                            },
                            new RecommendationAd {
                                recommendationId = "950bd852-b433-4e73-b046-00b481913847",
                                url = mockThaiAd1,
                                template = new RecommendationTemplate {
                                    style = 1,
                                    templateId = "********-0000-0000-0000-********0007",
                                    templateName = "320x50 - 1:1 asset",
                                    image = new RecommendationImage {
                                        url = mockThaiImage1,
                                        editable = true
                                    },
                                    shortHeadline = new RecommendationText {
                                        text = "Thai Food",
                                        editable = true
                                    },
                                    longHeadline = new RecommendationText {
                                        text = "Premium Thai Food",
                                        editable = true
                                    },
                                    description = new RecommendationText {
                                        text = "Quality ingredients make good food and they are delicious",
                                        editable = true
                                    },
                                    cta = new RecommendationText {
                                        text = "Learn more",
                                        editable = false
                                    },
                                }
                            }
                        }
                        }
                    };

                    var content = JsonConvert.SerializeObject(response);
                    return Ok(content);
                }
            }
        }

        [HttpPost]
        [Route("v1/Customers({customerId})/Accounts({accountId})/CreateDisplayAdsRecommendation")]
        public IActionResult CreateDisplayAdsRecommendation([FromBody] object request)
        {
            object response;
            dynamic requestBody = request as dynamic;
            var templateVersion = requestBody?.TemplateVersion[0];
            try
            {
                if ((templateVersion != null && Convert.ToDouble(templateVersion) >= 1.2)
                    || requestBody?.BrandKit != null)
                {
                    response = new
                    {
                        data = new
                        {
                            assets = new
                            {
                                shortHeadlines = new[] {
                                    new {
                                        text = "Thai",
                                        recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                        source = "LLM"
                                    }
                                },
                                longHeadlines = new[] {
                                    new {
                                        text = GetTextFromBrandVoice(request, "Premium Thai Food"),
                                        recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                        source = "LLM"
                                    },
                                    new {
                                        text = "Get your spice on",
                                        recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                        source = "LLM"
                                    }
                                },
                                businessName = requestBody?.BrandKit != null && requestBody?.BrandKit.businessName != null ?
                                new
                                {
                                    text = requestBody?.BrandKit.businessName,
                                    recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                    source = "LLM"
                                } : null,
                                descriptions = new[] {
                                    new {
                                        text = "Quality ingredients make good food and they are delicious",
                                        recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                        source = "LLM"
                                    },
                                    new {
                                        text = "Need more spice in life? Try our authentic Thai spices",
                                        recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                        source = "LLM"
                                    },
                                },
                                images = new[] {
                                    new {
                                        url = mockThaiImage1,
                                        recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                        source = "LLM",
                                        assetId = "23453",
                                        imageSize = new {
                                            width = 970,
                                            height = 250
                                        },
                                        cropSetting = new []  {
                                            new {
                                                sourceX = 0,
                                                sourceY = 0,
                                                sourceWidth = 970,
                                                sourceHeight = 250,
                                                aspectRatio = "1:1"
                                            }
                                        }
                                    },
                                    new {
                                        url = noodles_force2scrape_0,
                                        recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                        source = "LLM",
                                        assetId = "23454",
                                        imageSize = new {
                                            width = 1500,
                                            height = 1000
                                        },
                                        cropSetting = new []  {
                                            new {
                                                sourceX = 0,
                                                sourceY = 0,
                                                sourceWidth = 1500,
                                                sourceHeight = 1000,
                                                aspectRatio = "1:1"
                                            }
                                        }
                                    }
                                }
                            },
                            ads = new object[] {
                                new {
                                    recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                    url = mockThaiAd1,
                                    template = new {
                                        style = 1,
                                        templateId = "********-0000-0000-0000-********0005",
                                        templateName = "970x250 - Learn More CTA",
                                        version = "1.3",
                                        image = new {
                                            url = mockThaiImage1,
                                            editable = true,
                                            assetId = "23453",
                                            imageSize = new {
                                                width = 970,
                                                height = 250
                                            },
                                            cropSetting = new []  {
                                                new {
                                                    sourceX = 0,
                                                    sourceY = 0,
                                                    sourceWidth = 970,
                                                    sourceHeight = 250,
                                                    aspectRatio = "1:1"
                                                }
                                            }
                                        },
                                        logo = new {
                                            url = "https://aggsvcstorage.z13.web.core.windows.net/integration-test/Logo-Modern-minimalist-1.2.jpg", // not a valid URL
                                            editable = true,
                                            assetId = "23453",
                                            imageSize = new {
                                                width = 216,
                                                height = 54
                                            },
                                            cropSetting = new []  {
                                                new {
                                                    sourceX = 0,
                                                    sourceY = 0,
                                                    sourceWidth = 216,
                                                    sourceHeight = 54,
                                                    aspectRatio = "4:1"
                                                }
                                            }
                                        },
                                        businessName = requestBody?.BrandKit != null && requestBody?.BrandKit.businessName != null ?
                                        new {
                                            text = requestBody?.BrandKit.businessName,
                                            editable = true
                                        } : null,
                                        shotHeadline = new {
                                            text = "Thai Food",
                                            editable = true
                                        },
                                        longHeadline = new {
                                            text = "Premium Thai Food",
                                            editable = true
                                        },
                                        description = new {
                                            text = "Quality ingredients make good food and they are delicious",
                                            editable = true
                                        },
                                        cta = new {
                                            text = "Learn more",
                                            editable = false
                                        },
                                        assetProperties = new [] {
                                            new { assetName = "Logo", assetProperty = "Url", value = "https://www.bing.com//th?id=OADD2.9964469720606_1AB1WWQTTGALYBCY9P&pid=21.2" },
                                            new { assetName = "ShortHeadline", assetProperty = "FontFamily", value = "Helvetica Neue" },
                                            new { assetName = "ShortHeadline", assetProperty = "FontWeight", value = "800" },
                                            new { assetName = "LongHeadline", assetProperty = "FontFamily", value = "Arial" },
                                            new { assetName = "LongHeadline", assetProperty = "FontWeight", value = "500" },
                                            new { assetName = "Description", assetProperty = "FontFamily", value = "Sodo Sans" },
                                            new { assetName = "Description", assetProperty = "FontWeight", value = "200" },
                                            new { assetName = "Background", assetProperty = "Color", value = "#FFFFFF" },
                                            new { assetName = "ShortHeadline", assetProperty = "Color", value = "#000000" },
                                            new { assetName = "CTAWithoutText", assetProperty = "Color", value = "#FFFFFF" },
                                            new { assetName = "CTAWithoutText", assetProperty = "BackgroundColor", value = "#000000" },
                                        }
                                    }
                                },
                                new {
                                    recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                    url = mockThaiAd1,
                                    template = new {
                                        style = 1,
                                        templateId = "********-0000-0000-0000-********0005",
                                        templateName = "970x250 - Learn More CTA",
                                        version = "1.3",
                                        image = new {
                                            url = mockThaiImage1,
                                            editable = true,
                                            assetId = "23453",
                                            imageSize = new {
                                                width = 970,
                                                height = 250
                                            },
                                            cropSetting = new []  {
                                                new {
                                                    sourceX = 0,
                                                    sourceY = 0,
                                                    sourceWidth = 970,
                                                    sourceHeight = 250,
                                                    aspectRatio = "1:1"
                                                }
                                            }
                                        },
                                        logo = new {
                                            url = "https://aggsvcstorage.z13.web.core.windows.net/integration-test/Logo-Modern-minimalist-1.2.jpg", // not a valid URL
                                            editable = true,
                                            assetId = "23453",
                                            imageSize = new {
                                                width = 216,
                                                height = 54
                                            },
                                            cropSetting = new []  {
                                                new {
                                                    sourceX = 0,
                                                    sourceY = 0,
                                                    sourceWidth = 216,
                                                    sourceHeight = 54,
                                                    aspectRatio = "4:1"
                                                }
                                            }
                                        },
                                        businessName = requestBody?.BrandKit != null && requestBody?.BrandKit.businessName != null ?
                                        new {
                                            text = requestBody?.BrandKit.businessName,
                                            editable = true
                                        } : null,
                                        shotHeadline = new {
                                            text = "Thai Food",
                                            editable = true
                                        },
                                        longHeadline = new {
                                            text = "Premium Thai Food",
                                            editable = true
                                        },
                                        description = new {
                                            text = "Quality ingredients make good food and they are delicious",
                                            editable = true
                                        },
                                        cta = new {
                                            text = "Learn more",
                                            editable = false
                                        },
                                        assetProperties = new [] {
                                            new { assetName = "Logo", assetProperty = "Url", value = "https://www.bing.com//th?id=OADD2.9964469720606_1AB1WWQTTGALYBCY9P&pid=21.2" },
                                            new { assetName = "ShortHeadline", assetProperty = "FontFamily", value = "Helvetica Neue" },
                                            new { assetName = "ShortHeadline", assetProperty = "FontWeight", value = "800" },
                                            new { assetName = "LongHeadline", assetProperty = "FontFamily", value = "Arial" },
                                            new { assetName = "LongHeadline", assetProperty = "FontWeight", value = "500" },
                                            new { assetName = "Description", assetProperty = "FontFamily", value = "Sodo Sans" },
                                            new { assetName = "Description", assetProperty = "FontWeight", value = "200" },
                                            new { assetName = "Background", assetProperty = "Color", value = "#FFFFFF" },
                                            new { assetName = "ShortHeadline", assetProperty = "Color", value = "#000000" },
                                            new { assetName = "CTAWithoutText", assetProperty = "Color", value = "#FFFFFF" },
                                            new { assetName = "CTAWithoutText", assetProperty = "BackgroundColor", value = "#000000" },
                                        }
                                    }
                                },
                                new {
                                    recommendationId = "950bd852-b433-4e73-b046-00b481913847",
                                    url = mockThaiAd1,
                                    template = new {
                                        style = 1,
                                        templateId = "********-0000-0000-0000-********0007",
                                        templateName = "320x50 - 1:1 asset",
                                        version = "1.3",
                                        image = new {
                                            url = noodles_force2scrape_0,
                                            editable = true,
                                            assetId = "23454",
                                            imageSize = new {
                                                width = 320,
                                                height = 50
                                            },
                                            cropSetting = new []  {
                                                new {
                                                    sourceX = 0,
                                                    sourceY = 0,
                                                    sourceWidth = 320,
                                                    sourceHeight = 50,
                                                    aspectRatio = "1:1"
                                                }
                                            }
                                        },
                                        logo = new {
                                            url = "https://aggsvcstorage.z13.web.core.windows.net/integration-test/Logo-Modern-minimalist-1.2.jpg", // not a valid URL
                                            editable = true,
                                            assetId = "23453",
                                            imageSize = new {
                                                width = 216,
                                                height = 54
                                            },
                                            cropSetting = new []  {
                                                new {
                                                    sourceX = 0,
                                                    sourceY = 0,
                                                    sourceWidth = 216,
                                                    sourceHeight = 54,
                                                    aspectRatio = "4:1"
                                                }
                                            }
                                        },
                                        businessName = requestBody?.BrandKit != null && requestBody?.BrandKit.businessName != null ?
                                        new {
                                            text = requestBody?.BrandKit.businessName,
                                            editable = true
                                        } : null,
                                        shotHeadline = new {
                                            text = "Thai Food",
                                            editable = true
                                        },
                                        longHeadline = new {
                                            text = "Premium Thai Food",
                                            editable = true
                                        },
                                        description = new {
                                            text = "Quality ingredients make good food and they are delicious",
                                            editable = true
                                        },
                                        cta = new {
                                            text = "Learn more",
                                            editable = false
                                        },
                                        assetProperties = new [] {
                                            new { assetName = "Logo", assetProperty = "Url", value = "https://www.bing.com//th?id=OADD2.9964469720606_1AB1WWQTTGALYBCY9P&pid=21.2" },
                                            new { assetName = "ShortHeadline", assetProperty = "FontFamily", value = "Helvetica Neue" },
                                            new { assetName = "ShortHeadline", assetProperty = "FontWeight", value = "800" },
                                            new { assetName = "LongHeadline", assetProperty = "FontFamily", value = "Arial" },
                                            new { assetName = "LongHeadline", assetProperty = "FontWeight", value = "500" },
                                            new { assetName = "Description", assetProperty = "FontFamily", value = "Sodo Sans" },
                                            new { assetName = "Description", assetProperty = "FontWeight", value = "200" },
                                            new { assetName = "Background", assetProperty = "Color", value = "#FFFFFF" },
                                            new { assetName = "ShortHeadline", assetProperty = "Color", value = "#000000" },
                                            new { assetName = "CTAWithoutText", assetProperty = "Color", value = "#FFFFFF" },
                                            new { assetName = "CTAWithoutText", assetProperty = "BackgroundColor", value = "#000000" },
                                        }
                                    }
                                }
                            }
                        }
                    };
                }
                else
                {
                    response = new
                    {
                        data = new
                        {
                            assets = new
                            {
                                shortHeadlines = new[]
                                {
                                    new
                                    {
                                        text = "Thai",
                                        recommendationId = "b1a46c14-c8bd-4f08-ba38-5aa928a668e2",
                                        source = "LLM"
                                    }
                                },
                                longHeadlines = new[]
                                {
                                    new
                                    {
                                        text = "Premium Thai Food",
                                        recommendationId = "b1a46c14-c8bd-4f08-ba38-5aa928a668e2",
                                        source = "LLM"
                                    },
                                    new
                                    {
                                        text = "Get your spice on",
                                        recommendationId = "b1a46c14-c8bd-4f08-ba38-5aa928a668e2",
                                        source = "LLM"
                                    }
                                },
                                descriptions = new[]
                                {
                                    new
                                    {
                                        text = "Quality ingredients make good food and they are delicious",
                                        recommendationId = "b1a46c14-c8bd-4f08-ba38-5aa928a668e2",
                                        source = "LLM"
                                    },
                                    new
                                    {
                                        text = "Need more spice in life? Try our authentic Thai spices",
                                        recommendationId = "b1a46c14-c8bd-4f08-ba38-5aa928a668e2",
                                        source = "LLM"
                                    }
                                },
                                images = new[]
                                {
                                    new
                                    {
                                        url = mockThaiImage1,
                                        recommendationId = "b1a46c14-c8bd-4f08-ba38-5aa928a668e2",
                                        source = "LLM",
                                        assetId = "23453"
                                    }
                                }
                            },
                            ads = new[] {
                                new {
                                    recommendationId = "b1a46c14-c8bd-4f08-ba38-5aa928a668e2",
                                    url = mockThaiAd1,
                                    template = new {
                                        style = 1,
                                        templateId = "********-0000-0000-0000-********0005",
                                        templateName = "970x250 - Learn More CTA",
                                        version = "1.0",
                                        image = new {
                                            url = mockThaiImage1,
                                            editable = true,
                                            assetId = "234531"
                                        },
                                        shotHeadline = new {
                                            text = "Thai Food",
                                            editable = true
                                        },
                                        longHeadline = new {
                                            text = "Premium Thai Food",
                                            editable = true
                                        },
                                        description = new {
                                            text = "Quality ingredients make good food and they are delicious",
                                            editable = true
                                        },
                                        cta = new {
                                            text = "Learn more",
                                            editable = false
                                        },
                                    }
                                },
                                new {
                                    recommendationId = "b1a46c14-c8bd-4f08-ba38-5aa928a66123",
                                    url = mockThaiAd1,
                                    template = new {
                                        style = 1,
                                        templateId = "********-0000-0000-0000-********0005",
                                        templateName = "970x250 - Learn More CTA",
                                        version = "1.0",
                                        image = new {
                                            url = mockThaiImage1,
                                            editable = true,
                                            assetId = "234532"
                                        },
                                        shotHeadline = new {
                                            text = "Thai Food",
                                            editable = true
                                        },
                                        longHeadline = new {
                                            text = "Premium Thai Food",
                                            editable = true
                                        },
                                        description = new {
                                            text = "Quality ingredients make good food and they are delicious",
                                            editable = true
                                        },
                                        cta = new {
                                            text = "Learn more",
                                            editable = false
                                        },
                                    }
                                },
                                new {
                                    recommendationId = "950bd852-b433-4e73-b046-00b481913847",
                                    url = mockThaiAd1,
                                    template = new {
                                        style = 1,
                                        templateId = "********-0000-0000-0000-********0007",
                                        templateName = "320x50 - 1:1 asset",
                                        version = "1.0",
                                        image = new {
                                            url = mockThaiImage1,
                                            editable = true,
                                            assetId = "234533"
                                        },
                                        shotHeadline = new {
                                            text = "Thai Food",
                                            editable = true
                                        },
                                        longHeadline = new {
                                            text = "Premium Thai Food",
                                            editable = true
                                        },
                                        description = new {
                                            text = "Quality ingredients make good food and they are delicious",
                                            editable = true
                                        },
                                        cta = new {
                                            text = "Learn more",
                                            editable = false
                                        },
                                    }
                                }
                            }
                        }
                    };
                }
            } catch {
                response = new
                {
                    data = new
                    {
                        assets = new
                        {
                            shortHeadlines = new[] {
                                 new {
                                     text = "Thai",
                                     recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                     source = "LLM"
                                 }
                            },
                            longHeadlines = new[] {
                                 new {
                                     text = "Premium Thai Food",
                                     recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                     source = "LLM"
                                 },
                                 new {
                                     text = "Get your spice on",
                                     recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                     source = "LLM"
                                 }
                            },
                            descriptions = new[] {
                                 new {
                                     text = "Quality ingredients make good food and they are delicious",
                                     recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                     source = "LLM"
                                 },
                                 new {
                                     text = "Need more spice in life? Try our authentic Thai spices",
                                     recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                     source = "LLM"
                                 },
                            },
                            images = new[] {
                                 new {
                                     url = mockThaiImage1,
                                     recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                     source = "LLM",
                                     assetId = "23453",
                                     imageSize = new {
                                         width = 970,
                                         height = 250
                                     },
                                     cropSetting = new []  {
                                         new {
                                             sourceX = 0,
                                             sourceY = 0,
                                             sourceWidth = 970,
                                             sourceHeight = 250,
                                             aspectRatio = "1:1"
                                         }
                                     }
                                 },
                                 new {
                                     url = noodles_force2scrape_0,
                                     recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                     source = "LLM",
                                     assetId = "23454",
                                     imageSize = new {
                                         width = 1500,
                                         height = 1000
                                     },
                                     cropSetting = new []  {
                                         new {
                                             sourceX = 0,
                                             sourceY = 0,
                                             sourceWidth = 1500,
                                             sourceHeight = 1000,
                                             aspectRatio = "1:1"
                                         }
                                     }
                                 }
                             }
                        },
                        ads = new[] {
                             new {
                                 recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                 url = mockThaiAd1,
                                 template = new {
                                     style = 1,
                                     templateId = "********-0000-0000-0000-********0005",
                                     templateName = "970x250 - Learn More CTA",
                                     version = "1.3",
                                     image = new {
                                         url = mockThaiImage1,
                                         editable = true,
                                         assetId = "23453",
                                         imageSize = new {
                                             width = 970,
                                             height = 250
                                         },
                                         cropSetting = new []  {
                                             new {
                                                 sourceX = 0,
                                                 sourceY = 0,
                                                 sourceWidth = 970,
                                                 sourceHeight = 250,
                                                 aspectRatio = "1:1"
                                             }
                                         }
                                     },
                                     logo = new {
                                         url = "https://aggsvcstorage.z13.web.core.windows.net/integration-test/Logo-Modern-minimalist-1.2.jpg", // not a valid URL
                                         editable = true,
                                         assetId = "23453",
                                         imageSize = new {
                                             width = 216,
                                             height = 54
                                         },
                                         cropSetting = new []  {
                                             new {
                                                 sourceX = 0,
                                                 sourceY = 0,
                                                 sourceWidth = 216,
                                                 sourceHeight = 54,
                                                 aspectRatio = "4:1"
                                             }
                                         }
                                     },
                                     shotHeadline = new {
                                         text = "Thai Food",
                                         editable = true
                                     },
                                     longHeadline = new {
                                         text = "Premium Thai Food",
                                         editable = true
                                     },
                                     description = new {
                                         text = "Quality ingredients make good food and they are delicious",
                                         editable = true
                                     },
                                     cta = new {
                                         text = "Learn more",
                                         editable = false
                                     },
                                     assetProperties = new [] {
                                         new { assetName = "Logo", assetProperty = "Url", value = "https://www.bing.com//th?id=OADD2.9964469720606_1AB1WWQTTGALYBCY9P&pid=21.2" },
                                         new { assetName = "ShortHeadline", assetProperty = "FontFamily", value = "Helvetica Neue" },
                                         new { assetName = "ShortHeadline", assetProperty = "FontWeight", value = "800" },
                                         new { assetName = "LongHeadline", assetProperty = "FontFamily", value = "Arial" },
                                         new { assetName = "LongHeadline", assetProperty = "FontWeight", value = "500" },
                                         new { assetName = "Description", assetProperty = "FontFamily", value = "Sodo Sans" },
                                         new { assetName = "Description", assetProperty = "FontWeight", value = "200" },
                                         new { assetName = "Background", assetProperty = "Color", value = "#FFFFFF" },
                                         new { assetName = "ShortHeadline", assetProperty = "Color", value = "#000000" },
                                         new { assetName = "CTAWithoutText", assetProperty = "Color", value = "#FFFFFF" },
                                         new { assetName = "CTAWithoutText", assetProperty = "BackgroundColor", value = "#000000" },
                                     }
                                 }
                             },
                             new {
                                 recommendationId = "9edef27d-aaa8-49c5-903a-74e8dc3aa2c9",
                                 url = mockThaiAd1,
                                 template = new {
                                     style = 1,
                                     templateId = "********-0000-0000-0000-********0005",
                                     templateName = "970x250 - Learn More CTA",
                                     version = "1.3",
                                     image = new {
                                         url = mockThaiImage1,
                                         editable = true,
                                         assetId = "23453",
                                         imageSize = new {
                                             width = 970,
                                             height = 250
                                         },
                                         cropSetting = new []  {
                                             new {
                                                 sourceX = 0,
                                                 sourceY = 0,
                                                 sourceWidth = 970,
                                                 sourceHeight = 250,
                                                 aspectRatio = "1:1"
                                             }
                                         }
                                     },
                                     logo = new {
                                         url = "https://aggsvcstorage.z13.web.core.windows.net/integration-test/Logo-Modern-minimalist-1.2.jpg", // not a valid URL
                                         editable = true,
                                         assetId = "23453",
                                         imageSize = new {
                                             width = 216,
                                             height = 54
                                         },
                                         cropSetting = new []  {
                                             new {
                                                 sourceX = 0,
                                                 sourceY = 0,
                                                 sourceWidth = 216,
                                                 sourceHeight = 54,
                                                 aspectRatio = "4:1"
                                             }
                                         }
                                     },
                                     shotHeadline = new {
                                         text = "Thai Food",
                                         editable = true
                                     },
                                     longHeadline = new {
                                         text = "Premium Thai Food",
                                         editable = true
                                     },
                                     description = new {
                                         text = "Quality ingredients make good food and they are delicious",
                                         editable = true
                                     },
                                     cta = new {
                                         text = "Learn more",
                                         editable = false
                                     },
                                     assetProperties = new [] {
                                         new { assetName = "Logo", assetProperty = "Url", value = "https://www.bing.com//th?id=OADD2.9964469720606_1AB1WWQTTGALYBCY9P&pid=21.2" },
                                         new { assetName = "ShortHeadline", assetProperty = "FontFamily", value = "Helvetica Neue" },
                                         new { assetName = "ShortHeadline", assetProperty = "FontWeight", value = "800" },
                                         new { assetName = "LongHeadline", assetProperty = "FontFamily", value = "Arial" },
                                         new { assetName = "LongHeadline", assetProperty = "FontWeight", value = "500" },
                                         new { assetName = "Description", assetProperty = "FontFamily", value = "Sodo Sans" },
                                         new { assetName = "Description", assetProperty = "FontWeight", value = "200" },
                                         new { assetName = "Background", assetProperty = "Color", value = "#FFFFFF" },
                                         new { assetName = "ShortHeadline", assetProperty = "Color", value = "#000000" },
                                         new { assetName = "CTAWithoutText", assetProperty = "Color", value = "#FFFFFF" },
                                         new { assetName = "CTAWithoutText", assetProperty = "BackgroundColor", value = "#000000" },
                                     }
                                 }
                             },
                             new {
                                 recommendationId = "950bd852-b433-4e73-b046-00b481913847",
                                 url = mockThaiAd1,
                                 template = new {
                                     style = 1,
                                     templateId = "********-0000-0000-0000-********0007",
                                     templateName = "320x50 - 1:1 asset",
                                     version = "1.3",
                                     image = new {
                                         url = noodles_force2scrape_0,
                                         editable = true,
                                         assetId = "23454",
                                         imageSize = new {
                                             width = 320,
                                             height = 50
                                         },
                                         cropSetting = new []  {
                                             new {
                                                 sourceX = 0,
                                                 sourceY = 0,
                                                 sourceWidth = 320,
                                                 sourceHeight = 50,
                                                 aspectRatio = "1:1"
                                             }
                                         }
                                     },
                                     logo = new {
                                         url = "https://aggsvcstorage.z13.web.core.windows.net/integration-test/Logo-Modern-minimalist-1.2.jpg", // not a valid URL
                                         editable = true,
                                         assetId = "23453",
                                         imageSize = new {
                                             width = 216,
                                             height = 54
                                         },
                                         cropSetting = new [] {
                                             new {
                                                 sourceX = 0,
                                                 sourceY = 0,
                                                 sourceWidth = 216,
                                                 sourceHeight = 54,
                                                 aspectRatio = "4:1"
                                             }
                                         }
                                     },
                                     shotHeadline = new {
                                         text = "Thai Food",
                                         editable = true
                                     },
                                     longHeadline = new {
                                         text = "Premium Thai Food",
                                         editable = true
                                     },
                                     description = new {
                                         text = "Quality ingredients make good food and they are delicious",
                                         editable = true
                                     },
                                     cta = new {
                                         text = "Learn more",
                                         editable = false
                                     },
                                     assetProperties = new [] {
                                         new { assetName = "Logo", assetProperty = "Url", value = "https://www.bing.com//th?id=OADD2.9964469720606_1AB1WWQTTGALYBCY9P&pid=21.2" },
                                         new { assetName = "ShortHeadline", assetProperty = "FontFamily", value = "Helvetica Neue" },
                                         new { assetName = "ShortHeadline", assetProperty = "FontWeight", value = "800" },
                                         new { assetName = "LongHeadline", assetProperty = "FontFamily", value = "Arial" },
                                         new { assetName = "LongHeadline", assetProperty = "FontWeight", value = "500" },
                                         new { assetName = "Description", assetProperty = "FontFamily", value = "Sodo Sans" },
                                         new { assetName = "Description", assetProperty = "FontWeight", value = "200" },
                                         new { assetName = "Background", assetProperty = "Color", value = "#FFFFFF" },
                                         new { assetName = "ShortHeadline", assetProperty = "Color", value = "#000000" },
                                         new { assetName = "CTAWithoutText", assetProperty = "Color", value = "#FFFFFF" },
                                         new { assetName = "CTAWithoutText", assetProperty = "BackgroundColor", value = "#000000" },
                                     }
                                 }
                             }
                         }
                    }
                };
            }
            var content = JsonConvert.SerializeObject(response);
            return Ok(content);
        }

        [HttpPost]
        [Route("v1/Customers({customerId})/Accounts({accountId})/GetDisplayAdsTemplateDefinition")]
        public IActionResult GetDisplayAdsTemplateDefinition(long customerId, long accountId, [FromBody] DisplayAdsTemplateDefinitionRequest request)
        {
            // GetDisplayAdsTemplateDefinition will always return 1.3 templates
            var templateId = (request as dynamic)?.templateId;

            Dictionary<string, DisplayAdsTemplateDefinition> DisplayAdsTemplateDefinitionMap = new Dictionary<string, DisplayAdsTemplateDefinition>
            {
                {
                    "********-0000-0000-0000-************",
                    new DisplayAdsTemplateDefinition
                    {
                        templateId = "********-0000-0000-0000-************",
                        templateName = "320x50 - Modern minimalist",
                        version = "1.3",
                        width = 320,
                        height = 50,
                        style = 1,
                        businessName = new TextAssetConfiguration
                        {
                            minLengthInChar = 0,
                            maxLengthInChar = 25,
                            upper = false,
                            display = true,
                        },
                        logo = new ImageAssetConfiguration
                        {
                            aspectRatio = "4:1",
                            minHeight = 32,
                            targetWidth = 128,
                            targetHeight = 32,
                            display = true
                        },
                        image = new ImageAssetConfiguration
                        {
                            aspectRatio = "1.91:1",
                            minHeight = 50,
                            targetWidth = 96,
                            targetHeight = 50,
                            display = true
                        },
                        background = new BackgroundAssetConfiguration { display = true },
                        color = [
                            new ColorConfiguration
                            {
                                elementName = "Background",
                                suffix = "",
                                defaultColor = "#FFFFFF",
                                display = true
                            }
                        ],
                        assetPropertySettings = [
                            new RecommendationAssetProperty
                            {
                                assetName = "Background",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            }
                        ]
                    }
                },
                {
                    "********-0000-0000-0000-000010001001",
                    new DisplayAdsTemplateDefinition
                    {
                        templateId = "********-0000-0000-0000-000010001001",
                        templateName = "300x250 - Modern minimalist",
                        version = "1.3",
                        width = 300,
                        height = 250,
                        style = 1,
                        businessName = new TextAssetConfiguration
                        {
                            minLengthInChar = 0,
                            maxLengthInChar = 25,
                            upper = false,
                            display = true
                        },
                        logo = new ImageAssetConfiguration
                        {
                            aspectRatio = "4:1",
                            minHeight = 16,
                            targetWidth = 64,
                            targetHeight = 16,
                            display = true
                        },
                        image = new ImageAssetConfiguration
                        {
                            aspectRatio = "1.91:1",
                            minHeight = 140,
                            targetWidth = 268,
                            targetHeight = 140,
                            display = true
                        },
                        background = new BackgroundAssetConfiguration { display = true },
                        color = [
                            new ColorConfiguration
                            {
                                elementName = "Background",
                                suffix = "",
                                defaultColor = "#FFFFFF",
                                display = true
                            }
                        ],
                        assetPropertySettings = [
                            new RecommendationAssetProperty
                            {
                                assetName = "Background",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            }
                        ]
                    }
                },
                {
                    "000-0000-0000-0000-000010001002",
                    new DisplayAdsTemplateDefinition
                    {
                        templateId = "********-0000-0000-0000-000010001002",
                        templateName = "728x90 - Modern minimalist",
                        version = "1.3",
                        width = 728,
                        height = 90,
                        style = 1,
                        businessName = new TextAssetConfiguration
                        {
                            minLengthInChar = 0,
                            maxLengthInChar = 25,
                            upper = false,
                            display = true
                        },
                        logo = new ImageAssetConfiguration
                        {
                            aspectRatio = "4:1",
                            minHeight = 32,
                            targetWidth = 128,
                            targetHeight = 32,
                            display = true
                        },
                        image = new ImageAssetConfiguration
                        {
                            aspectRatio = "1.91:1",
                            minHeight = 90,
                            targetWidth = 172,
                            targetHeight = 90,
                            display = true
                        },
                        background = new BackgroundAssetConfiguration { display = true },
                        color = [
                            new ColorConfiguration
                            {
                                elementName = "Background",
                                suffix = "",
                                defaultColor = "#FFFFFF",
                                display = true
                            }
                        ],
                        assetPropertySettings = [
                            new RecommendationAssetProperty
                            {
                                assetName = "Background",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            }
                        ]
                    }
                },
                {
                    "********-0000-0000-0000-000010001003",
                    new DisplayAdsTemplateDefinition
                    {
                        templateId = "********-0000-0000-0000-000010001003",
                        templateName = "970x250 - Modern minimalist",
                        version = "1.3",
                        width = 970,
                        height = 250,
                        style = 1,
                        businessName = new TextAssetConfiguration
                        {
                            minLengthInChar = 0,
                            maxLengthInChar = 25,
                            upper = false,
                            display = true
                        },
                        logo = new ImageAssetConfiguration
                        {
                            aspectRatio = "4:1",
                            minHeight = 32,
                            targetWidth = 128,
                            targetHeight = 32,
                            display = true
                        },
                        image = new ImageAssetConfiguration
                        {
                            aspectRatio = "1.91:1",
                            minHeight = 202,
                            targetWidth = 386,
                            targetHeight = 202,
                            display = true
                        },
                        background = new BackgroundAssetConfiguration { display = true },
                        color = [
                            new ColorConfiguration
                            {
                                elementName = "Background",
                                suffix = "",
                                defaultColor = "#FFFFFF",
                                display = true
                            }
                        ],
                        assetPropertySettings = [
                            new RecommendationAssetProperty
                            {
                                assetName = "Background",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            }
                        ]
                    }
                },
                {
                    "********-0000-0000-0000-000010001004",
                    new DisplayAdsTemplateDefinition
                    {
                        templateId = "********-0000-0000-0000-000010001004",
                        templateName = "300x600 - Modern minimalist",
                        version = "1.3",
                        width = 300,
                        height = 600,
                        style = 1,
                        businessName = new TextAssetConfiguration
                        {
                            minLengthInChar = 0,
                            maxLengthInChar = 25,
                            upper = false,
                            display = true
                        },
                        logo = new ImageAssetConfiguration
                        {
                            aspectRatio = "4:1",
                            minHeight = 32,
                            targetWidth = 128,
                            targetHeight = 32,
                            display = true
                        },
                        image = new ImageAssetConfiguration
                        {
                            aspectRatio = "1:1",
                            minHeight = 252,
                            targetWidth = 252,
                            targetHeight = 252,
                            display = true
                        },
                        background = new BackgroundAssetConfiguration { display = true },
                        color = [
                            new ColorConfiguration
                            {
                                elementName = "Background",
                                suffix = "",
                                defaultColor = "#FFFFFF",
                                display = true
                            }
                        ],
                        assetPropertySettings = [
                            new RecommendationAssetProperty
                            {
                                assetName = "Background",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            }
                        ]
                    }
                },
                {
                    "********-0000-0000-0000-000010001005",
                    new DisplayAdsTemplateDefinition
                    {
                        templateId = "********-0000-0000-0000-000010001005",
                        templateName = "160x600 - Modern minimalist",
                        version = "1.3",
                        width = 160,
                        height = 600,
                        style = 1,
                        businessName = new TextAssetConfiguration
                        {
                            minLengthInChar = 0,
                            maxLengthInChar = 25,
                            upper = false,
                            display = true
                        },
                        logo = new ImageAssetConfiguration
                        {
                            aspectRatio = "4:1",
                            minHeight = 24,
                            targetWidth = 96,
                            targetHeight = 24,
                            display = true
                        },
                        image = new ImageAssetConfiguration
                        {
                            aspectRatio = "1:1",
                            minHeight = 136,
                            targetWidth = 136,
                            targetHeight = 136,
                            display = true
                        },
                        background = new BackgroundAssetConfiguration { display = true },
                        color = [
                            new ColorConfiguration
                            {
                                elementName = "Background",
                                suffix = "",
                                defaultColor = "#FFFFFF",
                                display = true
                            }
                        ],
                        assetPropertySettings = [
                            new RecommendationAssetProperty
                            {
                                assetName = "Background",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            }
                        ]
                    }
                }
            };

            // these templates were not included in the GetDisplayAdsTemplateGroupDetail mock response, created a close template definition approximation
            if (templateId == "********-0000-0000-0000-********0005" || templateId == "********-0000-0000-0000-********0007")
            {
                var templateName = "970x250 - Learn More CTA";
                var width = 970;
                var height = 250;

                if (templateId == "********-0000-0000-0000-********0007")
                {
                    templateName = "320x50 - 1:1 asset";
                    width = 320;
                    height = 50;
                }

                var templateDefinition = new DisplayAdsTemplateDefinition
                {
                    templateId = templateId,
                    templateName = templateName,
                    version = "1.3",
                    width = width,
                    height = height,
                    style = 1,
                    businessName = new TextAssetConfiguration
                    {
                        display = true,
                        minLengthInChar = 1,
                        maxLengthInChar = 25,
                        upper = false
                    },
                    shortHeadline = new TextAssetConfiguration
                    {
                        display = true,
                        minLengthInChar = 1,
                        maxLengthInChar = 30,
                        upper = false
                    },
                    longHeadline = new TextAssetConfiguration
                    {
                        display = true,
                        minLengthInChar = 1,
                        maxLengthInChar = 30,
                        upper = false
                    },
                    description = new TextAssetConfiguration
                    {
                        display = true,
                        minLengthInChar = 1,
                        maxLengthInChar = 90,
                        upper = false
                    },
                    cta = new TextAssetConfiguration
                    {
                        display = true,
                        minLengthInChar = 1,
                        maxLengthInChar = 15,
                        upper = false
                    },
                    ctaWithoutText = new TextAssetConfiguration
                    {
                        display = true,
                        minLengthInChar = 0,
                        maxLengthInChar = 0,
                        upper = false
                    },
                    image = new ImageAssetConfiguration
                    {
                        display = true,
                        aspectRatio = "1:1",
                        minHeight = height,
                        targetWidth = width,
                        targetHeight = height
                    },
                    logo = new ImageAssetConfiguration
                    {
                        display = true,
                        aspectRatio = "4:1",
                        minHeight = 50,
                        targetWidth = 216,
                        targetHeight = 54
                    },
                    background = new BackgroundAssetConfiguration
                    {
                        display = true
                    },
                    color = new List<ColorConfiguration>
                    {
                        new ColorConfiguration
                        {
                            elementName = "Background",
                            suffix = "Color",
                            display = true,
                            defaultColor = "#FFFFFF"
                        },
                        new ColorConfiguration
                        {
                            elementName = "ShortHeadline",
                            suffix = "Color",
                            display = true,
                            defaultColor = "#616161"
                        },
                        new ColorConfiguration
                        {
                            elementName = "CTAWithoutText",
                            suffix = "Color",
                            display = true,
                            defaultColor = "#FFFFFF"
                        },
                        new ColorConfiguration
                        {
                            elementName = "CTAWithoutText",
                            suffix = "BackgroundColor",
                            display = true,
                            defaultColor = "#000000"
                        }
                    },
                    assetPropertySettings = new List<RecommendationAssetProperty>
                    {
                        new RecommendationAssetProperty { assetName = "ShortHeadline", assetProperty = "FontFamily", value = "Helvetica Neue" },
                        new RecommendationAssetProperty { assetName = "ShortHeadline", assetProperty = "FontWeight", value = "800" },
                        new RecommendationAssetProperty { assetName = "LongHeadline", assetProperty = "FontFamily", value = "Arial" },
                        new RecommendationAssetProperty { assetName = "LongHeadline", assetProperty = "FontWeight", value = "500" },
                        new RecommendationAssetProperty { assetName = "Description", assetProperty = "FontFamily", value = "Sodo Sans" },
                        new RecommendationAssetProperty { assetName = "Description", assetProperty = "FontWeight", value = "200" }
                    }
                };

                var response = new DisplayAdsTemplateDefinitionResponse
                {
                    Data = new DisplayAdsTemplateDefinitionResponse.DisplayAdsTemplateDefinitionData
                    {
                        code = 200,
                        displayAdsTemplateDefinitions = new List<DisplayAdsTemplateDefinition> { templateDefinition }
                    }
                };

                return Ok(response);
            }
            var displayAdsTemplateDefinitions = DisplayAdsTemplateDefinitionMap[templateId];

            if (displayAdsTemplateDefinitions != null)
            {
                var response = new DisplayAdsTemplateDefinitionResponse
                {
                    Data = new DisplayAdsTemplateDefinitionResponse.DisplayAdsTemplateDefinitionData
                    {
                        code = 200,
                        displayAdsTemplateDefinitions = new List<DisplayAdsTemplateDefinition> { displayAdsTemplateDefinitions }
                    }
                };

                return Ok(response);
            }
            else
            {
                return NotFound(new { error = "Template not found", code = 404 });

            }
        }

        [HttpPost]
        [Route("v1/Customers({customerId})/Accounts({accountId})/GetDisplayAdsTemplateGroups")]
        public IActionResult GetDisplayAdsTemplateGroups(long customerId, long accountId, [FromBody] GetDisplayAdsTemplateGroupsRequest request)
        {
            // Create response structure
            var response = new GetDisplayAdsTemplateGroupsResponse
            {
                data = new GetDisplayAdsTemplateGroupsData
                {
                    code = 200,
                    displayAdsTemplateGroup = []
                }
            };
            // Check the version from the request, set a default if not specified
            string version = request?.version ?? "1.2";
            string locale = request?.locale ?? LCID.EN_US.ToString();

            // Add common template group
            response.data.displayAdsTemplateGroup.Add(new DisplayAdsTemplateGroup
            {
                templateGroupId = "********-0000-0000-0001-********0001",
                version = version,  // Use the requested version
                templateGroupName = "Modern Minimalist",
                preview = new Uri(baseDevIntegrationTestUri, "320x50 - Modern minimalist-1.3.jpeg").ToString(),
                tags =
                    [
                        new TemplateTag { id = 1, tagName = "Modern" },
                        new TemplateTag { id = 2, tagName = "Clean" }
                    ],
                numberOfLogo = 1,
                numberOfImage = 2,
                numberOfText = 3
            });

            // For version 1.2, include additional template groups to assert on
            if (version == "1.2")
            {
                response.data.displayAdsTemplateGroup.Add(new DisplayAdsTemplateGroup
                {
                    templateGroupId = "********-0000-0000-0001-********0002",
                    version = version,
                    templateGroupName = "Bold Impact",
                    preview = new Uri(baseDevIntegrationTestUri, "300x250 - Bold and colorful-1.2.jpeg").ToString(),
                    tags =
                        [
                            new TemplateTag { id = 3, tagName = "Bold" },
                            new TemplateTag { id = 4, tagName = "Dynamic" }
                        ],
                    numberOfLogo = 1,
                    numberOfImage = 3,
                    numberOfText = 4
                });

                response.data.displayAdsTemplateGroup.Add(new DisplayAdsTemplateGroup
                {
                    templateGroupId = "********-0000-0000-0001-********0003",
                    version = version,
                    templateGroupName = "Elegant Style",
                    preview = new Uri(baseDevIntegrationTestUri, "300x250 - Vintage, retro-1.2.jpeg").ToString(),
                    tags =
                        [
                            new TemplateTag { id = 5, tagName = "Elegant" },
                            new TemplateTag { id = 6, tagName = "Professional" }
                        ],
                    numberOfLogo = 2,
                    numberOfImage = 2,
                    numberOfText = 5
                });

                if (locale == LCID.ES_ES.ToString())
                {
                    return Ok(@"{
                        ""data"": {
                          ""code"": 200,
                          ""displayAdsTemplateGroup"": [
                            {
                              ""templateGroupId"": ""********-0000-0000-0001-********0001"",
                              ""version"": ""1.2"",
                              ""templateGroupName"": ""Moderno minimalista"",
                              ""preview"": ""https://aggsvcstoragedev.blob.core.windows.net/$web/integration-test/template-preview.jpg"",
                              ""tags"": [
                                {
                                  ""id"": 1,
                                  ""tagName"": ""Moderno""
                                },
                                {
                                  ""id"": 2,
                                  ""tagName"": ""Limpio""
                                }
                              ],
                              ""numberOfLogo"": 1,
                              ""numberOfImage"": 2,
                              ""numberOfText"": 3
                            }
                          ]
                        }
                    }");
                }
            }
            return Ok(response);
        }

        [HttpPost]
        [Route("v1/Customers({customerId})/Accounts({accountId})/GetDisplayAdsTemplateGroupDetail")]
        public IActionResult GetDisplayAdsTemplateGroupDetail(long customerId, long accountId, [FromBody] GetDisplayAdsTemplateGroupDetailRequest request)
        {
            var req = request as dynamic;
            if (req.locale == LCID.ES_ES.ToString())
            {
                var respJSON = new
                {
                    data = new
                    {
                        code = 200,
                        details = new[]
                        {
                            new
                            {
                                templateGroupId = "********-0000-0000-0001-********0001",
                                version = "1.2",
                                templateGroupName = "Moderno minimalista",
                                templateCount = 6,
                                displayAdsTemplateDetails = new []
                                {
                                    new
                                    {
                                        templateConfig = new
                                        {
                                            style = 1,
                                            image = new
                                            {
                                                aspectRatio = "1.91:1",
                                                minHeight = 50,
                                                targetWidth = 96,
                                                targetHeight = 50,
                                                display = true
                                            },
                                            businessName = new
                                            {
                                                minLengthInChar = 0,
                                                maxLengthInChar = 25,
                                                upper = false,
                                                display = true
                                            },
                                            logo = new
                                            {
                                                aspectRatio = "4:1",
                                                minHeight = 32,
                                                targetWidth = 128,
                                                targetHeight = 32,
                                                display = true
                                            },
                                            background = new
                                            {
                                                display = true
                                            },
                                            color = new[]
                                            {
                                                new
                                                {
                                                    elementName = "Background",
                                                    suffix = "",
                                                    defaultColor = "#FFFFFF",
                                                    display = true
                                                }
                                            },
                                            assetPropertySettings = new[]
                                            {
                                                new
                                                {
                                                    assetName = "Background",
                                                    assetProperty = "Color",
                                                    value = "#FFFFFF"
                                                }
                                            },
                                            templateId = "********-0000-0000-0000-************",
                                            templateName = "320x50 - Moderno minimalista",
                                            version = "",
                                            width = 320,
                                            height = 50,
                                            templateFileName = ""
                                        },
                                        displayAd= new
                                        {
                                            recommendationId= "6b837c30-bb7b-4847-a789-290c92310f59",
                                            url= modernMinimalist_300x250_13,
                                            template= new
                                            {
                                                style= 1,
                                                templateId= "********-0000-0000-0000-************",
                                                version= "",
                                                templateName= "320x50 - Moderno minimalista",
                                                image= new
                                                {
                                                    url= new Uri(baseDevIntegrationTestUri,"ProdImage-Moderno minimalista-1.2.jpg").ToString(),
                                                    editable= true,
                                                    imageSize= new { width= 268, height= 140 },
                                                    cropSetting= new[] { new { sourceX = 0, sourceY = 0, sourceWidth = 268, sourceHeight = 140 } }
                                                },
                                                shortHeadline= new { text= "", editable= false },
                                                longHeadline= new { text= "", editable= false },
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                };

                return Ok(respJSON);
            }

            var templateDetail0 = new DisplayAdsTemplateDetail
            {
                templateConfig = new MockDisplayAdsTemplateDefinition
                {
                    style = 1,
                    image = new ImageAssetConfiguration
                    {
                        aspectRatio = "1.91:1",
                        minHeight = 50,
                        targetWidth = 96,
                        targetHeight = 50,
                        display = true
                    },
                    logo = new ImageAssetConfiguration
                    {
                        aspectRatio = "4:1",
                        minHeight = 32,
                        targetWidth = 128,
                        targetHeight = 32,
                        display = true
                    },
                    businessName = new TextAssetConfiguration
                    {
                        minLengthInChar = 0,
                        maxLengthInChar = 25,
                        upper = false,
                        display = true
                    },
                    background = new BackgroundAssetConfiguration { display = true },
                    color = new List<ColorConfiguration>
                    {
                        new ColorConfiguration
                        {
                            elementName = "Background",
                            suffix = "",
                            defaultColor = "#FFFFFF",
                            display = true
                        }
                    },
                    assetPropertySettings = new List<RecommendationAssetProperty>()
                    {
                        new RecommendationAssetProperty
                        {
                            assetName = "Background",
                            assetProperty = "Color",
                            value = "#FFFFFF"
                        }
                    },
                    templateId = "********-0000-0000-0000-************",
                    templateName = "320x50 - Modern minimalist",
                    version = "",
                    width = 320,
                    height = 50,
                    TemplateFileName = "320x50.html",
                    TemplateContent = "<html>\\r\\n<head>\\r\\n  <meta http-equiv=\\\"\"Content-Type\\\"\" content=\\\"\"text/html; charset=utf-8\\\"\">  \\r\\n  <style>\\r\\n   #background {\\r\\n  background: {{BackgroundColor}};\\r\\n  width: 320px;\\r\\n  height: 50px;\\r\\n  position: relative;\\r\\n  overflow: hidden;  \\r\\n}\\r\\n#logo-container {\\r\\n  left: 48px;\\r\\n  top: 9px;\\r\\n  width: 128px;\\r\\n  height: 32px;\\r\\n  position: absolute;\\r\\n}\\r\\n#logo {\\r\\n  width: {{LogoImageResizeWidth}}px;\\r\\n  margin-left: {{LogoImageNegativeMarginX}}px;\\r\\n  margin-top: {{LogoImageNegativeMarginY}}px;\\r\\n}\\r\\n#product-image-container {\\r\\n  right: 0;\\r\\n  top: 0;\\r\\n  width: 96px;\\r\\n  height: 50px;\\r\\n  position: absolute;\\r\\n  overflow: hidden;\\r\\n}\\r\\n#product-image {\\r\\n  width: {{ImageResizeWidth}}px;\\r\\n  height: {{ImageResizeHeight}}px;\\r\\n  margin-left: {{ImageNegativeMarginX}}px;\\r\\n  margin-top: {{ImageNegativeMarginY}}px;\\r\\n}\\r\\n   </style>\\r\\n  <title>Document</title>\\r\\n</head>\\r\\n<body>\\r\\n  <div id=\\\"\"background\\\"\">\\r\\n    <div id=\\\"\"logo-container\\\"\">\\r\\n      <img id=\\\"\"logo\\\"\" src=\\\"\"{{LogoImageUrl}}\\\"\" />\\r\\n    </div>\\r\\n    <div id=\\\"\"product-image-container\\\"\">\\r\\n      <img id=\\\"\"product-image\\\"\" src=\\\"\"{{ImageURL}}\\\"\" />\\r\\n    </div>\\r\\n  </div>  \\r\\n</body>\\r\\n</html>"
                },
                displayAd = new RecommendationAd
                {
                    recommendationId = "6b837c30-bb7b-4847-a789-290c92310f59",
                    url = new Uri(baseDevIntegrationTestUri, "320x50 - Modern minimalist-1.2.jpeg").ToString(),
                    template = new RecommendationTemplate
                    {
                        style = 1,
                        templateId = "********-0000-0000-0000-************",
                        version = "1.2",
                        templateName = "320x50 - Modern minimalist",
                        image = new RecommendationImage
                        {
                            url = modernMinimalist_954x500_13,
                            editable = true,
                            imageSize = new RecommendationImageSize { width = 268, height = 140 },
                            cropSetting = new List<RecommendationCropSetting> { new RecommendationCropSetting { sourceX = 0, sourceY = 0, sourceWidth = 268, sourceHeight = 140 } }
                        },
                        shortHeadline = new RecommendationText { text = "Timeless Precision, Redefined", editable = false },
                        longHeadline = new RecommendationText { text = "", editable = false },
                        description = new RecommendationText { text = "Experience precision craftsmanship that stands the test of time, redefined for today.", editable = false },
                        cta = new RecommendationText { text = "", editable = false },
                        businessName = new RecommendationText { text = "TestBusinessName", editable = false },
                        color = new List<RecommendationColor>
                        {
                            new RecommendationColor
                            {
                                elementName = "Background",
                                suffix = "",
                                editable = true,
                                color = "#FFFFFF"
                            }
                        },
                        assetProperties = new List<RecommendationAssetProperty>
                        {
                            new RecommendationAssetProperty
                            {
                                assetName = "Background",
                                assetProperty = "Color",
                                value = "#FFFFFF"
                            }
                        }
                    }
                }
            };

            var templateDetail1 = new DisplayAdsTemplateDetail
            {
                templateConfig = new MockDisplayAdsTemplateDefinition
                {
                    style = 1,
                    image = new ImageAssetConfiguration
                    {
                        aspectRatio = "1.91:1",
                        minHeight = 140,
                        targetWidth = 268,
                        targetHeight = 140,
                        display = true
                    },
                    logo = new ImageAssetConfiguration
                    {
                        aspectRatio = "4:1",
                        minHeight = 16,
                        targetWidth = 64,
                        targetHeight = 16,
                        display = true
                    },
                    businessName = new TextAssetConfiguration
                    {
                        minLengthInChar = 0,
                        maxLengthInChar = 25,
                        upper = false,
                        display = true
                    },
                    background = new BackgroundAssetConfiguration { display = true },
                    color = new List<ColorConfiguration>
                    {
                        new ColorConfiguration
                        {
                            elementName = "ShortHeadline",
                            suffix = "",
                            defaultColor = "#616161",
                            display = true
                        },
                        new ColorConfiguration
                        {
                            elementName = "Background",
                            suffix = "",
                            defaultColor = "#FFFFFF",
                            display = true
                        }
                    },
                    assetPropertySettings = new List<RecommendationAssetProperty>()
                    {
                        new RecommendationAssetProperty
                        {
                            assetName = "Background",
                            assetProperty = "Color",
                            value = "#FFFFFF"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName = "ShortHeadline",
                            assetProperty = "Color",
                            value = "#616161"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName = "ShortHeadline",
                            assetProperty = "FontFamily",
                            value = "Segoe UI Bold"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName = "ShortHeadline",
                            assetProperty = "FontWeight",
                            value = "700"
                        }
                    },
                    shortHeadline = new TextAssetConfiguration
                    {
                        minLengthInChar = 0,
                        maxLengthInChar = 30,
                        upper = false,
                        display = true
                    },
                    templateId = "********-0000-0000-0000-000010001001",
                    templateName = "320x50 - Modern minimalist",
                    version = "",
                    width = 320,
                    height = 50,
                    TemplateFileName = "320x50.html",
                    TemplateContent = "<html>\\r\\n<head>\\r\\n  <meta http-equiv=\\\"\"Content-Type\\\"\" content=\\\"\"text/html; charset=utf-8\\\"\">  \\r\\n  <style>\\r\\n   #background {\\r\\n  background: {{BackgroundColor}};\\r\\n  width: 300px;\\r\\n  height: 250px;\\r\\n  position: relative;\\r\\n  overflow: hidden;  \\r\\n}\\r\\n#logo-container {\\r\\n  left: 128px;\\r\\n  top: 19px;\\r\\n  width: 64px;\\r\\n  height: 16px;\\r\\n  position: absolute;\\r\\n}\\r\\n#logo {\\r\\n  width: {{LogoImageResizeWidth}}px;\\r\\n  margin-left: {{LogoImageNegativeMarginX}}px;\\r\\n  margin-top: {{LogoImageNegativeMarginY}}px;\\r\\n}\\r\\n#short-headline {  \\r\\n  color: {{ShortHeadlineColor}};\\r\\n  text-align: center;\\r\\n  font-family: {{ShortHeadlineFontFamily}};\\r\\n  font-size: 18px;\\r\\n  font-weight: {{ShortHeadlineFontWeight}};\\r\\n  left: 24px;\\r\\n  top: 43px;\\r\\n  width: 272px;\\r\\n  height: 32px;\\r\\n  position: absolute;\\r\\n  word-break: break-word;  \\r\\n}\\r\\n#product-image-container {\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  width: 300px;\\r\\n  height: 167px;\\r\\n  position: absolute;\\r\\n  overflow: hidden;\\r\\n}\\r\\n#product-image {\\r\\n  width: {{ImageResizeWidth}}px;\\r\\n  height: {{ImageResizeHeight}}px;\\r\\n  margin-left: {{ImageNegativeMarginX}}px;\\r\\n  margin-top: {{ImageNegativeMarginY}}px;\\r\\n}\\r\\n   </style>\\r\\n  <title>Document</title>\\r\\n</head>\\r\\n<body>\\r\\n  <div id=\\\"\"background\\\"\">\\r\\n    <div id=\\\"\"logo-container\\\"\">\\r\\n      <img id=\\\"\"logo\\\"\" src=\\\"\"{{LogoImageUrl}}\\\"\" />\\r\\n    </div>\\r\\n    <div id=\\\"\"short-headline\\\"\">{{ShortHeadline}}</div>\\r\\n    <div id=\\\"\"product-image-container\\\"\">\\r\\n      <img id=\\\"\"product-image\\\"\" src=\\\"\"{{ImageURL}}\\\"\" />\\r\\n    </div>\\r\\n  </div>  \\r\\n</body>\\r\\n</html>"
                },
                displayAd = new RecommendationAd
                {
                    recommendationId = "fd1a5ae6-6f13-4dc7-aef0-72c17487e280",
                    url = new Uri(baseDevIntegrationTestUri, "300x250 - Modern minimalist-1.2.jpeg").ToString(),
                    template = new RecommendationTemplate
                    {
                        style = 1,
                        templateId = "********-0000-0000-0000-000010001001",
                        version = "1.2",
                        templateName = "300x250 - Modern minimalist",
                        image = new RecommendationImage
                        {
                            url = modernMinimalist_954x500_13,
                            editable = true,
                            imageSize = new RecommendationImageSize { width = 268, height = 140 },
                            cropSetting = new List<RecommendationCropSetting> { new RecommendationCropSetting { sourceX = 0, sourceY = 0, sourceWidth = 268, sourceHeight = 140 } }
                        },
                        shortHeadline = new RecommendationText { text = "Timeless Precision, Redefined", editable = true },
                        longHeadline = new RecommendationText { text = "", editable = false },
                        description = new RecommendationText { text = "Experience precision craftsmanship that stands the test of time, redefined for today.", editable = false },
                        cta = new RecommendationText { text = "", editable = false },
                        businessName = new RecommendationText { text = "TestBusinessName", editable = false },
                        color = new List<RecommendationColor>
                        {
                            new RecommendationColor
                            {
                                elementName = "ShortHeadline",
                                suffix = "",
                                editable = true,
                                color = "#616161"
                            },
                            new RecommendationColor
                            {
                                elementName = "Background",
                                suffix = "",
                                editable = true,
                                color = "#FFFFFF"
                            }
                        },
                        assetProperties = new List<RecommendationAssetProperty>
                        {
                            new RecommendationAssetProperty
                            {
                                assetName= "ShortHeadline",
                                assetProperty= "Color",
                                value= "#616161"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "ShortHeadline",
                                assetProperty= "FontFamily",
                                value= "Segoe UI Bold"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "ShortHeadline",
                                assetProperty= "FontWeight",
                                value= "700"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Background",
                                assetProperty= "Color",
                                value= "#FFFFFF"
                            }
                        }
                    }
                }
            };

            var templateDetail2 = new DisplayAdsTemplateDetail
            {
                templateConfig = new MockDisplayAdsTemplateDefinition
                {
                    style = 1,
                    shortHeadline = new TextAssetConfiguration
                    {
                        minLengthInChar = 0,
                        maxLengthInChar = 30,
                        upper = false,
                        display = true
                    },
                    description = new TextAssetConfiguration
                    {
                        minLengthInChar = 0,
                        maxLengthInChar = 150,
                        upper = false,
                        display = true
                    },
                    image = new ImageAssetConfiguration
                    {
                        aspectRatio = "1.91:1",
                        minHeight = 90,
                        targetWidth = 172,
                        targetHeight = 90,
                        display = true
                    },
                    businessName = new TextAssetConfiguration
                    {
                        minLengthInChar = 0,
                        maxLengthInChar = 25,
                        upper = false,
                        display = true
                    },
                    background = new BackgroundAssetConfiguration { display = true },
                    logo = new ImageAssetConfiguration
                    {
                        aspectRatio = "4:1",
                        minHeight = 16,
                        targetWidth = 64,
                        targetHeight = 16,
                        display = true
                    },
                    color = new List<ColorConfiguration>
                    {
                        new ColorConfiguration
                        {
                            elementName = "ShortHeadline",
                            suffix = "",
                            defaultColor = "#616161",
                            display = true
                        },
                        new ColorConfiguration
                        {
                            elementName = "Description",
                            suffix = "",
                            defaultColor = "#1A1A1A",
                            display = true
                        },
                        new ColorConfiguration
                        {
                            elementName = "Background",
                            suffix = "",
                            defaultColor = "#FFFFFF",
                            display = true
                        }
                    },
                    assetPropertySettings = new List<RecommendationAssetProperty>()
                    {
                        new RecommendationAssetProperty
                        {
                            assetName= "ShortHeadline",
                            assetProperty= "Color",
                            value= "#616161"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "ShortHeadline",
                            assetProperty= "FontFamily",
                            value= "Segoe UI Bold"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "ShortHeadline",
                            assetProperty= "FontWeight",
                            value= "700"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Description",
                            assetProperty= "Color",
                            value= "#1A1A1A"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Description",
                            assetProperty= "FontFamily",
                            value= "Segoe UI Regular"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Description",
                            assetProperty= "FontWeight",
                            value= "400"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Background",
                            assetProperty= "Color",
                            value= "#FFFFFF"
                        }
                    },

                    templateId = "********-0000-0000-0000-000010001002",
                    templateName = "728x90 - Modern minimalist",
                    version = "1.2",
                    width = 728,
                    height = 90,
                    TemplateFileName = "728x90.html",
                    TemplateContent = "<html>\\r\\n<head>\\r\\n  <meta http-equiv=\\\"\"Content-Type\\\"\" content=\\\"\"text/html; charset=utf-8\\\"\">  \\r\\n  <style>\\r\\n   #background {\\r\\n  background: {{BackgroundColor}};\\r\\n  width: 728px;\\r\\n  height: 90px;\\r\\n  position: relative;\\r\\n  overflow: hidden;  \\r\\n}\\r\\n#logo-container {\\r\\n  left: 16px;\\r\\n  top: 29px;\\r\\n  width: 128px;\\r\\n  height: 32px;\\r\\n  position: absolute;\\r\\n}\\r\\n#logo {\\r\\n  width: {{LogoImageResizeWidth}}px;\\r\\n  margin-left: {{LogoImageNegativeMarginX}}px;\\r\\n  margin-top: {{LogoImageNegativeMarginY}}px;\\r\\n}\\r\\n#short-headline {  \\r\\n  color: {{ShortHeadlineColor}};\\r\\n  text-align: center;\\r\\n  font-family: {{ShortHeadlineFontFamily}};\\r\\n  font-size: 27px;\\r\\n  font-weight: {{ShortHeadlineFontWeight}};\\r\\n  left: 160px;\\r\\n  top: 21px;\\r\\n  width: 380px;\\r\\n  height: 48px;\\r\\n  position: absolute;\\r\\n  word-break: break-word;  \\r\\n}\\r\\n#product-image-container {\\r\\n  right: 0;\\r\\n  top: 0;\\r\\n  width: 172px;\\r\\n  height: 90px;\\r\\n  position: absolute;\\r\\n  overflow: hidden;\\r\\n}\\r\\n#product-image {\\r\\n  width: {{ImageResizeWidth}}px;\\r\\n  height: {{ImageResizeHeight}}px;\\r\\n  margin-left: {{ImageNegativeMarginX}}px;\\r\\n  margin-top: {{ImageNegativeMarginY}}px;\\r\\n}\\r\\n   </style>\\r\\n  <title>Document</title>\\r\\n</head>\\r\\n<body>\\r\\n  <div id=\\\"\"background\\\"\">\\r\\n    <div id=\\\"\"logo-container\\\"\">\\r\\n      <img id=\\\"\"logo\\\"\" src=\\\"\"{{LogoImageUrl}}\\\"\" />\\r\\n    </div>\\r\\n    <div id=\\\"\"short-headline\\\"\">{{ShortHeadline}}</div>\\r\\n    <div id=\\\"\"product-image-container\\\"\">\\r\\n      <img id=\\\"\"product-image\\\"\" src=\\\"\"{{ImageURL}}\\\"\" />\\r\\n    </div>\\r\\n  </div>  \\r\\n</body>\\r\\n</html>"
                },
                displayAd = new RecommendationAd
                {
                    recommendationId = "1761aeb1-a60c-42d0-9351-85b9fffbc617",
                    url = new Uri(baseDevIntegrationTestUri, "728x90 - Modern minimalist-1.2.jpeg").ToString(),
                    template = new RecommendationTemplate
                    {
                        style = 1,
                        templateId = "********-0000-0000-0000-000010001002",
                        version = "1.2",
                        templateName = "728x90 - Modern minimalist",
                        image = new RecommendationImage
                        {
                            url = modernMinimalist_954x500_13,
                            editable = true,
                            imageSize = new RecommendationImageSize { width = 268, height = 140 },
                            cropSetting = new List<RecommendationCropSetting> { new RecommendationCropSetting { sourceX = 0, sourceY = 0, sourceWidth = 268, sourceHeight = 140 } }
                        },
                        shortHeadline = new RecommendationText { text = "Timeless Precision, Redefined", editable = false },
                        longHeadline = new RecommendationText { text = "", editable = false },
                        description = new RecommendationText { text = "Experience precision craftsmanship that stands the test of time, redefined for today.", editable = false },
                        cta = new RecommendationText { text = "", editable = false },
                        businessName = new RecommendationText { text = "TestBusinessName", editable = false },
                        color = new List<RecommendationColor>
                        {
                            new RecommendationColor
                            {
                                elementName= "ShortHeadline",
                                suffix= "",
                                editable= true,
                                color= "#616161"
                            },
                            new RecommendationColor
                            {
                                elementName= "Description",
                                suffix= "",
                                editable= true,
                                color= "#1A1A1A"
                            },
                            new RecommendationColor
                            {
                                elementName= "Background",
                                suffix= "",
                                editable= true,
                                color= "#FFFFFF"
                            }
                        },
                        assetProperties = new List<RecommendationAssetProperty>
                        {
                            new RecommendationAssetProperty
                            {
                                assetName= "ShortHeadline",
                                assetProperty= "Color",
                                value= "#616161"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "ShortHeadline",
                                assetProperty= "FontFamily",
                                value= "Segoe UI Bold"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "ShortHeadline",
                                assetProperty= "FontWeight",
                                value= "700"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Description",
                                assetProperty= "Color",
                                value= "#1A1A1A"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Description",
                                assetProperty= "FontFamily",
                                value= "Segoe UI Regular"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Description",
                                assetProperty= "FontWeight",
                                value= "400"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Background",
                                assetProperty= "Color",
                                value= "#FFFFFF"
                            }
                        }
                    }
                }
            };

            var templateDetail3 = new DisplayAdsTemplateDetail
            {
                templateConfig = new MockDisplayAdsTemplateDefinition
                {
                    style = 1,
                    shortHeadline = new TextAssetConfiguration
                    {
                        minLengthInChar = 0,
                        maxLengthInChar = 30,
                        upper = false,
                        display = true
                    },
                    description = new TextAssetConfiguration
                    {
                        minLengthInChar = 0,
                        maxLengthInChar = 150,
                        upper = false,
                        display = true
                    },
                    image = new ImageAssetConfiguration
                    {
                        aspectRatio = "1.91:1",
                        minHeight = 202,
                        targetWidth = 386,
                        targetHeight = 202,
                        display = true
                    },
                    businessName = new TextAssetConfiguration
                    {
                        minLengthInChar = 0,
                        maxLengthInChar = 25,
                        upper = false,
                        display = true
                    },
                    background = new BackgroundAssetConfiguration { display = true },
                    logo = new ImageAssetConfiguration
                    {
                        aspectRatio = "4:1",
                        minHeight = 32,
                        targetWidth = 128,
                        targetHeight = 32,
                        display = true
                    },
                    color = new List<ColorConfiguration>
                    {
                        new ColorConfiguration
                        {
                            elementName = "ShortHeadline",
                            suffix = "",
                            defaultColor = "#616161",
                            display = true
                        },
                        new ColorConfiguration
                        {
                            elementName = "Description",
                            suffix = "",
                            defaultColor = "#1A1A1A",
                            display = true
                        },
                        new ColorConfiguration
                        {
                            elementName = "Background",
                            suffix = "",
                            defaultColor = "#FFFFFF",
                            display = true
                        }
                    },
                    assetPropertySettings = new List<RecommendationAssetProperty>()
                    {
                        new RecommendationAssetProperty
                        {
                            assetName= "ShortHeadline",
                            assetProperty= "Color",
                            value= "#616161"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "ShortHeadline",
                            assetProperty= "FontFamily",
                            value= "Segoe UI Bold"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "ShortHeadline",
                            assetProperty= "FontWeight",
                            value= "700"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Description",
                            assetProperty= "Color",
                            value= "#1A1A1A"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Description",
                            assetProperty= "FontFamily",
                            value= "Segoe UI Regular"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Description",
                            assetProperty= "FontWeight",
                            value= "400"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Background",
                            assetProperty= "Color",
                            value= "#FFFFFF"
                        }
                    },

                    templateId = "********-0000-0000-0000-000010001002",
                    templateName = "970x90 - Modern minimalist",
                    version = "1.2",
                    width = 970,
                    height = 250,
                    TemplateFileName = "970x250.html",
                    TemplateContent = "<html>\\r\\n<head>\\r\\n  <meta http-equiv=\\\"\"Content-Type\\\"\" content=\\\"\"text/html; charset=utf-8\\\"\">  \\r\\n  <style>\\r\\n   #background {\\r\\n  background: {{BackgroundColor}};\\r\\n  width: 970px;\\r\\n  height: 250px;\\r\\n  position: relative;\\r\\n  overflow: hidden;  \\r\\n}\\r\\n#logo-container {\\r\\n  left: 183px;\\r\\n  top: 52px;\\r\\n  width: 128px;\\r\\n  height: 32px;\\r\\n  position: absolute;\\r\\n}\\r\\n#logo {\\r\\n  width: {{LogoImageResizeWidth}}px;\\r\\n  margin-left: {{LogoImageNegativeMarginX}}px;\\r\\n  margin-top: {{LogoImageNegativeMarginY}}px;\\r\\n}\\r\\n#short-headline {  \\r\\n  color: {{ShortHeadlineColor}};\\r\\n  text-align: center;\\r\\n  font-family: {{ShortHeadlineFontFamily}};\\r\\n  font-size: 30px;\\r\\n  font-weight: {{ShortHeadlineFontWeight}};\\r\\n  left: 40px;\\r\\n  top: 92px;\\r\\n  width: 413px;\\r\\n  height: 48px;\\r\\n  position: absolute;\\r\\n  word-break: break-word;  \\r\\n}\\r\\n#description {\\r\\n  color: {{DescriptionColor}};\\r\\n  text-align: center;\\r\\n  font-family: {{DescriptionFontFamily}};\\r\\n  font-size: 16px;\\r\\n  font-weight: {{DescriptionFontWeight}};\\r\\n  left: 40px;\\r\\n  top: 148px;\\r\\n  width: 413px;\\r\\n  height: 50px;\\r\\n  position: absolute;\\r\\n  word-break: break-word;  \\r\\n}\\r\\n#product-image-container {\\r\\n  right: 0;\\r\\n  top: 0;\\r\\n  width: 477px;\\r\\n  height: 250px;\\r\\n  position: absolute;\\r\\n  overflow: hidden;\\r\\n}\\r\\n#product-image {\\r\\n  width: {{ImageResizeWidth}}px;\\r\\n  height: {{ImageResizeHeight}}px;\\r\\n  margin-left: {{ImageNegativeMarginX}}px;\\r\\n  margin-top: {{ImageNegativeMarginY}}px;\\r\\n}\\r\\n   </style>\\r\\n  <title>Document</title>\\r\\n</head>\\r\\n<body>\\r\\n  <div id=\\\"\"background\\\"\">\\r\\n    <div id=\\\"\"logo-container\\\"\">\\r\\n      <img id=\\\"\"logo\\\"\" src=\\\"\"{{LogoImageUrl}}\\\"\" />\\r\\n    </div>\\r\\n    <div id=\\\"\"short-headline\\\"\">{{ShortHeadline}}</div>\\r\\n    <div id=\\\"\"description\\\"\">{{Description}}</div>\\r\\n    <div id=\\\"\"product-image-container\\\"\">\\r\\n      <img id=\\\"\"product-image\\\"\" src=\\\"\"{{ImageURL}}\\\"\" />\\r\\n    </div>\\r\\n  </div>  \\r\\n</body>\\r\\n</html>"
                },
                displayAd = new RecommendationAd
                {
                    recommendationId = "099d99e1-0fff-48db-8493-03f55d3780b9",
                    url = new Uri(baseDevIntegrationTestUri, "970x250 - Modern minimalist-1.2.jpeg").ToString(),
                    template = new RecommendationTemplate
                    {
                        style = 1,
                        templateId = "********-0000-0000-0000-000010001003",
                        version = "1.2",
                        templateName = "970x250 - Modern minimalist",
                        image = new RecommendationImage
                        {
                            url = modernMinimalist_954x500_13,
                            editable = true,
                            imageSize = new RecommendationImageSize { width = 268, height = 140 },
                            cropSetting = new List<RecommendationCropSetting> { new RecommendationCropSetting { sourceX = 0, sourceY = 0, sourceWidth = 268, sourceHeight = 140 } }
                        },
                        shortHeadline = new RecommendationText { text = "Timeless Precision, Redefined", editable = true },
                        longHeadline = new RecommendationText { text = "", editable = false },
                        description = new RecommendationText { text = "Experience precision craftsmanship that stands the test of time, redefined for today.", editable = true },
                        cta = new RecommendationText { text = "", editable = false },
                        businessName = new RecommendationText { text = "TestBusinessName", editable = false },
                        color = new List<RecommendationColor>
                        {
                            new RecommendationColor
                            {
                                elementName= "ShortHeadline",
                                suffix= "",
                                editable= true,
                                color= "#616161"
                            },
                            new RecommendationColor
                            {
                                elementName= "Description",
                                suffix= "",
                                editable= true,
                                color= "#1A1A1A"
                            },
                            new RecommendationColor
                            {
                                elementName= "Background",
                                suffix= "",
                                editable= true,
                                color= "#FFFFFF"
                            }
                        },
                        assetProperties = new List<RecommendationAssetProperty>
                        {
                            new RecommendationAssetProperty
                            {
                                assetName= "ShortHeadline",
                                assetProperty= "Color",
                                value= "#616161"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "ShortHeadline",
                                assetProperty= "FontFamily",
                                value= "Segoe UI Bold"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "ShortHeadline",
                                assetProperty= "FontWeight",
                                value= "700"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Description",
                                assetProperty= "Color",
                                value= "#1A1A1A"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Description",
                                assetProperty= "FontFamily",
                                value= "Segoe UI Regular"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Description",
                                assetProperty= "FontWeight",
                                value= "400"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Background",
                                assetProperty= "Color",
                                value= "#FFFFFF"
                            }
                        }
                    }
                }
            };

            var templateDetail4 = new DisplayAdsTemplateDetail
            {
                templateConfig = new MockDisplayAdsTemplateDefinition
                {
                    style = 1,
                    shortHeadline = new TextAssetConfiguration
                    {
                        minLengthInChar = 0,
                        maxLengthInChar = 30,
                        upper = false,
                        display = true
                    },
                    description = new TextAssetConfiguration
                    {
                        minLengthInChar = 0,
                        maxLengthInChar = 150,
                        upper = false,
                        display = true
                    },
                    image = new ImageAssetConfiguration
                    {
                        aspectRatio = "1:1",
                        minHeight = 202,
                        targetWidth = 386,
                        targetHeight = 202,
                        display = true
                    },
                    businessName = new TextAssetConfiguration
                    {
                        minLengthInChar = 0,
                        maxLengthInChar = 25,
                        upper = false,
                        display = true
                    },
                    background = new BackgroundAssetConfiguration { display = true },
                    logo = new ImageAssetConfiguration
                    {
                        aspectRatio = "4:1",
                        minHeight = 32,
                        targetWidth = 128,
                        targetHeight = 32,
                        display = true
                    },
                    color = new List<ColorConfiguration>
                    {
                        new ColorConfiguration
                        {
                            elementName= "ShortHeadline",
                            suffix= "",
                            defaultColor= "#616161",
                            display= true
                        },
                        new ColorConfiguration
                        {
                            elementName= "Description",
                            suffix= "",
                            defaultColor= "#1A1A1A",
                            display= true
                        },
                        new ColorConfiguration
                        {
                            elementName= "Background",
                            suffix= "",
                            defaultColor= "#FFFFFF",
                            display= true
                        }
                    },
                    assetPropertySettings = new List<RecommendationAssetProperty>()
                    {
                        new RecommendationAssetProperty
                        {
                            assetName= "ShortHeadline",
                            assetProperty= "Color",
                            value= "#616161"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "ShortHeadline",
                            assetProperty= "FontFamily",
                            value= "Segoe UI Bold"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "ShortHeadline",
                            assetProperty= "FontWeight",
                            value= "700"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Description",
                            assetProperty= "Color",
                            value= "#1A1A1A"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Description",
                            assetProperty= "FontFamily",
                            value= "Segoe UI Regular"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Description",
                            assetProperty= "FontWeight",
                            value= "400"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Background",
                            assetProperty= "Color",
                            value= "#FFFFFF"
                        }
                    },
                    templateId = "********-0000-0000-0000-000010001004",
                    templateName = "300x600 - Modern minimalist",
                    version = "1.2",
                    width = 300,
                    height = 600,
                    TemplateFileName = "300x600.html",
                    TemplateContent = "<html>\\r\\n<head>\\r\\n  <meta http-equiv=\\\"\"Content-Type\\\"\" content=\\\"\"text/html; charset=utf-8\\\"\">  \\r\\n  <style>\\r\\n   #background {\\r\\n  background: {{BackgroundColor}};\\r\\n  width: 300px;\\r\\n  height: 600px;\\r\\n  position: relative;\\r\\n  overflow: hidden;  \\r\\n}\\r\\n#logo-container {\\r\\n  left: 86px;\\r\\n  top: 40px;\\r\\n  width: 128px;\\r\\n  height: 32px;\\r\\n  position: absolute;\\r\\n}\\r\\n#logo {\\r\\n  width: {{LogoImageResizeWidth}}px;\\r\\n  margin-left: {{LogoImageNegativeMarginX}}px;\\r\\n  margin-top: {{LogoImageNegativeMarginY}}px;\\r\\n}\\r\\n#short-headline {  \\r\\n  color: {{ShortHeadlineColor}};\\r\\n  text-align: center;\\r\\n  font-family: {{ShortHeadlineFontFamily}};\\r\\n  font-size: 32px;\\r\\n  font-weight: {{ShortHeadlineFontWeight}};\\r\\n  left: 32px;\\r\\n  top: 88px;\\r\\n  width: 236px;\\r\\n  height: 88px;\\r\\n  position: absolute;\\r\\n  word-break: break-word;  \\r\\n}\\r\\n#description {\\r\\n  color: {{DescriptionColor}};\\r\\n  text-align: center;\\r\\n  font-family: {{DescriptionFontFamily}};\\r\\n  font-size: 16px;\\r\\n  font-weight: {{DescriptionFontWeight}};\\r\\n  left: 32px;\\r\\n  top: 192px;\\r\\n  width: 236px;\\r\\n  height: 72px;\\r\\n  position: absolute;\\r\\n  word-break: break-word;  \\r\\n}\\r\\n#product-image-container {\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  width: 300px;\\r\\n  height: 300px;\\r\\n  position: absolute;\\r\\n  overflow: hidden;\\r\\n}\\r\\n#product-image {\\r\\n  width: {{ImageResizeWidth}}px;\\r\\n  height: {{ImageResizeHeight}}px;\\r\\n  margin-left: {{ImageNegativeMarginX}}px;\\r\\n  margin-top: {{ImageNegativeMarginY}}px;\\r\\n}\\r\\n   </style>\\r\\n  <title>Document</title>\\r\\n</head>\\r\\n<body>\\r\\n  <div id=\\\"\"background\\\"\">\\r\\n    <div id=\\\"\"logo-container\\\"\">\\r\\n      <img id=\\\"\"logo\\\"\" src=\\\"\"{{LogoImageUrl}}\\\"\" />\\r\\n    </div>\\r\\n    <div id=\\\"\"short-headline\\\"\">{{ShortHeadline}}</div>\\r\\n    <div id=\\\"\"description\\\"\">{{Description}}</div>\\r\\n    <div id=\\\"\"product-image-container\\\"\">\\r\\n      <img id=\\\"\"product-image\\\"\" src=\\\"\"{{ImageURL}}\\\"\" />\\r\\n    </div>\\r\\n  </div>  \\r\\n</body>\\r\\n</html>"
                },
                displayAd = new RecommendationAd
                {
                    recommendationId = "f946e87e-3621-4483-a93d-3caf6777a585",
                    url = new Uri(baseDevIntegrationTestUri, "300x600 - Modern minimalist-1.2.jpeg").ToString(),
                    template = new RecommendationTemplate
                    {
                        style = 1,
                        templateId = "********-0000-0000-0000-000010001004",
                        version = "1.2",
                        templateName = "300x600 - Modern minimalist",
                        image = new RecommendationImage
                        {
                            url = modernMinimalist_954x500_13,
                            editable = true,
                            imageSize = new RecommendationImageSize { width = 268, height = 140 },
                            cropSetting = new List<RecommendationCropSetting> { new RecommendationCropSetting { sourceX = 64, sourceY = 0, sourceWidth = 140, sourceHeight = 140 } }
                        },
                        shortHeadline = new RecommendationText { text = "Timeless Precision, Redefined", editable = true },
                        longHeadline = new RecommendationText { text = "", editable = false },
                        description = new RecommendationText { text = "Experience precision craftsmanship that stands the test of time, redefined for today.", editable = true },
                        cta = new RecommendationText { text = "", editable = false },
                        businessName = new RecommendationText { text = "TestBusinessName", editable = false },
                        color = new List<RecommendationColor>
                        {
                            new RecommendationColor
                            {
                                elementName= "ShortHeadline",
                                suffix= "",
                                editable= true,
                                color= "#616161"
                            },
                            new RecommendationColor
                            {
                                elementName= "Description",
                                suffix= "",
                                editable= true,
                                color= "#1A1A1A"
                            },
                            new RecommendationColor
                            {
                                elementName= "Background",
                                suffix= "",
                                editable= true,
                                color= "#FFFFFF"
                            }
                        },
                        assetProperties = new List<RecommendationAssetProperty>
                        {
                            new RecommendationAssetProperty
                            {
                                assetName= "ShortHeadline",
                                assetProperty= "Color",
                                value= "#616161"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "ShortHeadline",
                                assetProperty= "FontFamily",
                                value= "Segoe UI Bold"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "ShortHeadline",
                                assetProperty= "FontWeight",
                                value= "700"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Description",
                                assetProperty= "Color",
                                value= "#1A1A1A"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Description",
                                assetProperty= "FontFamily",
                                value= "Segoe UI Regular"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Description",
                                assetProperty= "FontWeight",
                                value= "400"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Background",
                                assetProperty= "Color",
                                value= "#FFFFFF"
                            }
                        }
                    }
                }
            };

            var templateDetail5 = new DisplayAdsTemplateDetail
            {
                templateConfig = new MockDisplayAdsTemplateDefinition
                {
                    style = 1,
                    shortHeadline = new TextAssetConfiguration
                    {
                        minLengthInChar = 0,
                        maxLengthInChar = 30,
                        upper = false,
                        display = true
                    },
                    description = new TextAssetConfiguration
                    {
                        minLengthInChar = 0,
                        maxLengthInChar = 150,
                        upper = false,
                        display = true
                    },
                    image = new ImageAssetConfiguration
                    {
                        aspectRatio = "1:1",
                        minHeight = 136,
                        targetWidth = 136,
                        targetHeight = 136,
                        display = true
                    },
                    businessName = new TextAssetConfiguration
                    {
                        minLengthInChar = 0,
                        maxLengthInChar = 25,
                        upper = false,
                        display = true
                    },
                    background = new BackgroundAssetConfiguration { display = true },
                    logo = new ImageAssetConfiguration
                    {
                        aspectRatio = "4:1",
                        minHeight = 24,
                        targetWidth = 96,
                        targetHeight = 24,
                        display = true
                    },
                    color = new List<ColorConfiguration>
                    {
                        new ColorConfiguration
                        {
                            elementName= "ShortHeadline",
                            suffix= "",
                            defaultColor= "#616161",
                            display= true
                        },
                        new ColorConfiguration
                        {
                            elementName= "Description",
                            suffix= "",
                            defaultColor= "#1A1A1A",
                            display= true
                        },
                        new ColorConfiguration
                        {
                            elementName= "Background",
                            suffix= "",
                            defaultColor= "#FFFFFF",
                            display= true
                        }
                    },
                    assetPropertySettings = new List<RecommendationAssetProperty>()
                    {
                        new RecommendationAssetProperty
                        {
                            assetName= "ShortHeadline",
                            assetProperty= "Color",
                            value= "#616161"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "ShortHeadline",
                            assetProperty= "FontFamily",
                            value= "Segoe UI Bold"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "ShortHeadline",
                            assetProperty= "FontWeight",
                            value= "700"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Description",
                            assetProperty= "Color",
                            value= "#1A1A1A"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Description",
                            assetProperty= "FontFamily",
                            value= "Segoe UI Regular"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Description",
                            assetProperty= "FontWeight",
                            value= "400"
                        },
                        new RecommendationAssetProperty
                        {
                            assetName= "Background",
                            assetProperty= "Color",
                            value= "#FFFFFF"
                        }
                    },
                    templateId = "********-0000-0000-0000-000010001005",
                    templateName = "160x600 - Modern minimalist",
                    version = "1.2",
                    width = 160,
                    height = 600,
                    TemplateFileName = "160x600.html",
                    TemplateContent = "\"<html>\\r\\n<head>\\r\\n  <meta http-equiv=\\\"\"Content-Type\\\"\" content=\\\"\"text/html; charset=utf-8\\\"\">  \\r\\n  <style>\\r\\n   #background {\\r\\n  background: {{BackgroundColor}};\\r\\n  width: 160px;\\r\\n  height: 600px;\\r\\n  position: relative;\\r\\n  overflow: hidden;  \\r\\n}\\r\\n#logo-container {\\r\\n  left: 16px;\\r\\n  top: 68px;\\r\\n  width: 128px;\\r\\n  height: 32px;\\r\\n  position: absolute;\\r\\n}\\r\\n#logo {\\r\\n  width: {{LogoImageResizeWidth}}px;\\r\\n  margin-left: {{LogoImageNegativeMarginX}}px;\\r\\n  margin-top: {{LogoImageNegativeMarginY}}px;\\r\\n}\\r\\n#short-headline {  \\r\\n  color: {{ShortHeadlineColor}};\\r\\n  text-align: center;\\r\\n  font-family: {{ShortHeadlineFontFamily}};\\r\\n  font-size: 24px;\\r\\n  font-weight: {{ShortHeadlineFontWeight}};\\r\\n  left: 12px;\\r\\n  top: 140px;\\r\\n  width: 136px;\\r\\n  height: 128px;\\r\\n  position: absolute;\\r\\n  word-break: break-word;\\r\\n}\\r\\n#description {\\r\\n  color: {{DescriptionColor}};\\r\\n  text-align: center;\\r\\n  font-family: {{DescriptionFontFamily}};\\r\\n  font-size: 12px;\\r\\n  font-weight: {{DescriptionFontWeight}};\\r\\n  left: 12px;\\r\\n  top: 284px;\\r\\n  width: 136px;\\r\\n  height: 88px;\\r\\n  position: absolute;\\r\\n  word-break: break-word;  \\r\\n}\\r\\n#product-image-container {\\r\\n  right: 0;\\r\\n  bottom: 0;\\r\\n  width: 160px;\\r\\n  height: 160px;\\r\\n  position: absolute;\\r\\n  overflow: hidden;\\r\\n}\\r\\n#product-image {\\r\\n  width: {{ImageResizeWidth}}px;\\r\\n  margin-left: {{ImageNegativeMarginX}}px;\\r\\n  margin-top: {{ImageNegativeMarginY}}px;\\r\\n}\\r\\n   </style>\\r\\n  <title>Document</title>\\r\\n</head>\\r\\n<body>\\r\\n  <div id=\\\"\"background\\\"\">\\r\\n    <div id=\\\"\"logo-container\\\"\">\\r\\n      <img id=\\\"\"logo\\\"\" src=\\\"\"{{LogoImageUrl}}\\\"\" />\\r\\n    </div>\\r\\n    <div id=\\\"\"short-headline\\\"\">{{ShortHeadline}}</div>\\r\\n    <div id=\\\"\"description\\\"\">{{Description}}</div>\\r\\n    <div id=\\\"\"product-image-container\\\"\">\\r\\n      <img id=\\\"\"product-image\\\"\" src=\\\"\"{{ImageURL}}\\\"\" />\\r\\n    </div>\\r\\n  </div>  \\r\\n</body>\\r\\n</html>"
                },
                displayAd = new RecommendationAd
                {
                    recommendationId = "ab08f5bf-8534-4b56-a1b9-b2cc0afecac2",
                    url = new Uri(baseDevIntegrationTestUri, "160x600 - Modern minimalist-1.2.jpeg").ToString(),
                    template = new RecommendationTemplate
                    {
                        style = 1,
                        templateId = "********-0000-0000-0000-000010001005",
                        version = "1.2",
                        templateName = "160x600 - Modern minimalist",
                        image = new RecommendationImage
                        {
                            url = modernMinimalist_954x500_13,
                            editable = true,
                            imageSize = new RecommendationImageSize { width = 268, height = 140 },
                            cropSetting = new List<RecommendationCropSetting> { new RecommendationCropSetting { sourceX = 64, sourceY = 0, sourceWidth = 140, sourceHeight = 140 } }
                        },
                        shortHeadline = new RecommendationText { text = "Timeless Precision, Redefined", editable = true },
                        longHeadline = new RecommendationText { text = "", editable = false },
                        description = new RecommendationText { text = "Experience precision craftsmanship that stands the test of time, redefined for today.", editable = true },
                        cta = new RecommendationText { text = "", editable = false },
                        businessName = new RecommendationText { text = "TestBusinessName", editable = false },
                        color = new List<RecommendationColor>
                        {
                            new RecommendationColor
                            {
                                elementName= "ShortHeadline",
                                suffix= "",
                                editable= true,
                                color= "#616161"
                            },
                            new RecommendationColor
                            {
                                elementName= "Description",
                                suffix= "",
                                editable= true,
                                color= "#1A1A1A"
                            },
                            new RecommendationColor
                            {
                                elementName= "Background",
                                suffix= "",
                                editable= true,
                                color= "#FFFFFF"
                            }
                        },
                        assetProperties = new List<RecommendationAssetProperty>
                        {
                            new RecommendationAssetProperty
                            {
                                assetName= "ShortHeadline",
                                assetProperty= "Color",
                                value= "#616161"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "ShortHeadline",
                                assetProperty= "FontFamily",
                                value= "Segoe UI Bold"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "ShortHeadline",
                                assetProperty= "FontWeight",
                                value= "700"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Description",
                                assetProperty= "Color",
                                value= "#1A1A1A"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Description",
                                assetProperty= "FontFamily",
                                value= "Segoe UI Regular"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Description",
                                assetProperty= "FontWeight",
                                value= "400"
                            },
                            new RecommendationAssetProperty
                            {
                                assetName= "Background",
                                assetProperty= "Color",
                                value= "#FFFFFF"
                            }
                        }
                    }
                }
            };

            var responseJSON = new
            {
                data = new
                {
                    code = 200,
                    details = new[]
                    {
                        new
                        {
                            templateGroupId = "********-0000-0000-0001-********0001",
                            version = "1.2",
                            templateGroupName = "Modern minimalist",
                            templateCount = 6,
                            displayAdsTemplateDetails = new[]
                            {
                                templateDetail0,
                                templateDetail1,
                                templateDetail2,
                                templateDetail3,
                                templateDetail4,
                                templateDetail5
                            }
                        }
                    }
                }
            };         

            return Ok(responseJSON);
        }
    }
}
