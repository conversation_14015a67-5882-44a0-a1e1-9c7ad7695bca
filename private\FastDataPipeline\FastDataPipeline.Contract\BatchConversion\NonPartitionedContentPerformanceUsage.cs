namespace FastDataPipeline.Contract.BatchConversion
{
    using ProtoBuf;
    using System;

    [ClickhouseCsvContract(schema: "BICH", columnList: new[] { nameof(DateKey), nameof(HourNum), nameof(AccountId), nameof(OrderId), nameof(PropertyUrl), nameof(DistributionChannelId),
        "(t) => {return t.LoadTime.ToString(\"yyyy-MM-dd HH:mm:ss\");}", nameof(ImpressionCnt), nameof(ClickCnt), nameof(ConversionCnt),
        nameof(TotalPosition), nameof(TotalAmount), nameof(AssistCnt), nameof(AdvertiserReportedRevenue), nameof(ExtendedCost),
        nameof(DeviceOSId), nameof(DeviceTypeId), nameof(MatchTypeId), nameof(BiddedMatchTypeId), nameof(DeviceOSId2), nameof(CampaignId), nameof(NetworkId),
        nameof(FullConversionCnt), nameof(FullAdvertiserReportedRevenue), nameof(TargetValueId), nameof(TargetTypeId), nameof(FullViewConversionCnt),
        nameof(TotalConversionCnt), nameof(FullViewAdvertiserReportedRevenue), nameof(ConversionCredit), nameof(FullConversionCredit), nameof(FullViewConversionCredit),
        nameof(TotalConversionCredit), nameof(AdvertiserReportedRevenueAdjustment), nameof(FullAdvertiserReportedRevenueAdjustment), nameof(FullViewAdvertiserReportedRevenueAdjustment), nameof(WebsiteCountry), nameof(AdLanguage),
        nameof(MobileAppName), nameof(MobileAppBundle), nameof(MobileAppStoreUrl), nameof(Downloads), nameof(FirstLaunches), nameof(Purchases), nameof(Subscriptions) },
        Header = "DateKey,HourNum,AccountId,OrderId,PropertyUrl,DistributionChannelId,LoadTime,ImpressionCnt,ClickCnt,ConversionCnt,TotalPosition,TotalAmount,AssistCnt,AdvertiserReportedRevenue,ExtendedCost,DeviceOSId,DeviceTypeId,MatchTypeId,BiddedMatchTypeId,DeviceOSId2,CampaignId,NetworkId,FullConversionCnt,FullAdvertiserReportedRevenue,TargetValueId,TargetTypeId,FullViewConversionCnt,TotalConversionCnt,FullViewAdvertiserReportedRevenue,ConversionCredit,FullConversionCredit,FullViewConversionCredit,TotalConversionCredit,AdvertiserReportedRevenueAdjustment,FullAdvertiserReportedRevenueAdjustment,FullViewAdvertiserReportedRevenueAdjustment,WebsiteCountry,AdLanguage,AppName,AppBundle,AppStoreUrl,Downloads,FirstLaunches,Purchases,Subscriptions")]

    [ProtoContract]
    public partial class NonPartitionedContentPerformanceUsage
    {
        [ProtoMember(1)]
        [DbColumnName("DATEKEY")]
        public Int32? DateKey { get; set; }

        [ProtoMember(2)]
        [DbColumnName("HOURNUM")]
        public SByte? HourNum { get; set; }

        [ProtoMember(3)]
        [DbColumnName("ACCOUNTID")]
        public Int32? AccountId { get; set; }

        [ProtoMember(4)]
        [DbColumnName("ORDERID")]
        [AcceptableDsvType(typeof(UInt64?))]
        public Int64? OrderId { get; set; }

        [ProtoMember(5)]
        [DbColumnName("PROPERTYURL")]
        public String PropertyUrl { get; set; }

        [ProtoMember(6)]
        [DbColumnName("DISTRIBUTIONCHANNELID")]
        public Int16? DistributionChannelId { get; set; }

        [ProtoMember(7)]
        [DbColumnName("LOADTIME")]
        [DateTimeFormatString("yyyy-MM-dd HH:mm:ss")]
        public DateTime LoadTime { get; set; }

        [ProtoMember(8)]
        [DbColumnName("IMPRESSIONCNT")]
        public Int32 ImpressionCnt { get; set; }

        [ProtoMember(9)]
        [DbColumnName("CLICKCNT")]
        public Int32 ClickCnt { get; set; }

        [ProtoMember(10)]
        [DbColumnName("CONVERSIONCNT")]
        public Int32 ConversionCnt { get; set; }

        [ProtoMember(11)]
        [DbColumnName("TOTALPOSITION")]
        public Int32 TotalPosition { get; set; }

        [ProtoMember(12)]
        [DbColumnName("TOTALAMOUNT")]
        public Decimal TotalAmount { get; set; }

        [ProtoMember(13)]
        [DbColumnName("ASSISTCNT")]
        public Int32 AssistCnt { get; set; }

        [ProtoMember(14)]
        [DbColumnName("ADVERTISERREPORTEDREVENUE")]
        public Decimal AdvertiserReportedRevenue { get; set; }

        [ProtoMember(15)]
        [DbColumnName("EXTENDEDCOST")]
        public Decimal ExtendedCost { get; set; }

        [ProtoMember(16)]
        [DbColumnName("DEVICEOSID")]
        public Int32? DeviceOSId { get; set; }

        [ProtoMember(17)]
        [DbColumnName("DEVICETYPEID")]
        public SByte? DeviceTypeId { get; set; }

        [ProtoMember(18)]
        [DbColumnName("MATCHTYPEID")]
        public SByte? MatchTypeId { get; set; }

        [ProtoMember(19)]
        [DbColumnName("BIDDEDMATCHTYPEID")]
        public SByte? BiddedMatchTypeId { get; set; }

        [ProtoMember(20)]
        [DbColumnName("DEVICEOSID2")]
        public Int32? DeviceOSId2 { get; set; }

        [ProtoMember(21)]
        [DbColumnName("CAMPAIGNID")]
        public UInt64? CampaignId { get; set; }

        [ProtoMember(22)]
        [DbColumnName("NETWORKID")]
        public SByte? NetworkId { get; set; }

        public Int32? CustomerId { get; set; }

        [ProtoMember(23)]
        [DbColumnName("FULLCONVERSIONCNT")]
        public Int32 FullConversionCnt { get; set; }

        [ProtoMember(24)]
        [DbColumnName("FULLADVERTISERREPORTEDREVENUE")]
        public Decimal FullAdvertiserReportedRevenue { get; set; }

        [ProtoMember(25)]
        [DbColumnName("TargetValueId")]
        [AcceptableDsvType(typeof(Int32))]
        public Int64 TargetValueId { get; set; }

        [ProtoMember(26)]
        [DbColumnName("TargetTypeId")]
        public Byte TargetTypeId { get; set; }

        [ProtoMember(27)]
        [DbColumnName("FULLVIEWCONVERSIONCNT")]
        public Int32 FullViewConversionCnt { get; set; }

        [ProtoMember(28)]
        [DbColumnName("TOTALCONVERSIONCNT")]
        public Int32 TotalConversionCnt { get; set; }

        [ProtoMember(29)]
        [DbColumnName("FullViewAdvertiserReportedRevenue")]
        public Decimal FullViewAdvertiserReportedRevenue { get; set; }

        [ProtoMember(30)]
        [DbColumnName("ConversionCredit")]
        public Decimal ConversionCredit { get; set; }

        [ProtoMember(31)]
        [DbColumnName("FullConversionCredit")]
        public Decimal FullConversionCredit { get; set; }

        [ProtoMember(32)]
        [DbColumnName("FullViewConversionCredit")]
        public Decimal FullViewConversionCredit { get; set; }

        [ProtoMember(33)]
        [DbColumnName("TotalConversionCredit")]
        public Decimal TotalConversionCredit { get; set; }

        [ProtoMember(34)]
        [DbColumnName("WebsiteCountry")]
        public String WebsiteCountry { get; set; }

        [ProtoMember(35)]
        [DbColumnName("AdLanguage")]
        public String AdLanguage { get; set; }

        [ProtoMember(36)]
        [DbColumnName("AdvertiserReportedRevenueAdjustment")]
        public Decimal AdvertiserReportedRevenueAdjustment { get; set; }

        [ProtoMember(37)]
        [DbColumnName("FullAdvertiserReportedRevenueAdjustment")]
        public Decimal FullAdvertiserReportedRevenueAdjustment { get; set; }

        [ProtoMember(38)]
        [DbColumnName("FullViewAdvertiserReportedRevenueAdjustment")]
        public Decimal FullViewAdvertiserReportedRevenueAdjustment { get; set; }

        [ProtoMember(39)]
        [DbColumnName("AppName")]
        public String MobileAppName { get; set; }

        [ProtoMember(40)]
        [DbColumnName("AppBundle")]
        public String MobileAppBundle { get; set; }

        [ProtoMember(41)]
        [DbColumnName("AppStoreUrl")]
        public String MobileAppStoreUrl { get; set; }

        [ProtoMember(42)]
        [DbColumnName("Downloads")]
        public Decimal Downloads { get; set; }

        [ProtoMember(43)]
        [DbColumnName("FirstLaunches")]
        public Decimal FirstLaunches { get; set; }

        [ProtoMember(44)]
        [DbColumnName("Purchases")]
        public Decimal Purchases { get; set; }

        [ProtoMember(45)]
        [DbColumnName("Subscriptions")]
        public Decimal Subscriptions { get; set; }
    }
}
