﻿namespace Microsoft.Advertising.Advertiser.Api.V2.BrandKit
{
    using CampaignMiddleTierTest.Framework;
    using CampaignMiddleTierTest.Framework.Utilities;
    using Kusto.Cloud.Platform.Utils;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;    
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Newtonsoft.Json;
    using System.Collections;
    using System.Collections.Generic;
    using System.Configuration;
    using System.Net;
    using System.Net.Http;
    using System.Text;
    using static CampaignTest.Framework.Constants.Constants;

    [TestClass]
    public class DisplayAdsRecommendationTests : CampaignTestBase
    {
        private static readonly CustomerInfo customer = CustomerInfo.CreateStandardAdvertiserWithPilot(Features.BrandKit);
        private readonly CustomerInfo customerWithoutBrandKit = CampaignTestBase.DefaultCustomer;

        private static string ToJson(object obj)
        {
            return JsonConvert.SerializeObject(obj, Formatting.Indented, new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore,
                DefaultValueHandling = DefaultValueHandling.Include, // Include will deserialize Mock=false. Otherwise, it is omitted from the request body
                Converters = new List<JsonConverter> {
               new Newtonsoft.Json.Converters.StringEnumConverter()
           }
            }).Replace("odatatype", "@odata.type");
        }

        private dynamic BuildBrandKit()
        {
            var imageIds = ResponsiveAdTestHelper.CreateImages(customer, customer.AccountIds[0], true /* hasGenericImage */);
            var brandKit = new
            {
                Name = "BrandKitTest1",
                Images = new ArrayList()
                {
                    new
                    {
                        Id = imageIds[0],
                        Url = ImageHelper.GetImage(customer, imageIds[0]).Url,
                        CropHeight = 128,
                        CropWidth = 128,
                        CropX = 0,
                        CropY = 0
                    },
                    new
                    {
                        Id = imageIds[0],
                        Url = ImageHelper.GetImage(customer, imageIds[0]).Url,
                        CropHeight = 200,
                        CropWidth = 200,
                        CropX = 200,
                        CropY = 200
                    }
                },
                SquareLogos = new ArrayList()
                {
                    new
                    {
                        Id = imageIds[1],
                        Url = ImageHelper.GetImage(customer, imageIds[1]).Url,
                        CropHeight = 128,
                        CropWidth = 128,
                        CropX = 0,
                        CropY = 0
                    },
                    new
                    {
                        Id = imageIds[1],
                        Url = ImageHelper.GetImage(customer, imageIds[1]).Url,
                        CropHeight = 50,
                        CropWidth = 50,
                        CropX = 50,
                        CropY = 50
                    }
                },
                LandscapeLogos = new ArrayList()
                {
                    new
                    {
                        Id = imageIds[2],
                        Url = ImageHelper.GetImage(customer, imageIds[2]).Url,
                        CropHeight = 128,
                        CropWidth = 512,
                        CropX = 0,
                        CropY = 0
                    },
                    new
                    {
                        Id = imageIds[2],
                        Url = ImageHelper.GetImage(customer, imageIds[2]).Url,
                        CropHeight = 40,
                        CropWidth = 20,
                        CropX = 40,
                        CropY = 20
                    }
                },
                Palettes = new ArrayList()
                {
                    new
                    {
                        ColorType = "Primary",
                        Colors = new []
                        {
                            new
                            {
                                Name = "TestBrandKitColorTesting1",
                                HexCode = "#FFFFFF"
                            },
                            new
                            {
                                Name = "TestBrandKitColorTesting2",
                                HexCode = "#000000"
                            }
                        }
                    },
                    new
                    {
                        ColorType = "Secondary",
                        Colors = new []
                        {
                            new
                            {
                                Name = "TestBrandKitColorTesting3",
                                HexCode = "#FFFFFF"
                            },
                            new
                            {
                                Name = "TestBrandKitColorTesting4",
                                HexCode = "#000000"
                            }
                        }
                    }
                },
                Fonts = new ArrayList()
                {
                    new
                    {
                        Typeface = "Arial",
                        Weight = "Normal",
                        TextAssetType = FontTextAssetType.ShortHeadline
                    },
                    new
                    {
                        Typeface = "Times New Roman",
                        Weight = "Normal",
                        TextAssetType = FontTextAssetType.LongHeadline
                    }
                },
                BrandVoice = new 
                {
                    Personality = "Professional, friendly, and helpful voice that explains technical concepts in an accessible way.",
                    Tones = new[] { "Informative", "Educational", "Approachable" }
                }
            };
            return brandKit;
        }

        [TestMethod]
        [Priority(2)]
        public void CreateDisplayAdsRecommendations_WithBrandKit()
        {
            string baseUrl = ApiVersion.BaseUrl;
            var url = string.Format(
                baseUrl + "/Customers({0})/Accounts({1})/Default.CreateDisplayAdsRecommendation",
                customer.CustomerId, customer.AccountIds[0]);            

            var payload = new
            {
                Url = "http://www.microsoft.com/",
                Mock = false,
                LanguageCode = "en",
                Count = 1,
                TemplateVersion = "1.2",
                BrandKit = BuildBrandKit()
            };

            var jsonPayload = ToJson(payload);

            var resultJson = ApiHelper.CallApi(
                customer,
                c =>
                    c.SendAsync(new HttpRequestMessage(HttpMethod.Post, url)
                    {
                        Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json")
                    }),
                e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode),
                null);

            Assert.IsFalse(string.IsNullOrEmpty(resultJson.data.ToString()), "resultJson.data is null or empty");
            Assert.IsTrue(resultJson.data.assets.images[0].cropSetting != null || resultJson.data.assets.images[0].cropSetting.Count != 0, "cropSettings missing");
            Assert.IsTrue(resultJson.data.ads[0].template.image.imageSize != null, "ad.imageSize missing");
            Assert.IsTrue(resultJson.data.ads[0].template.assetProperties != null || resultJson.data.ads[0].template.assetProperties.Count != 0, "assetProperties missing");

            // Look for CTAWithoutText properties across all ad rec's returned, expect at least one ad to have them
            bool foundCTAWithoutTextColor = false;
            bool foundCTAWithoutTextBackgroundColor = false;         

            for (int i = 0; i < resultJson.data.ads.Count; i++)
            {
                var ad = resultJson.data.ads[i];
                
                if (ad?.template?.assetProperties == null) continue;

                // Check each asset property
                foreach (var ap in ad.template.assetProperties)
                {
                    if (ap.assetName == "CTAWithoutText" && ap.assetProperty == "Color" && ap.value == "#FFFFFF")
                    {
                        foundCTAWithoutTextColor = true;
                    }

                    if (ap.assetName == "CTAWithoutText" && ap.assetProperty == "BackgroundColor" && ap.value == "#000000")
                    {
                        foundCTAWithoutTextBackgroundColor = true;
                    }
                }
            }

            Assert.IsTrue(foundCTAWithoutTextColor, "CTAWithoutText Color with value #FFFFFF not found in any ad");
            Assert.IsTrue(foundCTAWithoutTextBackgroundColor, "CTAWithoutText BackgroundColor with value #000000 not found in any ad");
        }

        [TestMethod]
        [Priority(2)]        
        public void CreateDisplayAdsRecommendations_WithBrandKit_NoPilotWithTemplateVersion()        {
            string baseUrl = ApiVersion.BaseUrl;
            var url = string.Format(
                baseUrl + "/Customers({0})/Accounts({1})/Default.CreateDisplayAdsRecommendation",
                customerWithoutBrandKit.CustomerId, customerWithoutBrandKit.AccountIds[0]);
            var payload = new
            {
                Url = "http://www.microsoft.com/",
                Mock = false,
                LanguageCode = "en",
                Count = 1,
                TemplateVersion = "1.2",
                BrandKit = BuildBrandKit()
            };
            var jsonPayload = ToJson(payload);
            var resultJson = ApiHelper.CallApi(
                customerWithoutBrandKit,
                c =>
                    c.SendAsync(new HttpRequestMessage(HttpMethod.Post, url)
                    {
                        Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json")
                    }),
                e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode),
                null);

            Assert.IsFalse(string.IsNullOrEmpty(resultJson.data.ToString()), "resultJson.data is null or empty");
            Assert.IsTrue(resultJson.data.assets.images[0].cropSetting != null || resultJson.data.assets.images[0].cropSetting.Count != 0, "cropSettings missing");
            Assert.IsTrue(resultJson.data.ads[0].template.image.imageSize != null, "ad.imageSize missing");
            Assert.IsTrue(resultJson.data.ads[0].template.assetProperties != null || resultJson.data.ads[0].template.assetProperties.Count != 0, "assetProperties missing");
        }

        [TestMethod]
        [Priority(2)]
        [DataRow(null)] //tests null BK business name case
        [DataRow("TestBusinessName")]
        public void CreateDisplayAdsRecommendations_WithBrandKit_BusinessName(string businessName)
        {
            string baseUrl = ApiVersion.BaseUrl;
            var url = string.Format(
                baseUrl + "/Customers({0})/Accounts({1})/Default.CreateDisplayAdsRecommendation",
                customerWithoutBrandKit.CustomerId, customerWithoutBrandKit.AccountIds[0]);
            var brandKit = BuildBrandKit();
            var brandKitWithBusinessName = new
            {
                Name = "BrandKitTest1",
                brandKit.Images,
                brandKit.SquareLogos,
                brandKit.LandscapeLogos,
                brandKit.Palettes,
                brandKit.Fonts,
                BusinessName = businessName
            };
            var payload = new
            {
                Url = "http://www.microsoft.com/",
                Mock = false,
                LanguageCode = "en",
                Count = 1,
                TemplateVersion = "1.2",
                BrandKit = brandKitWithBusinessName
            };
            var jsonPayload = ToJson(payload);

            var resultJson = ApiHelper.CallApi(
                customerWithoutBrandKit,
                c =>
                    c.SendAsync(new HttpRequestMessage(HttpMethod.Post, url)
                    {
                        Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json")
                    }),
                e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode),
                null);

            Assert.IsFalse(string.IsNullOrEmpty(resultJson.data.ToString()), "resultJson.data is null or empty");
            Assert.IsTrue(resultJson.data.assets.images[0].cropSetting != null || resultJson.data.assets.images[0].cropSetting.Count != 0, "cropSettings missing");
            Assert.IsTrue(resultJson.data.ads[0].template.image.imageSize != null, "ad.imageSize missing");
            Assert.IsTrue(resultJson.data.assets != null || resultJson.data.assets.images != null, "assets missing");
            Assert.IsTrue(resultJson.data.ads[0].template.assetProperties != null || resultJson.data.ads[0].template.assetProperties.Count != 0, "assetProperties missing");

            if (businessName == null)
            {
                // For null case, verify the API handles it without errors
                // In SI, if a business name is recommended from the URL, should be set in assets and template
                Logger.Info($"Null business name test case handled successfully");
            }
            else
            {
                string assetsBusinessNameRec = resultJson.data.assets?.businessName?.text;
                Assert.IsNotNull(assetsBusinessNameRec, "BK Business Name recommendation in assets should not be null");

                bool foundInTemplate = false;
                foreach (var ad in resultJson.data.ads)
                {
                    string templateBusinessName = ad?.template?.businessName?.text;
                    if (templateBusinessName.IsNotNullOrEmpty() || templateBusinessName == "")  //In SI, may not be in every ad but property exists w/ empty string
                    {
                        foundInTemplate = true;                        
                        if (templateBusinessName != "")
                        {
                            Assert.AreEqual(assetsBusinessNameRec, templateBusinessName, 
                                $"BK Business Name recommendation in assets ({assetsBusinessNameRec}) does not match the one in template ({templateBusinessName})");
                        }
                        break;
                    }
                }
                Assert.IsTrue(foundInTemplate, $"BK Business Name property was not found in any template");
            }
        }

        [TestMethod]
        [Priority(2)]
        [DataRow(false)] //tests null BK brand voice case
        [DataRow(true)]
        public void CreateDisplayAdsRecommendations_WithBrandKit_BrandVoice_CIOnly(bool useVoice)
        {
            string baseUrl = ApiVersion.BaseUrl;
            var url = string.Format(
                baseUrl + "/Customers({0})/Accounts({1})/Default.CreateDisplayAdsRecommendation",
                customerWithoutBrandKit.CustomerId, customerWithoutBrandKit.AccountIds[0]);
            var brandKit = BuildBrandKit();
            var brandKitWithBrandVoice = new
            {
                Name = "BrandKitTest1",
                brandKit.Images,
                brandKit.SquareLogos,
                brandKit.LandscapeLogos,
                brandKit.Palettes,
                brandKit.Fonts,
                BrandVoice = useVoice ? brandKit.BrandVoice : null
            };
            var payload = new
            {
                Url = "http://www.microsoft.com/",
                Mock = false,
                LanguageCode = "en",
                Count = 1,
                TemplateVersion = "1.2",
                BrandKit = brandKitWithBrandVoice
            };
            var jsonPayload = ToJson(payload);

            var resultJson = ApiHelper.CallApi(
                customerWithoutBrandKit,
                c =>
                    c.SendAsync(new HttpRequestMessage(HttpMethod.Post, url)
                    {
                        Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json")
                    }),
                e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode),
                null);

            if (!useVoice)
            {
                // For null case, verify the API handles it without errors
                Logger.Info($"Null brand voice test case handled successfully");
            }
            else
            {
                Assert.IsNotNull(resultJson.data.assets?.longHeadlines, "Display Ad Recommendation longHeadlines should not be null");
                Assert.AreEqual(2, resultJson.data.assets.longHeadlines.Count);
                Assert.AreEqual("Your new favorite Thai restaurant",
                    resultJson.data.assets.longHeadlines[0].text.ToString(), "Display Ad Recommendation longHeadlines should take BrandVoice into account");
            }
        }

        [TestMethod]
        [Priority(2)]
        public void CreateDisplayAdsRecommendations()
        {
            string baseUrl = ApiVersion.BaseUrl;
            var url = string.Format(
                baseUrl + "/Customers({0})/Accounts({1})/Default.CreateDisplayAdsRecommendation",
                customerWithoutBrandKit.CustomerId, customerWithoutBrandKit.AccountIds[0]);

            var payload = new
            {
                Url = "http://www.microsoft.com/",
                Mock = true,
                LanguageCode = "en",
                Count = 1
            };

            var jsonPayload = ToJson(payload);

            var resultJson = ApiHelper.CallApi(
                customerWithoutBrandKit,
                c =>
                    c.SendAsync(new HttpRequestMessage(HttpMethod.Post, url)
                    {
                        Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json")
                    }),
                e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode),
                null);

            Assert.IsFalse(string.IsNullOrEmpty(resultJson.data.ToString()), "resultJson.data is null or empty");
            Assert.IsTrue(resultJson.data?.ads[0].template?.assetProperties == null || resultJson.data.ads[0].template.assetProperties.Count == 0); // Responses without BrandKit do not have assetProperties  
        }

        [TestMethod]
        [Priority(2)]
        public void CreateDisplayAdsRecommendations_UpdatedVersion_NoBrandKit_CIOnly()
        {
            string baseUrl = ApiVersion.BaseUrl;
            var url = string.Format(
                baseUrl + "/Customers({0})/Accounts({1})/Default.CreateDisplayAdsRecommendation",
                customerWithoutBrandKit.CustomerId, customerWithoutBrandKit.AccountIds[0]);

            var payload = new
            {
                Url = "http://www.microsoft.com/",
                Mock = false,
                LanguageCode = "en",
                Count = 1,
                TemplateVersion = "1.3"
            };

            var jsonPayload = ToJson(payload);

            var resultJson = ApiHelper.CallApi(
                customerWithoutBrandKit,
                c =>
                    c.SendAsync(new HttpRequestMessage(HttpMethod.Post, url)
                    {
                        Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json")
                    }),
                e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode),
                null);

            Assert.IsFalse(string.IsNullOrEmpty(resultJson.data.ToString()), "resultJson.data is null or empty");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetDisplayAdsTemplateGroupDetail_Locale_CIOnly()
        {
            var url = string.Format(
                ApiVersion.BaseUrl + "/Customers({0})/Accounts({1})/Default.GetDisplayAdsTemplateGroupDetail",
                customer.CustomerId, customer.AccountIds[0]);
            var payload = new
            {
                data = new
                {
                    templateGroupId = new List<string> { "********-0000-0000-0001-********0001" },
                    version = "1.2",
                }
            };
            int ES_ES = 3082;
            var jsonPayload = ToJson(payload);
            var request = new HttpRequestMessage(HttpMethod.Post, url)
            {
                Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json")
            };
            var resultJson = ApiHelper.CallApi(
                customer,
                c =>
                    c.SendAsync(request),
                e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode),
                null, false, null, false, false, $"{ES_ES}");
            Assert.IsTrue(resultJson.data.details[0].templateGroupName == "Moderno minimalista", "locale was not set to ES_ES");

        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ODataTests)]
        public void GetDisplayAdsTemplateGroups_Locale_CIOnly()
        {
            var url = string.Format(
                ApiVersion.BaseUrl + "/Customers({0})/Accounts({1})/Default.GetDisplayAdsTemplateGroups",
                customer.CustomerId, customer.AccountIds[0]);
            var payload = new
            {
                data = new
                {
                    version = "1.2"
                }
            };
            int ES_ES = 3082;
            var jsonPayload = ToJson(payload);
            var request = new HttpRequestMessage(HttpMethod.Post, url)
            {
                Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json")
            };
            var resultJson = ApiHelper.CallApi(
                customer,
                c =>
                    c.SendAsync(request),
                e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode),
                null, false, null, false, false, $"{ES_ES}");
            Assert.IsTrue(resultJson.data.displayAdsTemplateGroup[0].templateGroupName == "Moderno minimalista", "locale was not set to ES_ES");
        }

        [TestMethod]
        [Priority(2)]
        [Owner(TestOwners.ODataTests)]        
        public void EditDisplayAdsRecommendation_WithAssetProperites_OK()
        {
            var url = string.Format(
                ApiVersion.BaseUrl + "/Customers({0})/Accounts({1})/Default.EditDisplayAdsRecommendation",
                customer.CustomerId, customer.AccountIds[0]);

            bool siEnabled = ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI");

            var payload = new
            {
                data = new
                {
                    Mock = true,
                    ads = new[] {
                        new
                        {
                             recommendationId = "6b837c30-bb7b-4847-a789-290c92310f59",
                             url = "https://aggsvcstoragedev.z13.web.core.windows.net/integration-test/locale/en-US/320x50 - Modern minimalist-1.3.jpeg",
                             template = new
                             {
                                 style = 1,
                                 templateId = "********-0000-0000-0000-************",
                                 version = "1.3",
                                 templateName = "320x50 - Modern minimalist",
                                 image = new
                                 {
                                     url = "https://aggsvcstoragedev.z13.web.core.windows.net/integration-test/ProdImage-954x500-Modern minimalist-1.3.jpg",
                                     editable = true,
                                     imageSize = new
                                     {
                                         width = 268,
                                         height = 140
                                     },
                                     cropSetting = new[]
                                     {
                                         new
                                         {
                                             sourceX = 0,
                                             sourceY = 0,
                                             sourceWidth = 268,
                                             sourceHeight = 140
                                         }
                                     }
                                 },
                                 shortHeadline = new
                                 {
                                     text = "Timeless Precision, Redefined",
                                     editable = false,
                                 },
                                 longHeadline = new
                                 {
                                     text = "",
                                     editable = false
                                 },
                                 description = new
                                 {
                                     text = "Experience precision craftsmanship that stands the test of time, redefined for today.",
                                     editable = false
                                 },
                                 cta = new
                                 {
                                     text = "",
                                     editable = false
                                 },
                                 businessName = new
                                 {
                                     text = "TestBusinessName",
                                     editable = true
                                 },
                                 color = new []
                                 {
                                     new
                                     {
                                         elementName = "Background",
                                         suffix = "",
                                         editable = true,
                                         color = "#FFFFFF"
                                     }
                                 },
                                 assetProperties = new []
                                 {
                                     new
                                     {
                                     assetName = "Background",
                                     assetProperty = "Color",
                                     value = "#FFFFFF"
                                     },
                                     new
                                     {
                                         assetName = "CTAWithoutText",
                                         assetProperty = "Color",
                                         value = "#FFFFFF"
                                     },
                                     new
                                     {
                                         assetName = "CTAWithoutText",
                                         assetProperty = "BackgroundColor",
                                         value = "#000000"
                                     },
                                 }
                             }
                         },
                        new
                        {
                            recommendationId = "fd1a5ae6-6f13-4dc7-aef0-72c17487e280",
                            url = "https://aggsvcstoragedev.z13.web.core.windows.net/integration-test/locale/en-US/320x50 - Modern minimalist-1.3.jpeg",
                            template = new
                            {
                                style = 1,
                                templateId = "********-0000-0000-0000-000010001001",
                                version = "1.3",
                                templateName = "320x50 - Modern minimalist",
                                image = new
                                {
                                    url = "https://aggsvcstoragedev.z13.web.core.windows.net/integration-test/ProdImage-954x500-Modern minimalist-1.3.jpg",
                                    editable = true,
                                    imageSize = new
                                    {
                                        width = 268,
                                        height = 140
                                    },
                                    cropSetting = new []
                                    {
                                        new
                                        {
                                            sourceX = 0,
                                            sourceY = 0,
                                            sourceWidth = 268,
                                            sourceHeight = 140
                                        }
                                    }
                                },
                                shortHeadline = new
                                {
                                    text = "Timeless Precision, Redefined",
                                    editable = false,
                                },
                                longHeadline = new
                                {
                                    text = "",
                                    editable = false
                                },
                                description = new
                                {
                                    text = "Experience precision craftsmanship that stands the test of time, redefined for today.",
                                    editable = false
                                },
                                cta = new
                                {
                                    text = "",
                                    editable = false
                                },
                                businessName = new
                                {
                                    text = "TestBusinessName",
                                    editable = true
                                },
                                color = new[]
                                {
                                    new
                                    {
                                        elementName = "ShortHeadline",
                                        suffix = "",
                                        editable = true,
                                        color = "#616161"
                                    },
                                    new
                                    {
                                        elementName = "Background",
                                        suffix = "",
                                        editable = true,
                                        color = "#FFFFFF"
                                    }
                                },
                                assetProperties = new[]
                                {
                                    new
                                    {
                                        assetName = "ShortHeadline",
                                        assetProperty = "Color",
                                        value = "#616161"
                                    },
                                    new
                                    {
                                        assetName = "ShortHeadline",
                                        assetProperty = "FontFamily",
                                        value = "Segoe UI Bold"
                                    },
                                    new
                                    {
                                        assetName = "ShortHeadline",
                                        assetProperty = "FontWeight",
                                        value = "700"
                                    },
                                    new
                                    {
                                        assetName = "Background",
                                        assetProperty = "Color",
                                        value = "#FFFFFF"
                                    },
                                    new
                                    {
                                        assetName = "CTAWithoutText",
                                        assetProperty = "Color",
                                        value = "#FFFFFF"
                                    },
                                    new
                                    {
                                        assetName = "CTAWithoutText",
                                        assetProperty = "BackgroundColor",
                                        value = "#000000"
                                    },
                                }
                            }
                        }
                    }
                }
            };
            var jsonPayload = ToJson(payload);

            var resultJson = ApiHelper.CallApi(
                customer,
                c =>
                    c.SendAsync(new HttpRequestMessage(HttpMethod.Post, url)
                    {
                        Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json")
                    }),
                e => Assert.AreEqual(HttpStatusCode.OK, e.StatusCode),
                null);

            Assert.IsFalse(string.IsNullOrEmpty(resultJson.data.ToString()), "resultJson.Data is null or empty");

            if (!siEnabled)
            {
                foreach (var ad in payload?.data?.ads)
                {
                    Assert.IsTrue(resultJson.data.ToString().Contains(ad.template.templateId), $"Template ID ({ad.template.templateId}) expected to be included");
                    Assert.IsTrue(resultJson.data.ToString().Contains(ad.template.templateName), $"Template Name ({ad.template.templateName}) expected to be included");
                    Assert.IsTrue(resultJson.data.ToString().Contains(ad.template.version), $"Template Version ({ad.template.version}) expected to be included");
                    Assert.IsTrue(resultJson.data.ToString().Contains(ad.template.businessName?.text), $"Business Name ({ad.template.businessName?.text}) expected to be included");
                    Assert.IsTrue(resultJson.data.ads[0].template.assetProperties != null || resultJson.data.ads[0].template.assetProperties.Count != 0, "assetProperties missing");

                    // assert assetProperties has expected fields
                    foreach (var assetProperty in ad.template.assetProperties)
                    {
                        Assert.IsTrue(resultJson.data.ToString().Contains(assetProperty.assetName), $"Asset Name ({assetProperty.assetName}) expected to be included");
                        Assert.IsTrue(resultJson.data.ToString().Contains(assetProperty.assetProperty), $"Asset Property ({assetProperty.assetProperty}) expected to be included");
                        Assert.IsTrue(resultJson.data.ToString().Contains(assetProperty.value), $"Asset Value ({assetProperty.value}) expected to be included");
                    }
                }
                
                bool foundCTAWithoutTextColor = false;
                bool foundCTAWithoutTextBackgroundColor = false;

                for (int i = 0; i < resultJson.data.ads.Count; i++)
                {
                    var ad = resultJson.data.ads[i];

                    // Skip if assetProperties is null
                    if (ad?.template?.assetProperties == null) continue;

                    foreach (var ap in ad.template.assetProperties)
                    {
                        if (ap.assetName == "CTAWithoutText" && ap.assetProperty == "Color" && ap.value == "#FFFFFF")
                        {
                            foundCTAWithoutTextColor = true;
                        }

                        if (ap.assetName == "CTAWithoutText" && ap.assetProperty == "BackgroundColor" && ap.value == "#000000")
                        {
                            foundCTAWithoutTextBackgroundColor = true;
                        }
                    }
                }
                
                Assert.IsTrue(foundCTAWithoutTextColor, "CTAWithoutText Color with value #FFFFFF not found in any ad");
                Assert.IsTrue(foundCTAWithoutTextBackgroundColor, "CTAWithoutText BackgroundColor with value #000000 not found in any ad");
            }        
        }

    }
}
