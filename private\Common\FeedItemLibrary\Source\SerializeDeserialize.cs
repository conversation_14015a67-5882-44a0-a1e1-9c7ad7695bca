﻿namespace Microsoft.BingAds.Common.FeedItemLibrary
{
    using Microsoft.AdCenter.Shared.Logging;
    using ProtoBuf;
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Threading;
    using System.Threading.Tasks;

    public static class SerializeDeserialize
    {
        public static int RetryCount = 3;
        private static readonly int FeedItemIdProtoMemberTag = 1;
        private static readonly int ContentProtoMemberTag = 2;

        /// <summary>
        /// Serialize FeedIems to stream
        /// </summary>
        public static void SerializeToStream(Stream stream, List<FeedItemContent> feedItemContents, ILogCommon logger, CancellationToken cancellationToken = default)
        {
            try
            {
#pragma warning disable CS0618 // Type or member is obsolete
                using (var writer = ProtoWriter.Create(stream, null, null))
                {
                    foreach (var feedItemContent in feedItemContents)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        ProtoWriter.WriteFieldHeader(1, WireType.StartGroup, writer);
                        var token = ProtoWriter.StartSubItem(feedItemContent, writer);

                        ProtoWriter.WriteFieldHeader(FeedItemIdProtoMemberTag, WireType.Variant, writer);
                        ProtoWriter.WriteInt64(feedItemContent.FeedItemId, writer);

                        ProtoWriter.WriteFieldHeader(ContentProtoMemberTag, WireType.String, writer);
                        ProtoWriter.WriteString(feedItemContent.Content, writer);

                        ProtoWriter.EndSubItem(token, writer);
                    }

                    writer.Close();
                }
#pragma warning restore CS0618 // Type or member is obsolete

                    stream.Flush();
            }
            catch (Exception ex)
            {
                logger.LogException(ex);
                throw;
            }
        }

        /// <summary>
        /// Deserialize FeedItems from stream
        /// </summary>
        /// <param name="selectedFeedItemIds">Optional parameter if only a selected FeedItemIds are requested.</param>
        public static Dictionary<long, FeedItemContent> DeserializeFromStream(Stream stream, ILogCommon logger, List<long> selectedFeedItemIds = null, CancellationToken cancellationToken = default)
        {
            var feedItemHashSet = (selectedFeedItemIds == null) ? null : new HashSet<long>(selectedFeedItemIds);
            return DeserializeFromStream(stream, logger, feedItemHashSet, cancellationToken);
        }

        public static Dictionary<long, FeedItemContent> DeserializeFromStream(Stream stream, ILogCommon logger, HashSet<long> feedItemHashSet, CancellationToken cancellationToken = default)
        {
            var feedItemContents = new Dictionary<long, FeedItemContent>();
            var retriesRemaining = RetryCount;
            while (retriesRemaining > 0)
            {
                try
                {
                    feedItemContents = new Dictionary<long, FeedItemContent>();
#pragma warning disable CS0618 // Type or member is obsolete
                    using (var reader = ProtoReader.Create(stream, null, null))
                    {
                        while (reader.ReadFieldHeader() > 0 && (feedItemHashSet == null || feedItemContents.Count < feedItemHashSet.Count))
                        {
                            cancellationToken.ThrowIfCancellationRequested();

                            var token = ProtoReader.StartSubItem(reader);

                            reader.ReadFieldHeader();
                            var feedItemId = reader.ReadInt64();
                            
                            reader.ReadFieldHeader();
                            if (feedItemHashSet == null || feedItemHashSet.Contains(feedItemId))
                            {
                                var feedItemContent = new FeedItemContent();
                                feedItemContent.FeedItemId = feedItemId;
                                feedItemContent.Content = reader.ReadString();
                                feedItemContents.Add(feedItemId, feedItemContent);
                            }
                            else
                            {
                                reader.SkipField();
                            }

                            reader.ReadFieldHeader();
                            ProtoReader.EndSubItem(token, reader);
                        }
                    }
#pragma warning restore CS0618 // Type or member is obsolete

                    return feedItemContents;
                }
                catch (TaskCanceledException)
                {
                    // Don't log anything for task cancellation exception.
                }
                catch (Exception ex)
                {
                    logger.LogException(ex);
                    retriesRemaining--;
                    if (retriesRemaining <= 0)
                    {
                        throw;
                    }
                }
            }
            return feedItemContents;
        }
    }
}
