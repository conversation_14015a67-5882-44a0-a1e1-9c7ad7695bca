﻿namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.EO
{
    using System.Collections.Generic;
    using System.Linq;
    using Common;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ClientCenter;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.DAO;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities;
    using Microsoft.Advertiser.CampaignManagement.BusinessRules.Entities;
    using Microsoft.Advertiser.CampaignManagement.BusinessRules.SharedEntityValidators;
    using Microsoft.Advertiser.CampaignManagement.BusinessRules.SharedEntityValidators.Specialized;
    using Microsoft.Advertiser.CampaignManagement.BusinessRules.SharedListItemValidators;

    public class AddSharedEntityEO : IAddSharedEntityEO
    {
        private readonly IAddSharedEntityDAO addSharedEntityDao;
        private readonly IGetSharedEntitiesByAccountIdDAO getListsDao;
        private readonly ISystemLimitDao systemLimitDao;
        private readonly ICheckPilotFlag pilotChecker;

        public AddSharedEntityEO(
            IAddSharedEntityDAO addSharedEntityDao,
            IGetSharedEntitiesByAccountIdDAO getListsDao,
            ISystemLimitDao systemLimitDao,
            ICheckPilotFlag pilotChecker)
        {
            this.addSharedEntityDao = addSharedEntityDao;
            this.getListsDao = getListsDao;
            this.systemLimitDao = systemLimitDao;
            this.pilotChecker = pilotChecker;
        }

        public AddSharedEntityResult AddSharedEntity(BasicCallContext context, SharedEntity sharedEntity, List<SharedListItem> listItems, bool isSharedEntityCustomerLevel)
        {
            var addSharedEntityResult = new AddSharedEntityResult
            {
                BatchResult = new BatchResult<long>()
            };

            if (isSharedEntityCustomerLevel && !Pilot.Instance.IsEnabledForManagerAccountSharedWebsiteExclusions(context.Logger, context.Request))
            {
                addSharedEntityResult.BatchResult.AddError(CampaignManagementErrorCode.NotInPilotForManagerAccountSharedWebsiteExclusions);
                return addSharedEntityResult;
            }

            RequestContextValidator.ValidateRequestContext(addSharedEntityResult.BatchResult, context, isSharedEntityCustomerLevel);

            if (addSharedEntityResult.BatchResult.HasOperationErrors)
            {
                return addSharedEntityResult;
            }

            if (sharedEntity != null && sharedEntity.IsBrandSafety != null && sharedEntity.IsBrandSafety.Value)
            {
                RequestContextValidator brandSafetyValidator = new RequestContextValidator();
                brandSafetyValidator.ValidateUserforIsBrandSafety(addSharedEntityResult.BatchResult, context);
                if (addSharedEntityResult.BatchResult.HasOperationErrors)
                {
                    return addSharedEntityResult;
                }
            }

            var sharedEntityType = ValidateSharedEntity(addSharedEntityResult.BatchResult, sharedEntity, isSharedEntityCustomerLevel, context.AccountId);

            CheckPilotFlag(pilotChecker, context, addSharedEntityResult, sharedEntityType, sharedEntity);

            if (addSharedEntityResult.BatchResult.HasOperationErrors)
            {
                return addSharedEntityResult;
            }

            bool allowMobileAppBuddleIdExclusion =
                (sharedEntityType.Equals(nameof(PlacementExclusionList)) || sharedEntityType.Equals(nameof(AccountPlacementExclusionList)))
                && DynamicConfigValues.AllowMobileAppBundleIdExclusion;

            if (Pilot.Instance.IsUrlTrimmingEnabledForWebsiteExclusionLists() && (sharedEntityType.Equals(nameof(PlacementExclusionList)) || sharedEntityType.Equals(nameof(AccountPlacementExclusionList))))
            {
                PlacementExclusionListItemPreprocessor.TrimTrailingListItemWhitespace(listItems);
            }

            var allowYahooExclusionInSWF = pilotChecker.IsCustomerEnabledForAllowYahooExclusionInSWF(context.Logger, context.Request);
            ValidateSharedListItems(addSharedEntityResult.BatchResult, sharedEntityType, listItems, allowMobileAppBuddleIdExclusion, allowYahooExclusionInSWF);

            if (addSharedEntityResult.BatchResult.HasOperationErrors)
            {
                return addSharedEntityResult;
            }

            if (!sharedEntityType.Equals(nameof(BrandList)) && !sharedEntityType.Equals(nameof(AccountPlacementExclusionList)))
            {
                ValidateWithExistingEntities(context, addSharedEntityResult.BatchResult, sharedEntityType, sharedEntity);
            }

            if (addSharedEntityResult.BatchResult.HasOperationErrors)
            {
                return addSharedEntityResult;
            }

            CallDao(context, addSharedEntityResult, sharedEntityType, isSharedEntityCustomerLevel, sharedEntity, listItems);

            return addSharedEntityResult;
        }

        private static void CheckPilotFlag(ICheckPilotFlag pilotChecker, BasicCallContext context, AddSharedEntityResult addSharedEntityResult, string sharedEntityType, SharedEntity entity)
        {
            bool pilotFlag = false;
            switch (sharedEntityType)
            {
                case nameof(AccountPlacementExclusionList):
                {
                    pilotFlag = ((AccountPlacementExclusionList)entity).IsInclusion
                            ? pilotChecker.IsAccountEnabledForAccountPlacementInclusionLists(context.Logger, context.Request)
                            : pilotChecker.IsAccountEnabledForAccountPlacementExclusionLists(context.Logger, context.Request);
                    break;
                }
                case nameof(AccountNegativeKeywordList):
                {
                    pilotFlag = pilotChecker.IsAccountEnabledForAccountNegativeKeywordList(context.Logger, context.Request);
                    break;
                }
                default:
                    pilotFlag = true;
                    break;
            }

            if (!pilotFlag)
            {
                addSharedEntityResult.BatchResult.AddError(CampaignManagementErrorCode.SharedEntityInvalidType);
            }
        }

        private string ValidateSharedEntity(BatchResult<long> result, SharedEntity sharedEntity, bool isSharedEntityCustomerLevel, long? accountId)
        {
            SharedEntityValidationResult sharedEntityValidationResult = SharedEntityValidatorMiddleTier.ValidateAddSharedEntities(
                new List<ISharedEntity>() { sharedEntity },
                isSharedEntityCustomerLevel);

            BatchResult<long> listBatchResult = new BatchResult<long>();
            listBatchResult.ImportBusinessErrors(sharedEntityValidationResult.Errors);

            // Merging errors copies batch errors as well. As we want to move batch errors as operation errors, 
            // dont merge but copy operation errors separately.
            if (listBatchResult.HasOperationErrors)
            {
                result.AddErrors(listBatchResult.Errors);
            }

            if (listBatchResult.HasBatchErrors)
            {
                result.AddErrors(listBatchResult.BatchErrors[0]); //Move the batch errors for the list up to operation errors.
            }

            return sharedEntityValidationResult.SharedEntityType;
        }

        private void ValidateSharedListItems(BatchResult<long> result, string sharedEntityType, List<SharedListItem> listItems, bool allowMobileAppBundleIdExclusion, bool allowYahooExclusionInSWF)
        {
            var businessItems = listItems?.Cast<ISharedListItem>().ToList();
            var itemValidationResult = SharedListItemValidatorMiddleTier.ValidateSharedListItemsListAdd(businessItems, sharedEntityType, disableOutlookMsn: DynamicConfigValues.DisableOutlookMsnNegativeSite, isBingAllowedForSiteExclusion: DynamicConfigValues.IsBingAllowedForSiteExclusion, allowMobileAppBundleIdExclusion: allowMobileAppBundleIdExclusion, allowYahooExclusionInSWF: allowYahooExclusionInSWF);
            result.ImportBusinessErrors(itemValidationResult.Errors);
        }

        private void ValidateWithExistingEntities(BasicCallContext context, BatchResult<long> result, string sharedEntityType, SharedEntity sharedEntity)
        {
            if (sharedEntityType.Equals(nameof(PlacementExclusionList)))
            {
                return; // PlacementExclusionLists currently rely on DB to check limits
            }

            AccountCallContext accountCallContext = new AccountCallContext(context.Logger, context.Request, context.AccountId.Value);
            BatchResult<int> limitResponse = new BatchResult<int>();
            if (sharedEntityType.Equals(nameof(AccountNegativeKeywordList)))
            {
                limitResponse = this.systemLimitDao.GetSystemLimits(accountCallContext, new List<SystemLimitTypes> { SystemLimitTypes.AccountNegativeKeywordListsPerAccount });
            }
            else
            {
                limitResponse = this.systemLimitDao.GetSystemLimits(accountCallContext, new List<SystemLimitTypes> { SystemLimitTypes.NegativeKeywordListPerAccount });
            }
            
            if (limitResponse.Failed)
            {
                result.AddErrors(limitResponse.Errors);
                if (limitResponse.BatchErrors.TryGetValue(0, out List<CampaignManagementErrorDetail> batchErrors))
                {
                    result.AddErrors(batchErrors);
                }

                return;
            }

            var existingListsResponse = this.getListsDao.GetSharedEntitiesWithoutAssociationCountsByAccountId(accountCallContext, sharedEntity.Type);
            if (existingListsResponse.Failed)
            {
                result.AddErrors(existingListsResponse.Errors);
                return;
            }

            if (existingListsResponse.Entities.Count + 1 > limitResponse.Entities[0])
            {
                result.AddError(CampaignManagementErrorCode.SharedEntityLimitExceeded);
                return;
            }

            var checkResult = new BatchResult<AddUpdateResult>();
            UpdateSharedEntityEO.CheckForDuplicateNames(
                existingListsResponse.Entities,
                checkResult,
                new List<LineItemWrapper<SharedEntity>> { new LineItemWrapper<SharedEntity> { LineItem = sharedEntity, LineItemId = 0 } });

            if (checkResult.BatchErrors.TryGetValue(0, out List<CampaignManagementErrorDetail> checkErrors))
            {
                result.AddErrors(checkErrors);
                return;
            }
        }

        private void CallDao(BasicCallContext context, AddSharedEntityResult result, string sharedEntityType, bool isSharedEntityCustomerLevel, SharedEntity sharedEntity, List<SharedListItem> listItems)
        {
            AddSharedEntityResult sharedEntityResult = addSharedEntityDao.AddSharedEntityAndListItems(context, result.BatchResult, sharedEntityType, isSharedEntityCustomerLevel, sharedEntity, listItems);

            result.SharedEntityId = sharedEntityResult.SharedEntityId;
            result.LastModifiedTime = sharedEntityResult.LastModifiedTime;
            result.BatchResult.Merge(sharedEntityResult.BatchResult);
        }
    }
}
