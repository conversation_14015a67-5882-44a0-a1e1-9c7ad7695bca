﻿using OrderManagementSystem.Common;
using OrderManagementSystem.Entities;
using OrderManagementSystem.Entities.Internal;
using OrderManagementSystem.Requests;
using System.Collections.Concurrent;

namespace OrderManagementSystem.DataAccessObjects
{
    public class CachedMediaPlanDao : IMediaPlanDao
    {
        private readonly ConcurrentDictionary<MediaPlanCacheKey, Result<MediaPlan>> _cache;
        private MediaPlanDao _dao;

        public CachedMediaPlanDao(IProcDaoExecutor procDaoExecutor, IProductDao productDao, IMediaPlanCommitHelper mediaPlanCommitHelper)
        {
            this._cache = new ConcurrentDictionary<MediaPlanCacheKey, Result<MediaPlan>>();
            this._dao = new MediaPlanDao(procDaoExecutor, productDao, mediaPlanCommitHelper);
        }

        public async Task<Result<MediaPlan>> Post(MediaPlanPostRequest request)
        {
            return await _dao.Post(request);
        }

        public async Task<Result<MediaPlan[]>> Get(MediaPlanGetAllRequest request)
        {
            return await _dao.Get(request);
        }

        public async Task<Result<MediaPlan>> Get(MediaPlanGetRequest request)
        {
            return await Get(request, false);
        }

        public async Task<Result<MediaPlan[]>> Get(MediaPlanGetMultipleRequest request)
        {
            return await _dao.Get(request);
        }

        public async Task<Result<MediaPlanCommitOperation>> Get(MediaPlanCommitGetRequest request)
        {
            return await _dao.Get(request);
        }

        public async Task<Result> Put(MediaPlanPutRequest request)
        {
            var cacheKey = new MediaPlanCacheKey(request.MediaPlan.CustomerId, request.MediaPlan.Id);

            var result = await _dao.Put(request);
            if (result.Succeeded)
            {
                var cacheResult = new Result<MediaPlan>();
                cacheResult.Merge(result);
                cacheResult.Entity = request.MediaPlan;
                _cache[cacheKey] = cacheResult;
            }

            return result;
        }

        public async Task<Result> Delete(MediaPlanDeleteRequest request)
        {
            return await _dao.Delete(request);
        }

        public async Task<Result> Commit(XandrCommitRequest request)
        {
            return await _dao.Commit(request);
        }

        public async Task<Result<long>> TrackCommit(MediaPlanCommitUpsertRequest request)
        {
            return await _dao.TrackCommit(request);
        }

        public async Task<Result<MediaPlanCommitTask>> GetTask(MediaPlanCommitGetTaskRequest request)
        {
            return await _dao.GetTask(request);
        }

        public async Task<Result<MediaPlan>> Get(MediaPlanGetRequest request, bool forceRefresh = false)
        {
            var cacheKey = new MediaPlanCacheKey(request.CustomerId, request.Id);
            if (!forceRefresh && _cache.ContainsKey(cacheKey))
            {
                return _cache[cacheKey];
            }

            var result = await _dao.Get(request);
            if (result.Succeeded && result.Entity != null)
            {
                _cache[cacheKey] = result;
            }
            return result;
        }

        public Task<Result<MediaPlan[]>> Get(MediaPlanGetAllForUserRequest request)
        {
            // No caching needed
            return _dao.Get(request);
        }

        class MediaPlanCacheKey
        {
            public int CustomerId { get; }
            public long MediaPlanId { get; }

            public MediaPlanCacheKey(int customerId, long mediaPlanId)
            {
                CustomerId = customerId;
                MediaPlanId = mediaPlanId;
            }

            public override bool Equals(object? obj)
            {
                return obj is MediaPlanCacheKey key &&
                       CustomerId == key.CustomerId &&
                       MediaPlanId == key.MediaPlanId;
            }

            public override int GetHashCode()
            {
                return HashCode.Combine(CustomerId, MediaPlanId);
            }
        }
    }
}
