﻿using FastDataPipeline.ClickhouseSub;
using FastDataPipeline.Contract.FastBI;
using FastDataPipeline.CosmosPub;
using FastDataPipeline.HDFSPub;
using FastDataPipeline.DB;
using FastDataPipeline.DBSub;
using FastDataPipeline.DWC;
using FastDataPipeline.EventQueue;
using Microsoft.Azure.Management.ContainerService.Models;


namespace FastDataPipeline.JobFactory
{
    partial class JobFactory
    {
        // Download FastBI streaming data from cosmos and send to BI DB
        // Upstream: Ads Monetization DRI <<EMAIL>>
        // Downstream: BingAds BI Datamart DRI <<EMAIL>>
        private async Task CreateFastBIPipeline(OperationCounter pubCounter, OperationCounter subCounter, CancellationToken cancellationToken)
        {
            // EventQueue
            var fastBIPubEventQueue = new PubEventQueueV1("FastBIPubEvent", 6 * 12 /* cache 6 hour data */, storageProvider.GetEventTable(), storageProvider.GetEventContainer());
            eventQueues.Add(fastBIPubEventQueue);

            // Container
            containers.Add(CreateFastBIPublisher(fastBIPubEventQueue, pubCounter));
            //containers.Add(CreateFastBIHDFSPublisher(fastBIPubEventQueue, pubCounter));
            containers.Add(CreateFastBIAdGroup2Subscriber(fastBIPubEventQueue, subCounter));
            containers.Add(CreateFastBIAccount2Subscriber(fastBIPubEventQueue, subCounter));
            containers.Add(CreateFastBIVerticalSubscriber(fastBIPubEventQueue, subCounter));

            containers.Add(await CreateFastBIClickhouseAccountSubscriber(fastBIPubEventQueue, pubCounter, cancellationToken));
            containers.Add(await CreateFastBIClickhouseAdGroupSubscriber(fastBIPubEventQueue, pubCounter, cancellationToken));
        }

        private JobContainer CreateFastBIPublisher(PubEventQueueV1 eventQueue, OperationCounter operationCounter)
        {
            int bufferSize = -1; //-1 means no boundedCapacity, if the buffer size is too small, we pay big spin cost when add into BlockingCollection. when BlockingCollection is empty, we pay big spin cost again when dequeue, need to move to a no spin framework.

            // slit count and reader count for different size of articles
            var smallOptions = new HDFSPubOperationOptions { SplitCount = 1, ReaderCount = 32, BufferCapacity = bufferSize, ReturnNewPosition = true, LineReaderType = LineReaderType.FastBI, BlobBlockSize = 512 * 1024, CompressionBlockSize = 16 * 1024, BlobTimeoutInSeconds = 10 };
            var midOptions = new HDFSPubOperationOptions { SplitCount = 2, ReaderCount = 16, BufferCapacity = bufferSize, ReturnNewPosition = true, LineReaderType = LineReaderType.FastBI, BlobBlockSize = 512 * 1024, CompressionBlockSize = 16 * 1024, BlobTimeoutInSeconds = 10 };
            var largeOptions = new HDFSPubOperationOptions { SplitCount = 4, ReaderCount = 16, BufferCapacity = bufferSize, ReturnNewPosition = true, LineReaderType = LineReaderType.FastBI, BlobBlockSize = 512 * 1024, CompressionBlockSize = 16 * 1024, BlobTimeoutInSeconds = 10 };

            var hdfsClient = HDFSClientManager.GetHDFSClientInstanceOrDefault(Config.DefaultHDFSClientName);
            

            var operations = new List<HDFSPubOperation>
            {
                // AdGroup
                new HDFSPubMultipleOutputOperation<AdUsage>(hdfsClient, Article.AdUsage, dsvTableName: "AdUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAdUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 8)
                    .AttachOutput(Article.Delta_InProgressAdUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 8),

                new HDFSPubMultipleOutputOperation<AdFeedUsage>(hdfsClient, Article.AdFeedUsage, dsvTableName: "AdFeedUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressFeedItemUsage, t => GetBIAdGroupFeedItemPartitionIds(t.CustomerId, t.AccountId, t.FeedItemId), 8)
                    .AttachOutput(Article.Delta_InProgressFeedItemUsage_Clickhouse, t => GetBIClickhouseAdGroupFeedItemPartitionIds(t.CustomerId, t.AccountId, t.FeedItemId), 8),

                new HDFSPubMultipleOutputOperation<GenderAgeUsage>(hdfsClient, Article.GenderAgeUsage, dsvTableName: "GenderAgeUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressGenderAgeUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressGenderAgeUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<HourlyAdAdExtensionClickTypeUsage>(hdfsClient, Article.HourlyAdAdExtensionClickTypeUsage, dsvTableName: "HourlyAdAdExtensionClickTypeUsage", options : largeOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAdAdExtensionClickTypeUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressAdAdExtensionClickTypeUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<HourlyElementAdUsage>(hdfsClient, Article.HourlyElementAdUsage, dsvTableName: "HourlyElementAdUsage", options : largeOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressElementAdUsageByOrder, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 8)
                    .AttachOutput(Article.Delta_InProgressElementAdUsageByOrder_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 8),

                new HDFSPubMultipleOutputOperation<HourlyElementOrderItemUsage>(hdfsClient, Article.HourlyElementOrderItemUsage, dsvTableName: "HourlyElementOrderItemUsage", options: midOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressElementOrderItemUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressElementOrderItemUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<HourlyOrderItemAdExtensionClickTypeUsage>(hdfsClient, Article.HourlyOrderItemAdExtensionClickTypeUsage, dsvTableName: "HourlyOrderItemAdExtensionClickTypeUsage", options : largeOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressOrderItemAdExtensionClickTypeUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressOrderItemAdExtensionClickTypeUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<LocationHourUsage>(hdfsClient, Article.LocationHourUsage, dsvTableName: "LocationHourUsage", options: largeOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressLocationHourUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 6)
                    .AttachOutput(Article.Delta_InProgressLocationHourUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 6),

                // SubOrderItemUsage_NoIS is a sub fact for OrderItemUsage with limited dimension columns, to improve performance for Campaign UI calls
                new HDFSPubMultipleOutputOperation<OrderItemUsage>(hdfsClient, Article.OrderItemUsage, dsvTableName: "OrderItemUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressOrderItemUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressSubOrderItemUsage_NoIS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressOrderItemUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressSubOrderItemUsage_NoIS_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<OrderTargetUsage>(hdfsClient, Article.OrderTargetUsage, dsvTableName: "OrderTargetUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressOrderTargetUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressOrderTargetUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<OrderUsage>(hdfsClient, Article.OrderUsage, dsvTableName: "OrderUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressOrderUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressOrderUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<ProfessionalDemographicsUsage>(hdfsClient, Article.ProfessionalDemographicsUsage, dsvTableName: "ProfessionalDemographicsUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressProfessionalDemographicsUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressProfessionalDemographicsUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<PublisherPlacementUsage>(hdfsClient, Article.PublisherPlacementUsage, dsvTableName: "PublisherPlacementUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressPublisherPlacementUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressPublisherPlacementUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<ContentPerformanceUsage>(hdfsClient, Article.ContentPerformanceUsage, dsvTableName: "ContentPerformanceUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressContentPerformanceUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressContentPerformanceUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<RadiusTargetedLocationHourUsage>(hdfsClient, Article.RadiusTargetedLocationHourUsage, dsvTableName: "RadiusTargetedLocationHourUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressRadiusTargetedLocationHourUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressRadiusTargetedLocationHourUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<AutoTargetUsage>(hdfsClient, Article.AutoTargetUsage, dsvTableName: "AutoTargetUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAutoTargetUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressAutoTargetUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<AdLandingPageUrlUsage>(hdfsClient, Article.AdLandingPageUrlUsage, dsvTableName: "AdLandingPageUrlUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAdLandingPageUrlUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressAdLandingPageUrlUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<AutomatedExtensionUsage>(hdfsClient, Article.AutomatedExtensionUsage, dsvTableName: "AutomatedExtensionUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAutomatedExtensionUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressAutomatedExtensionUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<AssetUsage>(hdfsClient, Article.AdAssetUsage, dsvTableName: "AdAssetUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAssetUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressAssetUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<FeedItemAdExtensionUsage>(hdfsClient, Article.FeedItemAdExtensionUsage, dsvTableName: "FeedItemAdExtensionUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressFeedItemAdExtensionUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.AdExtensionItemFeedItemId), 4)
                    .AttachOutput(Article.Delta_InProgressFeedItemAdExtensionUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.AdExtensionItemFeedItemId), 4),

                // SubCampaignUsage_NoIS is a sub fact for CampaignUsage with limited dimension columns, to improve performance for Campaign UI calls
                new HDFSPubMultipleOutputOperation<CampaignUsage>(hdfsClient, Article.CampaignUsage, dsvTableName: "CampaignUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressCampaignUsageAccount, t => GetBIAccountPartitionIds(t.CustomerId, t.AccountId), 4)
                    .AttachOutput(Article.Delta_InProgressSubCampaignUsage_NoIS, t => GetBIAccountPartitionIds(t.CustomerId, t.AccountId), 4)
                    .AttachOutput(Article.Delta_InProgressCampaignUsageAccount_Clickhouse, t => GetBIClickhouseAccountPartitionIds(t.CustomerId, t.AccountId), 4)
                    .AttachOutput(Article.Delta_InProgressSubCampaignUsage_NoIS_Clickhouse, t => GetBIClickhouseAccountPartitionIds(t.CustomerId, t.AccountId), 4),
              
                // SubAccountUsage_NoIS is a sub fact for AccountUsage with limited dimension columns, to improve performance for Campaign UI calls
                new HDFSPubMultipleOutputOperation<AdvertiserAccountUsage>(hdfsClient, Article.AdvertiserAccountUsage, dsvTableName: "AdvertiserAccountUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAccountUsage, t => GetBIAccountPartitionIds(t.CustomerId, t.AccountId), 4)
                    .AttachOutput(Article.Delta_InProgressSubAccountUsage_NoIS, t => GetBIAccountPartitionIds(t.CustomerId, t.AccountId), 4)
                    .AttachOutput(Article.Delta_InProgressAccountUsage_Clickhouse, t => GetBIClickhouseAccountPartitionIds(t.CustomerId, t.AccountId), 4)
                    .AttachOutput(Article.Delta_InProgressSubAccountUsage_NoIS_Clickhouse, t => GetBIClickhouseAccountPartitionIds(t.CustomerId, t.AccountId), 4),

                new HDFSPubMultipleOutputOperation<OrderItemDDAUsage>(hdfsClient, Article.OrderItemDDAUsage, dsvTableName: "OrderItemDDAUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressOrderItemDDAUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressOrderItemDDAUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                // BSC
                new HDFSPubMultipleOutputOperation<BSCProductOfferUsage>(hdfsClient, Article.BSCProductOfferUsage, dsvTableName: "BSCProductOfferUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressBSCProductOfferUsage_BSC2BI, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressBSCProductOfferCampaignUsage_BSC2BI, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.GlobalOfferId), 4)
                    .AttachOutput(Article.Delta_InProgressBSCProductOfferUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressBSCProductOfferCampaignUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.GlobalOfferId), 4),

                //Vertical
                new HDFSPubMultipleOutputOperation<HotelVerticalUsage>(hdfsClient, Article.HotelVerticalUsage, dsvTableName: "HotelVerticalUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                   .AttachOutput(Article.Delta_InProgressHotelVerticalUsage, t => GetVerticalShardGroupId(t.CustomerId, t.AccountId), 4)
                   .AttachOutput(Article.Delta_InProgressHotelVerticalUsage_Vertical2BI, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.VerticalItemId), 4)
                   .AttachOutput(Article.Delta_InProgressHotelVerticalUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.VerticalItemId), 4),

                new HDFSPubMultipleOutputOperation<HotelUsage>(hdfsClient, Article.HotelUsage, dsvTableName: "HotelUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressHotelUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressHotelUsageByHotel, t=> GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.CampaignHotelId), 4)
                    .AttachOutput(Article.Delta_InProgressHotelUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressHotelUsageByHotel_Clickhouse, t=> GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.CampaignHotelId), 4),

                new HDFSPubMultipleOutputOperation<AssetGroupUsage>(hdfsClient, Article.AssetGroupUsage, dsvTableName: "AssetGroupUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAssetGroupUsage, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.AssetGroupId), 4)
                    .AttachOutput(Article.Delta_InProgressAssetGroupUsage_Clickhouse, t => GetBIClickhouseAdGroupPartitionIds(t.CustomerId, t.AccountId, t.AssetGroupId), 4)
            };

            IDwcEventMonitor dwcMonitor;

            if (string.IsNullOrEmpty(this.envManagerConnectionString))
            {
                logger.LogError("Use test DWC Object for HDFS");
                dwcMonitor = new FastBiHDFSDWCOutputEventMonitorMock(System.TimeSpan.FromMinutes(5),
                    operations.Select(t => t.HDFSArticleName).ToArray(),
                    "https://api-httpfs-mtprime-chie.magnetar.binginternal.com:83/webhdfs/v1/MTPrime-CHIE01-0-Extra-6/projects/BingAdsBIPub/local/prod/FastBIAggs/output/Resources/Combination/680f2db3fc2c28028db87b52/Advertiser.dsv",
                    "https://api-httpfs-mtprime-chie.magnetar.binginternal.com:83/webhdfs/v1/MTPrime-CHIE01-0-Extra-6/projects/BingAdsBIPub/local/prod/FastBIAggs/output/Combination",
                    (baseFolder, article, eventTime) => $"https://api-httpfs-mtprime-chie.magnetar.binginternal.com:83/webhdfs/v1/MTPrime-CHIE01-0-Extra-6/projects/BingAdsBIPub/local/prod/FastBIAggs/output/Combination/{eventTime:yyyy}/{eventTime:MM}/{eventTime:dd}/{eventTime:HH}/{eventTime:mm}/{article}/");

            }
            else
            {
                dwcMonitor = new FastBiHDFSDWCOutputEventMonitor(System.TimeSpan.FromMinutes(5)
                    , operations.Select(t => t.HDFSArticleName).ToArray()
                    , (currentCosmos, sparkOutputFolder, dsvFileName) => $"https://be.{currentCosmos}.osdinfra.net/cosmos/adCenter.BICore.prod2/{sparkOutputFolder}/{dsvFileName}"
                    , (currentCosmos, sparkOutputFolder, cosmosArticleName, eventTime) => $"https://be.{currentCosmos}.osdinfra.net/cosmos/adCenter.BICore.prod2/{sparkOutputFolder}/{eventTime:yyyy}/{eventTime:MM}/{eventTime:dd}/{eventTime:HH}/{eventTime:mm}/table={cosmosArticleName}/"
                    , "CombinationAggsV3"
                    , groupName: "Production"
                    , envManagerConnectionString
                    , verifierEventName: "BingAds.Cosmos.FastBI.Processing.Done"
                    , dwcDaoProd: sharedDataDwcDao
                    , dwcDaoBCP: sharedDatabcpDwcDao
                    , aggType: "Advertiser"
                    );
            }

            var container = new SimpleJobContainer("FastBICosmosPubContainer");

            var outputDwcDao = sharedDataDwcDao;
            var outputDwcEventName = "SA_FastBI_Pub_Done";

            var jobOptions = new HDFSPubJobOptions
            {
                SupportRestatement = true,
                MaxRestatementWaitTimeInMinute = 30,
                RestatementCoolDownTimeInSecondGetter = () => DynamicConfig.Get().FastBIRestatementCoolDownTimeInSecond,
                TrackHDFSStreamPositions = true,
                ChunkedPublish = false,
                UpstreamCheckIntervalInSeconds = 2
            };

            container.AddJob(new HDFSPubJob("FastBIPublication", container, operations, dwcMonitor, eventQueue, jobOptions, outputDwcDao, outputDwcEventName, storageProvider));
            return container;
        }

        private JobContainer CreateFastBIHDFSPublisher(PubEventQueueV1 eventQueue, OperationCounter operationCounter)
        {
            int bufferSize = -1; //-1 means no boundedCapacity, if the buffer size is too small, we pay big spin cost when add into BlockingCollection. when BlockingCollection is empty, we pay big spin cost again when dequeue, need to move to a no spin framework.

            // slit count and reader count for different size of articles
            var smallOptions = new HDFSPubOperationOptions { SplitCount = 1, ReaderCount = 32, BufferCapacity = bufferSize, ReturnNewPosition = true, LineReaderType = LineReaderType.FastBI, BlobBlockSize = 512 * 1024, CompressionBlockSize = 16 * 1024, BlobTimeoutInSeconds = 10 };
            var midOptions = new HDFSPubOperationOptions { SplitCount = 2, ReaderCount = 16, BufferCapacity = bufferSize, ReturnNewPosition = true, LineReaderType = LineReaderType.FastBI, BlobBlockSize = 512 * 1024, CompressionBlockSize = 16 * 1024, BlobTimeoutInSeconds = 10 };
            var largeOptions = new HDFSPubOperationOptions { SplitCount = 4, ReaderCount = 16, BufferCapacity = bufferSize, ReturnNewPosition = true, LineReaderType = LineReaderType.FastBI, BlobBlockSize = 512 * 1024, CompressionBlockSize = 16 * 1024, BlobTimeoutInSeconds = 10 };


            var hdfsClient = HDFSClientManager.GetHDFSClientInstanceOrDefault(Config.DefaultHDFSClientName);
            var operations = new List<HDFSPubOperation>
            {
                new HDFSPubMultipleOutputOperation<AdUsage>(hdfsClient, Article.AdUsage, dsvTableName: "AdUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAdUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 8),

                new HDFSPubMultipleOutputOperation<AdFeedUsage>(hdfsClient, Article.AdFeedUsage, dsvTableName: "AdFeedUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressFeedItemUsage_HDFS, t => GetBIAdGroupFeedItemPartitionIds(t.CustomerId, t.AccountId, t.FeedItemId), 8),

                new HDFSPubMultipleOutputOperation<GenderAgeUsage>(hdfsClient, Article.GenderAgeUsage, dsvTableName: "GenderAgeUsage", options : smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressGenderAgeUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<HourlyAdAdExtensionClickTypeUsage>(hdfsClient, Article.HourlyAdAdExtensionClickTypeUsage, dsvTableName: "HourlyAdAdExtensionClickTypeUsage", options : largeOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAdAdExtensionClickTypeUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<HourlyElementAdUsage>(hdfsClient, Article.HourlyElementAdUsage, dsvTableName: "HourlyElementAdUsage", options : largeOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressElementAdUsageByOrder_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 8),

                new HDFSPubMultipleOutputOperation<HourlyElementOrderItemUsage>(hdfsClient, Article.HourlyElementOrderItemUsage, dsvTableName: "HourlyElementOrderItemUsage", options: midOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressElementOrderItemUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<HourlyOrderItemAdExtensionClickTypeUsage>(hdfsClient, Article.HourlyOrderItemAdExtensionClickTypeUsage, dsvTableName: "HourlyOrderItemAdExtensionClickTypeUsage", options : largeOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressOrderItemAdExtensionClickTypeUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<LocationHourUsage>(hdfsClient, Article.LocationHourUsage, dsvTableName: "LocationHourUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressLocationHourUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                // SubOrderItemUsage_NoIS is a sub fact for OrderItemUsage with limited dimension columns, to improve performance for Campaign UI calls
                new HDFSPubMultipleOutputOperation<OrderItemUsage>(hdfsClient, Article.OrderItemUsage, dsvTableName: "OrderItemUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressOrderItemUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressSubOrderItemUsage_NoIS_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<OrderTargetUsage>(hdfsClient, Article.OrderTargetUsage, dsvTableName: "OrderTargetUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressOrderTargetUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<OrderUsage>(hdfsClient, Article.OrderUsage, dsvTableName: "OrderUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressOrderUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<ProfessionalDemographicsUsage>(hdfsClient, Article.ProfessionalDemographicsUsage, dsvTableName: "ProfessionalDemographicsUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressProfessionalDemographicsUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<PublisherPlacementUsage>(hdfsClient, Article.PublisherPlacementUsage, dsvTableName: "PublisherPlacementUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressPublisherPlacementUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<ContentPerformanceUsage>(hdfsClient, Article.ContentPerformanceUsage, dsvTableName: "ContentPerformanceUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressContentPerformanceUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<RadiusTargetedLocationHourUsage>(hdfsClient, Article.RadiusTargetedLocationHourUsage, dsvTableName: "RadiusTargetedLocationHourUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressRadiusTargetedLocationHourUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<AutoTargetUsage>(hdfsClient, Article.AutoTargetUsage, dsvTableName: "AutoTargetUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAutoTargetUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<AdLandingPageUrlUsage>(hdfsClient, Article.AdLandingPageUrlUsage, dsvTableName: "AdLandingPageUrlUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAdLandingPageUrlUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<AutomatedExtensionUsage>(hdfsClient, Article.AutomatedExtensionUsage, dsvTableName: "AutomatedExtensionUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAutomatedExtensionUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<AssetUsage>(hdfsClient, Article.AdAssetUsage, dsvTableName: "AdAssetUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressAssetUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                new HDFSPubMultipleOutputOperation<FeedItemAdExtensionUsage>(hdfsClient, Article.FeedItemAdExtensionUsage, dsvTableName: "FeedItemAdExtensionUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressFeedItemAdExtensionUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.AdExtensionItemFeedItemId), 4),

                // SubCampaignUsage_NoIS is a sub fact for CampaignUsage with limited dimension columns, to improve performance for Campaign UI calls
                new HDFSPubMultipleOutputOperation<CampaignUsage>(hdfsClient, Article.CampaignUsage, dsvTableName: "CampaignUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressCampaignUsageAccount_HDFS, t => GetBIAccountPartitionIds(t.CustomerId, t.AccountId), 4)
                    .AttachOutput(Article.Delta_InProgressSubCampaignUsage_NoIS_HDFS, t => GetBIAccountPartitionIds(t.CustomerId, t.AccountId), 4),
              
                // SubAccountUsage_NoIS is a sub fact for AccountUsage with limited dimension columns, to improve performance for Campaign UI calls
                new HDFSPubMultipleOutputOperation<AdvertiserAccountUsage>(hdfsClient, Article.AdvertiserAccountUsage, dsvTableName: "AdvertiserAccountUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                .AttachOutput(Article.Delta_InProgressAccountUsage_HDFS, t => GetBIAccountPartitionIds(t.CustomerId, t.AccountId), 4)
                .AttachOutput(Article.Delta_InProgressSubAccountUsage_NoIS_HDFS, t => GetBIAccountPartitionIds(t.CustomerId, t.AccountId), 4),

                new HDFSPubMultipleOutputOperation<OrderItemDDAUsage>(hdfsClient, Article.OrderItemDDAUsage, dsvTableName: "OrderItemDDAUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressOrderItemDDAUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4),

                // BSC
                new HDFSPubMultipleOutputOperation<BSCProductOfferUsage>(hdfsClient, Article.BSCProductOfferUsage, dsvTableName: "BSCProductOfferUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressBSCProductOfferUsage_BSC2BI_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressBSCProductOfferCampaignUsage_BSC2BI_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.GlobalOfferId), 4),

                //Vertical
                new HDFSPubMultipleOutputOperation<HotelVerticalUsage>(hdfsClient, Article.HotelVerticalUsage, dsvTableName: "HotelVerticalUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                   .AttachOutput(Article.Delta_InProgressHotelVerticalUsage_HDFS, t => GetVerticalShardGroupId(t.CustomerId, t.AccountId), 4)
                   .AttachOutput(Article.Delta_InProgressHotelVerticalUsage_Vertical2BI_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.VerticalItemId), 4),

                new HDFSPubMultipleOutputOperation<HotelUsage>(hdfsClient, Article.HotelUsage, dsvTableName: "HotelUsage", options: smallOptions, operationCounter: operationCounter, storageProvider: storageProvider)
                    .AttachOutput(Article.Delta_InProgressHotelUsage_HDFS, t => GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.OrderId), 4)
                    .AttachOutput(Article.Delta_InProgressHotelUsageByHotel_HDFS, t=> GetBIAdGroupPartitionIds(t.CustomerId, t.AccountId, t.CampaignHotelId), 4)
        };

            IDwcEventMonitor dwcMonitor;

            if (string.IsNullOrEmpty(this.envManagerConnectionString))
            {
                logger.LogError("Use test DWC Object");
                dwcMonitor = new FastBiHDFSDWCOutputEventMonitorMock(System.TimeSpan.FromMinutes(5),
                    operations.Select(t => t.HDFSArticleName).ToArray(),
                    "https://api-httpfs-mtprime-chie.magnetar.binginternal.com:83/webhdfs/v1/MTPrime-CHIE01-0-Extra-6/projects/BingAdsBIPub/local/prod/FastBIAggs/output/Resources/Combination/680f2db3fc2c28028db87b52/Advertiser.dsv",
                    "https://api-httpfs-mtprime-chie.magnetar.binginternal.com:83/webhdfs/v1/MTPrime-CHIE01-0-Extra-6/projects/BingAdsBIPub/local/prod/FastBIAggs/output/Combination",
                    (baseFolder, article, eventTime) => $"https://api-httpfs-mtprime-chie.magnetar.binginternal.com:83/webhdfs/v1/MTPrime-CHIE01-0-Extra-6/projects/BingAdsBIPub/local/prod/FastBIAggs/output/Combination/{eventTime:yyyy}/{eventTime:MM}/{eventTime:dd}/{eventTime:HH}/{eventTime:mm}/{article}/");

            }
            else
            {
                //TODO: update this to HDFS results
                //dwcMonitor = new FastBIDwcEventMonitor(groupName: "Production",
                //                    componentName: "AdvertiserAggs",
                //                    dwcEventName: "CombinationAggsV3",
                //                    envManagerConnectionString: this.envManagerConnectionString,
                //                    minimalEventDelayInMin: 10,
                //                    verifierEventName: "BingAds.Cosmos.FastBI.Processing.Done",
                //                    dwcDaoProd: sharedDataDwcDao,
                //                    dwcDaoBCP: sharedDatabcpDwcDao
                //                    );

                dwcMonitor = new FastBiHDFSDWCOutputEventMonitor(System.TimeSpan.FromMinutes(5)
                   ,operations.Select(t => t.HDFSArticleName).ToArray()
                   , (currentCosmos, sparkOutputFolder, dsvFileName) => $"https://be.{currentCosmos}.osdinfra.net/cosmos/adCenter.BICore.prod2/{sparkOutputFolder}/{dsvFileName}"
                   , (currentCosmos, sparkOutputFolder, cosmosArticleName, eventTime) => $"https://be.{currentCosmos}.osdinfra.net/cosmos/adCenter.BICore.prod2/{sparkOutputFolder}/{eventTime:yyyy}/{eventTime:MM}/{eventTime:dd}/{eventTime:HH}/{eventTime:mm}/table={cosmosArticleName}/"
                   , "CombinationAggsV3"
                   , groupName: "Production"
                   , envManagerConnectionString
                   , verifierEventName: "BingAds.Cosmos.FastBI.Processing.Done"
                   , dwcDaoProd: sharedDataDwcDao
                   , dwcDaoBCP: sharedDatabcpDwcDao
                   , aggType: "Advertiser"
                   );
            }

            var container = new SimpleJobContainer("FastBIHDFSPubContainer");

            var outputDwcDao = sharedDataDwcDao;
            var outputDwcEventName = "SA_FastBI_HDFS_Pub_Done";

            var jobOptions = new HDFSPubJobOptions
            {
                SupportRestatement = true,
                MaxRestatementWaitTimeInMinute = 30,
                RestatementCoolDownTimeInSecondGetter = () => DynamicConfig.Get().FastBIRestatementCoolDownTimeInSecond,
                TrackHDFSStreamPositions = true,
                ChunkedPublish = false,
                UpstreamCheckIntervalInSeconds = 2
            };

            container.AddJob(new HDFSPubJob("FastBIHDFSPublication", container, operations, dwcMonitor, eventQueue, jobOptions, outputDwcDao, outputDwcEventName, storageProvider));
            return container;
        }

        private JobContainer CreateFastBIAccount2Subscriber(PubEventQueueV1 eventQueue, OperationCounter operationCounter)
        {
            var processes = new List<DBArticleProcess>
            {
                //Note: order by article size desc (i.e. big articles first)
                new BIDBArticleProcess<CampaignUsage>(Article.Delta_InProgressCampaignUsageAccount, DBJobConfig.PartitionByPartitionId, "Delta_InProgressCampaignUsage"),
                new BIDBArticleProcess<CampaignUsage>(Article.Delta_InProgressSubCampaignUsage_NoIS, DBJobConfig.PartitionByPartitionId ,"Delta_InProgressSubCampaignUsage_NoIS"),
                new BIDBArticleProcess<AdvertiserAccountUsage>(Article.Delta_InProgressAccountUsage, DBJobConfig.PartitionByPartitionId ,"Delta_InProgressAccountUsage"),
                new BIDBArticleProcess<AdvertiserAccountUsage>(Article.Delta_InProgressSubAccountUsage_NoIS, DBJobConfig.PartitionByPartitionId ,"Delta_InProgressSubAccountUsage_NoIS"),
            };

            // Note: changing the operation id is a broken change
            // if some machines still run a different binary, it cannot find this operation name
            // then it will success silently. see header comment in Operation.cs
            var operation = new BIDBSubOperation("BIDBSubOperationAccount", processes, storageProvider, operationCounter);
            var subContainer = new DBSubContainer("FastBIAccount2SubContainer", storageProvider, operation, eventQueue);
            return subContainer;
        }

        private JobContainer CreateFastBIAdGroup2Subscriber(PubEventQueueV1 eventQueue, OperationCounter operationCounter)
        {
            var processes = new List<DBArticleProcess>
            {
                // Note: order by article size desc (i.e. big articles first)
                new BIDBArticleProcess<HourlyElementAdUsage>(Article.Delta_InProgressElementAdUsageByOrder, DBJobConfig.PartitionByPartitionId ,"Delta_InProgressElementAdUsageByOrder"),
                new BIDBArticleProcess<HourlyAdAdExtensionClickTypeUsage>(Article.Delta_InProgressAdAdExtensionClickTypeUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressAdAdExtensionClickTypeUsage"),
                new BIDBArticleProcess<AdUsage>(Article.Delta_InProgressAdUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressAdUsage"),
                new BIDBArticleProcess<HourlyElementOrderItemUsage>(Article.Delta_InProgressElementOrderItemUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressElementOrderItemUsage"),
                new BIDBArticleProcess<GenderAgeUsage>(Article.Delta_InProgressGenderAgeUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressGenderAgeUsage"),
                new BIDBArticleProcess<LocationHourUsage>(Article.Delta_InProgressLocationHourUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressLocationHourUsage"),
                new BIDBArticleProcess<HourlyOrderItemAdExtensionClickTypeUsage>(Article.Delta_InProgressOrderItemAdExtensionClickTypeUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressOrderItemAdExtensionClickTypeUsage"),
                new BIDBArticleProcess<OrderItemUsage>(Article.Delta_InProgressOrderItemUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressOrderItemUsage"),
                new BIDBArticleProcess<OrderItemUsage>(Article.Delta_InProgressSubOrderItemUsage_NoIS, DBJobConfig.PartitionByPartitionId, "Delta_InProgressSubOrderItemUsage_NoIS"),
                new BIDBArticleProcess<OrderTargetUsage>(Article.Delta_InProgressOrderTargetUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressOrderTargetUsage"),
                new BIDBArticleProcess<OrderUsage>(Article.Delta_InProgressOrderUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressOrderUsage"),
                new BIDBArticleProcess<PublisherPlacementUsage>(Article.Delta_InProgressPublisherPlacementUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressPublisherPlacementUsage"),
                new BIDBArticleProcess<ContentPerformanceUsage>(Article.Delta_InProgressContentPerformanceUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressContentPerformanceUsage"),
                new BIDBArticleProcess<RadiusTargetedLocationHourUsage>(Article.Delta_InProgressRadiusTargetedLocationHourUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressRadiusTargetedLocationHourUsage"),
                new BIDBArticleProcess<AutoTargetUsage>(Article.Delta_InProgressAutoTargetUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressAutoTargetUsage"),
                new BIDBArticleProcess<AdLandingPageUrlUsage>(Article.Delta_InProgressAdLandingPageUrlUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressAdLandingPageUrlUsage"),
                new BIDBArticleProcess<AssetUsage>(Article.Delta_InProgressAssetUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressAssetUsage"),
                new BIDBArticleProcess<ProfessionalDemographicsUsage>(Article.Delta_InProgressProfessionalDemographicsUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressProfessionalDemographicsUsage"),
                new BIDBArticleProcess<AdFeedUsage>(Article.Delta_InProgressFeedItemUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressFeedItemUsage"),
                new BIDBArticleProcess<FeedItemAdExtensionUsage>(Article.Delta_InProgressFeedItemAdExtensionUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressFeedItemAdExtensionUsage"),
                new BIDBArticleProcess<AutomatedExtensionUsage>(Article.Delta_InProgressAutomatedExtensionUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressAutomatedExtensionUsage"),
                new BIDBArticleProcess<HotelUsage>(Article.Delta_InProgressHotelUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressHotelUsage"),
                new BIDBArticleProcess<HotelUsage>(Article.Delta_InProgressHotelUsageByHotel, DBJobConfig.PartitionByPartitionId, "Delta_InProgressHotelUsageByHotel"),
                //BSC data to advbi
                new BIDBArticleProcess<BSCProductOfferUsage>(Article.Delta_InProgressBSCProductOfferUsage_BSC2BI, DBJobConfig.PartitionByPartitionId, "InProgressProductOfferUsage_delta", isBSCData2BI: true),
                new BIDBArticleProcess<HotelVerticalUsage>(Article.Delta_InProgressHotelVerticalUsage_Vertical2BI, DBJobConfig.PartitionByPartitionId, "Delta_InProgressHotelVerticalUsage"),
                new BIDBArticleProcess<BSCProductOfferUsage>(Article.Delta_InProgressBSCProductOfferCampaignUsage_BSC2BI, DBJobConfig.PartitionByPartitionId, "InProgressProductOfferCampaignUsage_delta", isBSCData2BI: true),
                new BIDBArticleProcess<OrderItemDDAUsage>(Article.Delta_InProgressOrderItemDDAUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressOrderItemDDAUsage"),
                new BIDBArticleProcess<AssetGroupUsage>(Article.Delta_InProgressAssetGroupUsage, DBJobConfig.PartitionByPartitionId, "Delta_InProgressAssetGroupUsage")
        };

            // Note: changing the operation id is a broken change
            // if some machines still run a different binary, it cannot find this operation name
            // then it will success silently. see header comment in Operation.cs
            var operation = new BIDBSubOperation("BIDBSubOperation", processes, storageProvider, operationCounter);
            var subContainer = new DBSubContainer("FastBIAdGroup2SubContainer", storageProvider, operation, eventQueue);
            return subContainer;
        }

        private JobContainer CreateFastBIVerticalSubscriber(PubEventQueueV1 eventQueue, OperationCounter operationCounter)
        {
            var processes = new List<DBArticleProcess>
            {
                //Note: order by article size desc (i.e. big articles first)
                new BIDBArticleProcess<HotelVerticalUsage>(Article.Delta_InProgressHotelVerticalUsage, DBJobConfig.PartitionByShardGroupId, "Delta_InProgressHotelVerticalUsage"),
            };

            // Note: changing the operation id is a broken change
            // if some machines still run a different binary, it cannot find this operation name
            // then it will success silently. see header comment in Operation.cs
            var operation = new BIDBSubOperation("FastBIVerticalSubOp", processes, storageProvider, operationCounter);
            var subContainer = new DBSubContainer("FastBIVerticalSubContainer", storageProvider, operation, eventQueue);
            return subContainer;
        }

        private async Task<JobContainer> CreateFastBIClickhouseAccountSubscriber(PubEventQueueV1 eventQueue, OperationCounter operationCounter, CancellationToken cancellationToken)
        {
            var processes = new List<IClickhouseSubArticleProcess>
            {
                //Note: order by article size desc (i.e. big articles first)
                new ClickhouseBISubArticleProcess<CampaignUsage>(Article.Delta_InProgressCampaignUsageAccount_Clickhouse, "BICH", "InProgressCampaignUsage"),
                new ClickhouseBISubArticleProcess<CampaignUsage>(Article.Delta_InProgressSubCampaignUsage_NoIS_Clickhouse, "BICH","InProgressSubCampaignUsage_NoIS"),
                new ClickhouseBISubArticleProcess<AdvertiserAccountUsage>(Article.Delta_InProgressAccountUsage_Clickhouse, "BICH","InProgressAccountUsage"),
                new ClickhouseBISubArticleProcess<AdvertiserAccountUsage>(Article.Delta_InProgressSubAccountUsage_NoIS_Clickhouse, "BICH","InProgressSubAccountUsage_NoIS"),
            };

            // Note: changing the operation id is a broken change
            // if some machines still run a different binary, it cannot find this operation name
            // then it will success silently. see header comment in Operation.cs
            var operation = new ClickhouseSubOperation("BIACDBClickhouseSubOperation", processes, operationCounter, storageProvider);
            var directoryOperation = new ClickhouseShareDirectoryOperation("BIACDBClickhouseShareDirectoryOperation", processes, operationCounter);

            var bidbBlobConnection = Config.IsLocal ? config("BIDBClickhouseBlobConnection")
                : await this.saKeyVaultClient.GetConnectionStringAsync(config("SAKeyVaultName"), config("BIDBClickhouseBlobConnection"), logger, cancellationToken).ConfigureAwait(false);

            var storageAccount = config("BIDBClickhouseSourceStorageAccount");
            var bidbLoaderEventQueue = new ClickhouseLoaderEventQueue(storageAccount, "BIDBClickhouseLoaderEventQueue");
            var articleTableMapping = processes.ToDictionary(t => t.Article, t => t.TableName);
            var metaFileOperation = new ClickhouseMetaFileOperation("BIACDBClickhouseMetaFileOperation", bidbLoaderEventQueue, articleTableMapping, operationCounter, true);


            var fileShare = config("BIDBClickhouseFileShareFastBI");
            var container = new ClickhouseSubContainer("FastBIClickhouseAccountSubContainer", storageAccount, bidbBlobConnection, operation, metaFileOperation, directoryOperation,
                fileShare, GetFastBIClickhouseAccountRootDir, eventQueue, new ClickhouseSubJobOptions(), storageProvider, eventIdInMetaFiles: true, diskFileRoot: config("BIDBClickhouseDiskFileRoot"));
            return container;
        }

        private async Task<JobContainer> CreateFastBIClickhouseAdGroupSubscriber(PubEventQueueV1 eventQueue, OperationCounter operationCounter, CancellationToken cancellationToken)
        {
            var processes = new List<IClickhouseSubArticleProcess>
            {
                // Note: order by article size desc (i.e. big articles first)
                new ClickhouseBISubArticleProcess<HourlyElementAdUsage>(Article.Delta_InProgressElementAdUsageByOrder_Clickhouse, "BICH", "InProgressElementAdUsage"),
                new ClickhouseBISubArticleProcess<HourlyAdAdExtensionClickTypeUsage>(Article.Delta_InProgressAdAdExtensionClickTypeUsage_Clickhouse, "BICH", "InProgressAdAdExtensionClickTypeUsage"),
                new ClickhouseBISubArticleProcess<AdUsage>(Article.Delta_InProgressAdUsage_Clickhouse, "BICH", "InProgressAdUsage"),
                new ClickhouseBISubArticleProcess<HourlyElementOrderItemUsage>(Article.Delta_InProgressElementOrderItemUsage_Clickhouse, "BICH", "InProgressElementOrderItemUsage"),
                new ClickhouseBISubArticleProcess<GenderAgeUsage>(Article.Delta_InProgressGenderAgeUsage_Clickhouse, "BICH", "InProgressGenderAgeUsage"),
                new ClickhouseBISubArticleProcess<LocationHourUsage>(Article.Delta_InProgressLocationHourUsage_Clickhouse, "BICH", "InProgressLocationHourUsage"),
                new ClickhouseBISubArticleProcess<HourlyOrderItemAdExtensionClickTypeUsage>(Article.Delta_InProgressOrderItemAdExtensionClickTypeUsage_Clickhouse, "BICH", "InProgressOrderItemAdExtensionClickTypeUsage"),
                new ClickhouseBISubArticleProcess<OrderItemUsage>(Article.Delta_InProgressOrderItemUsage_Clickhouse, "BICH", "InProgressOrderItemUsage"),
                new ClickhouseBISubArticleProcess<OrderItemUsage>(Article.Delta_InProgressSubOrderItemUsage_NoIS_Clickhouse, "BICH", "InProgressSubOrderItemUsage_NoIS"),
                new ClickhouseBISubArticleProcess<OrderTargetUsage>(Article.Delta_InProgressOrderTargetUsage_Clickhouse, "BICH", "InProgressOrderTargetUsage"),
                new ClickhouseBISubArticleProcess<OrderUsage>(Article.Delta_InProgressOrderUsage_Clickhouse, "BICH", "InProgressOrderUsage"),
                new ClickhouseBISubArticleProcess<PublisherPlacementUsage>(Article.Delta_InProgressPublisherPlacementUsage_Clickhouse, "BICH", "InProgressPublisherPlacementUsage"),
                new ClickhouseBISubArticleProcess<ContentPerformanceUsage>(Article.Delta_InProgressContentPerformanceUsage_Clickhouse, "BICH", "InProgressContentPerformanceUsage"),
                new ClickhouseBISubArticleProcess<RadiusTargetedLocationHourUsage>(Article.Delta_InProgressRadiusTargetedLocationHourUsage_Clickhouse, "BICH", "InProgressRadiusTargetedLocationHourUsage"),
                new ClickhouseBISubArticleProcess<AutoTargetUsage>(Article.Delta_InProgressAutoTargetUsage_Clickhouse, "BICH", "InProgressAutoTargetUsage"),
                new ClickhouseBISubArticleProcess<AdLandingPageUrlUsage>(Article.Delta_InProgressAdLandingPageUrlUsage_Clickhouse, "BICH", "InProgressAdLandingPageUrlUsage"),
                new ClickhouseBISubArticleProcess<AssetUsage>(Article.Delta_InProgressAssetUsage_Clickhouse, "BICH", "InProgressAssetUsage"),
                new ClickhouseBISubArticleProcess<ProfessionalDemographicsUsage>(Article.Delta_InProgressProfessionalDemographicsUsage_Clickhouse, "BICH", "InProgressProfessionalDemographicsUsage"),
                new ClickhouseBISubArticleProcess<AdFeedUsage>(Article.Delta_InProgressFeedItemUsage_Clickhouse, "BICH", "InProgressAdFeedUsage"),
                new ClickhouseBISubArticleProcess<FeedItemAdExtensionUsage>(Article.Delta_InProgressFeedItemAdExtensionUsage_Clickhouse, "BICH", "InProgressFeedItemAdExtensionUsage"),
                new ClickhouseBISubArticleProcess<AutomatedExtensionUsage>(Article.Delta_InProgressAutomatedExtensionUsage_Clickhouse, "BICH", "InProgressAutomatedExtensionUsage"),
                new ClickhouseBISubArticleProcess<OrderItemDDAUsage>(Article.Delta_InProgressOrderItemDDAUsage_Clickhouse, "BICH", "InProgressOrderItemDDAUsage"),
                new ClickhouseBISubArticleProcess<AssetGroupUsage>(Article.Delta_InProgressAssetGroupUsage_Clickhouse, "BICH", "InProgressAssetGroupUsage"),
                new ClickhouseBISubArticleProcess<HotelVerticalUsage>(Article.Delta_InProgressHotelVerticalUsage_Clickhouse, "BICH", "InProgressHotelVerticalUsage"),
                new ClickhouseBISubArticleProcess<HotelUsage>(Article.Delta_InProgressHotelUsage_Clickhouse, "BICH", "InProgressHotelUsage"),
                new ClickhouseBISubArticleProcess<HotelUsage>(Article.Delta_InProgressHotelUsageByHotel_Clickhouse, "BICH", "InProgressHotelUsageByHotel"),
                new ClickhouseBISubArticleProcess<BSCProductOfferUsage>(Article.Delta_InProgressBSCProductOfferUsage_Clickhouse, "BICH", "InProgressProductOfferUsage"),
                new ClickhouseBISubArticleProcess<BSCProductOfferUsage>(Article.Delta_InProgressBSCProductOfferCampaignUsage_Clickhouse, "BICH", "InProgressProductOfferCampaignUsage")
            };

            // Note: changing the operation id is a broken change
            // if some machines still run a different binary, it cannot find this operation name
            // then it will success silently. see header comment in Operation.cs
            var operation = new ClickhouseSubOperation("BIDBClickhouseSubOperation", processes, operationCounter, storageProvider);
            var directoryOperation = new ClickhouseShareDirectoryOperation("BIDBClickhouseShareDirectoryOperation", processes, operationCounter);

            var bidbBlobConnection = Config.IsLocal ? config("BIDBClickhouseBlobConnection")
                : await this.saKeyVaultClient.GetConnectionStringAsync(config("SAKeyVaultName"), config("BIDBClickhouseBlobConnection"), logger, cancellationToken).ConfigureAwait(false);

            var storageAccount = config("BIDBClickhouseSourceStorageAccount");
            var bidbLoaderEventQueue = new ClickhouseLoaderEventQueue(storageAccount, "BIDBClickhouseLoaderEventQueue");
            var articleTableMapping = processes.ToDictionary(t => t.Article, t => t.TableName);
            var metaFileOperation = new ClickhouseMetaFileOperation("BIDBClickhouseMetaFileOperation", bidbLoaderEventQueue, articleTableMapping, operationCounter, true);


            var fileShare = config("BIDBClickhouseFileShareFastBI");
            var container = new ClickhouseSubContainer("FastBIClickhouseAdGroupSubContainer", storageAccount, bidbBlobConnection, operation, metaFileOperation, directoryOperation,
                fileShare, GetFastBIClickhouseAdGroupRootDir, eventQueue, new ClickhouseSubJobOptions(), storageProvider, eventIdInMetaFiles: true, diskFileRoot: config("BIDBClickhouseDiskFileRoot"));
            return container;
        }

        private string GetFastBIClickhouseAccountRootDir(int partitionId, DateTime eventTime)
        {
            return "FastBI/BI/Account/" + eventTime.ToString("yyyy") + "/" + eventTime.ToString("MM") + "/" + eventTime.ToString("dd") + "/" +
                eventTime.ToString("HH") + "/" + partitionId;
        }

        private string GetFastBIClickhouseAdGroupRootDir(int partitionId, DateTime eventTime)
        {
            return "FastBI/BI/AdGroup/" + eventTime.ToString("yyyy") + "/" + eventTime.ToString("MM") + "/" + eventTime.ToString("dd") + "/" +
                eventTime.ToString("HH") + "/" + partitionId;
        }
    }
}