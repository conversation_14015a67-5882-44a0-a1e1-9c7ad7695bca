﻿namespace Microsoft.Advertising.Test.Reporting.APIV13.Proxy;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization.Metadata;

public static partial class RestApiGeneration
{
    public partial class OpenApiKnownTypesMapping
    {
        public static Dictionary<Type, Dictionary<Type, string>> TypesMap = new Dictionary<Type, Dictionary<Type, string>>
        {
            // adapi.microsoft.com entities
            { typeof(adapi.microsoft.com.ApplicationFault), new Dictionary<Type, string> {                
                { typeof(bingads.microsoft.com.Reporting.v13.ApiFaultDetail), "ApiFaultDetail" },
                { typeof(adapi.microsoft.com.AdApiFaultDetail), "AdApiFaultDetail" },
                { typeof(adapi.microsoft.com.ApplicationFault), "ApplicationFault" } }
            },

            // bingads.microsoft.com.Reporting.v13 entities
            { typeof(bingads.microsoft.com.Reporting.v13.ReportRequest), new Dictionary<Type, string> {                
                { typeof(bingads.microsoft.com.Reporting.v13.AccountPerformanceReportRequest), "AccountPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.CampaignPerformanceReportRequest), "CampaignPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.AdDynamicTextPerformanceReportRequest), "AdDynamicTextPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.AdGroupPerformanceReportRequest), "AdGroupPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.AdPerformanceReportRequest), "AdPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.KeywordPerformanceReportRequest), "KeywordPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.DestinationUrlPerformanceReportRequest), "DestinationUrlPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.BudgetSummaryReportRequest), "BudgetSummaryReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.AgeGenderAudienceReportRequest), "AgeGenderAudienceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.ProfessionalDemographicsAudienceReportRequest), "ProfessionalDemographicsAudienceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.UserLocationPerformanceReportRequest), "UserLocationPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.PublisherUsagePerformanceReportRequest), "PublisherUsagePerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.SearchQueryPerformanceReportRequest), "SearchQueryPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.ConversionPerformanceReportRequest), "ConversionPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.GoalsAndFunnelsReportRequest), "GoalsAndFunnelsReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.NegativeKeywordConflictReportRequest), "NegativeKeywordConflictReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.SearchCampaignChangeHistoryReportRequest), "SearchCampaignChangeHistoryReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.AdExtensionByAdReportRequest), "AdExtensionByAdReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.AdExtensionByKeywordReportRequest), "AdExtensionByKeywordReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.AudiencePerformanceReportRequest), "AudiencePerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.AdExtensionDetailReportRequest), "AdExtensionDetailReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.ShareOfVoiceReportRequest), "ShareOfVoiceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.ProductDimensionPerformanceReportRequest), "ProductDimensionPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.ProductPartitionPerformanceReportRequest), "ProductPartitionPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.ProductPartitionUnitPerformanceReportRequest), "ProductPartitionUnitPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.ProductSearchQueryPerformanceReportRequest), "ProductSearchQueryPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.ProductMatchCountReportRequest), "ProductMatchCountReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.ProductNegativeKeywordConflictReportRequest), "ProductNegativeKeywordConflictReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.CallDetailReportRequest), "CallDetailReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.GeographicPerformanceReportRequest), "GeographicPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.DSASearchQueryPerformanceReportRequest), "DSASearchQueryPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.DSAAutoTargetPerformanceReportRequest), "DSAAutoTargetPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.DSACategoryPerformanceReportRequest), "DSACategoryPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.HotelDimensionPerformanceReportRequest), "HotelDimensionPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.HotelGroupPerformanceReportRequest), "HotelGroupPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.AssetGroupPerformanceReportRequest), "AssetGroupPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.SearchInsightPerformanceReportRequest), "SearchInsightPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.AssetPerformanceReportRequest), "AssetPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.CategoryInsightsReportRequest), "CategoryInsightsReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.CategoryClickCoverageReportRequest), "CategoryClickCoverageReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.CombinationPerformanceReportRequest), "CombinationPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.AppsPerformanceReportRequest), "AppsPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.FeedItemPerformanceReportRequest), "FeedItemPerformanceReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.TravelQueryInsightReportRequest), "TravelQueryInsightReportRequest" },
                { typeof(bingads.microsoft.com.Reporting.v13.ReportRequest), "ReportRequest" } }
            },

       };
    }
}