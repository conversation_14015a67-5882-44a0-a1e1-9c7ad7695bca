﻿using OrderManagementSystem.Processors;

namespace OrderManagementSystem.Services
{
    public sealed class XandrSyncMonitoringService(IServiceScopeFactory serviceScopeFactory, ILogger<XandrSyncMonitoringService> logger) : BackgroundService
    {
        private const int MONITORING_WAIT_TIME_SECONDS = 12 * 60 * 60;

        protected override async Task ExecuteAsync(CancellationToken cancellationToken)
        {
            logger.LogInformation($"[{nameof(XandrSyncMonitoringService)}] Starting execution");
            await RunMonitoring(cancellationToken);
        }

        private async Task RunMonitoring(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                using (IServiceScope scope = serviceScopeFactory.CreateScope())
                {
                    logger.BeginScopeWithTracking(newRequestId: Guid.NewGuid(), newTrackingId: Guid.NewGuid());
                    logger.LogInformation("XandrSyncMonitoringService.RunMonitoring: Starting monitoring execution");
                    IXandrSyncMonitoringProcessor processor = scope.ServiceProvider.GetRequiredService<IXandrSyncMonitoringProcessor>();

                    //Step 1: get committed entities from OMS
                    var getResult = await processor.GetOMSCommittedEntities();
                    if (getResult.Failed || getResult.Entity == null)
                    {
                        if (getResult.Failed) logger.LogInformation($"XandrSyncMonitoringService.RunMonitoring: DB call failed, please investigate");
                        else logger.LogInformation($"XandrSyncMonitoringService.RunMonitoring: No committed entities on the DB, so no monitoring checks required");

                        await Task.Delay(TimeSpan.FromSeconds(MONITORING_WAIT_TIME_SECONDS));
                        continue;
                    }

                    var commits = getResult.Entity!;
                    logger.LogInformation($"XandrSyncMonitoringService.RunMonitoring: Commit entities pulled from DB and there is at least 1 committed entity. Committed media plans: {commits.Keys.Count}");

                    //Step 2: poll Monetize and check each entity
                    var issueResult = await processor.PollAndCompareMonetize(commits);
                    if (issueResult.Failed)
                    {
                        //Means some Monetize call failed. Let's log it
                        logger.LogWarning($"XandrSyncMonitoringService.RunMonitoring: Some Monetize call(s) failed. Check Kusto logs for failure details");
                    }

                    logger.LogInformation($"XandrSyncMonitoringService.RunMonitoring: Poll from Monetize completed. Write any issues to DB. Num issues: {issueResult.Entity?.Length ?? 0}");

                    //Step 3: sync results to DB (always write back because even if no issues, we need to do a write to mark any previous issues as "resolved" (if they don't exist because they were fixed)
                    if (issueResult.Entity != null)
                    {
                        await processor.SyncResultsToDB(issueResult.Entity);
                    }

                    logger.LogInformation("XandrSyncMonitoringService.RunMonitoring: Completed monitoring execution");
                    await Task.Delay(TimeSpan.FromSeconds(MONITORING_WAIT_TIME_SECONDS));
                }
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            logger.LogInformation($"[{nameof(XandrSyncMonitoringService)}] Stopping execution");
            await base.StopAsync(cancellationToken);
        }
    }
}
